// scripts/check-branch.ts
import { $ } from 'bun';

async function checkGitBranch(): Promise<true> {
  const branchName = (await $`git rev-parse --abbrev-ref HEAD`.text()).trim();

  const restrictedBranches = ['main', 'development'];

  if (restrictedBranches.includes(branchName.toLowerCase())) {
    throw new Error('Cannot commit directly to main or development branch');
  }

  return true;
}

try {
  await checkGitBranch();
  process.exit(0);
} catch (error: any) {
  console.error('\x1B[31m%s\x1B[0m', `🚫 ${error.message}`);
  console.error(
    '\x1B[33m%s\x1B[0m',
    'Please create a feature branch and commit there.'
  );
  process.exit(1);
}
