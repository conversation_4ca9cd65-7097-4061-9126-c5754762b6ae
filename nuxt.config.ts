// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  // Nuxt Modules
  modules: [
    '@nuxthub/core',
    '@nuxt/eslint',
    '@nuxtjs/tailwindcss',
    '@nuxtjs/color-mode',
    '@vueuse/nuxt',
    'shadcn-nuxt',
    '@pinia/nuxt',
    '@nuxt/icon',
    '@nuxtjs/google-fonts',
    '@nuxt/image',
    'nuxt-swiper',
    'nuxt-svgo',
    'nuxt-auth-utils',
    'nuxt-mcp',
    'nuxt-aos',
    '@nuxt/scripts',
    '@nuxtjs/robots',
    'nuxt-simple-sitemap',
    // Local module for pre-rendering routes
    // './modules/prerender-routes',
  ],
  // Site config for SEO
  site: {
    url: process.env.NUXT_HUB_APP_URL || 'https://dbcollegemannuthy.edu.in/',
  },
  // Runtime configuration for nuxt-auth-utils
  runtimeConfig: {
    appUrl: process.env.NUXT_HUB_APP_URL || 'https://dbcollegemannuthy.edu.in/',
    public: {
      appUrl:
        process.env.NUXT_HUB_APP_URL || 'https://dbcollegemannuthy.edu.in/',
      siteUrl:
        process.env.NUXT_HUB_APP_URL || 'https://dbcollegemannuthy.edu.in/',
    },
    session: {
      name: 'college-cms-session',
      password: process.env.NUXT_SESSION_PASSWORD,
      maxAge: 60 * 60 * 24 * 7, // 1 week
    },
  },
  svgo: {
    autoImportPath: './assets/icons/',
  },
  googleFonts: {
    families: {
      Inter: {
        wght: [100, 200, 300, 400, 500, 600, 700, 800, 900],
        ital: [400, 500],
      },
      Raleway: {
        wght: [100, 400],
        ital: [100],
      },
    },
  },
  experimental: {
    payloadExtraction: false,
    componentIslands: 'auto',
    typedPages: true,
  },
  ssr: true,
  imports: {
    dirs: ['./lib'],
  },
  devtools: {
    enabled: true,
    timeline: {
      enabled: true,
    },
  },
  app: {
    head: {
      title: 'Don Bosco College Mannuthy, Thrissur',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'format-detection', content: 'telephone=no' },
        { name: 'theme-color', content: '#ffffff' },
        {
          name: 'description',
          content:
            'Don Bosco College Mannuthy, Thrissur - A leading arts and science college in Thrissur, Kerala, offering undergraduate and postgraduate programs. Known for its quality education, vibrant campus life, and strong focus on student development.',
        },
        { property: 'og:site_name', content: 'Don Bosco College Mannuthy' },
        { property: 'og:type', content: 'website' },
        { property: 'og:locale', content: 'en_US' },
        { name: 'twitter:card', content: 'summary_large_image' },
      ],
      link: [
        { rel: 'icon', href: '/favicon.ico' },
        {
          rel: 'apple-touch-icon',
          sizes: '180x180',
          href: '/apple-touch-icon.png',
        },
      ],
      htmlAttrs: {
        lang: 'en',
      },
    },
  },
  // Nuxt 4 directory structure and features
  future: {
    compatibilityVersion: 4,
  },
  compatibilityDate: '2025-02-28',
  // SEO configurations
  robots: {
    disallow: ['/admin', '/admin/**'],
  },
  sitemap: {
    exclude: ['/admin/**'],
  },
  nitro: {
    compatibilityDate: '2025-02-28',
    minify: false,
    prerender: {
      routes: ['/'],
      crawlLinks: true,
      ignore: ['/admin/**'],
    },
    experimental: {
      openAPI: true,
      tasks: true,
      websocket: true,
    },
  },
  alias: {
    '@server': './server',
  },
  hub: {
    database: true,
    kv: true,
    blob: true,
    cache: true,
  },
  eslint: {
    checker: true,
  },
  routeRules: {
    // Enable dynamic pre-rendering for all routes except admin
    '/': { prerender: false },
    // '/admin/**': { prerender: false },
    '/programs-offered': { redirect: '/programs-offered/ug' },
    // Pre-render all public pages
    // '/**': { prerender: true },
  },
  shadcn: {
    prefix: '',
    componentDir: './app/components/ui',
  },
  aos: {
    // Global settings:
    startEvent: 'DOMContentLoaded', // name of the event dispatched on the document, that AOS should initialize on

    // Settings that can be overridden on per-element basis, by `data-aos-*` attributes:
    once: true, // whether animation should happen only once - while scrolling down
  },
});
