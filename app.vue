<script setup lang="ts">
// With auto-imports in Nuxt, we don't need to explicitly import composables
const colorMode = useColorMode();

const color = computed(() =>
  colorMode.value === 'dark' ? '#09090b' : '#ffffff'
);

// Set up basic SEO metadata
useSeoMeta({
  title: 'Don Bosco College Mannuthy, Thrissur',
  ogTitle: 'Don Bosco College Mannuthy, Thrissur',
  twitterTitle: 'Don Bosco College Mannuthy, Thrissur',

  description:
    'Don Bosco College Mannuthy, Thrissur - A leading arts and science college in Thrissur, Kerala, offering undergraduate and postgraduate programs. Known for its quality education, vibrant campus life, and strong focus on student development.',
  ogDescription:
    'Don Bosco College Mannuthy, Thrissur - A leading arts and science college in Thrissur, Kerala, offering undergraduate and postgraduate programs. Known for its quality education, vibrant campus life, and strong focus on student development.',
  twitterDescription:
    'Don Bosco College Mannuthy, Thrissur - A leading arts and science college in Thrissur, Kerala, offering undergraduate and postgraduate programs. Known for its quality education, vibrant campus life, and strong focus on student development.',

  ogImage: 'https://dbcollegemannuthy.edu.in/og-image.jpg',
  twitterImage: 'https://dbcollegemannuthy.edu.in/og-image.jpg',

  ogType: 'website',
  twitterCard: 'summary_large_image',

  keywords:
    'Don Bosco College, Mannuthy, Thrissur, Kerala, Education, Higher Education, College, Academic Programs, Admissions',

  robots: 'index, follow',
});

// Add canonical URL separately
useHead({
  link: [{ rel: 'canonical', href: 'https://dbcollegemannuthy.edu.in/' }],
});

// Add structured data and other head elements
useHead({
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { key: 'theme-color', name: 'theme-color', content: color },
    { name: 'format-detection', content: 'telephone=no' },
    { property: 'og:url', content: 'https://dbcollegemannuthy.edu.in/' },
  ],
  link: [
    { rel: 'icon', href: '/favicon.ico' },
    {
      rel: 'apple-touch-icon',
      sizes: '180x180',
      href: '/apple-touch-icon.png',
    },
  ],
  htmlAttrs: {
    lang: 'en',
  },
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'CollegeOrUniversity',
        name: 'Don Bosco College Mannuthy',
        alternateName: 'DBC Mannuthy',
        url: 'https://dbcollegemannuthy.edu.in/',
        logo: 'https://dbcollegemannuthy.edu.in/logo.png',
        address: {
          '@type': 'PostalAddress',
          streetAddress: 'Don Bosco College, Mannuthy',
          addressLocality: 'Thrissur',
          addressRegion: 'Kerala',
          postalCode: '680651',
          addressCountry: 'IN',
        },
        telephone: '+91-4872371512',
        email: '<EMAIL>',
        description:
          'Don Bosco College Mannuthy, Thrissur - A leading arts and science college in Thrissur, Kerala, offering undergraduate and postgraduate programs. Known for its quality education, vibrant campus life, and strong focus on student development.',
      }),
    },
  ],
});
</script>

<template>
  <NuxtLayout vaul-drawer-wrapper>
    <NuxtPage />
  </NuxtLayout>
</template>
