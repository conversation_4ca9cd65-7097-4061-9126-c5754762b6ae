import { z } from 'zod';
import { ANNOUNCEMENT_TYPES } from '~~/server/database/tables/enums';
import { linkButtonSchema, fileSchema } from '..';

export const updateAnnouncementSchema = z.object({
  id: z.number(),
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(50, 'Content is required'),
  link: z.string().optional(),
  eventDate: z
    .string()
    .transform((str) => new Date(str))
    .nullable(),
  scheduledAt: z.string().transform((str) => new Date(str)),
  expiresAt: z.string().transform((str) => new Date(str)),
  isActive: z.boolean(),
  files: fileSchema.array().optional(),
  priority: z.number().min(0).default(0),
  announcementType: z.enum(ANNOUNCEMENT_TYPES),
  button: linkButtonSchema.nullable(),
});
