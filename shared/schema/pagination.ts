import { z } from 'zod';

export const paginationSchema = z.object({
  page: z
    .string()
    .optional()
    .default('1')
    .transform((val) => Math.max(1, parseInt(val, 10))),
  limit: z
    .string()
    .optional()
    .default('10')
    .transform((val) => Math.min(50, Math.max(1, parseInt(val, 10)))),
});

export type PaginationQuery = z.infer<typeof paginationSchema>;

export type PaginationResponse = {
  page: number;
  limit: number;
  totalItems: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
};
