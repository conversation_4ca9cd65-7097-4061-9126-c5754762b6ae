import { z } from 'zod';

export const departmentSchema = z.object({
  name: z.string().min(1, 'Department name is required'),
  slug: z
    .string()
    .min(1, 'Slug is required')
    .refine(
      (val) => !val || /^[a-z0-9-/]+$/.test(val),
      'Route path can only contain lowercase letters, numbers, and hyphens'
    ),
});

export const deleteDepartmentSchema = z.object({
  id: z.number(),
});

export const updateDepartmentSchema = z.object({
  id: z.number(),
  name: z.string().min(1, 'Department name is required'),
  slug: z
    .string()
    .min(1, 'Slug is required')
    .refine(
      (val) => !val || /^[a-z0-9-/]+$/.test(val),
      'Route path can only contain lowercase letters, numbers, and hyphens'
    ),
});
