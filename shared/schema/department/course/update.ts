import { z } from 'zod';
import { fileSchema, linkButtonSchema } from '../..';
import { COURSE_TYPES } from '~~/server/database/tables/enums';

export const courseUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  specialization: z.string().nullable().optional(),
  durationInMonths: z
    .number()
    .min(1, 'Duration must be at least 1 month')
    .optional(),
  semesterCount: z
    .number()
    .min(1, 'Semester count must be at least 1')
    .optional(),
  departmentId: z.number().min(1, 'Department ID is required').optional(),
  type: z.enum(COURSE_TYPES),
  title: z.string().min(1, 'Title is required').optional(),
  seatsCount: z.number().min(1, 'Seats count must be at least 1').optional(),
  content: z.string().min(1, 'Content is required').optional(),
  image: fileSchema.nullable().optional(),
  syllabus: fileSchema.nullable().optional(),
  pos: fileSchema.nullable().optional(),
  linkButton: linkButtonSchema.nullable(),
});

export type CourseUpdate = z.infer<typeof courseUpdateSchema>;
