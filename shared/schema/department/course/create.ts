import { z } from 'zod';
import { linkButtonSchema } from '../..';
import { COURSE_TYPES } from '~~/server/database/tables/enums';

export const courseSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  specialization: z.string().optional(),
  durationInMonths: z.number().min(1, 'Duration must be at least 1 month'),
  semesterCount: z.number().min(1, 'Semester count must be at least 1'),
  title: z.string().min(1, 'Title is required'),
  type: z.enum(COURSE_TYPES),
  seatsCount: z.number().min(1, 'Seats count must be at least 1'),
  content: z.string().min(1, 'Content is required'),
  image: z
    .object({
      pathname: z.string().min(1, 'Path name is required'),
      title: z.string().default('course image'),
      type: z.string().min(1, 'Type is required'),
      prefix: z.string().default('course'),
    })
    .nullable()
    .optional(),
  syllabus: z
    .object({
      pathname: z.string().min(1, 'Path name is required'),
      title: z.string().default('syllabus'),
      type: z.string().min(1, 'Type is required'),
    })
    .nullable()
    .optional(),
  pos: z
    .object({
      pathname: z.string().min(1, 'Path name is required'),
      title: z.string().default('program outcome'),
      type: z.string().min(1, 'Type is required'),
    })
    .nullable()
    .optional(),
  linkButton: linkButtonSchema.nullable(),
});

export type Course = z.infer<typeof courseSchema>;
