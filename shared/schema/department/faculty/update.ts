import { z } from 'zod';

export const facultyUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  designation: z.string().min(1, 'Designation is required').optional(),
  startDate: z.string().min(1, 'Start date is required').optional(),
  endDate: z.string().nullable().optional(),
  priority: z.number().min(0, 'Priority must be a positive number').optional(),
  resume: z
    .object({
      pathname: z.string().min(1, 'Path name is required'),
      title: z.string().default('resume'),
      type: z.string().min(1, 'Type is required'),
    })
    .optional(),
  image: z
    .object({
      pathname: z.string().min(1, 'Path name is required'),
      title: z.string().default('image'),
      type: z.string().min(1, 'Type is required'),
    })
    .optional(),
  education: z
    .array(
      z.object({
        degree: z.string().min(1, 'Degree is required'),
        university: z.string().min(1, 'University is required'),
        passOutYear: z.number().min(1900, 'Invalid pass out year'),
      })
    )
    .optional(),
  experience: z
    .array(
      z.object({
        startYear: z.number().min(1900, 'Invalid start year'),
        organization: z.string().min(1, 'Organization is required'),
        designation: z.string().min(1, 'Designation is required'),
      })
    )
    .optional(),
  areaOfInterest: z
    .array(z.string().min(1, 'Area of interest is required'))
    .optional(),
});

export const facultyUpdateTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  designation: z.string().min(1, 'Designation is required'),
  startDate: z.date(),
  endDate: z.date().nullable().optional(),
  priority: z.number().min(0, 'Priority must be a positive number').default(0),
  resume: z
    .object({
      pathname: z.string().min(1, 'Path name is required'),
      title: z.string().default('resume'),
      type: z.string().min(1, 'Type is required'),
    })
    .optional(),
  image: z
    .object({
      pathname: z.string().min(1, 'Path name is required'),
      title: z.string().default('image'),
      type: z.string().min(1, 'Type is required'),
    })
    .optional(),
  education: z
    .array(
      z.object({
        degree: z.string().min(1, 'Degree is required'),
        university: z.string().min(1, 'University is required'),
        passOutYear: z.number().min(1900, 'Invalid pass out year'),
      })
    )
    .optional(),
  experience: z
    .array(
      z.object({
        startYear: z.number().min(1900, 'Invalid start year'),
        organization: z.string().min(1, 'Organization is required'),
        designation: z.string().min(1, 'Designation is required'),
      })
    )
    .optional(),
  areaOfInterest: z
    .array(z.string().min(1, 'Area of interest is required'))
    .optional(),
});
export type FacultyUpdate = z.infer<typeof facultyUpdateSchema>;
