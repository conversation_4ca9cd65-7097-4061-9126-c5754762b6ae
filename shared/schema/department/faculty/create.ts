import { z } from 'zod';

export const facultySchema = z.object({
  name: z.string(),
  designation: z.string(),
  startDate: z.string(),
  endDate: z.string().nullable().optional(),
  priority: z.number().default(0),
  resume: z
    .object({
      pathname: z.string().min(1, 'Path name is required'),
      title: z.string().default('resume'),
      type: z.string().min(1, 'Type is required'),
    })
    .nullable()
    .optional(),
  image: z.object({
    pathname: z.string().min(1, 'Path name is required'),
    title: z.string().default('image'),
    type: z.string().min(1, 'Type is required'),
  }),
  education: z.array(
    z.object({
      degree: z.string(),
      university: z.string(),
      passOutYear: z.number(),
    })
  ),
  experience: z.array(
    z.object({
      startYear: z.number(),
      endYear: z.number().default(() => new Date().getFullYear()),
      organization: z.string(),
      designation: z.string(),
    })
  ),
  areaOfInterest: z.array(z.string()),
});

export type Faculty = z.infer<typeof facultySchema>;
