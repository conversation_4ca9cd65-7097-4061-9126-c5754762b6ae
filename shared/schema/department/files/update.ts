import { z } from 'zod';
import { fileSchema, linkButtonSchema } from '../..';

export const updateQuestionBankSchema = z.object({
  type: z.literal('question_paper'),
  title: z.string().min(1, 'Title is required').optional(),
  year: z.number().int().positive().optional(),
  courseId: z.number().int().positive().optional(),
  semester: z.number().int().min(1).max(8).optional(),
  file: fileSchema.optional(),
});

export const updateProjectSchema = z.object({
  type: z.literal('project'),
  title: z.string().min(1, 'Title is required').optional(),
  year: z.number().int().positive().optional(),
  courseId: z.number().int().positive().optional(),
  semester: z.number().int().min(1).max(8).optional(),
  file: fileSchema.optional(),
});

export const updateDownloadSchema = z.object({
  type: z.literal('download'),
  title: z.string().min(1, 'Title is required').optional(),
  file: fileSchema.optional(),
});

export const updateAttachmentSchema = z.object({
  type: z.literal('attachment'),
  title: z.string().min(1, 'Title is required').optional(),
  linkButton: linkButtonSchema.optional().nullable(),
  files: fileSchema.array().optional(),
});
export type UpdateQuestionBank = z.infer<typeof updateQuestionBankSchema>;
export type UpdateProject = z.infer<typeof updateProjectSchema>;
export type UpdateDownload = z.infer<typeof updateDownloadSchema>;
export type UpdateAttachment = z.infer<typeof updateAttachmentSchema>;
