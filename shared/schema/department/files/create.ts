import { z } from 'zod';
import { linkButtonSchema } from '../..';

const fileSchema = z.object({
  pathname: z.string().min(1, 'Pathname is required'),
  title: z.string().min(1, 'Title is required'),
  type: z.string().min(1, 'Type is required'),
});

export const createQuestionBankSchema = z.object({
  type: z.literal('question_paper'),
  title: z.string().min(1, 'Title is required'),
  year: z.number().int().positive(),
  courseId: z.number().int().positive(),
  semester: z.number().int().min(1).max(8),
  file: fileSchema,
});

export const createProjectSchema = z.object({
  type: z.literal('project'),
  title: z.string().min(1, 'Title is required'),
  year: z.number().int().positive(),
  courseId: z.number().int().positive(),
  semester: z.number().int().min(1).max(8),
  file: fileSchema,
});

export const createDownloadSchema = z.object({
  type: z.literal('download'),
  title: z.string().min(1, 'Title is required'),
  file: fileSchema,
});

export const createAttachmentSchema = z.object({
  type: z.literal('attachment'),
  title: z.string().min(1, 'Title is required'),
  linkButton: linkButtonSchema.optional().nullable(),
  files: fileSchema.array().optional(),
});

export type CreateQuestionBank = z.infer<typeof createQuestionBankSchema>;
export type CreateProject = z.infer<typeof createProjectSchema>;
export type CreateDownload = z.infer<typeof createDownloadSchema>;
export type CreateAttachment = z.infer<typeof createAttachmentSchema>;
