import { z } from 'zod';
import { STUDENT_TYPES } from '~~/server/database/tables';
import { fileSchema } from '../..';

export const createStudentSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  type: z.enum(STUDENT_TYPES, {
    errorMap: () => ({ message: 'Invalid student type' }),
  }),
  courseId: z.number().int().positive('Course is required'),
  startYear: z
    .number()
    .int()
    .min(1900)
    .max(new Date().getFullYear(), 'Invalid start year'),
  passOutYear: z
    .number()
    .int()
    .min(1900)
    .max(new Date().getFullYear() + 10, 'Invalid pass out year')
    .optional(),
  mark: z.number().min(0).max(100, 'Mark must be between 0 and 100').optional(),
  placedAt: z.string().optional(),
  image: fileSchema.optional().nullable(),
});

export type CreateStudentInput = z.infer<typeof createStudentSchema>;
