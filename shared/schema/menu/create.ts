import { z } from 'zod';

// Validation schema for menu creation
export const createMenuSchema = z.object({
  title: z
    .string()
    .min(1, 'Title is required')
    .max(30, 'Title must not exceed 30 characters'),
  menuName: z
    .string()
    .min(1, 'Menu name is required')
    .max(30, 'Menu name must not exceed 15 characters'),
  isActive: z.boolean(),
  routeEnabled: z.boolean(),
  slug: z
    .string()
    .min(1, 'Route path is required')
    .refine(
      (val) => /^[a-z0-9-/]+$/.test(val),
      'Route path can only contain lowercase letters, numbers, and hyphens'
    ),
});

export type CreateMenuSchema = z.infer<typeof createMenuSchema>;

export const addSubmenuSchema = z.object({
  menuId: z.number(),
  menuName: z.string().min(1, 'Menu name is required'),
  title: z.string().min(1, 'Title is required'),
  isActive: z.boolean(),
  routeEnabled: z.boolean(),
  slug: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[a-z0-9-/]+$/.test(val),
      'Route path can only contain lowercase letters, numbers, and hyphens'
    ),
});

export type AddSubmenuSchema = z.infer<typeof addSubmenuSchema>;

export const updatePrioritySchema = z.object({
  id: z.number(),
  priority: z.number().int().min(0),
});

export type UpdatePrioritySchema = z.infer<typeof updatePrioritySchema>;
