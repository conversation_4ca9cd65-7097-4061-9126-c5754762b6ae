import { z } from 'zod';
import { linkButtonSchema, downloadButtonSchema } from '..';

export const profileCardSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  subtitle1: z.string().min(1, 'Subtitle 1 is required'),
  image: z.string().min(1, 'Image is required'),
  linkButton: linkButtonSchema.optional(),
  downloadButton: downloadButtonSchema.optional(),
  // TODO: Add modal
});

export type ProfileCardSchema = z.infer<typeof profileCardSchema>;

export const standardCardSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  subtitle1: z.string().min(1, 'Subtitle 1 is required'),
  content1: z.string().min(1, 'Content 1 is required'),
  image: z.string().min(1, 'Image is required'),
});

export type StandardCardSchema = z.infer<typeof standardCardSchema>;

export const visionCardSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content1: z.string().min(1, 'Content 1 is required'),
  image: z.string().min(1, 'Image is required'),
});

export type VisionCardSchema = z.infer<typeof visionCardSchema>;
