import { z } from 'zod';
import { linkButtonSchema, fileSchema, downloadButtonSchema } from '..';

const contentSchema = z.object({
  title: z.string().optional().default(''),
  content: z.string().optional().default(''),
});

const cardSchema = z.discriminatedUnion('layout', [
  z.object({
    layout: z.literal('standard'),
    title: z.string().min(1),
    content1: contentSchema,
    image: fileSchema.nullable(),
    linkButton: linkButtonSchema.nullable(),
    downloadButton: downloadButtonSchema.nullable(),
    files: fileSchema.array().optional(),
    key: z.string().min(1),
  }),
  z.object({
    layout: z.literal('vision'),
    content1: contentSchema,
    image: fileSchema,
    key: z.string().min(1),
  }),
  z.object({
    layout: z.literal('profile'),
    content1: contentSchema.optional(),
    content2: contentSchema.optional(),
    image: fileSchema,
    linkButton: linkButtonSchema.nullable(),
    downloadButton: downloadButtonSchema.nullable(),
    key: z.string().min(1),
  }),
]);

// Define the valid layout values based on the SQLite enum table
// Configuration schemas for each section type
const cardListConfigSchema = z.object({
  title: z.string().optional(),
  description: z.string().optional(),
  maxCards: z.number().int().positive(),
  minCards: z.number().int().min(0),
  cards: z.array(cardSchema).min(1),
});

const accordionConfigSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  content: z.string().optional(),
});

const statsConfigSchema = z.object({
  title: z.string().min(1),
  value: z.number().int().positive(),
  icon: z.string().min(1),
});

const toggleConfigSchema = z.object({
  title: z.string().min(1),
  isActive: z.boolean(),
  type: z.enum(['menu', 'event', 'gallery']),
  menuId: z.number().optional().nullable(),
});

export const createSectionSchema = z.discriminatedUnion('type', [
  z.object({
    templateId: z.number(),
    type: z.literal('card'),
    priority: z.number().default(0),
    configuration: cardSchema,
  }),
  z.object({
    templateId: z.number(),
    type: z.literal('cardList'),
    priority: z.number().default(0),
    configuration: cardListConfigSchema,
  }),
  z.object({
    templateId: z.number(),
    type: z.literal('accordion'),
    priority: z.number().default(0),
    configuration: accordionConfigSchema,
  }),
  z.object({
    templateId: z.number(),
    type: z.literal('stats'),
    priority: z.number().default(0),
    configuration: statsConfigSchema,
  }),
  z.object({
    templateId: z.number(),
    type: z.literal('toggle'),
    priority: z.number().default(0),
    configuration: toggleConfigSchema,
  }),
]);

export type CreateSectionInput = z.infer<typeof createSectionSchema>;

export const removeSectionSchema = z.object({
  id: z.number(),
});

export type RemoveSectionInput = z.infer<typeof removeSectionSchema>;
