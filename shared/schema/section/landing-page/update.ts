import { z } from 'zod';
import { fileSchema } from '../../album/create';

// Button style enum
const ButtonStyleEnum = z.enum(['primary', 'secondary', 'outline']);

// Link type enum
const LinkTypeEnum = z.enum(['internal', 'external']);

// Call to Action Button schema
const CallToActionButtonSchema = z
  .object({
    enabled: z.boolean(),
    buttonText: z.string().min(1, 'Button text is required'),
    icon: z.string().optional(),
    newTab: z.boolean(),
    buttonStyle: ButtonStyleEnum,
    linkType: LinkTypeEnum,
    internalLink: z.string().optional(),
    externalLink: z.string().optional(),
  })
  .refine(
    (data) => {
      // Only validate internalLink when linkType is 'internal'
      if (data.linkType === 'internal') {
        return !!data.internalLink;
      }

      // Only validate externalLink when linkType is 'external'
      if (data.linkType === 'external') {
        return !!data.externalLink;
      }

      return true;
    },
    {
      message: 'Link value is required based on the selected link type',
      path: ['internalLink', 'externalLink'],
    }
  )
  .refine(
    (data) => {
      // Apply URL validation to externalLink only when linkType is 'external'
      if (data.linkType === 'external' && data.externalLink) {
        try {
          new URL(data.externalLink);
          return true;
        } catch {
          return false;
        }
      }
      return true;
    },
    {
      message: 'Invalid url',
      path: ['externalLink'],
    }
  );

// Principal's Message schema
const PrincipalsMessageSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  name: z.string().min(1, "Principal's name is required"),
  designation: z.string().min(1, 'Designation is required'),
  message: z.string().min(1, 'Message is required'),
  image: fileSchema.optional(),
  callToAction: CallToActionButtonSchema.optional(),
});

// Campus Details schema
const CampusDetailsSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  image: fileSchema.optional(),
  callToAction: CallToActionButtonSchema.optional(),
});

// Research section schema
const ResearchSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  callToAction: CallToActionButtonSchema.optional(),
});

// Stat item schema
export const StatItemSchema = z.object({
  icon: z.string().min(1, 'Icon is required'),
  value: z.number().min(1, 'Value is required'),
  title: z.string().min(1, 'Title is required'),
});

export type StatItem = typeof StatItemSchema._type;

// Stats section schema
export const StatsSchema = z.object({
  items: z
    .array(StatItemSchema)
    .min(1, 'At least one stat item is required')
    .max(4),
});

// Section visibility schema
export const SectionVisibilitySchema = z.object({
  programsWeOffer: z.boolean(),
  events: z.boolean(),
  gallery: z.boolean(),
});

// Main landing page configuration schema
export const landingPageConfigurationSchema = z.object({
  principalsMessage: PrincipalsMessageSchema,
  campusDetails: CampusDetailsSchema,
  research: ResearchSchema,
  stats: StatsSchema,
  // sectionVisibility: SectionVisibilitySchema,
});

export type LandingPageConfiguration = z.infer<
  typeof landingPageConfigurationSchema
>;
