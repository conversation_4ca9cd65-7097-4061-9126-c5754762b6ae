import { z } from 'zod';
import { downloadButtonSchema, fileSchema, linkButtonSchema } from '..';

const contentUpdateSchema = z
  .object({
    title: z.string().min(1),
    content: z.string().optional(),
  })
  .partial();

const cardUpdateSchema = z.discriminatedUnion('layout', [
  z.object({
    layout: z.literal('standard'),
    title: z.string().min(1).optional(),
    content1: contentUpdateSchema.optional(),
    image: fileSchema.optional(),
    linkButton: linkButtonSchema.nullable(),
    downloadButton: downloadButtonSchema.nullable(),
    files: fileSchema.array().optional(),
    key: z.string().optional(),
  }),
  z.object({
    layout: z.literal('vision'),
    content1: contentUpdateSchema.optional(),
    image: fileSchema.optional(),
    key: z.string().optional(),
  }),
  z.object({
    layout: z.literal('profile'),
    content1: contentUpdateSchema.optional(),
    content2: contentUpdateSchema.optional(),
    image: fileSchema.optional(),
    linkButton: linkButtonSchema.nullable(),
    downloadButton: downloadButtonSchema.nullable(),
    key: z.string().optional(),
  }),
]);

// Configuration schemas for updates
const cardListConfigUpdateSchema = z
  .object({
    title: z.string().min(1),
    description: z.string().optional(),
    maxCards: z.number().int().positive(),
    minCards: z.number().int().min(0),
    cards: z.array(cardUpdateSchema).min(1),
  })
  .partial();

const accordionConfigUpdateSchema = z
  .object({
    title: z.string().min(1),
    description: z.string().optional(),
    content: z.string().optional(),
  })
  .partial();

const statsConfigUpdateSchema = z
  .object({
    title: z.string().min(1),
    value: z.number().int().positive(),
    icon: z.string().min(1),
  })
  .partial();

const toggleConfigUpdateSchema = z
  .object({
    title: z.string().min(1),
    isActive: z.boolean(),
    type: z.enum(['menu', 'event', 'gallery']),
    menuId: z.number().optional().nullable(),
  })
  .partial();

export const updateSectionSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('card'),
    id: z.number(),
    priority: z.number().optional(),
    configuration: cardUpdateSchema,
  }),
  z.object({
    type: z.literal('cardList'),
    id: z.number(),
    priority: z.number().optional(),
    configuration: cardListConfigUpdateSchema,
  }),
  z.object({
    type: z.literal('accordion'),
    id: z.number(),
    priority: z.number().optional(),
    configuration: accordionConfigUpdateSchema,
  }),
  z.object({
    type: z.literal('stats'),
    id: z.number(),
    priority: z.number().optional(),
    configuration: statsConfigUpdateSchema,
  }),
  z.object({
    type: z.literal('toggle'),
    id: z.number(),
    priority: z.number().optional(),
    configuration: toggleConfigUpdateSchema,
  }),
]);

export type UpdateSectionInput = z.infer<typeof updateSectionSchema>;
