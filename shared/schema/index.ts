import { z } from 'zod';
import { BUTTON_TYPES, BUTTON_STYLES } from '~~/server/database/tables/enums';

export const idSchema = z.object({
  id: z.coerce.number(),
});

export const fileSchema = z.object({
  pathname: z.string().min(1, 'Path name is required'),
  title: z.string().optional(),
  type: z.string().min(1, 'Type is required'),
  prefix: z.string().optional(),
});

const baseButtonSchema = z.object({
  title: z.string().min(1, 'Button title is required'),
  icon: z.string().optional().nullable(),
  style: z.enum(BUTTON_STYLES),
  type: z.enum(BUTTON_TYPES),
});

export const linkButtonSchema = z.object({
  ...baseButtonSchema.shape,
  internalLink: z.string().optional().nullable(),
  externalLink: z.string().optional().nullable(),
  newTab: z.boolean().default(false),
});

export const downloadButtonSchema = z.object({
  ...baseButtonSchema.shape,
  newTab: z.boolean().default(false),
  file: fileSchema,
});

// TODO: Add modal Schema correctly linking to sections[]
export const modalSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
});
