import { z } from 'zod';
import { fileSchema, linkButtonSchema } from '..';

export const createClubSchema = z.object({
  title: z.string(),
  overview: z.string(),
  slug: z.string(),
  image: fileSchema.optional().nullable(),
  type: z.enum(['club', 'committee']),
  attachments: fileSchema.array().optional().nullable(),
  linkButton: linkButtonSchema.optional().nullable(),
  galleries: z.array(z.number()).optional().nullable(),
  events: z.array(z.number()).optional().nullable(),
});

export type CreateClub = z.infer<typeof createClubSchema>;
