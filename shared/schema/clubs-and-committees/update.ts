import { z } from 'zod';
import { fileSchema, linkButtonSchema } from '..';

export const updateClubSchema = z.object({
  title: z.string().optional(),
  overview: z.string().optional(),
  slug: z.string().optional(),
  image: fileSchema.optional().nullable(),
  type: z.enum(['club', 'committee']).optional(),
  attachments: fileSchema.array().optional().nullable(),
  linkButton: linkButtonSchema.optional().nullable(),
  galleries: z.array(z.number()).optional().nullable(),
  events: z.array(z.number()).optional().nullable(),
});

export type UpdateClub = z.infer<typeof updateClubSchema>;
