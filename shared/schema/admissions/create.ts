import { z } from 'zod';
import { fileSchema, linkButtonSchema } from '..';

export const createAdmissionSchema = z.object({
  title: z.string(),
  image: fileSchema.optional().nullable(),
  content: z.array(
    z.object({
      title: z.string(),
      content: z.string(),
      linkButton: linkButtonSchema.optional().nullable(),
      file: fileSchema.optional().nullable(),
    })
  ),
});

export type CreateAdmission = z.infer<typeof createAdmissionSchema>;
