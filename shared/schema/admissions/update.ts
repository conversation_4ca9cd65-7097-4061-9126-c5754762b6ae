import { z } from 'zod';
import { fileSchema, linkButtonSchema } from '..';

export const updateAdmissionSchema = z.object({
  id: z.number().optional(),
  title: z.string().optional(),
  image: fileSchema.optional().nullable(),
  content: z
    .array(
      z.object({
        title: z.string().optional(),
        content: z.string().optional(),
        linkButton: linkButtonSchema.optional().nullable(),
        file: fileSchema.optional().nullable(),
      })
    )
    .optional(),
});

export type UpdateAdmission = z.infer<typeof updateAdmissionSchema>;
