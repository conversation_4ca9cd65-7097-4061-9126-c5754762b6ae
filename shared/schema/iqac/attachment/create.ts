import { z } from 'zod';
import { fileSchema, linkButtonSchema } from '../..';

// Schema for a single attachment item
export const iqacAttachmentItemSchema = z.object({
  title: z.string(),
  files: fileSchema.array(),
  linkButton: linkButtonSchema.optional().nullable(),
});

// For backward compatibility, keep the array schema
export const iqacAttachmentSchema = z.array(iqacAttachmentItemSchema);

export type IQACAttachmentItem = z.infer<typeof iqacAttachmentItemSchema>;
export type IQACAttachment = z.infer<typeof iqacAttachmentSchema>;
