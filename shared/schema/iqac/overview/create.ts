import { z } from 'zod';
import { fileSchema, linkButtonSchema } from '../..';

export const iqacOverviewSchema = z.object({
  title: z.string().optional().nullable(),
  image: fileSchema.optional().nullable(),
  content: z.string().nullable(),
  linkButton: linkButtonSchema.optional().nullable(),
  files: fileSchema.array().optional().nullable(),
});

export type IQACOverview = z.infer<typeof iqacOverviewSchema>;

export const iqacDownloadSchema = z.array(
  z.object({
    title: z.string(),
    file: fileSchema,
  })
);

export type IQACDownload = z.infer<typeof iqacDownloadSchema>;
