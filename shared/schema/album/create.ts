import { z } from 'zod';

export const fileSchema = z.object({
  pathname: z.string().min(1, 'Path name is required'),
  title: z.string().default('album'),
  type: z.string().min(1, 'Type is required'),
});

export const createAlbumSchema = z.object({
  title: z.string().min(1, 'Title is required'),
});

export const uploadFileSchema = z.object({
  files: z.array(fileSchema).min(1, 'At least one file is required'),
});
