import { z } from 'zod';

const MAX_FILE_SIZE = 8 * 1024 * 1024; // 8MB in bytes
const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/webp',
  'application/pdf',
] as const;

const fileValidation = z
  .instanceof(File)
  .refine((file) => file.size > 0, 'File is required')
  .refine(
    (file) => file.size <= MAX_FILE_SIZE,
    `File size must be less than ${MAX_FILE_SIZE / 1024 / 1024}MB`
  )
  .refine(
    (file) =>
      ALLOWED_FILE_TYPES.includes(
        file.type as (typeof ALLOWED_FILE_TYPES)[number]
      ),
    `File type must be one of: ${ALLOWED_FILE_TYPES.join(', ')}`
  );

export const uploadSchema = z.object({
  files: z.array(fileValidation).min(1, 'At least one file is required'),
  title: z.string().optional(),
});

export type UploadSchema = z.infer<typeof uploadSchema>;
