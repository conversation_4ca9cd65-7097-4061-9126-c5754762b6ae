# lefthook.yml
# pre-commit:
#   parallel: true
#   commands:
#     protect-branches:
#       glob: '*.{ts,vue,js,json,yml,yaml,md,mdx,css,scss,html,png,jpg,jpeg,gif,svg,ico}'
#       run: bun run check-branch
#     lint:fix:
#       glob: '*.{ts,vue,json,yml,yaml}'
#       run: bun run lint:fix

# pre-push:
#   commands:
#     check-lockfile:
#       run: |
#         CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
#         if [ "$CURRENT_BRANCH" = "development" ]; then
#           if [ -n "$(git diff --name-only package.json)" ] && [ -z "$(git diff --name-only bun.lockb)" ]; then
#             echo "Error: package.json was modified but bun.lockb wasn't updated. Run 'bun install' to update."
#             exit 1
#           fi
#           if [ -n "$(git diff --name-only bun.lockb)" ]; then
#             echo "Error: bun.lockb has uncommitted changes. Commit them before pushing."
#             exit 1
#           fi
#         fi
#     build-check:
#       run: |
#         CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
#         if [ "$CURRENT_BRANCH" = "development" ]; then
#           bun run build
#         fi
