import type { NavbarMenu } from '~/types/home/<USER>';

export const NavbarItemsData: NavbarMenu = [
  {
    label: 'About Us',
    children: [
      { label: 'Introduction', link: '/about-us/introduction' },
      { label: 'Vision and Mission', link: '/about-us/vision-and-mission' },
      { label: 'Profile', link: '/about-us/profile' },
      { label: 'Our Management', link: '/about-us/our-management' },
      { label: 'Former Managers', link: '/about-us/former-managers' },
      { label: 'Former Principals', link: '/about-us/former-principals' },
      // { label: 'College Reports', link: '/about-us/college-reports' },
    ],
  },
  {
    label: 'Academics',
    children: [
      {
        label: 'Programs Offered',
        children: [
          {
            label: 'UG Courses',
            link: '/programs-offered/ug',
          },
          {
            label: 'PG Courses',
            link: '/programs-offered/pg',
          },
          {
            label: 'Add-on Courses',
            link: '/programs-offered/add-on',
          },
        ],
      },
      {
        label: 'Departments',
        children: [
          { label: 'Commerce', link: '/departments/commerce' },
          { label: 'Management', link: '/departments/management' },
        ],
      },
    ],
  },
  {
    label: 'Examinations',
    children: [
      { label: 'Annual Reports', link: '/examinations/annual-reports' },
      { label: 'Policies', link: '/examinations/policies' },
    ],
  },
  {
    label: 'Campus',
    children: [
      { label: 'Library', link: '/facilities/library' },
      { label: 'Hostel', link: '/facilities/hostel' },
      { label: 'Sports Complex', link: '/facilities/sports-complex' },
    ],
  },
  {
    label: 'IQAC',
    children: [
      { label: 'Annual Reports', link: '/iqsc/reports' },
      { label: 'Policies', link: '/iqsc/policies' },
    ],
  },
  {
    label: 'E-Services',
    children: [
      { label: 'Student Portal', link: 'https://student.dbcm.ac.in/' },
      { label: 'Parent Portal', link: 'https://parent.dbcm.ac.in/' },
      { label: 'Staff Portal', link: 'https://staff.dbcm.ac.in/' },
      { label: 'Digi locker Services', link: 'https://digilocker.dbcm.ac.in/' },
      { label: 'Academic Bank of Credits', link: 'https://aboc.dbcm.ac.in/' },
      { label: 'Swayam Portal', link: 'https://swayam.dbcm.ac.in/' },
      { label: 'My Bharath Portal', link: 'https://mybharath.dbcm.ac.in/' },
    ],
  },
  {
    label: 'Student Services',
    children: [
      { label: 'Counseling', link: '/student-services/counseling' },
      { label: 'Scholarships', link: '/student-services/scholarships' },
    ],
  },
];
