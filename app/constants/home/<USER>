import type { NavbarMenu } from '~/types/home';

export const NavbarMenuData: NavbarMenu = [
  // {
  //   label: 'Home',
  //   children: [], // No dropdown, keep children empty
  // },
  {
    label: 'About Us',
    children: [
      { label: 'Vision', link: '/about-us/vision' },
      { label: 'Mission', link: '/about-us/mission' },
      { label: 'Management', link: '/about-us/management' },
    ],
  },
  {
    label: 'Academics',
    children: [
      {
        label: 'Programs Offered',
        children: [
          {
            label: 'UG Courses',
            children: [
              {
                label: 'B.Com with Computer',
                link: '/academics/ug-courses/bcom',
              },
              { label: 'B.BA', link: '/academics/ug-courses/bba' },
            ],
          },
          {
            label: 'PG Courses',
            children: [
              { label: 'M.Com', link: '/academics/pg-courses/mcom' },
              {
                label: 'MBA',
                link: '/academics/pg-courses/mba',
                children: [{ label: 'Hello World' }],
              },
            ],
          },
          {
            label: 'Add-on Courses',
            children: [
              {
                label: 'Digital Marketing',
                link: '/academics/add-on/digital-marketing',
              },
              {
                label: 'Data Science Basics',
                link: '/academics/add-on/data-science',
              },
            ],
          },
        ],
      },
      {
        label: 'Departments',
        children: [
          { label: 'Commerce', link: '/academics/departments/commerce' },
          { label: 'Management', link: '/academics/departments/management' },
        ],
      },
    ],
  },
  {
    label: 'Examination',
    children: [
      { label: 'Exam Schedule', link: '/examination/schedule' },
      { label: 'Results', link: '/examination/results' },
    ],
  },
  {
    label: 'Facilities',
    children: [
      { label: 'Library', link: '/facilities/library' },
      { label: 'Hostel', link: '/facilities/hostel' },
      { label: 'Sports Complex', link: '/facilities/sports-complex' },
    ],
  },
  // {
  //   label: 'IQSC',
  //   children: [
  //     { label: 'Annual Reports', link: '/iqsc/reports' },
  //     { label: 'Policies', link: '/iqsc/policies' },
  //   ],
  // },
  // {
  //   label: 'E-Services',
  //   children: [
  //     { label: 'Student Portal', link: '/e-services/student-portal' },
  //     { label: 'Faculty Portal', link: '/e-services/faculty-portal' },
  //   ],
  // },
  // {
  //   label: 'Student Services',
  //   children: [
  //     { label: 'Counseling', link: '/student-services/counseling' },
  //     { label: 'Scholarships', link: '/student-services/scholarships' },
  //   ],
  // },
];
