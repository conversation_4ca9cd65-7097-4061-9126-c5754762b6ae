import type { NavMenuItems } from '@/types/admin/nav';

export const navMenu: NavMenuItems = [
  {
    title: 'Dashboard',
    icon: 'i-lucide-layout-dashboard',
    link: '/admin',
  },
  {
    heading: 'Components',
  },
  {
    title: 'Home Page',
    icon: 'i-lucide-home',
    link: '/admin/home-page',
  },
  {
    title: 'Departments',
    icon: 'i-lucide-graduation-cap',
    link: '/admin/departments',
  },
  {
    title: 'Clubs & Committees',
    icon: 'i-lucide-users',
    link: '/admin/clubs-and-committees',
  },
  {
    title: 'Admissions',
    icon: 'i-lucide-credit-card',
    link: '/admin/admissions',
  },
  {
    title: 'IQAC',
    icon: 'i-lucide-book-open',
    link: '/admin/iqac',
  },
  {
    title: 'E-Services',
    icon: 'i-lucide-book',
    link: '/admin/e-services',
  },
  {
    title: 'Other Menus',
    icon: 'i-lucide-logs',
    link: '/admin/menus',
  },
  {
    title: 'Gallery',
    icon: 'i-lucide-image',
    link: '/admin/gallery',
  },
  {
    heading: 'Navigation',
  },
  {
    title: 'Quick Links',
    icon: 'i-lucide-rectangle-ellipsis',
    link: '/admin/quick-links',
  },
  {
    title: 'File Management',
    icon: 'i-lucide-arrow-down-from-line',
    link: '/admin/file-management',
  },
  // {
  //   title: 'Footer',
  //   icon: 'i-lucide-arrow-down-from-line',
  //   link: '/admin/footer',
  // },
  {
    heading: 'Announcements',
  },
  {
    title: 'Popup Notice',
    icon: 'i-lucide-expand',
    link: '/admin/pop-up',
  },
  {
    title: 'Notices & Updates',
    icon: 'i-lucide-bell-ring',
    link: '/admin/notices-updates',
  },
  {
    title: 'Events',
    icon: 'i-lucide-calendar-days',
    link: '/admin/events',
  },
];

export const navMenuBottom: NavMenuItems = [
  {
    title: 'Settings',
    icon: 'i-lucide-settings',
    link: '/admin/settings',
  },
];
