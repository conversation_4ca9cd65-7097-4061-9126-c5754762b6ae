<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { Menu } from 'lucide-vue-next';
import type { HomeMenusResponse } from '~~/server/api/home/<USER>';

const { data: menuData } = await useFetch<HomeMenusResponse>('/api/home/<USER>');

const isMenuOpen = ref(false);

function toggleMenu() {
  isMenuOpen.value = !isMenuOpen.value;
}

const isScrolled = ref(false);
const { width } = useWindowSize();

// Create reactive state for dropdown menus
const openDropdownIndex = ref<number | null>(null);
const closeTimeouts = ref<Record<number, number | undefined>>({});

// Functions to handle dropdown open/close with delay
function openDropdown(index: number): void {
  // Clear any existing timeout for this dropdown
  if (closeTimeouts.value[index]) {
    window.clearTimeout(closeTimeouts.value[index]);
    closeTimeouts.value = { ...closeTimeouts.value, [index]: undefined };
  }
  // Open immediately
  openDropdownIndex.value = index;
}

function startCloseDropdown(index: number): void {
  // Set a timeout to close after a delay
  closeTimeouts.value[index] = window.setTimeout(() => {
    if (openDropdownIndex.value === index) {
      openDropdownIndex.value = null;
    }
    closeTimeouts.value = { ...closeTimeouts.value, [index]: undefined };
  }, 800); // 800ms delay gives more time to move to the dropdown
}

// Detect if the page is scrolled
const handleScroll = () => {
  isScrolled.value = window.scrollY > 0;
};

onMounted(() => {
  window.addEventListener('scroll', handleScroll);
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);

  // Clear any remaining timeouts
  for (const timeoutId of Object.values(closeTimeouts.value)) {
    window.clearTimeout(timeoutId);
  }
});
const route = useRoute();
const isLanding = computed(() => route.path === '/');

const visibleMenus = computed(() => {
  let menus: HomeMenusResponse['menus'] | undefined;
  if (width.value > 1500) menus = menuData.value?.menus;
  else if (width.value > 1350) menus = menuData.value?.menus.slice(0, 8);
  else if (width.value >= 1350) menus = menuData.value?.menus.slice(0, 7);
  else if (width.value < 1130 && width.value > 1055)
    menus = menuData.value?.menus.slice(0, 4);
  else if (width.value <= 1055) menus = menuData.value?.menus.slice(0, 3);
  else menus = menuData.value?.menus.slice(0, 5);

  return menus;
});
</script>

<template>
  <header
    :class="
      cn(
        '!text-left whitespace-nowrap fixed top-0 left-0 right-0 z-40 transition-all duration-300',
        isLanding
          ? 'text-white border-b-[6px] border-[#3E86CA]'
          : 'text-primary border-b-[6px] border-[#3E86CA]',
        (isScrolled && isLanding) || !isLanding
          ? 'bg-white/95 backdrop-blur-md text-primary shadow-lg'
          : 'bg-transparent border-none'
      )
    "
  >
    <div class="flex items-center px-4 md:px-12 w-full h-[60px] bg-transparent">
      <div
        class="flex items-center group hover:scale-[1.01] transition-transform duration-300 ease-out"
      >
        <NuxtImg
          width="60"
          format="webp"
          height="auto"
          class="w-[40px] md:w-[54px] h-auto object-scale-down"
          src="/img/db-college-logo.png"
          alt="Don Bosco College Mannuthy"
        />
        <NuxtLink to="/">
          <div
            class="ml-2 lg:ml-4 whitespace-nowrap leading-none flex flex-col"
          >
            <h4
              class="text-[10px] sm:text-sm lg:text-base font-bold leading-none tracking-normal transition-colors"
            >
              DON BOSCO COLLEGE MANUTHY
            </h4>
            <span
              class="text-[7px] sm:text-[8px] lg:text-[10px] md:mt-[2px] italic text-right font-light w-full tracking-wider"
              >(Affiliated to University of Calicut)</span
            >
            <span
              class="text-[7px] sm:text-[8px] lg:text-[10px] mt-1 md:mt-[4px] uppercase text-right font-light w-full tracking-wider"
              >Accredited with NAAC B+</span
            >
          </div>
        </NuxtLink>
        <NuxtImg
          format="webp"
          width="80"
          height="auto"
          class="w-[35px] md:w-[60px] h-auto object-scale-down ml-2"
          src="/img/naac.png"
          alt="Don Bosco College Mannuthy"
        />
      </div>

      <div class="ml-auto flex items-center justify-end h-[50px] pl-4 md:pl-10">
        <div class="flex items-center gap-x-2 xl:gap-x-5">
          <nav
            v-if="menuData?.menus"
            class="hidden lg:flex items-center gap-x-1 2xl:gap-x-2 text-sm font-normal select-none"
          >
            <template v-for="(item, index) in visibleMenus" :key="item.label">
              <!-- ----if root item contain link render directly without dropdown----- -->
              <NuxtLink v-if="item.link" target="_blank" :to="item.link">
                <div
                  class="nav-item relative px-3 py-2 cursor-pointer overflow-hidden rounded-md group w-full"
                >
                  <!-- Animated background and effects -->
                  <div
                    class="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary/90 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-md z-0"
                  ></div>
                  <div
                    class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 nav-item-glow z-0"
                  ></div>

                  <!-- Text with animations -->
                  <span
                    class="relative z-10 font-medium group-hover:text-white transition-all duration-300 flex items-center"
                  >
                    <span class="nav-item-text">{{ item.label }}</span>
                    <!-- <span
                      class="nav-item-icon ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    >
                      <ArrowUpRight class="w-3.5 h-3.5" />
                    </span> -->
                  </span>
                </div>
              </NuxtLink>

              <!-- --------if no children render label only----- -->
              <div
                v-else-if="!item.children"
                class="nav-item relative px-3 py-2 cursor-pointer overflow-hidden rounded-md group w-full"
              >
                <!-- Animated background and effects -->
                <!-- <div
                  class="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary/90 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-md z-0"
                ></div>
                <div
                  class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 nav-item-glow z-0"
                ></div> -->

                <!-- Text with animations -->
                <span
                  class="relative z-10 font-medium group-hover:text-white transition-all duration-300"
                >
                  <span class="nav-item-text">{{ item.label }}</span>
                </span>
              </div>

              <!-- Items with dropdown menus -->
              <!-- Custom dropdown implementation -->
              <div
                v-else
                class="custom-dropdown nav-item"
                @mouseenter="openDropdown(index)"
                @mouseleave="startCloseDropdown(index)"
              >
                <div
                  class="nav-item relative px-3 py-2 cursor-pointer overflow-hidden rounded-md group w-full"
                >
                  <!-- Animated background and effects -->
                  <div
                    class="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary/90 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-md z-0"
                  ></div>
                  <div
                    class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 nav-item-glow z-0"
                  ></div>

                  <!-- Text with dropdown indicator -->
                  <span
                    class="relative z-10 font-medium group-hover:text-white transition-all duration-300 flex items-center"
                  >
                    <span class="nav-item-text">{{ item.label }}</span>
                    <!-- <span
                      class="nav-item-icon ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    >
                      <ChevronDown class="w-3.5 h-3.5" />
                    </span> -->
                  </span>
                </div>

                <!-- Custom dropdown panel -->
                <div
                  v-show="openDropdownIndex === index"
                  :class="[
                    'custom-dropdown-panel bg-white p-0',
                    index + 1 === visibleMenus?.length ? 'right-0' : 'left-0',
                    item.children && item.children.length > 8
                      ? 'w-[500px] max-w-[90vw]'
                      : item.children && item.children.length > 4
                        ? 'w-[400px] max-w-[90vw]'
                        : 'w-[300px] max-w-[90vw]',
                  ]"
                  @mouseenter="openDropdown(index)"
                  @mouseleave="startCloseDropdown(index)"
                >
                  <!-- Dropdown content -->
                  <div class="p-2 sm:p-3 custom-dropdown-content">
                    <HomeNavigationDropdownRecursive
                      :data="item.children || []"
                    />
                  </div>

                  <!-- Decorative elements -->
                  <!-- <div class="custom-dropdown-decoration top-left"></div>
                  <div class="custom-dropdown-decoration top-right"></div>
                  <div class="custom-dropdown-decoration bottom-left"></div>
                  <div class="custom-dropdown-decoration bottom-right"></div> -->
                </div>
              </div>
            </template>
          </nav>
          <Button
            variant="ghost"
            :class="
              cn(
                'bg-transparent w-fit px-3 py-2 text-sm hover:text-white font-bold border transition-all duration-300 overflow-hidden relative group',
                isScrolled
                  ? 'border-primary hover:border-primary'
                  : 'border-white hover:border-primary',
                !isLanding && 'border-primary hover:border-primary',
                'rounded-md hover:shadow-lg'
              )
            "
            @click="toggleMenu"
          >
            <div class="relative z-10 flex items-center gap-2">
              <Menu
                class="w-5 h-auto transition-transform group-hover:rotate-90 duration-300"
              />
              <span class="hidden md:block">Menu</span>
            </div>
            <div
              class="absolute inset-0 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
            ></div>
          </Button>
        </div>
      </div>
    </div>
  </header>
  <div v-if="!isLanding" class="h-[60px]" />
  <HomeNavigationNavMenu
    :menus="[...(menuData?.menus || []), ...(menuData?.quickLinks || [])]"
    :is-open="isMenuOpen"
    @on-close="toggleMenu"
  />
</template>

<style scoped>
/* Nav item styling with fixed dimensions */
.nav-item {
  position: relative;
  /* Add fixed height to prevent jumping */
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  /* Prevent content from affecting dimensions */
  box-sizing: border-box;
}

/* Nav item underline effect */
.nav-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--primary);
  transition: all 0.3s ease;
  transform: translateX(-50%);
  z-index: 1;
}

.nav-item:hover::after {
  width: 100%;
}

/* Nav item glow effect */
.nav-item-glow {
  box-shadow: 0 0 15px 5px rgba(59, 130, 246, 0.3);
  filter: blur(10px);
  border-radius: 0.5rem;
  /* Ensure glow doesn't affect layout */
  pointer-events: none;
}

/* Text animations */
.nav-item-text {
  display: inline-block;
  transition: all 0.3s ease;
  /* Add a fixed height to prevent jumping */
  height: 1.5rem;
  line-height: 1.5rem;
}

.nav-item:hover .nav-item-text {
  /* Remove vertical movement that causes jumping */
  transform: none;
  text-shadow: 0 2px 10px rgba(255, 255, 255, 0.5);
}

/* Icon animations */
.nav-item-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  /* Add fixed dimensions to prevent layout shifts */
  width: 1rem;
  height: 1rem;
  position: relative;
}

/* Dropdown indicator animation - use opacity instead of movement */
.nav-item:hover .nav-item-icon {
  /* Remove bounce animation that causes jumping */
  animation: none;
  opacity: 1;
}

/* Custom dropdown styling */
.custom-dropdown {
  position: relative;
  display: inline-block;
  width: 100%;
}

/* Dropdown panel styling */
.custom-dropdown-panel {
  pointer-events: auto;
  opacity: 1;
  visibility: visible;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease,
    transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Animation for dropdown */
.custom-dropdown-panel {
  animation: dropdown-in 0.25s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Create a larger hover area for the dropdown */
.custom-dropdown::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 30px; /* Extra hover area below the menu item */
  background: transparent;
}

/* Create a hover bridge between menu items */
.nav-item:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 0;
  right: -10px;
  width: 20px;
  height: 100%;
  background: transparent;
  z-index: 10;
}

/* Create a hover bridge above the dropdown panel */
.custom-dropdown-panel::before {
  content: '';
  position: absolute;
  top: -30px;
  left: 0;
  width: 100%;
  height: 30px;
  background: transparent;
}

/* Create hover bridges between dropdown panels */
.custom-dropdown-panel::after {
  content: '';
  position: absolute;
  top: 0;
  right: -20px;
  width: 40px;
  height: 100%;
  background: transparent;
  z-index: 10;
}

.custom-dropdown-panel {
  border-radius: 1rem;
  position: absolute;
  top: 100%;
  margin-top: 0.5rem; /* Increased from 0.25rem to position dropdown lower */
  animation: nav-in 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow:
    0 10px 40px -5px rgba(0, 0, 0, 0.3),
    0 8px 20px -6px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(0, 0, 0, 0.1),
    0 0 15px rgba(59, 130, 246, 0.3);
  transform-origin: top center;
  transition:
    width 0.3s ease-in-out,
    transform 0.3s ease-in-out;
  max-width: 90vw; /* Ensure it doesn't overflow on small screens */
  border: 2px solid var(--primary);
  overflow: visible;
  z-index: 100;
  pointer-events: auto;
  opacity: 1;
}

/* Fix for the last menu item's dropdown positioning */
.custom-dropdown-panel.right-0 {
  right: 0;
  transform-origin: top right;
}

/* Add a dropdown arrow indicator */
.custom-dropdown-panel::after {
  content: '';
  position: absolute;
  top: -8px;
  width: 14px;
  height: 14px;
  background-color: white;
  border-top: 1px solid rgba(19, 89, 168, 0.2);
  border-left: 1px solid rgba(19, 89, 168, 0.2);
  transform: rotate(45deg);
  z-index: 1;
  box-shadow: -3px -3px 5px rgba(0, 0, 0, 0.02);
  animation: arrow-in 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

/* Position the arrow for left-aligned dropdowns */
.custom-dropdown-panel.left-0::after {
  left: 20px;
}

/* Position the arrow for right-aligned dropdowns */
.custom-dropdown-panel.right-0::after {
  right: 20px;
  left: auto; /* Ensure left is not set */
}

/* Ensure the dropdown is properly positioned for the last item */
.nav-item:last-child .custom-dropdown-panel.right-0 {
  right: 0;
  left: auto;
  transform-origin: top right;
}

/* Background elements */
.custom-dropdown-bg {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(240, 249, 255, 0.95) 100%
  );
  z-index: -2;
}

.custom-dropdown-glow {
  position: absolute;
  width: 150%;
  height: 150%;
  top: -25%;
  left: -25%;
  background: radial-gradient(
    circle,
    rgba(19, 89, 168, 0.15) 0%,
    rgba(38, 123, 202, 0) 70%
  );
  z-index: -1;
  opacity: 0;
  animation: glow-pulse 4s ease-in-out infinite;
}

.top-left {
  top: -10px;
  left: -10px;
}

.top-right {
  top: -10px;
  right: -10px;
  animation-delay: 0.3s;
}

.bottom-left {
  bottom: -10px;
  left: -10px;
  animation-delay: 0.4s;
}

.bottom-right {
  bottom: -10px;
  right: -10px;
  animation-delay: 0.5s;
}

/* Arrow indicator for dropdown */
/* .custom-dropdown-panel::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 2rem;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  border-radius: 2px;
  z-index: 1;
  border-top: 1px solid rgba(19, 89, 168, 0.2);
  border-left: 1px solid rgba(19, 89, 168, 0.2);
  box-shadow: -3px -3px 5px rgba(0, 0, 0, 0.02);
  animation: arrow-in 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
} */

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
  .custom-dropdown-panel {
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    max-width: 100%;
    margin-top: 0.25rem;
    border-radius: 0.5rem;
  }
}

/* Enhanced dropdown animations */
.custom-dropdown-content {
  opacity: 0;
  transform: translateY(-5px);
  animation: fade-in-down 0.4s ease forwards 0.1s;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible;
  z-index: 1;
}

/* Animation keyframes */
@keyframes bounce {
  0%,
  100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

@keyframes nav-in {
  0% {
    opacity: 0;
    transform: translateY(5px) scale(0.98);
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes fade-in-down {
  from {
    opacity: 0;
    transform: translateY(-5px);
    filter: blur(3px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

@keyframes glow-pulse {
  0%,
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes decoration-in {
  from {
    opacity: 0;
    transform: scale(0);
  }
  to {
    opacity: 0.8;
    transform: scale(1);
  }
}

@keyframes dropdown-in {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes arrow-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
