<script setup lang="ts">
import type { MenuWithRelations } from '~~/server/database/tables/menu';

defineProps<{
  configuration: MenuWithRelations[];
}>();
</script>

<template>
  <div class="menu-links">
    <h2 class="text-xl font-bold mb-4">Quick Links</h2>

    <div v-for="menu in configuration" :key="menu.title">
      <p v-if="menu.title" class="text-sm mb-4">
        {{ menu.title }}
      </p>
    </div>
  </div>
</template>
