<script setup lang="ts">
import type { AnnouncementListData } from '~/types/home';

defineProps<{
  eventData: AnnouncementListData;
}>();

const formatEventDate = (date: number | null) => {
  return formatDate(date || Date.now());
};
</script>

<template>
  <NuxtLink :to="`/events/${eventData.id}`">
    <div class="w-full h-full bg-white shadow rounded-[6px]">
      <NuxtImg
        loading="lazy"
        class="rounded-t-[6px] h-[170px] w-full object-cover"
        format="webp"
        :src="getPreviewUrl(eventData.primaryImage.pathname)"
        :alt="eventData.title"
      />
      <div class="flex items-center gap-x-4 rounded-[5px] p-3">
        <div
          class="px-4 py-2 flex flex-col gap-y-1 justify-center items-center bg-primary-gradient text-white rounded-[5px]"
        >
          <span class="text-sm font-light leading-none">{{
            formatEventDate(eventData.date).month
          }}</span>
          <span class="text-xl font-bold leading-none">{{
            formatEventDate(eventData.date).day
          }}</span>
          <span class="text-sm font-light leading-none">{{
            formatEventDate(eventData.date).year
          }}</span>
        </div>
        <h4 class="text-lg font-medium leading-normal">
          {{ eventData.title }}
        </h4>
      </div>
    </div>
  </NuxtLink>
</template>
