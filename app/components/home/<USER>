<script setup lang="ts">
import EducationIcon from '@/assets/icons/education.svg';
import ExperienceIcon from '@/assets/icons/experience.svg';
import AreaOfInterestIcon from '@/assets/icons/area-of-intrest.svg';
import { Loader2 } from 'lucide-vue-next';
import type { FacultyFullData } from '~/types/home';
import { ref, watch } from 'vue';

const props = defineProps<{
  facultyId: number;
  slug: string;
  isOpen: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:open', open: boolean): void;
}>();

const selectedFacultyId = ref<number>(props.facultyId);
const isFacultyLoading = ref(true);
const faculty = ref<FacultyFullData | null>(null);

const facultyLoad = async (id: number) => {
  isFacultyLoading.value = true;
  selectedFacultyId.value = id;
  const response = await $fetch<FacultyFullData>(
    `/api/home/<USER>/${props.slug}/faculty/${id}`
  );
  faculty.value = response;
  isFacultyLoading.value = false;
};

watch(
  () => props.facultyId,
  (value) => {
    facultyLoad(value);
  }
);

// Handle opening CV modal
const openCVModal = () => {
  const url = getPreviewUrl(faculty.value?.resume?.pathname ?? '');
  // Open in new tab
  window.open(url, '_blank');
};
</script>

<template>
  <Dialog :open="isOpen" @update:open="emit('update:open', false)">
    <DialogContent v-if="isFacultyLoading" class="!p-0 lg:max-w-[1000px]">
      <div class="flex-center h-full">
        <Loader2 class="w-10 h-10 animate-spin" />
      </div>
    </DialogContent>
    <DialogContent
      v-else-if="faculty && facultyId"
      class="!p-0 lg:max-w-[1000px]"
    >
      <div class="grid grid-cols-[250px_auto]">
        <div class="bg-white text-primary flex flex-col items-center py-14">
          <div
            class="relative w-[118px] h-[118px] rounded-full overflow-hidden"
          >
            <img
              v-if="faculty.image"
              loading="lazy"
              class="w-full object-cover h-full absolute inset-0"
              fit="cover"
              format="webp"
              :src="getPreviewUrl(faculty.image.pathname)"
              alt="profile pic"
            />
          </div>
          <span class="font-semibold mt-5">{{ faculty.name }}</span>
          <span class="text-xs mt-2 font-medium">{{
            faculty.designation
          }}</span>
          <span class="text-xs mt-2 font-medium"
            >Date of Joining :
            {{
              new Date(faculty.startDate).toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric',
              })
            }}</span
          >
          <button
            v-if="faculty.resume"
            class="flex-center mt-5 text-xs font-medium text-white bg-primary-gradient px-8 h-[35px] rounded-[5px]"
            @click="openCVModal"
          >
            View CV
          </button>
        </div>
        <div class="bg-[#F4F4F4] py-14 pl-10 pr-12">
          <div>
            <div class="flex gap-x-3 items-center">
              <EducationIcon
                class="w-5 h-auto text-primary"
                :font-controlled="false"
              />
              <span class="text-primary font-semibold">Education</span>
            </div>
            <div class="mt-3">
              <Table class="rounded-[5px]">
                <TableHeader class="bg-[#6390C3]">
                  <TableRow>
                    <TableHead
                      class="ps-5 w-[150px] text-white border-r border-[#E7EAED]"
                      >Degree</TableHead
                    >
                    <TableHead class="ps-5 text-white border-r border-[#E7EAED]"
                      >University</TableHead
                    >
                    <TableHead class="ps-5 w-[100px] text-white"
                      >Year</TableHead
                    >
                  </TableRow>
                </TableHeader>
                <TableBody class="bg-white">
                  <TableRow
                    v-for="(item, index) of faculty.education"
                    :key="index"
                    class="text-sm !text-left"
                  >
                    <TableCell class="ps-5 border-r border-[#E7EAED]">{{
                      item.degree
                    }}</TableCell>
                    <TableCell class="ps-5 border-r border-[#E7EAED]">{{
                      item.university
                    }}</TableCell>
                    <TableCell class="ps-5">{{ item.passOutYear }}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>

          <div class="mt-6">
            <div class="flex gap-x-3 items-center">
              <ExperienceIcon
                class="w-4 h-auto text-primary"
                :font-controlled="false"
              />
              <span class="text-primary font-semibold">Experience</span>
            </div>
            <div class="mt-3">
              <Table>
                <TableHeader class="bg-[#6390C3]">
                  <TableRow>
                    <TableHead
                      class="ps-5 border-r border-[#E7EAED] w-[100px] text-white"
                      >Year</TableHead
                    >
                    <TableHead
                      class="ps-5 border-r border-[#E7EAED] w-[100px] text-white"
                      >Year To</TableHead
                    >
                    <TableHead class="ps-5 border-r border-[#E7EAED] text-white"
                      >Organization</TableHead
                    >
                    <TableHead class="ps-5 text-white">Designation</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody class="bg-white">
                  <TableRow
                    v-for="(item, index) of faculty.experience"
                    :key="index"
                    class="text-sm"
                  >
                    <TableCell class="ps-5 border-r border-[#E7EAED]">{{
                      item.startYear
                    }}</TableCell>
                    <TableCell class="ps-5 border-r border-[#E7EAED]">{{
                      item.endYear
                    }}</TableCell>
                    <TableCell class="ps-5 border-r border-[#E7EAED]">{{
                      item.organization
                    }}</TableCell>
                    <TableCell class="ps-5">{{ item.designation }}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>

          <div class="mt-6">
            <div class="flex gap-x-3 items-center">
              <AreaOfInterestIcon
                class="w-4 h-auto text-primary"
                :font-controlled="false"
              />
              <span class="text-primary font-semibold">Area Of Interest</span>
            </div>
            <div class="mt-3 flex items-center flex-wrap gap-x-2">
              <div
                v-for="(item, index) of faculty.areaOfInterest"
                :key="index"
                class="flex-center shadow-sm bg-white text-sm px-5 h-[50px] font-medium rounded-[5px]"
              >
                {{ item }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DialogContent>
    <DialogContent v-else> No Data found for faculty </DialogContent>
  </Dialog>
</template>
