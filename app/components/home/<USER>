<template>
  <div class="flex flex-col items-center justify-center gap-5 loader-container">
    <div class="logo-container">
      <NuxtImg
        format="webp"
        width="60"
        loading="lazy"
        height="auto"
        class="w-24 h-auto object-scale-down logo-image"
        src="/img/db-college-logo.png"
        alt="Don Bosco College Mannuthy"
      />
      <!-- Animated rings around the logo -->
      <div class="logo-ring ring-1"></div>
      <div class="logo-ring ring-2"></div>
      <div class="logo-ring ring-3"></div>
    </div>

    <div class="text-primary text-lg font-semibold tracking-wide loading-text">
      Loading<span class="dot">.</span><span class="dot">.</span
      ><span class="dot">.</span>
    </div>

    <div class="loading-bar-background">
      <div class="loading-bar">
        <div class="white-bars-container">
          <div class="white-bar"></div>
          <div class="white-bar"></div>
          <div class="white-bar"></div>
          <div class="white-bar"></div>
          <div class="white-bar"></div>
          <div class="white-bar"></div>
          <div class="white-bar"></div>
          <div class="white-bar"></div>
          <div class="white-bar"></div>
          <div class="white-bar"></div>
        </div>
      </div>
    </div>

    <!-- Animated particles -->
    <div class="particles-container">
      <div class="particle p1"></div>
      <div class="particle p2"></div>
      <div class="particle p3"></div>
      <div class="particle p4"></div>
      <div class="particle p5"></div>
      <div class="particle p6"></div>
    </div>
  </div>
</template>

<style scoped>
/* Container animation */
.loader-container {
  position: relative;
  animation: container-fade-in 0.5s ease-out forwards;
}

@keyframes container-fade-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Logo animations */
.logo-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
}

.logo-image {
  position: relative;
  z-index: 2;
  animation: logo-pulse 2s ease-in-out infinite;
}

@keyframes logo-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Animated rings */
.logo-ring {
  position: absolute;
  border-radius: 50%;
  border: 2px solid transparent;
  background: linear-gradient(90deg, #11508b, #267bca, #64b4ff, #11508b)
    border-box;
  -webkit-mask:
    linear-gradient(#fff 0 0) padding-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
  opacity: 0.7;
}

.ring-1 {
  width: 100%;
  height: 100%;
  animation: ring-rotate 4s linear infinite;
}

.ring-2 {
  width: 85%;
  height: 85%;
  animation: ring-rotate 6s linear infinite reverse;
}

.ring-3 {
  width: 70%;
  height: 70%;
  animation: ring-rotate 8s linear infinite;
}

@keyframes ring-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Loading text animation */
.loading-text {
  position: relative;
  z-index: 2;
  text-shadow: 0 2px 10px rgba(19, 89, 168, 0.2);
}

.dot {
  margin-left: 3px;
  animation: blink 1.5s infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.3s;
}

.dot:nth-child(3) {
  animation-delay: 0.6s;
}

/* Loading bar animations */
.loading-bar-background {
  --height: 12px;
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 2px;
  width: 280px;
  height: var(--height);
  background-color: #f0f0f0;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px inset;
  border-radius: calc(var(--height) / 2);
  overflow: hidden;
}

.loading-bar {
  position: relative;
  display: flex;
  justify-content: center;
  flex-direction: column;
  --height: 8px;
  width: 0%;
  height: var(--height);
  overflow: hidden;
  background: #00428c;
  background: linear-gradient(90deg, rgb(5, 94, 195) 0%, rgb(8, 90, 184) 100%);
  border-radius: calc(var(--height) / 2);
  animation: loading 2s ease-in-out infinite;
  box-shadow: 0 0 10px rgba(19, 89, 168, 0.5);
}

.white-bars-container {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 12px;
}

.white-bar {
  background: rgb(255, 255, 255);
  background: linear-gradient(
    -45deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  width: 8px;
  height: 20px;
  opacity: 0.3;
  rotate: 45deg;
}

/* Animated particles */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 10px;
  height: 10px;
  background: linear-gradient(90deg, #11508b, #267bca);
  border-radius: 50%;
  opacity: 0.2;
  filter: blur(1px);
}

.p1 {
  top: 20%;
  left: 20%;
  animation: float-particle 8s ease-in-out infinite;
}

.p2 {
  top: 70%;
  left: 30%;
  width: 15px;
  height: 15px;
  animation: float-particle 12s ease-in-out infinite 1s;
}

.p3 {
  top: 40%;
  left: 70%;
  width: 8px;
  height: 8px;
  animation: float-particle 9s ease-in-out infinite 2s;
}

.p4 {
  top: 80%;
  left: 60%;
  width: 12px;
  height: 12px;
  animation: float-particle 10s ease-in-out infinite 0.5s;
}

.p5 {
  top: 30%;
  left: 80%;
  width: 6px;
  height: 6px;
  animation: float-particle 11s ease-in-out infinite 1.5s;
}

.p6 {
  top: 60%;
  left: 10%;
  width: 14px;
  height: 14px;
  animation: float-particle 13s ease-in-out infinite 0.2s;
}

@keyframes float-particle {
  0%,
  100% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(20px, 20px);
  }
  50% {
    transform: translate(0, 40px);
  }
  75% {
    transform: translate(-20px, 20px);
  }
}

@keyframes loading {
  0% {
    width: 0;
  }
  50% {
    width: 100%;
  }
  100% {
    width: 0%;
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}
</style>
