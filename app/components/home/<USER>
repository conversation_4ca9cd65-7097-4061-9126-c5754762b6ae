<script setup lang="ts">
import type { RelatedLinks } from './about-us/related-links.vue';

const props = defineProps<{
  title: string;
  subHeader: {
    breadcrumbs: Array<{ label: string; link?: string }>;
  };
}>();

const links: RelatedLinks = [
  { link: '/', label: 'Overview' },
  { label: 'Courses', link: '/courses' },
  { label: 'Faculty', link: '/faculty' },
  { label: 'Placement', link: '/placement' },
  { label: 'Downloads', link: '/downloads' },
  { label: 'Question Bank', link: '/question-bank' },
  { label: 'Project Work', link: '/project-work' },
  // { label: 'Achievements', link: '/achievements' },
  { label: 'Gallery', link: '/gallery' },
  { label: 'Events', link: '/events' },
  { label: 'Toppers', link: '/toppers' },
];
const route = useRoute('departments-slug');
const slug = route.params.slug;
</script>

<template>
  <HomeSubHeader
    title="Departments"
    :links="links"
    :link-prefix="`/departments/${slug}`"
    :breadcrumbs="props.subHeader.breadcrumbs"
  />
  <div class="bg-[#F4F4F4]">
    <div class="container py-10 flex gap-x-5">
      <section class="grow">
        <h2
          class="text-[25px] text-primary font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
        >
          {{ title }}
        </h2>
        <div class="flex items-start gap-x-4 mt-6">
          <div class="grow">
            <slot />
          </div>
          <div class="hidden lg:block">
            <HomeAboutUsRelatedLinks
              :links="links"
              :link-prefix="`/departments/${slug}`"
            />
          </div>
        </div>
      </section>
    </div>
  </div>
</template>
