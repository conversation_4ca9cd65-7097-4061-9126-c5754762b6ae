<script setup lang="ts">
// import type { SectionWithRelations } from '~~/server/database/tables';

// const props = defineProps<{
//   section: SectionWithRelations;
// }>();

// Create a components map
// const components = {
//   HomeTemplateCards,
//   HomeTemplateAccordion,
//   HomeTemplateText,
// };

// const componentName = computed(
//   () =>
//     `HomeTemplate${props.section.type.charAt(0).toUpperCase()}${props.section.type.slice(1)}`
// );

// Transform the configuration based on component type
// const configuration = computed(() => {
//   return props.section.type === 'cardsList'
//     ? props.section.cardListConfig
//     : props.section.type === 'accordion'
//       ? props.section.accordionConfig
//       : props.section.textEditorConfig;
// });
</script>

<template>
  <ClientOnly>
    <div class="space-y-4">
      <!--
      <component
        :is="components[componentName as keyof typeof components]"
        :configuration="configuration"
      />
      -->
    </div>
    <!-- Fallback for SSR -->
    <template #fallback>
      <div>Loading interactive components...</div>
    </template>
  </ClientOnly>
</template>
