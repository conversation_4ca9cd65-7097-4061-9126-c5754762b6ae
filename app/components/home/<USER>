<!-- components/ContentModal.vue -->
<script setup lang="ts">
import type { ModalWithRelations } from '~~/server/database/tables';
import HomeDynamicSection from '../home/<USER>';

interface Props {
  isOpen: boolean;
  modal: ModalWithRelations | null;
}

defineProps<Props>();

const emit = defineEmits<{
  (e: 'update:isOpen', value: boolean): void;
}>();
</script>

<template>
  <Dialog :open="isOpen" @update:open="emit('update:isOpen', $event)">
    <DialogContent class="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>{{ modal?.title }}</DialogTitle>
        <DialogDescription v-if="modal?.description">
          {{ modal.description }}
        </DialogDescription>
      </DialogHeader>
      <div v-if="modal?.template" class="mt-4">
        <HomeDynamicSection
          v-for="section in modal.template.sections"
          :key="section.id"
          :section="section"
        />
      </div>
    </DialogContent>
  </Dialog>
</template>
