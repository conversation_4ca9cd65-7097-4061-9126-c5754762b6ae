<script setup lang="ts">
defineProps<{
  message?: string;
  rootClass?: string;
}>();
</script>

<template>
  <div :class="cn('flex-center flex-col', rootClass)">
    <img
      src="/img/no-data-found.png"
      width="523"
      height="342"
      class="w-[260px] h-[170px] lg:w-[523px] lg:h-[342px] object-contain"
      alt="Preview"
    />
    <span
      class="mt-2 text-xl lg:text-2xl leading-none capitalize text-primary font-semibold"
      >{{ message || 'No Data Found' }}</span
    >
  </div>
</template>
