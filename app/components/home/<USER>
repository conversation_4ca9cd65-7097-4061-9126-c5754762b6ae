<script setup lang="ts">
import type { FileData } from '~/types/home';
import { FileText, Image, FileBox, Download } from 'lucide-vue-next';
import { NuxtImg } from '#components';

defineProps<{
  filesList: FileData[];
}>();

const getFileType = (type: string) => {
  if (type === 'image' || type.includes('image')) return 'image';
  if (type === 'pdf' || type.includes('pdf')) return 'pdf';
  return 'document';
};
const getFileIcon = (fileType: string) => {
  switch (fileType) {
    case 'image':
      return Image;
    case 'pdf':
      return FileText;
    default:
      return FileBox;
  }
};
</script>
<template>
  <div
    class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
  >
    <div
      v-for="file in filesList"
      :key="file.title"
      class="group relative flex flex-col border border-gray-200 rounded-xl overflow-hidden bg-white hover:border-primary-500 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
    >
      <!-- Preview Section -->
      <div
        class="h-48 bg-gray-50 flex items-center justify-center border-b border-gray-100 group-hover:bg-gray-100/80 transition-colors duration-300"
      >
        <!-- Image Preview -->
        <NuxtImg
          v-if="getFileType(file.type) === 'image'"
          format="webp"
          :src="getPreviewUrl(file.pathname)"
          :alt="file.title"
          loading="lazy"
          class="max-h-full w-full object-cover group-hover:scale-[1.02] transition-transform duration-300"
        />
        <!-- PDF Preview -->
        <div
          v-else-if="getFileType(file.type) === 'pdf'"
          class="w-full h-full flex flex-col items-center justify-center gap-2"
        >
          <FileText
            class="w-16 h-16 text-primary group-hover:scale-110 transition-transform duration-300"
          />
          <span class="text-sm text-gray-600">PDF Document</span>
        </div>
        <!-- Other File Types -->
        <div
          v-else
          class="w-full h-full flex flex-col items-center justify-center gap-2"
        >
          <component
            :is="getFileIcon(file.type)"
            class="w-16 h-16 text-primary-600 group-hover:scale-110 transition-transform duration-300"
          />
          <span class="text-sm text-gray-600">Document</span>
        </div>
      </div>

      <!-- File Info -->
      <div class="p-4 flex flex-col gap-3">
        <h5 class="font-medium text-sm truncate text-gray-900">
          {{ file.title }}
        </h5>
        <NuxtLink
          :to="getPreviewUrl(file.pathname)"
          target="_blank"
          class="inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 text-sm font-medium bg-primary-50 hover:bg-primary-100 px-4 py-2 rounded-lg transition-colors duration-300"
        >
          <Download class="w-4 h-4" />
          Download File
        </NuxtLink>
      </div>

      <!-- Hover Overlay -->
      <div
        class="absolute inset-0 bg-primary-600/0 group-hover:bg-primary-600/5 pointer-events-none transition-colors duration-300"
      />
    </div>
  </div>
</template>
