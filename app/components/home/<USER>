<script setup lang="ts">
import { Cross2Icon } from '@radix-icons/vue';
import { Menu } from 'lucide-vue-next';
import type { RelatedLinks } from './about-us/related-links.vue';
defineProps<{
  title: string;
  breadcrumbs: Array<{ label: string; link?: string }>;
  links?: RelatedLinks;
  linkPrefix?: string;
}>();
</script>

<template>
  <div class="relative w-full h-[128px]">
    <NuxtImg
      loading="lazy"
      class="w-full h-[128px]"
      width="auto"
      height="260"
      format="webp"
      src="/temp/breadcrumb-banner.png"
      alt="breadcrumb-banner"
    />
    <div class="absolute container inset-0">
      <h1
        class="text-lg md:text-2xl lg:text-4xl line-clamp-2 font-bold mt-6 text-primary"
      >
        {{ title }}
      </h1>
      <div
        class="mt-3 bg-white/80 flex items-center justify-between px-3 lg:px-5 h-[30px] w-full backdrop:blur-md rounded-[4px]"
        style="box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1)"
      >
        <Breadcrumb class="overflow-hidden mr-4">
          <BreadcrumbList
            class="text-primary flex-nowrap overflow-auto scrollbar-hidden pr-2"
          >
            <template v-for="(item, index) of breadcrumbs" :key="index">
              <BreadcrumbItem v-if="item.link" class="max-w-52">
                <NuxtLink :to="item.link" class="truncate capitalize">
                  {{ item.label }}
                </NuxtLink>
              </BreadcrumbItem>
              <BreadcrumbItem v-if="!item.link">
                <BreadcrumbPage
                  :class="
                    cn(
                      'text-primary text-nowrap max-w-52 truncate',
                      index + 1 === breadcrumbs.length && 'font-semibold'
                    )
                  "
                >
                  {{ item.label }}
                </BreadcrumbPage>
              </BreadcrumbItem>

              <BreadcrumbSeparator v-if="index + 1 < breadcrumbs.length" />
            </template>
          </BreadcrumbList>
        </Breadcrumb>

        <Sheet>
          <SheetTrigger>
            <Menu
              v-if="links"
              class="w-5 h-auto text-primary block lg:hidden"
            />
          </SheetTrigger>
          <SheetContent side="bottom" class="rounded-t-[20px]">
            <SheetHeader>
              <SheetTitle>
                <div
                  class="flex items-center justify-between gap-x-4 px-4 lg:justify-center text-lg font-semibold rounded-[6px] text-white w-full h-[45px] bg-primary-gradient"
                >
                  Related Links
                  <DialogClose
                    class="rounded-sm lg:hidden ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary"
                  >
                    <Cross2Icon class="w-5 h-5 text-white" />
                  </DialogClose>
                </div>
              </SheetTitle>
            </SheetHeader>
            <SheetDescription class="max-h-[70svh] overflow-auto">
              <HomeAboutUsRelatedLinks
                v-if="links"
                :links="links"
                :link-prefix="linkPrefix"
              />
            </SheetDescription>
          </SheetContent>
        </Sheet>
      </div>
    </div>
  </div>
</template>
