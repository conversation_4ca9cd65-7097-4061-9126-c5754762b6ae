<script setup lang="ts">
import { Download } from 'lucide-vue-next';
import type { QuestionBankFiles } from '~/types/home';

defineProps<{
  data: QuestionBankFiles;
}>();
</script>

<template>
  <div
    class="w-full bg-white mt-4 first:mt-0 flex items-center justify-between md:px-4 lg:px-5 md:py-3 rounded-[6px]"
  >
    <div
      class="w-full flex flex-col md:flex-row items-center text-sm xl:text-lg font-semibold text-primary overflow-auto"
    >
      <div
        class="flex items-center w-full md:w-auto px-3 md:px-0 md:py-0 py-1.5 gap-x-3"
        style="box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05)"
      >
        <span>{{ data?.year }}</span>
        <span
          class="w-[1px] h-[30px] shrink-0 bg-[#f2f2f2] inline-block"
        ></span>
        <span>{{ data?.courseName }}</span>
        <Button
          class="flex-center md:hidden ml-auto shrink-0 h-[45px] !bg-primary-gradient rounded-[6px] gap-x-2 text-sm font-semibold text-white bg-primary"
          @click="
            () =>
              downloadFile({
                fileUrl: data.file.pathname,
                fileName: data.file.title,
              })
          "
        >
          <Download class="!w-5 !h-auto" :font-controlled="false" />
          <span class="hidden md:inline-block">Download</span>
        </Button>
      </div>
      <div
        class="flex items-center w-full md:w-auto font-normal gap-x-3 px-3 py-4 md:py-0 md:px-0"
      >
        <span
          class="w-[1px] ml-3 h-[30px] hidden md:inline-block shrink-0 bg-[#f2f2f2]"
        ></span>
        <span>{{ data?.semester }} Semester</span>
        <span
          class="w-[1px] h-[30px] shrink-0 bg-[#f2f2f2] inline-block"
        ></span>
        <span>{{ data?.title }}</span>
      </div>
    </div>
    <Button
      class="hidden md:flex-center shrink-0 h-[45px] !bg-primary-gradient rounded-[6px] gap-x-2 text-sm xl:text-lg font-semibold text-white bg-primary"
      @click="
        () =>
          downloadFile({
            fileUrl: data.file.pathname,
            fileName: data.file.title,
          })
      "
    >
      <Download class="!w-5 !h-auto" :font-controlled="false" />
      <span class="hidden md:inline-block">Download</span>
    </Button>
  </div>
</template>
