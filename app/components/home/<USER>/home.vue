<script setup lang="ts">
import { <PERSON>R<PERSON>, ChevronLeft, ChevronRight } from 'lucide-vue-next';
import type { AnnouncementListData } from '~/types/home';

defineProps<{
  events: AnnouncementListData[];
}>();

const swiperRef = ref(null);
const swiperInstance = useSwiper(swiperRef);
const { width } = useWindowSize();
</script>

<template>
  <section id="latest-events" class="container mt-10">
    <div class="text-primary flex justify-between items-center">
      <h2
        class="text-[25px] font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
      >
        College Events
      </h2>
      <NuxtLink
        v-if="width >= 768"
        to="/events"
        class="flex items-center gap-x-2 text-sm text-nowrap"
      >
        See All Events
        <ArrowRight :size="18" />
      </NuxtLink>
    </div>
    <div class="relative mt-5">
      <ClientOnly>
        <div v-if="width >= 768">
          <swiper-container
            ref="swiperRef"
            :slides-per-view="1"
            :loop="true"
            :breakpoints="{
              768: { slidesPerView: 2.3, spaceBetween: 20 },
              1024: { slidesPerView: 3, spaceBetween: 30 },
            }"
            data-aos="fade-up"
            class="swiper-basic"
          >
            <swiper-slide
              v-for="(item, index) of events"
              :key="`slide-basic-${index}`"
              class="swiper-slide"
            >
              <HomeEventsCard :event-data="item!" />
            </swiper-slide>
          </swiper-container>

          <div class="swiper-basic-buttons hidden lg:block">
            <button
              class="button-prev flex-center bg-slate-200 rounded-full p-1 absolute top-[45%] -translate-y-1/2 -left-6 z-20 shadow-md"
              @click="swiperInstance.prev()"
            >
              <ChevronLeft :size="34" class="text-primary" />
            </button>
            <button
              class="button-next swiper-button-disabled flex-center bg-slate-200 rounded-full p-1 absolute top-[45%] -translate-y-1/2 -right-6 z-20 shadow-md"
              @click="swiperInstance.next()"
            >
              <ChevronRight :size="34" class="text-primary" />
            </button>
          </div>
        </div>

        <div v-else class="space-y-10">
          <HomeEventsCard
            v-for="(item, index) of events.slice(0, 3)"
            :key="index"
            :event-data="item!"
          />
        </div>
      </ClientOnly>

      <NuxtLink
        v-if="width < 768"
        to="/events"
        data-aos="fade-up"
        class="w-full flex-center font-semibold h-[48px] rounded-[6px] border border-primary mt-7 text-primary"
      >
        See All Events
      </NuxtLink>
    </div>
  </section>
</template>
