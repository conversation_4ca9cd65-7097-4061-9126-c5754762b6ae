<script setup lang="ts">
import type { ProfileCard } from '~/types/home';

const props = defineProps<{
  data: ProfileCard;
}>();
const {
  title,
  subtitle1,
  content1,
  subtitle2,
  content2,
  image,
  linkButton,
  downloadButton,
} = props.data;
</script>

<template>
  <div
    class="w-full flex-center flex-col max-w-w-[340px] py-6 bg-white shadow-md text-primary rounded-[6px] overflow-hidden border-b-[9px] border-[#3B7FCC]"
  >
    <div class="relative w-[150px] h-[150px] rounded-full overflow-hidden">
      <NuxtImg
        loading="lazy"
        class="w-full h-full absolute inset-0 object-cover"
        fit="cover"
        format="webp"
        :src="getPreviewUrl(image.pathname)"
        alt="profile pic"
      />
    </div>
    <span class="font-semibold mt-5">{{ title }}</span>
    <span class="text-xs mt-2 font-medium">{{ subtitle1 }}</span>
    <span v-if="subtitle2" class="text-xs mt-4 font-medium">{{
      subtitle2
    }}</span>
    <span v-if="content1" class="text-[25px] font-bold mt-4">{{
      content1
    }}</span>

    <span v-if="content2" class="text-[25px] font-bold mt-4">{{
      content2
    }}</span>

    <HomeTemplateButton v-if="linkButton" :button="linkButton" class="mt-6" />

    <HomeTemplateButton
      v-if="downloadButton"
      :button="downloadButton"
      class="mt-6"
    />
  </div>
</template>
