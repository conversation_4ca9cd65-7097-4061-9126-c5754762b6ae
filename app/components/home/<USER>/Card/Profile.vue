<script setup lang="ts">
import type { ProfileCard } from '~/types/home';

const props = defineProps<{
  data: ProfileCard;
}>();
const {
  title,
  subtitle1,
  content1,
  subtitle2,
  content2,
  image,
  linkButton,
  downloadButton,
} = props.data;
</script>

<template>
  <section class="grow">
    <h2
      v-if="title"
      class="text-[25px] text-primary font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
    >
      {{ title }}
    </h2>
    <div
      class="w-full flex-center flex-col max-w-w-[340px] py-6 bg-white shadow-md text-primary rounded-[6px] overflow-hidden border-b-[9px] border-[#3B7FCC]"
    >
      <div class="relative w-[150px] h-[150px] rounded-full overflow-hidden">
        <NuxtImg
          v-if="image"
          loading="lazy"
          class="w-full h-full absolute inset-0 object-cover"
          fit="cover"
          format="webp"
          :src="getPreviewUrl(image.pathname)"
          alt="profile pic"
        />
      </div>
      <span
        v-if="subtitle1 && subtitle1 !== 'null'"
        class="text-sm mt-2 font-bold"
        >{{ subtitle1 }}</span
      >
      <span
        v-if="subtitle2 && subtitle2 !== 'null'"
        class="text-xs mt-4 font-medium"
        >{{ subtitle2 }}</span
      >
      <span
        v-if="content1 && content1 !== 'null'"
        class="text-sm mt-3 font-semibold text-[#2A97FF]"
        >{{ content1 }}</span
      >

      <span
        v-if="content2 && content2 !== 'null'"
        class="text-xs mt-3 font-normal"
        >{{ content2 }}</span
      >

      <HomeTemplateButton v-if="linkButton" :button="linkButton" class="mt-6" />

      <HomeTemplateButton
        v-if="downloadButton"
        :button="downloadButton"
        class="mt-6"
      />
    </div>
  </section>
</template>
