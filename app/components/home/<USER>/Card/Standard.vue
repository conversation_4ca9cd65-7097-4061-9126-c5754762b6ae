<script setup lang="ts">
import type { StandardCard } from '~/types/home';
import { FileBox } from 'lucide-vue-next';

const props = defineProps<{
  data: StandardCard;
}>();
const { title, subtitle, content, image, linkButton, files } = props.data;
</script>

<template>
  <section class="grow">
    <h2
      class="text-[25px] text-primary font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
    >
      {{ title }}
    </h2>
    <NuxtImg
      loading="lazy"
      class="w-full h-auto max-h-[370px] object-cover mt-3"
      fit="cover"
      format="webp"
      :src="getPreviewUrl(image.pathname)"
      :alt="image.title"
    />
    <h4 class="text-lg font-semibold mt-6 text-primary">
      {{ subtitle }}
    </h4>
    <div class="text-sm mt-2 !leading-8" v-html="content" />
    <div class="mt-6 flex gap-x-4">
      <HomeTemplateButton
        v-if="linkButton"
        :button="{
          title: linkButton.title,
          style: linkButton.style,
          newTab: linkButton.newTab,
          icon: linkButton.icon,
          link: linkButton?.externalLink ?? linkButton?.internalLink ?? '',
        }"
        class="mt-4 xl:mt-7"
      />
    </div>
    <!-- Updated Attachments Section -->
    <div
      v-if="files && files.length > 0"
      class="mt-12 border-t-4 border-primary pt-8 mb-8"
    >
      <h4
        class="text-lg font-medium leading-normal mb-6 flex items-center gap-2 text-primary"
      >
        <FileBox class="w-5 h-5 text-primary-600" />
        Attachments
      </h4>
      <HomeAttachmentsList :files-list="files" />
    </div>
  </section>
</template>
