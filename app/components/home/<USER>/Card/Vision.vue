<script setup lang="ts">
import { QuoteIcon } from 'lucide-vue-next';
import type { VisionCard } from '~/types/home';

const props = defineProps<{
  data: VisionCard;
}>();
const { title, description, image } = props.data;
</script>

<template>
  <div
    class="relative w-full lg:h-[290px] rounded-[6px] bg-white mt-7 shadow-sm"
  >
    <QuoteIcon
      :font-controlled="false"
      class="absolute z-[0] top-0 -left-1 w-[160px] h-[130px] text-[#EFF6FF]"
    />

    <div
      class="relative z-[5] px-4 lg:px-[60px] pb-10 pt-10 md:pt-0 h-full flex flex-col-reverse lg:flex-row gap-x-10 items-end"
    >
      <div>
        <h4 class="text-lg font-semibold text-primary">
          {{ title }}
        </h4>
        <p class="text-base !leading-8 mt-2">
          {{ description }}
        </p>
      </div>
      <NuxtImg
        loading="lazy"
        format="webp"
        :src="getPreviewUrl(image.pathname)"
        class="self-end w-[150px] h-[180px] object-contain"
      />
    </div>
  </div>
</template>
