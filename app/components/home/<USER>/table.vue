<script setup lang="ts">
import type { Composition } from '~/types/home';

defineProps<{
  compositionData: Composition[];
}>();
</script>

<template>
  <Table
    :composition-data="compositionData"
    class="overflow-hidden rounded-[5px] border border-gray-200"
  >
    <TableBody class="bg-white rounded-[5px]">
      <TableRow
        v-for="invoice in compositionData"
        :key="invoice.title"
        class="grid grid-cols-3"
      >
        <TableCell
          class="bg-[#6390C3] text-white text-sm text-center col-span-1 py-4"
        >
          {{ invoice.title }}
        </TableCell>
        <TableCell
          class="text-center border-r border-[#E7EAED] text-sm col-span-1"
        >
          <div
            v-for="member in invoice.members"
            :key="member.name"
            class="py-2"
          >
            {{ member.name }}
          </div>
        </TableCell>
        <TableCell class="text-center text-sm col-span-1">
          <div
            v-for="member in invoice.members"
            :key="member.name"
            class="py-2"
          >
            {{ member.designation }}
          </div>
        </TableCell>
      </TableRow>
    </TableBody>
  </Table>
</template>
