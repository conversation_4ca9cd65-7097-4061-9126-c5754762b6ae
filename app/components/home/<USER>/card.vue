<script setup lang="ts">
defineProps<{
  data: {
    id: number;
    timestamp: number | null;
    title: string;
    description: string;
  };
}>();
</script>

<template>
  <div class="w-full h-auto mt-10 first:mt-0">
    <div class="flex items-center gap-x-4 rounded-[5px]">
      <div
        class="px-4 py-2 flex flex-col gap-y-1 justify-center items-center bg-primary-gradient text-white rounded-[5px]"
      >
        <span class="text-sm font-light leading-none">{{
          formatDate(data?.timestamp!).month
        }}</span>
        <span class="text-xl font-bold leading-none">{{
          formatDate(data?.timestamp!).day
        }}</span>
        <span class="text-sm font-light leading-none">{{
          formatDate(data?.timestamp!).year
        }}</span>
      </div>
      <div class="flex flex-col max-w-[70%]">
        <h4 class="text-lg font-medium leading-normal">
          {{ data.title }}
        </h4>
        <p
          class="mt-1 text-sm font-medium !leading-7 line-clamp-2 truncate hidden lg:inline-block"
        >
          {{ data.description }}
        </p>
      </div>
      <NuxtLink
        :href="`/notice-board/${data.id}`"
        class="hidden lg:flex-center shrink-0 text-base font-semibold text-primary hover:bg-primary/20 px-5 h-[40px] border border-primary rounded-[6px] ml-auto"
        variant="ghost"
      >
        Read More
      </NuxtLink>
    </div>
    <p class="mt-3 text-sm font-medium !leading-7 lg:hidden line-clamp-2">
      {{ data.description }}
    </p>
    <Button
      class="w-full flex-center shrink-0 text-base font-semibold text-primary hover:bg-primary/20 px-5 h-[40px] md:w-auto border border-primary rounded-[6px] block mt-2 mx-auto lg:hidden"
      variant="ghost"
    >
      Read More
    </Button>
  </div>
</template>
