<script setup lang="ts">
import type { AnnouncementListData } from '~/types/home';

defineProps<{
  eventData: AnnouncementListData;
}>();

const { width } = useWindowSize();

const formatEventDate = (date: number | null) => {
  return formatDate(date || Date.now());
};
</script>

<template>
  <div class="w-full h-auto">
    <NuxtImg
      loading="lazy"
      class="w-full h-[180px] object-cover md:h-[260px] rounded-t-[6px]"
      fit="cover"
      height="260"
      format="webp"
      :src="getPreviewUrl(eventData.primaryImage.pathname)"
      :alt="eventData.title"
    />
    <div class="flex items-center gap-x-4 rounded-[5px] mt-2">
      <div
        class="px-4 py-2 flex flex-col gap-y-1 justify-center items-center bg-primary-gradient text-white rounded-[5px]"
      >
        <span class="text-sm font-light leading-none">{{
          formatEventDate(eventData.date).month
        }}</span>
        <span class="text-xl font-bold leading-none">{{
          formatEventDate(eventData.date).day
        }}</span>
        <span class="text-sm font-light leading-none">{{
          formatEventDate(eventData.date).year
        }}</span>
      </div>
      <h4 class="text-lg font-medium leading-normal">
        {{ eventData.title }}
      </h4>
    </div>
    <div
      class="mt-3 text-sm font-medium !leading-8 line-clamp-3"
      v-html="eventData.description"
    />
    <NuxtLink :to="`/events/${eventData.id}`">
      <Button
        v-if="width >= 768"
        class="border border-primary rounded-[6px] block mt-4 mx-auto"
        variant="ghost"
      >
        Read More
      </Button>
    </NuxtLink>
  </div>
</template>
