<script setup lang="ts">
import { Image } from 'lucide-vue-next';
import { cn } from '~/lib/utils';
import ZoomIcon from '@/assets/icons/zoom.svg';
import type { FileData } from '~/types/home';

interface Props {
  data: {
    groupId?: number;
    photo: FileData;
    isSeeMore?: boolean;
  };
  imgClass?: string;
  openModal?: (image: FileData) => void;
}

defineProps<Props>();
</script>

<template>
  <div
    class="relative w-full h-auto group rounded-[10px] overflow-hidden group"
  >
    <NuxtImg
      loading="lazy"
      :class="
        cn(
          'w-full lg:h-[190px] h-[100px] md:h-[150px] object-cover transition-all duration-1000 group-hover:scale-110',
          imgClass
        )
      "
      format="webp"
      :src="getPreviewUrl(data.photo.pathname)"
      alt="Gallery image"
    />
    <div
      :class="
        cn(
          'flex-center flex-col absolute bg-[#00428C]/70 backdrop-blur-sm bottom-0 left-0 right-0 w-full h-0 group-hover:h-full group-hover:p-4 transition-all duration-300 overflow-hidden rounded-[5px] cursor-pointer ',
          data.isSeeMore && 'h-full hover:bg-[#00428C]/90'
        )
      "
    >
      <ZoomIcon
        v-if="!data.isSeeMore && openModal"
        :font-controlled="false"
        class="w-[40px] h-auto lg:w-[50px] shrink-0 text-white cursor-pointer"
        @click="openModal(data.photo)"
      />
      <NuxtLink v-else-if="data.isSeeMore" :to="`/gallery/${data?.groupId}`">
        <div class="flex gap-x-1.5 items-center">
          <Image class="text-white w-5 lg:w-8" />
          <span class="text-white text-xs lg:text-base">See More Photos</span>
        </div>
      </NuxtLink>
    </div>
  </div>
</template>
