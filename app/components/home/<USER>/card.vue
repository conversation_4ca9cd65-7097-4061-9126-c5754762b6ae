<script setup lang="ts">
import { ArrowRight } from 'lucide-vue-next';
import type { Course } from '~/types/home';
defineProps<{
  data: Partial<Course>;
}>();
</script>

<template>
  <NuxtLink
    :to="`/departments/${data.departmentSlug}/courses?courseId=${data.id}`"
    class="group"
  >
    <div class="relative w-full h-auto group rounded-[5px] overflow-hidden">
      <NuxtImg
        loading="lazy"
        class="w-full h-[250px] object-cover"
        fit="cover"
        height="260"
        format="webp"
        :src="getPreviewUrl(data.image?.pathname!)"
        alt="course img"
      />
      <div class="flex-center gap-x-4 rounded-[5px] mt-2">
        <span class="text-base font-medium leading-normal">
          {{ data.name }}
        </span>
      </div>
      <div
        class="flex-center flex-col absolute top-3 right-3 z-20 w-[60px] h-[60px] rounded-full bg-white text-primary shrink-0"
      >
        <span class="text-3xl font-semibold leading-none">{{
          Math.ceil(data?.durationInMonths! / 12)
        }}</span>
        <span class="text-xs font-light">Years</span>
      </div>

      <div
        class="absolute group-hover:p-3 w-full h-0 group-hover:h-full transition-all duration-300 overflow-hidden bottom-0 left-0 right-0 z-10 text-white bg-black/50 backdrop-blur-sm"
      >
        <div class="opacity-0 group-hover:opacity-100 transition-all">
          <div class="flex items-center justify-between">
            <div class="flex flex-col">
              <span class="text-base font-semibold leading-tight">
                {{ data.name }}
              </span>
              <span class="text-xs mt-2"
                >{{ data.semesterCount }} Semester</span
              >
            </div>
            <div class="w-[60px] h-[60px] rounded-full shrink-0" />
          </div>
          <p class="text-sm font-medium mt-4 line-clamp-[7]">
            {{ data.content?.description }}
          </p>
          <NuxtLink to="/">
            <ArrowRight class="w-8 mx-auto mt-2 h-auto text-white" />
          </NuxtLink>
        </div>
      </div>
    </div>
  </NuxtLink>
</template>
