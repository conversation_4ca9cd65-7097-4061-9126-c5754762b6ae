<!-- components/TemplateCards.vue -->
<script setup lang="ts">
import type { CardWithRelations } from '~~/server/database/tables';
import { ExternalLink } from 'lucide-vue-next';

defineProps<{
  configuration: CardWithRelations;
}>();

function handleClick(card: CardWithRelations) {
  if (card.onClick === 'openModal') {
    // Handle modal opening
  } else if (card.cta) {
    // Handle navigation
    if (card.cta.routeType === 'internal') {
      navigateTo(card.cta.route!);
    } else {
      window.open(card.cta.externalLink!, card.cta.newTab ? '_blank' : '_self');
    }
  }
}
</script>

<template>
  <Card
    class="hover:shadow-lg transition-shadow duration-200 cursor-pointer"
    @click="handleClick(configuration)"
  >
    <CardHeader>
      <CardTitle>{{ configuration.title }}</CardTitle>
      <CardDescription>
        <div class="space-y-1">
          <p>{{ configuration.subtitle1 }}</p>
          <p>{{ configuration.subtitle2 }}</p>
        </div>
      </CardDescription>
    </CardHeader>
    <CardContent>
      <p class="text-muted-foreground">
        {{ configuration.description }}
      </p>
    </CardContent>
    <CardFooter v-if="configuration.cta">
      <Button variant="ghost" class="w-full justify-between group">
        {{ configuration.cta.title }}
        <ExternalLink
          v-if="configuration.cta.routeType === 'external'"
          class="h-4 w-4 transition-transform group-hover:translate-x-1"
        />
      </Button>
    </CardFooter>
  </Card>
</template>
