<script setup lang="ts">
import type { RelatedLinks } from '../about-us/related-links.vue';

const props = defineProps<{
  title: string;
  subHeader: {
    breadcrumbs: Array<{ label: string; link?: string }>;
  };
}>();

const links: RelatedLinks = [
  { label: 'About', link: '/about' },
  { label: 'Objectives', link: '/objectives' },
  { label: 'Composition', link: '/composition' },
  { label: 'Institution Distinctiveness', link: '/institution' },

  { label: 'SSR', link: '/self_study_report' },
  { label: 'AQAR', link: '/aqar' },
  { label: 'Quality Audits', link: '/quality_audits' },
  { label: 'MOU', link: '/mou' },
  { label: 'SAAC', link: '/saac' },
  { label: 'Policy Documents', link: '/policy_documents' },
  { label: 'SSS', link: '/sss' },

  { label: 'Minutes', link: '/minutes' },
  { label: 'Reports', link: '/reports' },
  { label: 'Best Practices', link: '/best_practice' },
  { label: 'Feedback', link: '/feedback' },
  { label: 'NIRF', link: '/nirf' },
];
</script>

<template>
  <HomeSubHeader title="IQAC" :breadcrumbs="props.subHeader.breadcrumbs" />
  <div class="bg-[#F4F4F4]">
    <div class="container py-10 flex gap-x-5">
      <section class="grow">
        <h2
          class="text-[25px] text-primary font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
        >
          {{ title }}
        </h2>
        <div class="flex items-start gap-x-4 mt-6">
          <div class="grow">
            <slot />
          </div>
          <div class="hidden lg:block">
            <HomeAboutUsRelatedLinks :links="links" :link-prefix="`/iqac`" />
          </div>
        </div>
      </section>
    </div>
  </div>
</template>
