<script setup lang="ts">
import { Download, ChevronUp } from 'lucide-vue-next';
definePageMeta({
  layout: 'landing',
});

defineProps<{
  data: {
    imgSrc: string;
    courseName: string;
  };
  keyLelo: number;
}>();
const isEligibleOpen = ref(false);

const handleEligibleOpen = () => {
  isEligibleOpen.value = !isEligibleOpen.value;
};
</script>

<template>
  <div
    v-for="(_, index) of Array.from({ length: 10 })"
    :key="index"
    class="w-full bg-white px-5 py-6 rounded-[8px]"
  >
    <div class="flex flex-col lg:flex-row lg:gap-x-5">
      <div
        class="relative w-full lg:w-[230px] h-[150px] lg:h-[170px] rounded-[6px] overflow-hidden"
      >
        <NuxtImg
          loading="lazy"
          class="w-full h-full"
          fit="cover"
          format="webp"
          :src="data.imgSrc"
          alt="course img"
        />
      </div>
      <div class="flex flex-col">
        <h4 class="text-lg lg:text-xl font-medium mt-3 lg:mt-0">
          {{ data.courseName }}{{ keyLelo }}
        </h4>
        <div class="text-xs lg:text-base font-medium mt-2">
          <span class="text-primary mr-1">Specialization:</span>
          <span>Human Resource Administration</span>
        </div>
        <div class="text-xs lg:text-base font-medium mt-3">
          <span class="text-primary mr-1">Duration:</span>
          <span>3 Years | 6 Semester</span>
        </div>
        <div class="flex gap-x-4 mt-3 lg:max-w-[250px] lg:mt-auto">
          <button
            class="w-full h-[35px] flex-center gap-x-2 text-xs font-medium rounded-[6px] border border-primary text-primary"
          >
            Syllabus
            <Download class="w-4 h-auto text-primary" />
          </button>
          <button
            class="w-full h-[35px] flex-center gap-x-2 text-xs font-medium rounded-[6px] bg-primary border border-primary text-white"
          >
            Apply Now
          </button>
        </div>
      </div>
    </div>
    <div
      class="flex items-center justify-between select-none text-primary mt-7 cursor-pointer hover:bg-slate-100 px-2 rounded-sm"
      @click="handleEligibleOpen"
    >
      <span class="text-sm lg:text-base font-medium"
        >Eligibility for Admission</span
      >
      <ChevronUp class="w-5 h-auto text-primary" />
    </div>
    <p
      v-if="isEligibleOpen"
      class="text-sm select-none lg:text-base font-medium !leading-8 mt-2"
    >
      Any candidate who has passed the Higher Secondary Examination or any other
      examination recognized as equivalent thereto, with not less than 50 per
      cent marks is eligible for admission to the BBA degree course. A
      concession of 5 per cent will be given to OBC/OEC candidates. The SC/ST
      candidates need to get only a pass.
    </p>
  </div>
</template>
