<!-- components/TemplateAccordion.vue -->
<script setup lang="ts">
import type { Accordion as AccordionType } from '~~/server/database/tables';

defineProps<{
  configuration: AccordionType;
}>();
</script>

<template>
  <Accordion type="single" collapsible>
    <AccordionItem value="item-1">
      <AccordionTrigger>{{ configuration.title }}</AccordionTrigger>
      <AccordionContent>
        <div class="space-y-4">
          <p v-if="configuration.description" class="text-muted-foreground">
            {{ configuration.description }}
          </p>
          <div v-if="configuration.content" class="prose max-w-none">
            {{ configuration.content }}
          </div>
        </div>
      </AccordionContent>
    </AccordionItem>
  </Accordion>
</template>
