<script lang="ts" setup>
import type { MenuItem } from '~/types/home';
import {
  ArrowUpRight,
  BookOpen,
  GraduationCap,
  Presentation,
  Award,
  Building,
  Users,
  LandPlot,
  Image,
  Newspaper,
  Contact,
  Calendar,
  Info,
  Home,
  Bookmark,
  BookCopy,
  Trophy,
  Gift,
  Library,
  Heart,
  Landmark,
  CircleHelp,
  Map,
  FileText,
  School,
  PenTool,
  Shapes,
  Album,
  LayoutList,
  Rocket,
  MessagesSquare,
  Clock,
  Building2,
  GraduationCap as GraduationCapIcon,
  Link,
  Briefcase,
  Microscope,
  Target,
  ClipboardCheck,
  Star,
  BarChart,
  MessageSquare,
} from 'lucide-vue-next';

const props = defineProps<{
  data: MenuItem['children'];
}>();

// Function to get the appropriate icon for a menu item
const getIconForItem = (item: MenuItem) => {
  if (!item.icon) return null;

  const iconMap: Record<string, any> = {
    ArrowUpRight,
    BookOpen,
    GraduationCap,
    Presentation,
    Award,
    Building,
    Users,
    LandPlot,
    Image,
    Newspaper,
    Contact,
    Calendar,
    Info,
    Home,
    Bookmark,
    BookCopy,
    Trophy,
    Gift,
    Library,
    Heart,
    Landmark,
    CircleHelp,
    Map,
    FileText,
    School,
    PenTool,
    Shapes,
    Album,
    LayoutList,
    Rocket,
    MessagesSquare,
    Clock,
    Building2,
    GraduationCapIcon,
    Link,
    Briefcase,
    Microscope,
    Target,
    ClipboardCheck,
    Star,
    BarChart,
    MessageSquare,
  };

  return iconMap[item.icon] || null;
};
</script>

<template>
  <ul class="menu-list">
    <li
      v-for="(item, index) in props.data"
      :key="index"
      class="menu-item-wrapper"
    >
      <!-- Regular menu item with link -->
      <NuxtLink
        v-if="!item.children || item.children.length === 0"
        :to="item.link || '#'"
        class="menu-item"
      >
        <component
          :is="getIconForItem(item)"
          v-if="getIconForItem(item)"
          class="menu-icon"
        />
        <span class="menu-label">{{ item.label }}</span>
        <ArrowUpRight v-if="item.link" class="menu-arrow" />
      </NuxtLink>

      <!-- Menu item with children -->
      <div v-else class="menu-item-with-children">
        <div class="menu-item">
          <component
            :is="getIconForItem(item)"
            v-if="getIconForItem(item)"
            class="menu-icon"
          />
          <span class="menu-label">{{ item.label }}</span>
          <div class="menu-dots">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </div>
        </div>

        <!-- First level submenu -->
        <div class="submenu">
          <div class="submenu-header">
            <component
              :is="getIconForItem(item)"
              v-if="getIconForItem(item)"
              class="submenu-header-icon"
            />
            <h3 class="submenu-header-title">{{ item.label }}</h3>
          </div>

          <ul class="submenu-list">
            <li
              v-for="(subItem, subIndex) in item.children"
              :key="subIndex"
              class="submenu-item-wrapper"
            >
              <!-- Regular submenu item with link -->
              <NuxtLink
                v-if="!subItem.children || subItem.children.length === 0"
                :to="subItem.link || '#'"
                class="submenu-item"
              >
                <component
                  :is="getIconForItem(subItem)"
                  v-if="getIconForItem(subItem)"
                  class="submenu-icon"
                />
                <span class="submenu-label">{{ subItem.label }}</span>
                <ArrowUpRight v-if="subItem.link" class="submenu-arrow" />
              </NuxtLink>

              <!-- Submenu item with children -->
              <div v-else class="submenu-item-with-children">
                <div class="submenu-item">
                  <component
                    :is="getIconForItem(subItem)"
                    v-if="getIconForItem(subItem)"
                    class="submenu-icon"
                  />
                  <span class="submenu-label">{{ subItem.label }}</span>
                  <div class="submenu-dots">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                  </div>
                </div>

                <!-- Second level submenu (nested) -->
                <div class="nested-submenu">
                  <div class="submenu-header">
                    <component
                      :is="getIconForItem(subItem)"
                      v-if="getIconForItem(subItem)"
                      class="submenu-header-icon"
                    />
                    <h3 class="submenu-header-title">{{ subItem.label }}</h3>
                  </div>

                  <ul class="submenu-list">
                    <li
                      v-for="(nestedItem, nestedIndex) in subItem.children"
                      :key="nestedIndex"
                    >
                      <NuxtLink
                        :to="nestedItem.link || '#'"
                        class="submenu-item"
                      >
                        <component
                          :is="getIconForItem(nestedItem)"
                          v-if="getIconForItem(nestedItem)"
                          class="submenu-icon"
                        />
                        <span class="submenu-label">{{
                          nestedItem.label
                        }}</span>
                        <ArrowUpRight
                          v-if="nestedItem.link"
                          class="submenu-arrow"
                        />
                      </NuxtLink>
                    </li>
                  </ul>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </li>
  </ul>
</template>

<style scoped>
/* Base menu styling */
.menu-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;
  width: 100%;
}

/* Menu item styling */
.menu-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  background-color: white;
  border: 1px solid rgba(19, 89, 168, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px -2px rgba(19, 89, 168, 0.05);
}

.menu-item:hover {
  background-color: rgba(19, 89, 168, 0.03);
  border-color: rgba(19, 89, 168, 0.25);
  transform: translateY(-2px);
  box-shadow:
    0 6px 16px -4px rgba(19, 89, 168, 0.15),
    0 2px 8px -2px rgba(19, 89, 168, 0.1),
    0 0 0 1px rgba(19, 89, 168, 0.05);
}

.menu-icon {
  width: 1.25rem;
  height: 1.25rem;
  opacity: 0.7;
  color: rgba(19, 89, 168, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item:hover .menu-icon {
  color: #1359a8;
  transform: scale(1.15);
  opacity: 1;
}

.menu-label {
  font-weight: 500;
  margin-left: 0.75rem;
  flex-grow: 1;
  color: rgba(0, 0, 0, 0.75);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item:hover .menu-label {
  transform: translateX(2px);
  color: #1359a8;
}

.menu-arrow {
  width: 1rem;
  height: 1rem;
  opacity: 0;
  color: #1359a8;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item:hover .menu-arrow {
  opacity: 1;
  transform: translateX(2px) scale(1.1);
}

/* Menu dots indicator */
.menu-dots {
  display: flex;
  gap: 3px;
  margin-left: 4px;
}

.dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: rgba(19, 89, 168, 0.6);
  opacity: 0.5;
  transition: all 0.3s ease;
}

.menu-item:hover .dot {
  opacity: 1;
  background-color: #1359a8;
  box-shadow: 0 0 4px rgba(19, 89, 168, 0.4);
  animation: pulse 1.5s infinite;
}

.menu-item:hover .dot:nth-child(1) {
  animation-delay: 0s;
}

.menu-item:hover .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.menu-item:hover .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
}

/* Items with children */
.menu-item-with-children {
  position: relative;
}

.menu-item-with-children .submenu {
  display: none;
  position: absolute;
  top: 0;
  left: 101%;
  width: 16rem;
  background-color: white;
  /* border: 2px solid #1359a8; */
  border-radius: 0.75rem;
  box-shadow:
    0 10px 40px -10px rgba(0, 0, 0, 0.25),
    0 4px 16px -4px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(19, 89, 168, 0.05),
    0 0 15px rgba(19, 89, 168, 0.2);
  z-index: 100;
  overflow: hidden;
  transform: translateX(8px);
  pointer-events: none;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease,
    transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item-with-children:hover .submenu {
  display: block;
  pointer-events: auto;
  animation: fadeIn 0.25s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Create hover bridge between menu item and submenu */
.menu-item-with-children::after {
  content: '';
  position: absolute;
  top: 0;
  left: 100%;
  width: 30px;
  height: 100%;
  background: transparent;
  z-index: 90;
}

/* Submenu styling */
.submenu-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: linear-gradient(
    to right,
    rgba(19, 89, 168, 0.15),
    rgba(38, 123, 202, 0.05)
  );
  border-bottom: 1px solid rgba(19, 89, 168, 0.15);
  position: relative;
  overflow: hidden;
}

.submenu-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(
    to right,
    rgba(19, 89, 168, 0.3),
    rgba(19, 89, 168, 0.1),
    rgba(19, 89, 168, 0.3)
  );
}

.submenu-header-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #1359a8;
  animation: pulse-subtle 2s infinite;
}

@keyframes pulse-subtle {
  0%,
  100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

.submenu-header-title {
  font-size: 0.875rem;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.8);
  letter-spacing: 0.01em;
}

.submenu-list {
  padding: 0.5rem;
}

.submenu-item-wrapper {
  position: relative;
}

.submenu-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid transparent;
  margin-bottom: 2px;
}

.submenu-item:hover {
  background-color: rgba(19, 89, 168, 0.05);
  border-color: rgba(19, 89, 168, 0.1);
  transform: translateX(2px);
  box-shadow: 0 2px 8px -2px rgba(19, 89, 168, 0.1);
}

.submenu-icon {
  width: 1rem;
  height: 1rem;
  opacity: 0.7;
  color: rgba(19, 89, 168, 0.7);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.submenu-item:hover .submenu-icon {
  color: #1359a8;
  transform: scale(1.15);
  opacity: 1;
}

.submenu-label {
  font-weight: 500;
  font-size: 0.875rem;
  margin-left: 0.5rem;
  flex-grow: 1;
  color: rgba(0, 0, 0, 0.75);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.submenu-item:hover .submenu-label {
  transform: translateX(2px);
  color: #1359a8;
}

.submenu-arrow {
  width: 0.875rem;
  height: 0.875rem;
  opacity: 0;
  color: #1359a8;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.submenu-item:hover .submenu-arrow {
  opacity: 1;
  transform: translateX(2px) scale(1.1);
}

/* Submenu dots indicator */
.submenu-dots {
  display: flex;
  gap: 3px;
  margin-left: 4px;
}

.submenu-dots .dot {
  width: 3px;
  height: 3px;
  background-color: rgba(19, 89, 168, 0.6);
}

/* Nested submenu (second level) */
.submenu-item-with-children {
  position: relative;
}

.submenu-item-with-children .nested-submenu {
  display: none;
  position: absolute;
  top: 0;
  left: 100%;
  width: 16rem;
  background-color: white;
  border: 2px solid #1359a8;
  border-radius: 0.75rem;
  box-shadow:
    0 10px 40px -10px rgba(0, 0, 0, 0.25),
    0 4px 16px -4px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(19, 89, 168, 0.05),
    0 0 15px rgba(19, 89, 168, 0.2);
  z-index: 110;
  overflow: hidden;
  transform: translateX(8px);
  pointer-events: none;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease,
    transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.submenu-item-with-children:hover .nested-submenu {
  display: block;
  pointer-events: auto;
  animation: fadeIn 0.25s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Create hover bridges for nested submenus */
.submenu-item-with-children::after {
  content: '';
  position: absolute;
  top: 0;
  left: 100%;
  width: 30px;
  height: 100%;
  background: transparent;
  z-index: 90;
}

.submenu::after,
.nested-submenu::before {
  content: '';
  position: absolute;
  top: 0;
  width: 30px;
  height: 100%;
  background: transparent;
  z-index: 90;
}

.submenu::after {
  right: -30px;
}

.nested-submenu::before {
  left: -30px;
}

/* Add hover bridges between submenu items */
.submenu-item-wrapper:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 10px;
  background: transparent;
  z-index: 90;
}

/* Animation */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateX(16px);
    box-shadow: 0 0 0 rgba(19, 89, 168, 0);
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
    transform: translateX(8px);
    box-shadow:
      0 10px 40px -10px rgba(0, 0, 0, 0.25),
      0 4px 16px -4px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(19, 89, 168, 0.05),
      0 0 15px rgba(19, 89, 168, 0.2);
  }
}

/* Responsive grid layout */
@media (min-width: 640px) {
  .menu-list.grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
