<script setup lang="ts">
import { MapPin, Mail, Phone } from 'lucide-vue-next';
const links = [
  { name: 'Home', link: '/' },
  { name: 'About Us', link: '/about-us/don-bosco-friend-of-youth' },
  // { name: 'Our Management', link: '/about-us/our-management' },
  { name: 'IQAC', link: '/iqac/about' },
  { name: 'Contact Us', link: '/contact-us' },
  // { name: 'NIRF', link: '/' },
  // { name: 'Internal Exam', link: '/' },
  // { name: 'University Exam', link: '/' },
  // { name: 'Grievances', link: 'https://dbcollegemannuthy.edu.in/welfare/gr' },
  // { name: 'PTA', link: '/' },
  { name: 'Question Bank', link: '/question-bank' },
  // { name: 'College Student Union', link: '/' },
  // { name: 'Clubs & Committees', link: '/' },
  // { name: 'Library', link: '/' },
  { name: 'Gallery', link: '/gallery' },
] as const;
</script>

<template>
  <footer
    id="footer-section"
    class="w-full text-white text-sm relative"
    style="
      background:
        linear-gradient(
          180deg,
          #00428c 0%,
          #00428c 13%,
          rgba(0, 66, 140, 0) 100%
        ),
        #085195;
    "
  >
    <!-- Main footer content -->
    <div
      class="bg-gradient-to-b from-[#00428c] to-[#085195] border-t border-white/10 relative"
    >
      <div class="container mx-auto w-full">
        <!-- Logo and description section -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 py-16">
          <!-- Quick links section -->
          <div
            class="col-span-1 lg:col-span-1 flex flex-col items-start justify-start"
          >
            <h4 class="text-base font-bold mb-5 tracking-wider">Quick Links</h4>
            <ul class="space-y-3">
              <li v-for="item of links.slice(0, 4)" :key="item.name">
                <NuxtLink
                  :to="item.link"
                  class="text-white/80 hover:text-white transition-colors duration-200 inline-block"
                >
                  {{ item.name }}
                </NuxtLink>
              </li>
            </ul>
          </div>

          <!-- More links section -->
          <div
            class="col-span-1 lg:col-span-1 flex flex-col items-start justify-start"
          >
            <h4 class="text-base font-bold mb-5 tracking-wider">Resources</h4>
            <ul class="space-y-3">
              <li v-for="item of links.slice(4)" :key="item.name">
                <NuxtLink
                  :to="item.link"
                  class="text-white/80 hover:text-white transition-colors duration-200 inline-block"
                >
                  {{ item.name }}
                </NuxtLink>
              </li>
            </ul>
          </div>

          <!-- Contact information -->
          <div
            class="col-span-1 lg:col-span-1 flex flex-col items-start justify-start text-left"
          >
            <h4 class="text-base font-bold mb-5 tracking-wider">Contact Us</h4>
            <div class="space-y-4 flex flex-col items-center md:items-start">
              <div class="flex items-start justify-start gap-x-2 w-full">
                <MapPin
                  class="h-5 w-5 text-white/70 mt-0.5 mr-3 flex-shrink-0"
                />
                <span class="text-white/80"
                  >Mannuthy, Thrissur, Kerala, India 680 651</span
                >
              </div>
              <div class="flex items-start justify-start gap-x-2 w-full">
                <Mail class="h-5 w-5 text-white/70 mt-0.5 mr-3 flex-shrink-0" />
                <a
                  href="mailto:<EMAIL>"
                  class="text-white/80 hover:text-white transition-colors duration-200"
                >
                  <EMAIL>
                </a>
              </div>
              <div class="flex items-start justify-start gap-x-2 w-full">
                <Phone
                  class="h-5 w-5 text-white/70 mt-0.5 mr-3 flex-shrink-0"
                />
                <div class="flex flex-col space-y-1">
                  <a
                    href="tel:0487-2371337"
                    class="text-white/80 hover:text-white transition-colors duration-200"
                  >
                    0487 - 2 371 337
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Copyright section -->
      <div class="bg-border-blue py-5">
        <div
          class="container mx-auto w-full flex flex-col md:flex-row justify-between items-center"
        >
          <p class="text-white/80 text-sm text-center md:text-left">
            © 2025 Don Bosco College Mannuthy. All Rights Reserved.
          </p>
          <a
            href="https://revontulet.com"
            target="_blank"
            class="text-white/70 text-sm mt-3 md:mt-0 text-center md:text-right"
          >
            Powered By
            <span
              class="text-white hover:no-underline transition-all duration-200"
              >Revontulet.Solutions</span
            >
          </a>
        </div>
      </div>
    </div>
  </footer>
</template>

<style scoped>
/* Subtle hover effect for links */
a {
  position: relative;
}

a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 1px;
  bottom: -2px;
  left: 0;
  background-color: white;
  transition: width 0.3s ease;
}

a:hover::after {
  width: 100%;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  h3,
  h4 {
    text-align: center;
  }

  ul {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .flex.items-start {
    justify-content: center;
  }

  .flex.flex-col.space-y-1 {
    align-items: center;
  }
}

/* Smooth transitions */
.social-icon {
  transition:
    transform 0.3s ease,
    background-color 0.3s ease;
}

.social-icon:hover {
  transform: translateY(-3px);
}
</style>
