<script setup lang="ts">
import { FileBox } from 'lucide-vue-next';
import type { OverviewResponse } from '~~/app/types/home/<USER>';

defineProps<{
  overviewData: OverviewResponse;
}>();
</script>

<template>
  <section>
    <NuxtImg
      v-if="overviewData?.image?.pathname"
      loading="lazy"
      class="w-full h-[180px] md:h-[260px] lg:h-[300px] object-cover"
      fit="cover"
      width="100%"
      height="260"
      format="webp"
      :src="getPreviewUrl(overviewData?.image?.pathname || '')"
      alt="logo"
    />
    <div class="flex items-center gap-x-4 rounded-[5px] mt-7">
      <h4 class="text-lg font-medium leading-normal">
        {{ overviewData.title }}
      </h4>
    </div>
    <div
      class="mt-3 text-sm font-medium !leading-8"
      v-html="overviewData.content"
    />
    <HomeTemplateButton
      v-if="overviewData.linkButton"
      :button="overviewData.linkButton"
      class="mt-6"
    />

    <!-- Updated Attachments Section -->
    <div
      v-if="overviewData.files && overviewData.files.length > 0"
      class="mt-12 border-t-4 border-primary pt-8 mb-8"
    >
      <h4
        class="text-lg font-medium leading-normal mb-6 flex items-center gap-2"
      >
        <FileBox class="w-5 h-5 text-primary-600" />
        Attachments
      </h4>
      <HomeAttachmentsList :files-list="overviewData.files" />
    </div>
  </section>
</template>
