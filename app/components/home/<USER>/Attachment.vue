<script setup lang="ts">
import type { Attachment } from '~/types/home';

defineProps<{
  attachmentData: Attachment;
  attachmentKey: string;
  attachmentValue: string;
}>();
</script>

<template>
  <AccordionItem
    :key="attachmentKey"
    :value="attachmentValue"
    class="border-none mb-4"
  >
    <AccordionTrigger
      class="bg-[#E4E4E4] px-5 rounded-[5px] w-full flex items-center justify-between"
      chevron-class="text-primary w-5 h-auto"
    >
      <span>{{ attachmentData.title }}</span>
      <template #icon>
        <HomeTemplateButton
          v-if="attachmentData?.button"
          :button="{
            ...attachmentData.button,
            newTab: true,
          }"
        />
      </template>
    </AccordionTrigger>
    <AccordionContent class="mt-4 border-b">
      <HomeAttachmentsList :files-list="attachmentData.files" />
    </AccordionContent>
  </AccordionItem>
</template>
