<script setup lang="ts">
import NoticesMarquee from './notices-marquee.vue';
import { ClipboardList, ExternalLink } from 'lucide-vue-next';
import type {
  NotificationData,
  UpdateData,
  PrincipalMessage,
} from '~/types/home';

const props = defineProps<{
  notifications: NotificationData[];
  updates: UpdateData[];
  principalMessage: PrincipalMessage;
}>();

// Add comprehensive watchers for debugging reactivity
watch(
  () => props.updates,
  (newUpdates, oldUpdates) => {
    console.log('🔄 Updates changed:', { newUpdates, oldUpdates });
  },
  { deep: true }
);

watch(
  () => props.notifications,
  (newNotifications, oldNotifications) => {
    console.log('🔔 Notifications changed:', {
      newNotifications,
      oldNotifications,
    });
  },
  { deep: true }
);

watch(
  () => props.principalMessage,
  (newMessage, oldMessage) => {
    console.log('👨‍💼 Principal message changed:', { newMessage, oldMessage });
  },
  { deep: true }
);
</script>

<template>
  <section id="latest-notices">
    <div class="w-full flex flex-col lg:flex-row items-center bg-white">
      <div
        class="w-full lg:w-[280px] flex px-6 items-center justify-start lg:justify-end h-[60px] text-white bg-primary-gradient animate-fade-in-left"
      >
        <ClipboardList :size="20" class="mr-2 animate-pulse-subtle" />
        <span class="font-medium tracking-wide">Updates</span>
      </div>

      <div class="relative overflow-hidden w-full animate-fade-in-right">
        <div class="marquee-container !flex flex-nowrap justify-start">
          <div class="track">
            <template v-for="update in props.updates" :key="update.id">
              <div
                class="text-sm font-semibold px-4 py-4 lg:py-0 inline-flex items-center hover:text-primary transition-colors duration-300"
              >
                <p v-html="update.title" />
                <NuxtLink
                  v-if="update.link"
                  :to="update.link"
                  class="text-primary ml-2 flex items-center gap-x-1 hover:underline transition-all duration-300 hover:gap-x-2"
                  tabindex="0"
                  target="_blank"
                  aria-label="Apply for this update"
                  @keydown.enter="$event.target.click()"
                >
                  {{ update.buttonTitle || 'View' }}
                  <ExternalLink
                    class="w-4 h-4 ml-1 transition-transform duration-300 group-hover:translate-x-1"
                  />
                </NuxtLink>
                <span class="mx-4 text-primary/70">•</span>
              </div>
            </template>
          </div>
          <!-- ----------must to have a duplicate track for smooth marquee effect------- -->
          <div class="track" aria-hidden="true">
            <template v-for="update in props.updates" :key="update.id">
              <div
                class="text-sm font-semibold px-4 py-4 lg:py-0 inline-flex items-center hover:text-primary transition-colors duration-300"
              >
                <p v-html="update.title" />
                <NuxtLink
                  v-if="update.link"
                  :to="update.link"
                  class="text-primary ml-2 flex items-center gap-x-1 hover:underline transition-all duration-300 hover:gap-x-2"
                  tabindex="0"
                  target="_blank"
                  aria-label="Apply for this update"
                  @keydown.enter="$event.target.click()"
                >
                  {{ update.buttonTitle || 'View' }}
                  <ExternalLink
                    class="w-4 h-4 ml-1 transition-transform duration-300 group-hover:translate-x-1"
                  />
                </NuxtLink>
                <span class="mx-4 text-primary/70">•</span>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-[#F4F4F4]">
      <div
        class="container flex flex-col-reverse gap-y-6 py-6 lg:grid lg:grid-cols-[auto_35%] lg:gap-x-3 xl:flex xl:flex-row lg:py-10"
      >
        <div
          data-aos="fade-up"
          data-aos-duration="800"
          class="bg-white flex flex-col 2xl:flex-row 2xl:items-center rounded-[6px] px-5 py-5 lg:py-7 lg:px-10 shadow-sm hover:shadow-md transition-shadow duration-500 principal-message-card"
        >
          <div class="shrink-0 flex-center flex-col text-center">
            <div
              class="principal-image-container overflow-hidden rounded-full relative"
            >
              <NuxtImg
                loading="lazy"
                class="w-[180px] h-[180px] 2xl:w-[244px] 2xl:h-[244px] rounded-full object-cover transition-transform duration-500 hover:scale-110"
                fit="cover"
                format="webp"
                :src="
                  getPreviewUrl(props.principalMessage.primaryImage.pathname)
                "
                alt="Principal Message"
              />
              <!-- Animated border effect -->
              <div class="principal-image-border"></div>
            </div>
            <span
              class="text-lg font-bold mt-3 block text-primary principal-name"
              >{{ props.principalMessage.name }}</span
            >
            <span class="font-light block principal-designation">{{
              props.principalMessage.designation
            }}</span>
          </div>

          <div
            class="flex gap-y-3 xl:gap-y-5 flex-col lg:ml-10 mt-5 2xl:mt-0 principal-content"
          >
            <h2
              class="text-[25px] font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px] principal-title"
            >
              {{ props.principalMessage.title }}
            </h2>
            <p class="text-sm xl:text-base !leading-8 principal-message">
              {{ props.principalMessage.message }}
            </p>

            <HomeTemplateButton
              v-if="props.principalMessage.button"
              :button="props.principalMessage.button"
              class="mt-4 xl:mt-7 principal-button"
            />
          </div>
        </div>
        <NoticesMarquee
          :notifications="notifications"
          class="animate-fade-in-up"
        />
      </div>
    </div>
  </section>
</template>

<style scoped>
.marquee-container {
  @apply overflow-hidden inline-block relative w-full;
  mask-image: linear-gradient(
    to right,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%
  );
}

.track {
  @apply whitespace-nowrap inline-block;
  animation: marquee 30s linear infinite;
}

@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

.track:hover {
  animation-play-state: paused;
}

/* Principal message card animations */
.principal-message-card {
  position: relative;
  overflow: hidden;
}

.principal-message-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #11508b 0%, #267bca 100%);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.5s ease-out;
}

.principal-message-card:hover::before {
  transform: scaleX(1);
}

/* Principal image animations */
.principal-image-container {
  position: relative;
  z-index: 1;
}

.principal-image-border {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 3px solid transparent;
  border-radius: 50%;
  background: linear-gradient(90deg, #11508b, #267bca, #64b4ff, #11508b)
    border-box;
  -webkit-mask:
    linear-gradient(#fff 0 0) padding-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
  animation: rotate 4s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.principal-image-container:hover .principal-image-border {
  opacity: 1;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Staggered animations for principal content */
.principal-name,
.principal-designation,
.principal-title,
.principal-message,
.principal-button {
  opacity: 0;
  transform: translateY(20px);
  animation: content-fade-in 0.5s ease-out forwards;
}

.principal-name {
  animation-delay: 0.2s;
}

.principal-designation {
  animation-delay: 0.3s;
}

.principal-title {
  animation-delay: 0.4s;
}

.principal-message {
  animation-delay: 0.5s;
}

.principal-button {
  animation-delay: 0.6s;
}

@keyframes content-fade-in {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
