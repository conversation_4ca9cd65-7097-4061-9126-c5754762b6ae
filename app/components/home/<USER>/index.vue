<script setup lang="ts">
import { ArrowRight } from 'lucide-vue-next';
import type { FileData } from '~/types/home';

defineProps<{
  gallery: FileData[];
}>();

const swiperRef = ref(null);
const { width } = useWindowSize();

const isModalOpen = ref(false);

const openModal = () => {
  isModalOpen.value = true;
};
</script>

<template>
  <section id="gallery-section" class="mt-10 bg-[#f4f4f4] py-10">
    <div class="container">
      <div class="text-primary flex justify-between items-center">
        <h2
          class="text-[25px] font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
        >
          Gallery
        </h2>
        <NuxtLink
          v-if="width >= 768"
          to="/gallery"
          class="flex items-center gap-x-2 text-sm text-nowrap"
        >
          See All Albums
          <ArrowRight :size="18" />
        </NuxtLink>
      </div>
      <div class="relative mt-5">
        <div class="hidden md:grid grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-7">
          <HomeGalleryCard
            v-for="(item, index) of gallery.slice(0, 6) ?? []"
            :key="index"
            :data="{
              photo: item,
              isSeeMore: false,
            }"
            img-class="h-[190px] lg:h-[286px]"
            :open-modal="openModal"
          />
        </div>

        <ClientOnly v-if="width < 768">
          <swiper-container
            ref="swiperRef"
            :slides-per-view="1.15"
            space-between="16"
            :loop="true"
            class="swiper-basic"
          >
            <swiper-slide
              v-for="(item, index) of gallery.slice(0, 6) ?? []"
              :key="`slide-basic-${index}`"
              class="swiper-slide"
            >
              <HomeGalleryCard
                :data="{
                  photo: item,
                  isSeeMore: false,
                }"
                img-class="h-[190px]"
                :open-modal="openModal"
              />
            </swiper-slide>
          </swiper-container>
        </ClientOnly>

        <NuxtLink
          v-if="width < 768"
          to="/gallery"
          data-aos="fade-up"
          class="w-full flex-center font-semibold h-[48px] rounded-[6px] border border-primary mt-7 text-primary"
        >
          See All Albums
        </NuxtLink>
      </div>
    </div>
  </section>
  <HomeGalleryImageViewer
    multiple
    :is-open="isModalOpen"
    :photos="gallery.slice(0, 6) ?? []"
    @update:is-open="isModalOpen = $event"
  />
</template>
