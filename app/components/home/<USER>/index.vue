<script setup lang="ts">
import { ArrowRight } from 'lucide-vue-next';
import type { Programs } from '~/types/home';

defineProps<{
  programs: Programs;
}>();

// State management
// const isLoading = ref(false);
const courseType = ref<keyof Programs>('ug');

const changeCourseType = (newTab: keyof Programs) => {
  courseType.value = newTab;
};
</script>

<template>
  <section
    id="program-we-offer"
    class="relative overflow-hidden w-full pt-10 pb-16 bg-[#f4f4f4]"
  >
    <div class="container">
      <div
        class="text-primary flex flex-col lg:flex-row lg:justify-between lg:items-center"
      >
        <h2
          class="text-[25px] pl-4 lg:pl-0 font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
        >
          Programs We Offer
        </h2>

        <div
          data-aos="fade-left"
          class="flex text-white items-center h-[70px] px-4 lg:px-10 mt-5 lg:mt-0 lg:absolute lg:right-0 lg:top-6 min-w-[35%] bg-primary-gradient lg:rounded-tl-[35px]"
        >
          <span class="text-2xl lg:text-3xl font-bold">13+</span>

          <div
            class="flex items-center gap-x-4 divide-x-2 text-lg lg:text-xl ml-2 lg:ml-4"
          >
            <span>Courses</span>
            <NuxtLink
              to="/programs-offered/ug"
              class="pl-4 flex gap-x-2 items-center flex-nowrap"
            >
              See All Courses
              <ArrowRight />
            </NuxtLink>
          </div>
        </div>
      </div>

      <Tabs
        class="w-full mt-10"
        default-value="ug"
        @update:model-value="(value: keyof Programs) => changeCourseType(value)"
      >
        <div
          class="w-full px-3 lg:px-0 border-b border-[#D8D8D8] mb-8 overflow-auto"
        >
          <TabsList class="w-fit bg-transparent border-none">
            <TabsTrigger value="ug"> UG Courses </TabsTrigger>
            <TabsTrigger value="pg"> PG Courses </TabsTrigger>
            <TabsTrigger value="addOn"> Add On Courses </TabsTrigger>
          </TabsList>
        </div>
        <TabsContent :value="courseType" data-aos="fade-up">
          <HomeProgramsOfferSlider
            v-if="programs[courseType]"
            :data="programs[courseType]"
          />
          <div
            v-else
            class="flex justify-center items-center h-full text-center"
          >
            <p>No {{ courseType }} courses available</p>
          </div>
        </TabsContent>
        <!-- <TabsContent value="pg">
          <HomeProgramsOfferSlider v-if="hasCourseList" :data="courseList.data" />
           <div v-else class="flex justify-center items-center h-full text-center">
            <p>No UG courses available</p>
          </div>
        </TabsContent>
        <TabsContent  value="add-on">
          <HomeProgramsOfferSlider v-if="hasCourseList" :data="courseList.data" />
           <div v-else class="flex justify-center items-center h-full text-center">
            <p>No UG courses available</p>
          </div>
        </TabsContent> -->
      </Tabs>
    </div>
  </section>
</template>
