<script setup lang="ts">
import type { CampusDetails, Stat } from '~/types/home';

defineProps<{
  stats: Stat[];
  campusDetails: CampusDetails;
}>();
</script>

<template>
  <section
    class="relative py-10 flex-center flex-col w-full min-h-[1000px] lg:min-h-[747px]"
  >
    <NuxtImg
      loading="lazy"
      class="absolute object-cover inset-0 w-full h-full"
      fit="cover"
      height="747"
      format="webp"
      :src="getPreviewUrl(campusDetails.primaryImage.pathname)"
      alt="logo"
    />

    <div class="container" data-aos="fade-up">
      <div class="bg-white/80 p-[30px] backdrop-blur-[10px] rounded-[6px]">
        <h2
          class="text-[25px] font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
        >
          {{ campusDetails.title }}
        </h2>
        <p class="mt-4 line-clamp-[8] !leading-8">
          {{ campusDetails.description }}
        </p>
        <HomeTemplateButton
          :button="campusDetails.button"
          class="mt-4 xl:mt-7"
        />
      </div>
      <div
        data-aos="fade-up"
        class="relative grid grid-cols-2 gap-5 p-4 lg:flex h-[470px] lg:justify-around lg:items-center mt-8 lg:mt-4 rounded-[6px] text-white lg:h-[245px] backdrop-blur-[10px]"
        style="background: rgba(8, 81, 149, 0.8)"
      >
        <div
          v-if="stats[0]"
          class="flex-center flex-col text-center"
          data-aos="zoom-in"
        >
          <Icon
            :name="stats[0].icon"
            class="w-9 h-9 text-white"
            :font-controlled="false"
          />
          <span class="text-[35px] font-bold mt-2">{{ stats[0].value }}</span>
          <span class="text-sm font-medium">{{ stats[0].title }}</span>
        </div>
        <div
          class="absolute top-1/2 -translate-y-1/2 w-[calc(100%-40px)] h-[1px] lg:translate-y-0 lg:static mx-[20px] bg-[#D8D8D8] lg:w-[1px] lg:h-[88px] shrink-0"
        />
        <div
          v-if="stats[1]"
          class="flex-center flex-col text-center"
          data-aos="zoom-in"
        >
          <Icon
            :name="stats[1].icon"
            class="w-9 h-9 text-white"
            :font-controlled="false"
          />
          <span class="text-[35px] font-bold mt-2">{{ stats[1].value }}</span>
          <span class="text-sm font-medium">{{ stats[1].title }}</span>
        </div>
        <div
          class="absolute left-1/2 -translate-x-1/2 h-[calc(100%-80px)] w-[1px] lg:translate-y-0 lg:static my-[40px] bg-[#D8D8D8] lg:w-[1px] lg:h-[88px] shrink-0"
        />
        <div
          v-if="stats[2]"
          class="flex-center flex-col text-center"
          data-aos="zoom-in"
        >
          <Icon
            :name="stats[2].icon"
            class="w-9 h-9 text-white"
            :font-controlled="false"
          />
          <span class="text-[35px] font-bold mt-2">{{ stats[2].value }}</span>
          <span class="text-sm font-medium">{{ stats[2].title }}</span>
        </div>
        <div
          class="hidden lg:inline-block bg-[#D8D8D8] w-[1px] h-[88px] shrink-0"
        />
        <div
          v-if="stats[3]"
          class="flex-center flex-col text-center"
          data-aos="zoom-in"
        >
          <Icon
            :name="stats[3].icon"
            class="w-9 h-9 text-white"
            :font-controlled="false"
          />
          <span class="text-[35px] font-bold mt-2">{{ stats[3].value }}</span>
          <span class="text-sm font-medium">{{ stats[3].title }}</span>
        </div>
      </div>
    </div>
  </section>
</template>
