<script setup lang="ts">
import type { Research } from '~/types/home';

defineProps<{
  research: Research;
}>();
</script>

<template>
  <section
    id="research-section"
    class="container overflow-hidden relative w-full pt-12 pb-20"
  >
    <h2
      class="text-[25px] lg:pl-0 font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
    >
      {{ research.title }}
    </h2>
    <p class="my-4 !leading-8" data-aos="fade-right">
      {{ research.description }}
    </p>
    <HomeTemplateButton :button="research.button" />
  </section>
</template>
