<script setup lang="ts">
import { Download } from 'lucide-vue-next';
import type { DownloadFiles } from '~/types/home';

defineProps<{
  data: DownloadFiles;
}>();
</script>

<template>
  <div
    class="w-full bg-white mt-4 first:mt-0 flex items-center justify-between px-[15px] md:px-4 lg:px-5 py-3 rounded-[6px]"
  >
    <div
      class="w-full text-sm lg:text-lg font-semibold text-primary overflow-auto"
    >
      {{ data.title }}
    </div>
    <Button
      class="flex-center shrink-0 h-[45px] !bg-primary-gradient hover:opacity-80 active:scale-105 transition-all duration-300 rounded-[6px] gap-x-2 text-base tracking-wider font-medium text-white bg-primary"
      @click="
        () =>
          downloadFile({
            fileUrl: data.file.pathname,
            fileName: data.file.title,
          })
      "
    >
      <Download class="!w-5 !h-auto" :font-controlled="false" />
      <span class="hidden md:inline-block">Download</span>
    </Button>
  </div>
</template>
