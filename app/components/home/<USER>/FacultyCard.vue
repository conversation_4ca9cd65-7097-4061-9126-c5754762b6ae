<script setup lang="ts">
import type { FacultyCard } from '~/types/home';

const props = defineProps<{
  data: FacultyCard;
  viewProfile?: boolean;
  static?: boolean;
}>();
const { name, caption, imgSrc, companyName, percentage } = props.data;

const emit = defineEmits<{
  (e: 'viewProfile'): void;
}>();
</script>

<template>
  <div
    class="w-full flex-center flex-col max-w-w-[340px] py-6 px-4 text-center lg:px-7 bg-white shadow-md text-primary rounded-[6px] overflow-hidden border-b-[9px] border-[#3B7FCC]"
  >
    <div class="relative w-[150px] h-[150px] rounded-full overflow-hidden">
      <NuxtImg
        loading="lazy"
        class="w-full h-full absolute inset-0 object-cover"
        fit="cover"
        format="webp"
        :src="static ? imgSrc : getPreviewUrl(imgSrc)"
        alt="profile pic"
      />
    </div>
    <span class="font-semibold mt-5">{{ name }}</span>
    <span class="text-xs mt-2 font-medium">{{ caption }}</span>
    <span v-if="companyName" class="text-xs mt-4 font-medium">{{
      companyName
    }}</span>
    <span v-if="percentage" class="text-[25px] font-bold mt-4"
      >{{ percentage }}%</span
    >

    <Button
      v-if="viewProfile"
      class="mt-3 rounded-[5px]"
      @click="emit('viewProfile')"
      >View Profile</Button
    >
  </div>
</template>
