<!-- components/ImageModal.vue -->
<script setup lang="ts">
import { ChevronLeft, ChevronRight } from 'lucide-vue-next';
import type { FileData } from '~/types/home';

interface Props {
  isOpen: boolean;
  multiple: boolean;
  photo?: FileData;
  photos?: FileData[];
}

const props = defineProps<Props>();

const currentIndex = ref(0);
const previewPhoto = ref<FileData | undefined>();

const computedPhoto = computed<FileData | undefined>(() =>
  props.multiple ? props.photos?.[currentIndex.value] : props.photo
);

// Update previewPhoto reactively using a watcher
watchEffect(() => {
  previewPhoto.value = computedPhoto.value;
});

// Preview navigation
function navigatePreview(direction: 'prev' | 'next') {
  if (props.multiple && props.photos) {
    const index = currentIndex.value;
    const newIndex = direction === 'prev' ? index - 1 : index + 1;

    if (newIndex >= 0 && newIndex < props.photos.length) {
      currentIndex.value = newIndex;
      previewPhoto.value = props.photos[newIndex];
    }
  }
}

const emit = defineEmits<{
  (e: 'update:isOpen', value: boolean): void;
}>();
</script>

<template>
  <Dialog :open="isOpen" @update:open="emit('update:isOpen', $event)">
    <DialogContent class="max-w-screen-lg w-full max-h-screen">
      <Button
        v-if="multiple && currentIndex > 0"
        variant="ghost"
        size="icon"
        class="absolute left-0 lg:left-4 top-1/2 -translate-y-1/2 z-10 w-9 h-9 lg:h-12 lg:w-12 rounded-full bg-black/60 hover:bg-black/70"
        @click="navigatePreview('prev')"
      >
        <ChevronLeft class="h-8 w-8 text-white" />
      </Button>
      <NuxtImg
        v-if="previewPhoto"
        quality="100"
        :src="getPreviewUrl(previewPhoto.pathname)"
        placeholder
        placeholder-class="w-full h-full max-h-[300px] md:max-h-[500px]"
        :alt="'Gallery image'"
        format="webp"
        loading="lazy"
        class="w-full h-full max-h-[300px] md:max-h-[500px] object-contain"
      />
      <p v-else>Photo not available</p>
      <Button
        v-if="multiple && photos && currentIndex < photos.length - 1"
        variant="ghost"
        size="icon"
        class="absolute right-0 lg:right-4 top-1/2 -translate-y-1/2 z-10 w-9 h-9 lg:h-12 lg:w-12 rounded-full bg-black/60 hover:bg-black/70"
        @click="navigatePreview('next')"
      >
        <ChevronRight class="h-8 w-8 text-white" />
      </Button>
    </DialogContent>
  </Dialog>
</template>
