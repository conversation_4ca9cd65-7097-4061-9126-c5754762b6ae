<script setup lang="ts">
import type { NotificationData } from '~/types/home';

defineProps<{
  notifications: NotificationData[];
}>();
</script>

<template>
  <div
    data-aos="fade-up"
    class="bg-border-blue w-full xl:max-w-[430px] px-4 py-5 2xl:p-7 rounded-[6px] text-white overflow-hidden"
  >
    <h1 class="text-[25px] font-semibold">Notice Board</h1>

    <div class="notices max-h-[400px] overflow-hidden mt-7 mb-10">
      <ul class="space-y-6 marquee">
        <li
          v-for="item in notifications ?? []"
          :key="item.id"
          class="flex items-center gap-x-2"
        >
          <div
            class="flex-center flex-col gap-y-1 px-4 py-2 2xl:gap-y-1 bg-white lg:bg-white/90 text-primary rounded-[5px]"
          >
            <span class="text-sm font-light leading-none">{{
              formatDate(item?.date!).month
            }}</span>
            <span class="text-xl font-bold leading-none">{{
              formatDate(item?.date!).day
            }}</span>
            <span class="text-sm font-light leading-none">{{
              formatDate(item?.date!).year
            }}</span>
          </div>
          <NuxtLink
            :to="`/notice-board/${item.id}`"
            class="text-sm lg:mt-2 2xl:mt-0"
          >
            {{ item.title }}
          </NuxtLink>
        </li>
      </ul>
      <!---------- Clone just for marquee animation------ -->
      <ul class="space-y-6 mt-6 marquee" aria-hidden="true">
        <li
          v-for="item in notifications ?? []"
          :key="item.id"
          class="flex items-center gap-x-2"
        >
          <div
            class="flex-center flex-col gap-y-1 px-4 py-2 2xl:gap-y-1 bg-white lg:bg-white/90 text-primary rounded-[5px]"
          >
            <span class="text-sm font-light leading-none">{{
              formatDate(item?.date!).month
            }}</span>
            <span class="text-xl font-bold leading-none">{{
              formatDate(item?.date!).day
            }}</span>
            <span class="text-sm font-light leading-none">{{
              formatDate(item?.date!).year
            }}</span>
          </div>
          <NuxtLink
            :to="`/notice-board/${item.id}`"
            class="text-sm lg:mt-2 2xl:mt-0"
          >
            {{ item.title }}
          </NuxtLink>
        </li>
      </ul>
    </div>
    <div
      class="w-full absolute bottom-4 backdrop-blur-lg py-10 -ml-4 2xl:-ml-7"
    >
      <NuxtLink
        to="/notice-board"
        class="w-[160px] mx-auto h-[48px] border border-white rounded-[6px] flex-center hover:bg-white/20"
      >
        See All
      </NuxtLink>
    </div>
  </div>
</template>
