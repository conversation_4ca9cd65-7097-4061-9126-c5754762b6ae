<script setup lang="ts">
import type { QuickLink } from '~/types/home';

defineProps<{
  quickLinks: QuickLink[];
}>();

const videoRef = ref<HTMLVideoElement | null>(null);
const heroRef = ref<HTMLElement | null>(null);
const parallaxOffset = ref(0);

// Handle parallax effect on scroll
const handleScroll = () => {
  if (!heroRef.value) return;
  const scrollPosition = window.scrollY;
  const heroHeight = heroRef.value.offsetHeight;

  // Only apply parallax when scrolling within the hero section
  if (scrollPosition <= heroHeight) {
    parallaxOffset.value = scrollPosition * 0.4; // Adjust the multiplier for parallax intensity
  }
};

onMounted(() => {
  if (videoRef.value) {
    videoRef.value.play();
  }

  // Add scroll event listener for parallax effect
  window.addEventListener('scroll', handleScroll);

  // Initialize AOS with custom settings for this component
  if (typeof document !== 'undefined') {
    const quickLinks = document.querySelectorAll('.quick-link-item');
    quickLinks.forEach((link, index) => {
      link.setAttribute('data-aos', 'fade-up');
      link.setAttribute('data-aos-delay', `${index * 100}`);
      link.setAttribute('data-aos-duration', '800');
    });
  }
});

onUnmounted(() => {
  // Clean up event listener
  window.removeEventListener('scroll', handleScroll);
});
</script>

<template>
  <div
    ref="heroRef"
    class="relative h-[calc(100vh-60px)] select-none overflow-hidden"
  >
    <!-- Video background with parallax effect -->
    <video
      ref="videoRef"
      class="absolute inset-0 w-full h-full object-cover"
      autoplay
      muted
      loop
      playsinline
      preload="auto"
      poster="/img/hero-banner.png"
    >
      <source
        src="https://video.dbcollegemannuthy.edu.in/0602.mp4"
        type="video/mp4"
      />
    </video>

    <!-- Video gradient overlay with improved animation -->
    <div
      class="absolute top-0 left-0 right-0 h-[150px] z-10 animate-gradient-shift"
      style="
        background: linear-gradient(
          180deg,
          #00428c 0%,
          #00428c 5%,
          rgba(0, 66, 140, 0.7) 27%,
          rgba(255, 255, 255, 0) 82%
        );
      "
    />

    <!-- Quick links overlay with staggered animations -->
    <div
      class="quick-links-container absolute bottom-10 w-full flex pl-6 pr-6 py-4 lg:justify-center z-[5] gap-x-4 lg:gap-x-7 xl:gap-x-8 overflow-auto no-scrollbar"
    >
      <HomeHeroHighlight
        v-for="item in quickLinks"
        :key="item.id"
        class="quick-link-item"
        :data="{
          link: item.link,
          name: item.title,
          icon: item.icon,
        }"
      />
    </div>
  </div>
</template>

<style scoped>
/* Hide video controls */
video::-webkit-media-controls {
  display: none !important;
}
video::-webkit-media-controls-enclosure {
  display: none !important;
}

/* Animated gradient shift */
@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

@keyframes slide-up {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Quick links container animation */
.quick-links-container {
  transform: translateY(20px);
  opacity: 0;
  animation: slide-up 1s ease forwards 1.5s;
}

/* Enhance hover effects for quick links */
.quick-link-item {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-link-item:hover {
  transform: translateY(-8px) scale(1.05);
}
</style>
