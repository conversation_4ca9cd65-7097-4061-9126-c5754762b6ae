<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ArrowLeft, ChevronDownIcon, MoveUpRight, X } from 'lucide-vue-next';
import type { NavbarMenu, MenuItem } from '~/types/home';

const props = defineProps<{
  menus: NavbarMenu;
  isOpen: boolean;
}>();

const emit = defineEmits<{
  (e: 'onClose'): void;
}>();

const { width } = useWindowSize();
const isMobile = ref(width.value < 768);
const currentMenu = ref<NavbarMenu>([...props.menus]);
const menuHistory = ref<NavbarMenu[]>([]);
const selectedItem = ref<MenuItem | null>(null);
const menuStack = ref([props.menus]);
const selectedTitle = ref('');

// Spherical Animation State
const isNavVisible = ref(false);
const isContentVisible = ref(false);
const diameter = ref(0);

// Calculate the diameter of the circle based on window size
const calculateDiameter = () => {
  diameter.value =
    Math.sqrt(
      Math.pow(window.innerHeight, 2) + Math.pow(window.innerWidth, 2)
    ) * 2;
};

const toggleItem = (item: MenuItem, index: number) => {
  if (item.children) {
    if (isMobile.value) {
      menuHistory.value.push(currentMenu.value);
      currentMenu.value = item.children;
      selectedTitle.value = item.label;
    } else {
      menuStack.value = menuStack.value.slice(0, index + 1);
      menuStack.value.push(item.children);
    }
    selectedItem.value = item;
  }
};

// Styles for the overlay navigation
const overlayNavStyle = computed(() => ({
  transform: isNavVisible.value ? 'scale(1)' : 'scale(0)',
  height: `${diameter.value}px`,
  width: `${diameter.value}px`,
  top: `-${diameter.value / 2}px`,
  right: `-${diameter.value / 2}px`,
  transition: 'transform 0.5s ease-in-out',
}));

// Styles for the overlay content
const overlayContentStyle = computed(() => ({
  transform: isNavVisible.value ? 'scale(1)' : 'scale(0)',
  height: `${diameter.value}px`,
  width: `${diameter.value}px`,
  top: `-${diameter.value / 2}px`,
  right: `-${diameter.value / 2}px`,
  transition: 'transform 0.5s ease-in-out',
}));

// Toggle spherical animation when menu opens/closes
watch(
  () => props.isOpen,
  (isOpen) => {
    if (isOpen) {
      // Start animation first
      isNavVisible.value = true;
      // Show content after animation starts
      setTimeout(() => {
        isContentVisible.value = true;
      }, 300); // Delay showing content to match animation
    } else {
      // Hide content first
      isContentVisible.value = false;
      // Then start closing animation
      setTimeout(() => {
        isNavVisible.value = false;
      }, 300); // Delay hiding overlay to complete animation
    }
  }
);

// Initialize the diameter and add resize listener
const goBack = () => {
  if (menuHistory.value.length) {
    currentMenu.value = menuHistory.value.pop() || [];
    const lastMenu = menuHistory.value.at(-1);
    selectedTitle.value =
      lastMenu && lastMenu.length > 0 ? (lastMenu[0]?.label ?? '') : '';

    selectedItem.value = null;
  }
};

onMounted(() => {
  calculateDiameter();
  window.addEventListener('resize', calculateDiameter);
});
</script>
<template>
  <Teleport to="body">
    <!-- Overlay Elements for Spherical Animation -->
    <div class="cd-overlay-nav">
      <span :style="overlayNavStyle"></span>
    </div>
    <div class="cd-overlay-content">
      <span :style="overlayContentStyle"></span>
    </div>

    <!-- Navigation Menu -->
    <div
      v-if="props.isOpen"
      ref="navMenu"
      class="fixed top-0 left-0 right-0 py-7 lg:px-10 h-full overflow-auto z-[100] transition-opacity duration-300"
      :class="isContentVisible ? 'opacity-100' : 'opacity-0'"
    >
      <div class="bg-primary h-full w-full absolute top-0 left-0 -z-10"></div>

      <button
        class="fixed top-5 right-10 text-white z-[101] transition-opacity duration-300"
        :class="isContentVisible ? 'opacity-100' : 'opacity-0'"
        @click="emit('onClose')"
      >
        <X class="text-white" />
      </button>

      <div class="relative h-full flex w-full">
        <!-- Image positioned absolutely at the left center -->
        <div
          class="absolute hidden lg:block top-[45%] left-0 transform -translate-y-1/2"
        >
          <NuxtImg
            width="60"
            height="auto"
            format="webp"
            class="w-[40px] md:w-[64px] h-auto object-scale-down"
            src="/img/menu.png"
            alt="Don Bosco College Mannuthy"
          />
        </div>

        <!-- Flex container with padding to avoid overlapping the image -->
        <div
          class="flex gap-5 md:pl-20 justify-center text-white transition-opacity duration-300 m-auto"
          :class="[
            { 'md:flex-row m-auto': !isMobile, 'flex-col': isMobile },
            isContentVisible ? 'opacity-100' : 'opacity-0',
          ]"
        >
          <div v-if="!isMobile" class="flex gap-3">
            <div
              v-for="(menu, index) in menuStack"
              :key="index"
              class="p-2 rounded-lg w-full"
            >
              <ul class="list-none p-0 min-w-[240px]">
                <li
                  v-for="item in menu"
                  :key="item.label"
                  :class="
                    cn(
                      'p-2 cursor-pointer transition-colors h-[50px] border-t first:border-none border-t-[#324E95] hover:bg-white/40 flex justify-between items-center',
                      selectedItem === item && 'bg-[#1359A8]'
                    )
                  "
                  @click="!item.link && toggleItem(item, index)"
                >
                  <NuxtLink
                    v-if="item.link"
                    :to="item.link"
                    class="flex items-center text-sm gap-x-2 justify-between w-full"
                    target="_blank"
                  >
                    <span>{{ item.label }}</span>
                    <MoveUpRight class="w-4 h-auto ml-auto" />
                  </NuxtLink>
                  <span v-else class="text-base font-semibold">{{
                    item.label
                  }}</span>
                  <span v-if="item.children" class="ml-2">
                    <ChevronDownIcon
                      :class="
                        cn('w-4 h-auto', selectedItem === item && '-rotate-90')
                      "
                    />
                  </span>
                </li>
              </ul>
            </div>
          </div>

          <div v-else class="w-full">
            <div
              v-if="menuHistory.length"
              class="flex bg-[#1359A8] h-[50px] px-4 items-center mb-2"
            >
              <button class="text-blue-500 mr-2" @click="goBack">
                <ArrowLeft class="w-5 h-auto text-white" />
              </button>
              <span class="text-base font-semibold">{{ selectedTitle }}</span>
            </div>
            <ul class="list-none pl-8 pr-4">
              <li
                v-for="item in currentMenu"
                :key="item.label"
                :class="
                  cn(
                    'p-2 transition-colors h-[50px] border-t first:border-none border-t-[#2C6EAE] flex justify-between items-center',
                    selectedItem === item && 'bg-[#1359A8]'
                  )
                "
                @click="!item.link && toggleItem(item, 0)"
              >
                <NuxtLink
                  v-if="item.link"
                  target="_blank"
                  :to="item.link"
                  class="flex items-center text-sm gap-x-2 justify-between w-full"
                >
                  <span>{{ item.label }}</span>
                  <MoveUpRight class="w-4 h-auto ml-auto" />
                </NuxtLink>
                <span v-else class="text-sm font-medium">{{ item.label }}</span>
                <span v-if="item.children" class="ml-2">
                  <svg
                    v-if="selectedItem === item"
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M6 10l4-4 4 4H6z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <svg
                    v-else
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 14l-4-4h8l-4 4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<style scoped>
/* Overlay Styles */
.cd-overlay-nav,
.cd-overlay-content {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 99;
  pointer-events: none;
}

.cd-overlay-nav span,
.cd-overlay-content span {
  display: inline-block;
  position: absolute;
  border-radius: 50%;
  background-color: none; /* Adjust color as needed */
  transform: scale(0);
  transition: transform 0.5s ease-in-out;
}

.cd-overlay-content span {
  @apply bg-primary;
}
</style>
