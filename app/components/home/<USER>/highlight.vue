<script setup lang="ts">
defineProps<{
  data: {
    name: string;
    link: string;
    icon: string;
  };
}>();
</script>

<template>
  <NuxtLink :to="data.link" class="cursor-pointer overflow-visible">
    <div
      class="group flex items-center flex-col gap-y-2 lg:gap-y-3 w-[100px] cursor-pointer transition-all duration-300 ease-in-out hover:-translate-y-1 overflow-visible"
    >
      <div
        class="highlight-icon flex-center !cursor-pointer w-[70px] h-[60px] lg:w-[90px] lg:h-[75px] rounded-md backdrop-blur-md shadow-md transition-all duration-500 ease-out group-hover:shadow-lg group-hover:shadow-[#64B4FF]/30 relative overflow-hidden"
        style="
          background: rgba(100, 180, 255, 0.5);
          backdrop-filter: blur(10px);
        "
      >
        <!-- Animated background effect -->
        <div
          class="highlight-bg absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
        ></div>

        <!-- Icon with enhanced animations -->
        <Icon
          :name="data.icon"
          class="size-8 !cursor-pointer text-white transition-all duration-500 ease-out group-hover:size-10 group-hover:text-white group-hover:rotate-3 group-hover:scale-110 relative z-10"
        />

        <!-- Animated glow effect -->
        <div
          class="absolute inset-0 highlight-glow opacity-0 group-hover:opacity-100 transition-opacity duration-500"
        ></div>
      </div>
      <div
        class="highlight-label flex-center !cursor-pointer h-[30px] px-2 lg:px-3 text-xs lg:text-sm whitespace-nowrap rounded-[15px] text-white border border-white transition-all duration-500 ease-out group-hover:bg-[#64B4FF] group-hover:border-[#64B4FF] group-hover:font-medium bg-black/40 backdrop-blur-sm relative overflow-hidden"
      >
        <!-- Text label -->
        <span class="relative z-10">{{ data.name }}</span>

        <!-- Animated fill effect -->
        <div
          class="absolute inset-0 bg-[#64B4FF] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"
        ></div>
      </div>
    </div>
  </NuxtLink>
</template>

<style scoped>
/* Animated background effect */
.highlight-bg {
  background: radial-gradient(
    circle at center,
    rgba(100, 180, 255, 0.6) 0%,
    rgba(100, 180, 255, 0.3) 100%
  );
  animation: pulse-bg 2s ease-in-out infinite;
}

@keyframes pulse-bg {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Glow effect */
.highlight-glow {
  box-shadow: 0 0 20px 5px rgba(100, 180, 255, 0.8) inset;
  border-radius: 8px;
}

/* Enhanced hover animations */
.highlight-icon {
  transform: perspective(800px) rotateY(0);
  transition: transform 0.5s ease-out;
}

.group:hover .highlight-icon {
  transform: perspective(800px) rotateY(15deg);
}

/* Label animation */
.highlight-label {
  transform-origin: bottom;
}

.group:hover .highlight-label {
  letter-spacing: 0.5px;
}
</style>
