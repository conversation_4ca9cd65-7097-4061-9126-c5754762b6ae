<script setup lang="ts">
import type { LinkButton } from '~/types/home';

defineProps<{
  button: LinkButton;
  className?: string;
}>();

const getButtonLink = (button: LinkButton) => {
  if (!button) return '#';
  return button.link || '#';
};
</script>

<template>
  <div v-if="button && button.link">
    <NuxtLink
      :to="getButtonLink(button)"
      :target="button.newTab ? '_blank' : '_self'"
      :class="[
        'inline-flex items-center justify-center gap-2 rounded-[6px] font-medium transition-colors text-sm w-fit px-4 py-2 min-w-[120px]',
        button.style === 'primary' &&
          'bg-primary text-white hover:bg-primary/90',
        button.style === 'secondary' &&
          'border border-primary text-primary hover:bg-primary/10',
        className,
      ]"
    >
      {{ button.title }}
      <Icon v-if="button.icon" :name="button.icon" class="size-4" />
    </NuxtLink>
  </div>
</template>
