<script setup lang="ts">
export type RelatedLinks = {
  link: string;
  label: string;
}[];

defineProps<{
  links: RelatedLinks;
  linkPrefix?: string;
}>();

const route = useRoute();
const pathname = computed(() => route.path);
</script>

<template>
  <div
    class="lg:w-[200px] xl:w-[250px] h-full lg:h-auto bg-white lg:shadow-sm shrink-0 lg:p-4 pt-4"
  >
    <div
      class="hidden lg:flex-center text-lg font-semibold rounded-[6px] text-white w-full h-[45px] bg-primary-gradient"
    >
      Related Links
    </div>
    <ul class="lg:pt-4 lg:pl-3 pl-2.5">
      <NuxtLink
        v-for="(item, index) of links"
        :key="index"
        :to="linkPrefix + item.link"
      >
        <li
          :class="
            cn(
              `relative flex items-center px-4 py-5 text-left rounded-[6px] hover:bg-[#ECF5FF] hover:text-primary hover:font-semibold`,
              pathname === `${linkPrefix + item.link}` &&
                `bg-[#ECF5FF] text-primary font-semibold before:contents-[''] before:inline-block before:w-[6px] before:rounded-[6px] before:h-full before:bg-primary-gradient before:absolute before:-left-2.5`
            )
          "
        >
          {{ item.label }}
        </li>
      </NuxtLink>
    </ul>
  </div>
</template>
