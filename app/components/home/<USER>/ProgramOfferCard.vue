<script setup lang="ts">
import { Download, ChevronUp } from 'lucide-vue-next';
import type { Course } from '~/types/home';
definePageMeta({
  layout: 'landing',
});

defineProps<{
  data: Partial<Course>;
}>();
const isEligibleOpen = ref(false);

const handleEligibleOpen = () => {
  isEligibleOpen.value = !isEligibleOpen.value;
};

const handleDownload = (pathname: string) => {
  if (pathname) {
    const url = getPreviewUrl(pathname);
    window.open(`${url}`, '_blank');
  }
};
</script>

<template>
  <div class="w-full bg-white px-4 lg:px-5 py-6 mt-5 first:mt-0 rounded-[8px]">
    <div class="flex flex-col lg:flex-row lg:gap-x-5">
      <div
        class="relative w-full lg:w-[230px] h-[150px] lg:h-[170px] rounded-[6px] overflow-hidden"
      >
        <NuxtImg
          loading="lazy"
          class="w-full h-full object-cover"
          format="webp"
          :src="getPreviewUrl(data.image?.pathname!)"
          alt="course img"
        />
      </div>
      <div class="flex flex-col w-full">
        <div
          class="relative flex justify-between items-start pr-[50px] lg:pr-[60px] mt-3 lg:mt-0"
        >
          <h4 class="text-lg lg:text-xl font-medium">
            {{ data.name }}
          </h4>
          <div
            v-if="data.seatsCount"
            class="absolute top-0 right-0 flex-center flex-col w-[50px] h-[50px] lg:w-[60px] lg:h-[60px] shrink-0 bg-primary rounded-full"
          >
            <span
              class="text-lg lg:text-[22px] leading-none font-semibold text-white mt-1"
            >
              {{ data.seatsCount }}
            </span>
            <span class="text-[10px] lg:text-xs font-medium text-[#ABD6FF]">
              Seats
            </span>
          </div>
        </div>
        <div class="text-xs lg:text-base font-medium mt-2">
          <span class="text-primary mr-1">Specialization:</span>
          <span>{{ data?.specialization }}</span>
        </div>
        <div class="text-xs lg:text-base font-medium mt-3">
          <span class="text-primary mr-1">Duration:</span>
          <span>
            {{
              data?.durationInMonths! < 12
                ? data?.durationInMonths! +
                  (data?.durationInMonths! === 1 ? ' Month' : ' Months')
                : Math.ceil(data?.durationInMonths! / 12) +
                  (Math.ceil(data?.durationInMonths! / 12) === 1
                    ? ' Year'
                    : ' Years')
            }}
            | {{ data?.semesterCount }} Semester
          </span>
        </div>
        <div class="flex flex-col lg:flex-row gap-3 mt-5 lg:mt-auto">
          <Button
            type="button"
            variant="outline"
            size="sm"
            aria-label="Syllabus"
            @click="handleDownload(data?.syllabus?.pathname!)"
          >
            Syllabus
            <Download class="w-4 h-auto text-primary" />
          </Button>
          <Button
            type="button"
            variant="outline"
            size="sm"
            aria-label="POs PSOs & COs"
            @click="handleDownload(data?.pos?.pathname!)"
          >
            POs PSOs & COs
            <Download class="w-4 h-auto text-primary" />
          </Button>
          <HomeTemplateButton
            v-if="data?.primaryButton"
            class-name="w-full justify-between lg:justify-center"
            :button="data?.primaryButton"
          />
        </div>
      </div>
    </div>
    <div
      class="flex items-center justify-between select-none text-primary py-2 mt-7 cursor-pointer hover:bg-slate-100 px-2 rounded-sm"
      @click="handleEligibleOpen"
    >
      <span class="text-sm lg:text-base font-medium"
        >Eligibility for Admission</span
      >
      <ChevronUp
        :class="cn('w-5 h-auto text-primary', { 'rotate-180': isEligibleOpen })"
      />
    </div>
    <div
      v-if="isEligibleOpen"
      class="text-sm select-none lg:text-base font-medium !leading-8 mt-2"
      v-html="data?.content?.description"
    ></div>
  </div>
</template>
