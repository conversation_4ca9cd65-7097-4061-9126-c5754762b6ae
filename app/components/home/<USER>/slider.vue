<script setup lang="ts">
import type { QuickLink } from '~/types/home';

defineProps<{
  quickLinks: QuickLink[];
}>();
const swiperRef = ref(null);

const dummyArr: Array<{
  type: 'img' | 'video';
  link: string;
}> = [{ type: 'img', link: '/img/hero-banner.png' }];

const extendedArr: Array<{
  type: 'img' | 'video';
  link: string;
}> = Array.from(
  { length: 1 },
  (_, index) => dummyArr[index % 3] || { type: 'img', link: '' }
);
</script>

<template>
  <div class="relative h-[calc(100vh-60px)] select-none">
    <div
      class="absolute top-0 left-0 right-0 h-[150px] z-10"
      style="
        background: linear-gradient(
          180deg,
          #00428c 0%,
          #00428c 5%,
          rgba(0, 66, 140, 0.7) 27%,
          rgba(255, 255, 255, 0) 82%
        );
      "
    />
    <ClientOnly>
      <swiper-container
        ref="swiperRef"
        :slides-per-view="1"
        :loop="true"
        class="swiper-basic h-full"
      >
        <swiper-slide
          v-for="(item, index) of extendedArr"
          :key="`slide-basic-${index}`"
          class="swiper-slide"
        >
          <HomeHeroSlide :data="item" />
        </swiper-slide>
      </swiper-container>

      <div
        data-aos="fade-up"
        class="absolute bottom-10 w-full flex pl-6 pr-6 lg:justify-center z-10 gap-x-4 lg:gap-x-7 xl:gap-x-8 overflow-auto no-scrollbar"
      >
        <HomeHeroHighlight
          v-for="item in quickLinks"
          :key="item.id"
          :data="{
            link: item.link,
            name: item.title,
            icon: item.icon,
          }"
        />
      </div>
    </ClientOnly>
  </div>
</template>
