<script setup lang="ts">
import { ChevronLeft, ChevronRight } from 'lucide-vue-next';
import type { Course } from '~/types/home';

const swiperRef = ref(null);
const swiperInstance = useSwiper(swiperRef);
defineProps<{
  data: Course[];
}>();
</script>

<template>
  <div class="relative mt-5 pl-4 lg:px-4">
    <ClientOnly>
      <swiper-container
        ref="swiperRef"
        :slides-per-view="1.3"
        space-between="20"
        :loop="true"
        :breakpoints="{
          768: { slidesPerView: 2.3, spaceBetween: 20 },
          1024: { slidesPerView: 3, spaceBetween: 30 },
          1280: { slidesPerView: 4, spaceBetween: 30 },
        }"
        class="swiper-basic"
      >
        <swiper-slide
          v-for="(item, index) of data"
          :key="`slide-basic-${index}`"
          class="swiper-slide"
        >
          <HomeProgramsOfferCard :data="item" />
        </swiper-slide>
      </swiper-container>

      <div class="swiper-basic-buttons hidden lg:block">
        <button
          class="button-prev flex-center bg-slate-200 rounded-full p-1 absolute top-[45%] -translate-y-1/2 -left-10 z-20 shadow-md"
          @click="swiperInstance.prev()"
        >
          <ChevronLeft :size="34" class="text-primary" />
        </button>
        <button
          class="button-next flex-center bg-slate-200 rounded-full p-1 absolute top-[45%] -translate-y-1/2 -right-10 z-20 shadow-md"
          @click="swiperInstance.next()"
        >
          <ChevronRight :size="34" class="text-primary" />
        </button>
      </div>
    </ClientOnly>
  </div>
</template>
