<script lang="ts" setup>
import type { MenuItem } from '~/types/home';
import { ref, onMounted, onUnmounted } from 'vue';

const props = defineProps<{
  data: MenuItem['children'];
  isLast?: boolean;
  isScrolled?: boolean;
}>();

// State for hover-based dropdown
const isOpen = ref(false);
const dropdownRef = ref<HTMLElement | null>(null);
let hoverTimeout: number | null = null;

// Determine if the panel should be wider based on the number of menu items
const panelWidth = computed(() => {
  if (!props.data) return 'w-[95vw] sm:w-[400px]';

  const itemCount = props.data.length;
  const columnsNeeded = Math.ceil(itemCount / 4); // Assume 4 items per row is ideal
  const minWidth = Math.min(columnsNeeded * 150, 600); // 150px per column, max 600px

  // If more than 8 items, use a wider panel with grid layout
  if (itemCount > 8) {
    return `w-full min-w-[50vw] max-w-[95vw] sm:max-w-[${minWidth}px]`;
  } else if (itemCount > 4) {
    return `w-full min-w-[50vw] max-w-[95vw] sm:max-w-[${Math.min(minWidth, 500)}px]`;
  } else {
    return 'min-w-[50vw] max-w-[95vw] sm:max-w-[350px] md:max-w-[400px]';
  }
});

// Open dropdown on hover
function openDropdown(): void {
  if (hoverTimeout) clearTimeout(hoverTimeout);
  isOpen.value = true;
}

// Close dropdown with delay to allow moving to dropdown content
function closeDropdownWithDelay(): void {
  hoverTimeout = window.setTimeout(() => {
    isOpen.value = false;
  }, 400); // Increased delay for better user experience
}

// Cancel close when moving from trigger to dropdown content
function cancelCloseDropdown(): void {
  if (hoverTimeout) clearTimeout(hoverTimeout);
}

// Close dropdown when clicking outside
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

function handleClickOutside(event: MouseEvent): void {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false;
  }
}
</script>

<template>
  <div
    ref="dropdownRef"
    class="relative inline-block"
    @mouseenter="openDropdown"
    @mouseleave="closeDropdownWithDelay"
  >
    <!-- Trigger element with slot -->
    <div class="dropdown-trigger">
      <slot />
    </div>

    <!-- Custom dropdown panel -->
    <div
      v-show="isOpen"
      :class="[
        panelWidth,
        'nav-panel absolute z-50 border border-[#1359A8]/20 bg-white/95 backdrop-blur-xl p-0 overflow-hidden',
        isLast ? 'right-0' : 'left-0',
      ]"
      @mouseenter="cancelCloseDropdown"
      @mouseleave="closeDropdownWithDelay"
    >
      <!-- Animated background elements -->
      <div class="nav-panel-bg"></div>
      <div class="nav-panel-glow"></div>

      <!-- Dropdown content -->
      <div class="p-1 sm:p-2 dropdown-content">
        <HomeNavigationDropdownRecursive :data="props.data || []" />
      </div>

      <!-- Decorative elements -->
      <div class="nav-panel-decoration top-left"></div>
      <div class="nav-panel-decoration top-right"></div>
      <div class="nav-panel-decoration bottom-left"></div>
      <div class="nav-panel-decoration bottom-right"></div>
    </div>
  </div>
</template>

<style scoped>
.dropdown-trigger {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dropdown-trigger:hover {
  transform: translateY(-2px);
}

/* Main panel styling */
.nav-panel {
  border-radius: 1rem;
  top: 100%;
  margin-top: 0.5rem;
  animation: nav-in 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow:
    0 10px 40px -5px rgba(0, 0, 0, 0.2),
    0 8px 20px -6px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(0, 0, 0, 0.05),
    0 0 15px rgba(59, 130, 246, 0.15);
  transform-origin: top center;
  transition:
    width 0.3s ease-in-out,
    transform 0.3s ease-in-out;
  max-width: 90vw; /* Ensure it doesn't overflow on small screens */
  border: 1px solid rgba(59, 130, 246, 0.2);
  position: relative;
  overflow: hidden;
}

/* Background elements */
.nav-panel-bg {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(240, 249, 255, 0.9) 100%
  );
  z-index: -2;
}

.nav-panel-glow {
  position: absolute;
  width: 150%;
  height: 150%;
  top: -25%;
  left: -25%;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.15) 0%,
    rgba(59, 130, 246, 0) 70%
  );
  z-index: -1;
  opacity: 0;
  animation: glow-pulse 4s ease-in-out infinite;
}

/* Decorative corner elements */
.nav-panel-decoration {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(59, 130, 246, 0.05) 100%
  );
  z-index: -1;
  opacity: 0;
  transform: scale(0);
  animation: decoration-in 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards 0.2s;
}

.top-left {
  top: -10px;
  left: -10px;
}

.top-right {
  top: -10px;
  right: -10px;
  animation-delay: 0.3s;
}

.bottom-left {
  bottom: -10px;
  left: -10px;
  animation-delay: 0.4s;
}

.bottom-right {
  bottom: -10px;
  right: -10px;
  animation-delay: 0.5s;
}

/* Arrow indicator for dropdown */
.nav-panel::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 2rem;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  border-radius: 2px;
  z-index: -1;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
  border-left: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: -3px -3px 5px rgba(0, 0, 0, 0.02);
  animation: arrow-in 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  opacity: 0;
  transform: translateY(-5px) rotate(45deg);
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
  .nav-panel {
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    max-width: 100%;
    margin-top: 0.25rem;
    border-radius: 0.5rem;
  }
}

/* Enhanced dropdown animations */
.dropdown-content {
  opacity: 0;
  transform: translateY(-5px);
  animation: fade-in-down 0.4s ease forwards 0.1s;
  width: 100%;
  height: 100%;
  overflow: visible;
  position: relative;
  z-index: 1;
}

/* Animation keyframes */
@keyframes nav-in {
  0% {
    opacity: 0;
    transform: translateY(12px) scale(0.97);
    transform-origin: top;
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    transform-origin: top;
    filter: blur(0);
  }
}

@keyframes fade-in-down {
  from {
    opacity: 0;
    transform: translateY(-5px);
    filter: blur(3px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

@keyframes glow-pulse {
  0%,
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes decoration-in {
  from {
    opacity: 0;
    transform: scale(0);
  }
  to {
    opacity: 0.8;
    transform: scale(1);
  }
}

@keyframes arrow-in {
  from {
    opacity: 0;
    transform: translateY(-5px) rotate(45deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotate(45deg);
  }
}
</style>
