<script setup lang="ts">
import {
  DropdownMenuTrigger,
  type DropdownMenuTriggerProps,
  useForwardProps,
} from 'radix-vue';
import { cn } from '@/lib/utils';
import type { HTMLAttributes } from 'vue';

const props = defineProps<
  DropdownMenuTriggerProps & { class?: HTMLAttributes['class'] }
>();

const forwardedProps = useForwardProps(props);
</script>

<template>
  <DropdownMenuTrigger
    :class="cn('outline-none cursor-pointer', props.class)"
    v-bind="forwardedProps"
  >
    <slot />
  </DropdownMenuTrigger>
</template>
