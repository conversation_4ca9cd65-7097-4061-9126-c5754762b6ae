<script setup lang="ts">
import { cn } from '@/lib/utils';
import { ChevronDownIcon } from '@radix-icons/vue';
import {
  AccordionHeader,
  AccordionTrigger,
  type AccordionTriggerProps,
} from 'radix-vue';
import { computed, type HTMLAttributes } from 'vue';

const props = defineProps<
  AccordionTriggerProps & {
    class?: HTMLAttributes['class'];
    chevronClass?: HTMLAttributes['class'];
  }
>();

const delegatedProps = computed(() => {
  const { class: _, chevronClass, ...delegated } = props;

  return delegated;
});
</script>

<template>
  <AccordionHeader class="flex">
    <AccordionTrigger
      v-bind="delegatedProps"
      :class="
        cn(
          'flex flex-1 items-center justify-between py-4 text-sm font-medium transition-all [&[data-state=open]>svg]:rotate-180',
          props.class
        )
      "
    >
      <slot />
      <slot name="icon">
        <ChevronDownIcon
          :class="
            cn(
              'h-4 w-4 shrink-0 text-muted-foreground transition-transform duration-200',
              props.chevronClass
            )
          "
        />
      </slot>
    </AccordionTrigger>
  </AccordionHeader>
</template>
