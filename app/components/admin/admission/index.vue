<script setup lang="ts">
import { Loader2 } from 'lucide-vue-next';
import { toast } from 'vue-sonner';
import type { FileData } from '~/types/home';
import type { AdmissionResponse } from '~/types/admin/admissions';
import type { LinkButtonConfig } from '~/types/admin/card';

const form = reactive<AdmissionResponse>({
  id: 0,
  title: '',
  image: null,
  content: [],
});

const contentError = ref('');
const _isSubmitting = ref(false);

// Fetch admission data if param is provided
const { data: admissionData, status: admissionStatus } =
  useFetch<AdmissionResponse>('/api/admin/admissions');

// Watch for changes in fetched data and update form
watch(admissionData, (newData) => {
  console.log('newData', newData);
  if (newData) {
    form.id = newData.id || 0;
    form.title = newData.title || '';
    form.image = newData.image || null;
    form.content = newData.content || [];
  }
});

// Add a new content section
const addContentSection = () => {
  form.content.push({
    title: '',
    content: '',
    linkButton: null,
    file: null,
  });
};

// Remove a content section
const removeContentSection = (index: number) => {
  form.content.splice(index, 1);
  toast.success('Section removed successfully');
};

async function uploadPrimaryImage(file: File | undefined) {
  if (!file) {
    form.image = null;
    return;
  }

  try {
    const formVal = new FormData();
    formVal.append('files', file);
    formVal.append('prefix', 'admissions');
    formVal.append('title', 'Primary Image');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', { method: 'POST', body: formVal });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'admissions',
    };
    form.image = fileData;
  } catch (err) {
    console.error('Failed to upload primary file:', err);
    toast.error('Failed to upload primary image');
  }
}

const uploadFiles = async (
  files: { file: File; title?: string }[],
  sectionIndex: number
) => {
  if (!files.length) return;
  const formData = new FormData();

  // Add each file to FormData
  files.forEach(({ file, title }) => {
    formData.append('files', file);
    formData.append('prefix', 'admissions');
    formData.append('title', title || file.name);
    formData.append('type', file.type);
  });

  try {
    const response = await $fetch<FileData[]>('/api/blob/upload', {
      method: 'POST',
      body: formData,
    });

    if (response && Array.isArray(response)) {
      const uploadedFile = response[0];
      if (uploadedFile) {
        const fileData = {
          pathname: uploadedFile.pathname,
          title: uploadedFile.title,
          type: uploadedFile.type,
          prefix: 'admissions',
        };

        // Update the file in the specific content section
        if (form.content[sectionIndex]) {
          form.content[sectionIndex].file = fileData;
        }

        toast.success('File uploaded successfully');
      }
    }
  } catch (error) {
    console.error('Error uploading files:', error);
    toast.error('Failed to upload file');
  }
};

// Update file removal handler
function handleFileRemoval(sectionIndex: number) {
  if (form.content[sectionIndex]) {
    form.content[sectionIndex].file = null;
    toast.success('File removed successfully');
  }
}

function handleSectionFileTitleChange(
  newTitle: string | undefined,
  identifier: string | undefined,
  sectionIndex: number
) {
  const section = form.content[sectionIndex];
  // Ensure the section and its file property exist before updating
  if (section && section.file) {
    console.log(
      `Updating title for section ${sectionIndex + 1} file (ID: ${identifier}) to: ${newTitle}`
    );
    section.file.title = newTitle || ''; // Update the title in the parent component's reactive state
    // Optional: Provide user feedback
    // toast.info(`File title in Section ${sectionIndex + 1} updated.`);
  } else {
    console.warn(
      `Cannot update title: Section ${sectionIndex + 1} or its file data not found.`
    );
  }
}

const validateForm = () => {
  // Reset errors
  contentError.value = '';

  // Validate title
  if (!form.title || form.title.trim() === '') {
    toast.error('Title is required');
    return false;
  }

  // Validate content sections
  if (form.content.length === 0) {
    toast.error('At least one content section is required');
    return false;
  }

  for (let i = 0; i < form.content.length; i++) {
    const section = form.content[i];
    if (!section) continue;
    if (!section.title || section.title.trim() === '') {
      toast.error(`Section ${i + 1} title is required`);
      return false;
    }

    if (!section.content || section.content.trim() === '') {
      toast.error(`Section ${i + 1} content is required`);
      return false;
    }
  }

  return true;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  _isSubmitting.value = true;

  try {
    const method = admissionData.value ? 'PUT' : 'POST';
    const url = '/api/admin/admissions';
    let param: any = {
      title: form.title,
      image: form.image,
      content: form.content,
    };
    if (admissionData.value) {
      param = {
        id: admissionData.value.id,
        ...param,
      };
    }

    await $fetch(url, {
      method,
      body: param,
    });

    toast.success(
      admissionData.value
        ? 'Admission has been updated successfully'
        : 'Admission has been created successfully'
    );

    // Reset form if creating new admission
    if (!admissionData.value) {
      form.title = '';
      form.image = null;
      form.content = [];
    }
  } catch (error) {
    console.error('Error saving admission:', error);
    toast.error('Failed to save admission. Please try again.');
  } finally {
    _isSubmitting.value = false;
  }
};
</script>

<template>
  <div class="w-full">
    <div v-if="admissionStatus === 'pending'" class="flex justify-center py-8">
      <Loader2 class="h-4 w-4 animate-spin" />
    </div>
    <div v-else class="space-y-6">
      <!-- Title and Slug -->
      <div class="grid grid-cols-1 gap-4">
        <div class="space-y-2" name="title">
          <Label>Title<span class="text-destructive">*</span></Label>
          <Input
            v-model="form.title"
            placeholder="Enter title"
            :disabled="_isSubmitting"
          />
          <p class="text-sm text-gray-500">The title of the admission</p>
        </div>
      </div>

      <!-- Primary Image -->
      <div class="space-y-4">
        <AdminBaseDropzone
          :model-value="form.image"
          :disabled="_isSubmitting"
          :is-multiple-allowed="false"
          :file-types="['image']"
          title="Featured Image"
          description="Upload the featured image (required)"
          :existing-files="form.image ? [form.image] : []"
          @files-selected="
            (files) => uploadPrimaryImage(files ? files[0]?.file : undefined)
          "
          @title-changed="
            (newTitle, identifier) =>
              handleSectionFileTitleChange(newTitle, identifier, 0)
          "
          @file-removed="() => (form.image = null)"
        />
      </div>

      <!-- Content Sections -->
      <div class="space-y-6">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium">Content Sections</h3>
          <Button
            :disabled="_isSubmitting"
            type="button"
            @click="addContentSection"
          >
            Add Section
          </Button>
        </div>

        <div
          v-if="form.content.length === 0"
          class="text-center py-8 border border-dashed rounded-md"
        >
          <p class="text-gray-500">
            No content sections added. Click "Add Section" to begin.
          </p>
        </div>

        <div
          v-for="(section, index) in form.content"
          :key="index"
          class="border rounded-md p-4 space-y-4"
        >
          <div class="flex justify-between items-center">
            <h4 class="font-medium">Section {{ index + 1 }}</h4>
            <Button
              variant="destructive"
              size="sm"
              :disabled="_isSubmitting"
              @click="removeContentSection(index)"
            >
              Remove
            </Button>
          </div>

          <!-- Section Title -->
          <div class="space-y-2">
            <Label>Section Title<span class="text-destructive">*</span></Label>
            <Input
              v-model="section.title"
              placeholder="Enter section title"
              :disabled="_isSubmitting"
            />
          </div>

          <!-- Section Content -->
          <div class="space-y-2">
            <Label>Content<span class="text-destructive">*</span></Label>
            <AdminBaseTextEditor
              v-model="section.content"
              placeholder="Enter content here"
              :disabled="_isSubmitting"
            />
          </div>

          <!-- Section File -->
          <div class="space-y-4">
            <AdminBaseDropzone
              :model-value="section.file"
              :disabled="_isSubmitting"
              :enable-titles="true"
              :is-multiple-allowed="false"
              :file-types="['pdf', 'image', 'text', 'video', 'audio']"
              title="Section File"
              description="Upload a file for this section (optional)"
              :existing-files="section.file ? [section.file] : []"
              @files-selected="(files) => uploadFiles(files, index)"
              @file-removed="() => handleFileRemoval(index)"
            />
          </div>

          <!-- Link Button -->
          <div class="space-y-4">
            <h3 class="text-sm font-medium">Link Button</h3>
            <AdminTemplateButton
              v-model="section.linkButton"
              :disabled="_isSubmitting"
              :show-button="!!section.linkButton"
              @update:model-value="
                (value) => {
                  section.linkButton = value as LinkButtonConfig | null;
                }
              "
            />
          </div>
        </div>
      </div>

      <!-- Submit Buttons -->
      <div class="flex justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          class="w-24"
          :disabled="_isSubmitting"
          @click="
            () => {
              if (admissionData) {
                form.id = admissionData.id;
                form.title = admissionData.title || '';
                form.image = admissionData.image || null;
                form.content = admissionData.content || [];
              }
            }
          "
        >
          Cancel
        </Button>
        <Button :disabled="_isSubmitting" class="w-24" @click="handleSubmit">
          <Loader2 v-if="_isSubmitting" class="mr-2 h-4 w-4 animate-spin" />
          Save
        </Button>
      </div>
    </div>
  </div>
</template>
