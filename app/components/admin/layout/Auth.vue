<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';

// Define props with default values
const props = withDefaults(
  defineProps<{
    reverse?: boolean;
    title?: string;
    subtitle?: string;
    quote?: {
      text: string;
      author: string;
    };
  }>(),
  {
    title: 'College CMS',
    subtitle: 'Website management portal for educational institutions',
    quote: () => ({
      text: '"This CMS has streamlined how we publish academic content and keep our community informed about campus activities."',
      author: 'Prof<PERSON>, IT Department Chair',
    }),
  }
);

// Dark mode implementation
const isDarkMode = ref(false);

// Check for system preference and previously saved theme
onMounted(() => {
  // Check if user previously selected a theme
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme) {
    isDarkMode.value = savedTheme === 'dark';
    applyTheme(isDarkMode.value);
  } else {
    // Check system preference
    const prefersDark = window.matchMedia(
      '(prefers-color-scheme: dark)'
    ).matches;
    isDarkMode.value = prefersDark;
    applyTheme(prefersDark);
  }

  // Listen for system preference changes
  window
    .matchMedia('(prefers-color-scheme: dark)')
    .addEventListener('change', (e) => {
      if (!localStorage.getItem('theme')) {
        isDarkMode.value = e.matches;
        applyTheme(e.matches);
      }
    });
});

// Toggle dark mode
const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value;
  applyTheme(isDarkMode.value);
  localStorage.setItem('theme', isDarkMode.value ? 'dark' : 'light');
};

// Apply theme to document
const applyTheme = (dark: boolean) => {
  if (dark) {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }
};

// Watch for dark mode changes
watch(isDarkMode, (newValue) => {
  applyTheme(newValue);
});
</script>

<template>
  <div
    class="relative flex min-h-dvh flex-col lg:flex-row items-stretch overflow-hidden bg-background dark:bg-zinc-900"
    :class="{ 'lg:flex-row-reverse': reverse }"
  >
    <!-- Sidebar/Brand Panel -->
    <div
      class="relative hidden lg:flex h-full w-full lg:max-w-[45%] xl:max-w-[40%] flex-col justify-between bg-muted p-8 xl:p-12 text-white overflow-hidden"
    >
      <!-- Background gradient with improved contrast -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-primary-950 to-primary-800 dark:from-zinc-900 dark:to-zinc-800"
      >
        <!-- Pattern overlay to add texture -->
        <div class="absolute inset-0 opacity-10">
          <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <pattern
                id="smallGrid"
                width="8"
                height="8"
                patternUnits="userSpaceOnUse"
              >
                <path
                  d="M 8 0 L 0 0 0 8"
                  fill="none"
                  stroke="white"
                  stroke-width="0.5"
                />
              </pattern>
              <pattern
                id="grid"
                width="80"
                height="80"
                patternUnits="userSpaceOnUse"
              >
                <rect width="80" height="80" fill="url(#smallGrid)" />
                <path
                  d="M 80 0 L 0 0 0 80"
                  fill="none"
                  stroke="white"
                  stroke-width="1"
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>
        </div>

        <!-- Radial gradient for spotlight effect -->
        <div
          class="absolute inset-0 opacity-30 mix-blend-soft-light bg-[radial-gradient(circle_at_30%_30%,rgba(255,255,255,0.3),transparent_70%)]"
        ></div>

        <!-- Bottom gradient for text readability -->
        <div
          class="absolute bottom-0 left-0 right-0 h-60 bg-gradient-to-t from-black/50 to-transparent"
        ></div>
      </div>

      <!-- Brand section with better spacing and contrast -->
      <div class="relative z-20">
        <div class="flex items-center space-x-3 text-2xl font-bold">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="h-10 w-10"
          >
            <!-- College/education themed icon: graduation cap -->
            <path d="M12 2L2 7l10 5 10-5-10-5z" />
            <path d="M2 7v7" />
            <path d="M12 12v9" />
            <path d="M22 7v7" />
            <path d="M17 14l-5 3-5-3" />
          </svg>
          <span class="tracking-tight">{{ props.title }}</span>
        </div>

        <!-- Subtitle with improved visibility -->
        <p
          v-if="props.subtitle"
          class="relative z-20 text-white mt-4 max-w-sm text-lg font-medium"
        >
          {{ props.subtitle }}
        </p>
      </div>

      <!-- Testimonial quote with better readability -->
      <div
        class="relative z-20 mt-auto bg-black/20 backdrop-blur-sm rounded-xl p-6"
      >
        <blockquote class="space-y-3">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="45"
            height="36"
            class="mb-1 text-white/40"
            fill="currentColor"
          >
            <path
              d="M13.415.001C6.07 5.185.887 13.681.887 23.041c0 7.632 4.608 12.096 9.936 12.096 5.04 0 8.784-4.032 8.784-8.784 0-4.752-3.312-8.208-7.632-8.208-.864 0-2.016.144-2.304.288.72-4.896 5.328-10.656 9.936-13.536L13.415.001zm24.768 0c-7.2 5.184-12.384 13.68-12.384 23.04 0 7.632 4.608 12.096 9.936 12.096 4.896 0 8.784-4.032 8.784-8.784 0-4.752-3.456-8.208-7.776-8.208-.864 0-1.872.144-2.16.288.72-4.896 5.184-10.656 9.792-13.536L38.183.001z"
            ></path>
          </svg>
          <p class="text-xl font-light leading-relaxed text-white">
            {{ props.quote.text }}
          </p>
          <footer class="flex items-center mt-4">
            <span
              class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white font-semibold"
            >
              {{ props.quote.author.charAt(0) }}
            </span>
            <div class="ml-3">
              <p class="text-sm font-semibold text-white">
                {{ props.quote.author }}
              </p>
            </div>
          </footer>
        </blockquote>
      </div>
    </div>

    <!-- Mobile Brand (only visible on small screens) -->
    <div
      class="relative flex py-6 items-center justify-center bg-gradient-to-r from-primary-900 to-primary-700 dark:from-zinc-900 dark:to-zinc-800 lg:hidden"
    >
      <div class="flex items-center space-x-2 text-xl font-bold text-white">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="h-7 w-7"
        >
          <!-- College/education themed icon: graduation cap -->
          <path d="M12 2L2 7l10 5 10-5-10-5z" />
          <path d="M2 7v7" />
          <path d="M12 12v9" />
          <path d="M22 7v7" />
          <path d="M17 14l-5 3-5-3" />
        </svg>
        <span>{{ props.title }}</span>
      </div>
    </div>

    <!-- Content area with improved padding and structure -->
    <div
      class="w-full lg:w-[55%] xl:w-[60%] mx-auto flex flex-col justify-center py-8 px-4 sm:px-6 lg:px-12 xl:px-16 2xl:px-24 dark:bg-zinc-900 dark:text-white"
    >
      <!-- Dark mode toggle -->
      <div class="absolute top-4 right-4 sm:top-6 sm:right-6 z-50">
        <button
          class="p-2 rounded-full bg-gray-200 dark:bg-zinc-800 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
          aria-label="Toggle dark mode"
          tabindex="0"
          @click="toggleDarkMode"
          @keydown.enter="toggleDarkMode"
        >
          <!-- Sun icon for light mode -->
          <svg
            v-if="isDarkMode"
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
              clip-rule="evenodd"
            />
          </svg>
          <!-- Moon icon for dark mode -->
          <svg
            v-else
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"
            />
          </svg>
        </button>
      </div>

      <slot />
    </div>
  </div>
</template>

<style scoped>
.from-primary-950 {
  --tw-gradient-from: hsl(var(--primary) / 0.95);
}

.to-primary-800 {
  --tw-gradient-to: hsl(var(--primary) / 0.8);
}

.from-primary-900 {
  --tw-gradient-from: hsl(var(--primary) / 0.9);
}

.to-primary-700 {
  --tw-gradient-to: hsl(var(--primary) / 0.7);
}

/* Add dark mode transition */
:root {
  --transition-duration: 0.3s;
}

html {
  transition: background-color var(--transition-duration) ease;
}

.dark {
  color-scheme: dark;
}
</style>
