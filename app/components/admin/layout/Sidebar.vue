<script setup lang="ts">
import type { NavGroup, NavLink, NavSectionTitle } from '@/types/admin/nav';
import { navMenu, navMenuBottom } from '@/constants/admin/menus';
import { cn } from '@/lib/utils';
import { ChevronLeft, ChevronRight } from 'lucide-vue-next';

const store = useNavbar();
const { toggle } = store;

const { isOpen } = storeToRefs(store);

function resolveNavItemComponent(item: NavLink | NavGroup | NavSectionTitle) {
  if ('heading' in item) return resolveComponent('AdminLayoutNavHeading');
  else if ('children' in item) return resolveComponent('AdminLayoutNavGroup');

  return resolveComponent('AdminLayoutNavLink');
}
</script>

<template>
  <aside
    class="inset-y fixed left-0 z-20 hidden h-full flex-col items-center bg-background transition-width duration-300 md:flex"
    :class="cn('w-20', isOpen ? 'lg:w-56 xl:w-64' : 'lg:w-20')"
  >
    <div
      class="relative px-5 py-4 text-center"
      :class="cn('w-20', isOpen ? 'lg:w-56 xl:w-64' : 'lg:w-20')"
    >
      <!-- <div
        class="flex items-center gap-3"
        :class="
          cn(
            'justify-center lg:justify-start',
            isOpen ? 'lg:justify-start' : 'lg:justify-center'
          )
        "
      >
        <Button variant="outline" size="icon" aria-label="Home">
          <Triangle class="size-5 fill-foreground" />
        </Button>
        <span v-if="isOpen" class="hidden text-xl font-semibold lg:inline-block"
          >Dashboard</span
        >
      </div> -->

      <NuxtLink
        class="flex items-center gap-3"
        :class="
          cn(
            'justify-center lg:justify-start',
            isOpen ? 'lg:justify-start' : 'lg:justify-center'
          )
        "
        to="/"
      >
        <NuxtImg
          width="60"
          height="auto"
          class="size-8 h-auto object-scale-down"
          src="/img/db-college-logo.png"
          alt="Don Bosco College Mannuthy"
        />
        <div v-if="isOpen" class="hidden lg:flex flex-col text-left">
          <span class="text-sm lg:text-xs xl:text-sm text-primary font-semibold"
            >DON BOSCO COLLEGE</span
          >
          <span class="text-xs text-muted-foreground font-bold"
            >Admin Portal</span
          >
        </div>
      </NuxtLink>
      <Button
        variant="outline"
        class="absolute top-4 hidden size-6 rounded-full p-0 -right-3 lg:inline-flex"
        @click="toggle"
      >
        <component :is="isOpen ? ChevronLeft : ChevronRight" class="size-4" />
      </Button>
    </div>
    <ScrollArea class="w-full">
      <nav class="grid w-full gap-1 p-2">
        <component
          :is="resolveNavItemComponent(item)"
          v-for="(item, index) in navMenu"
          :key="index"
          :item="item"
        />
      </nav>
    </ScrollArea>
    <nav class="grid mt-auto w-full gap-1 p-2">
      <component
        :is="resolveNavItemComponent(item)"
        v-for="(item, index) in navMenuBottom"
        :key="index"
        :item="item"
      />
    </nav>
  </aside>
</template>

<style scoped></style>
