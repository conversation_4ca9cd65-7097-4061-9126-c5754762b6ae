<script setup lang="ts">
import {
  Loader2,
  Plus,
  Trash2,
  Edit2,
  Camera,
  CalendarIcon,
  ClockIcon,
  Search,
  PlusCircle,
  ImageIcon,
  Check,
} from 'lucide-vue-next';
import { toast } from 'vue-sonner';
import type { ClubCommitteeResponse } from '~/types/admin/clubs-and-committees';
import type { FileData, DepartmentEvent } from '~/types/home';
import type { Album } from '~~/shared/schema/album/get';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import {
  createClubSchema,
  type CreateClub,
} from '~~/shared/schema/clubs-and-committees/create';
import {
  updateClubSchema,
  type UpdateClub,
} from '~~/shared/schema/clubs-and-committees/update';

// State for list view
const isLoading = ref(false);
const clubsAndCommittees = ref<ClubCommitteeResponse[]>([]);
const filterType = ref<'all' | 'club' | 'committee'>('all');

// State for create/edit form
const showForm = ref(false);
const isEditMode = ref(false);
const currentItemId = ref<number | null>(null);
const isSubmitting = ref(false);

// Delete dialog state
const showDeleteDialog = ref(false);
const itemToDelete = ref<ClubCommitteeResponse | null>(null);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

// Use appropriate schema based on whether we're editing or creating
const validationSchema = computed(() =>
  toTypedSchema(isEditMode.value ? updateClubSchema : createClubSchema)
);

// Form validation
const overviewError = ref('');
const titleError = ref('');
const slugError = ref('');

const showLinkButton = ref(false);
const primaryImage = ref<FileData | null>(null);
const attachedFiles = ref<FileData[]>([]);

// Inside script setup section, after primaryImage declaration
const galleryList = ref<Album[]>([]);
const eventList = ref<DepartmentEvent[]>([]);
const isLoadingGalleries = ref(false);
const isLoadingEvents = ref(false);

// Gallery attachment dialog
const showGalleryDialog = ref(false);
const availableGalleries = ref<Album[]>([]);
const isLoadingAvailableGalleries = ref(false);
const gallerySearchQuery = ref('');
const selectedGallery = ref<Album | null>(null);

// Event attachment dialog
const showEventDialog = ref(false);
const availableEvents = ref<DepartmentEvent[]>([]);
const isLoadingAvailableEvents = ref(false);
const eventSearchQuery = ref('');
const selectedEvent = ref<DepartmentEvent | null>(null);

// Filtered galleries and events
const filteredGalleries = computed(() => {
  if (!gallerySearchQuery.value) return availableGalleries.value;
  const query = gallerySearchQuery.value.toLowerCase();
  return availableGalleries.value.filter((gallery) =>
    gallery.title.toLowerCase().includes(query)
  );
});

const filteredEvents = computed(() => {
  if (!eventSearchQuery.value) return availableEvents.value;
  const query = eventSearchQuery.value.toLowerCase();
  return availableEvents.value.filter((event) =>
    event.title.toLowerCase().includes(query)
  );
});

// Format event date
const formatEventDate = (date: number | null) => {
  if (!date) return 'Not specified';
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

// Load clubs and committees
const fetchClubsAndCommittees = async () => {
  isLoading.value = true;
  try {
    // Fetch data from API
    const response = await $fetch<ClubCommitteeResponse[]>(
      '/api/admin/clubs-and-committees'
    );
    clubsAndCommittees.value = response;
  } catch (error) {
    console.error('Error fetching clubs and committees:', error);
    toast.error('Failed to load clubs and committees');
  } finally {
    isLoading.value = false;
  }
};

// Filter clubs and committees by type
const filteredItems = computed(() => {
  if (filterType.value === 'all') {
    return clubsAndCommittees.value;
  }
  return clubsAndCommittees.value.filter(
    (item) => item.type === filterType.value
  );
});

// Initialize form with default values or from existing item
const initialValues = computed(() => {
  if (isEditMode.value && currentItemId.value) {
    const item = clubsAndCommittees.value.find(
      (i) => i.id === currentItemId.value
    );
    if (item) {
      return {
        title: item.title,
        overview: item.overview,
        slug: item.slug,
        image: item.image,
        type: item.type,
        attachments: item.attachments || [],
        linkButton: item.linkButton,
        galleries: item.galleries || [],
        events: item.events || [],
      };
    }
  }

  return {
    title: '',
    overview: '',
    slug: '',
    image: null,
    type: 'club' as const,
    attachments: [],
    linkButton: null,
    galleries: [],
    events: [],
  };
});

// Set up form with validation
const { values, setFieldValue, resetForm } = useForm<CreateClub | UpdateClub>({
  validationSchema,
  initialValues: initialValues.value,
});

// Reset form to initial state
const resetFormState = () => {
  resetForm();
  primaryImage.value = null;
  overviewError.value = '';
  titleError.value = '';
  slugError.value = '';
  showLinkButton.value = false;
  currentItemId.value = null;
  isEditMode.value = false;
  galleryList.value = [];
  eventList.value = [];
};

// Open create form
const handleCreateNew = () => {
  resetFormState();
  showForm.value = true;
};

// Update galleries for a club/committee
const updateGalleries = async (galleryIds: number[]) => {
  try {
    const currentGalleries: number[] = values.galleries || [];
    const updatedGalleries = [...currentGalleries, ...galleryIds];
    // update by removing duplicates
    const uniqueUpdatedGalleries = [...new Set(updatedGalleries)];
    setFieldValue('galleries', uniqueUpdatedGalleries);
    toast.success('Galleries updated successfully');
  } catch (error) {
    console.error('Error updating galleries:', error);
    toast.error('Failed to update galleries');
    throw error;
  }
};

// Update events for a club/committee
const updateEvents = async (eventIds: number[]) => {
  try {
    const currentEvents: number[] = values.events || [];
    const updatedEvents = [...currentEvents, ...eventIds];
    // update by removing duplicates
    const uniqueUpdatedEvents = [...new Set(updatedEvents)];
    setFieldValue('events', uniqueUpdatedEvents);
    toast.success('Events updated successfully');
  } catch (error) {
    console.error('Error updating events:', error);
    toast.error('Failed to update events');
    throw error;
  }
};

// Open gallery attachment dialog
const openGalleryDialog = async () => {
  try {
    isLoadingAvailableGalleries.value = true;
    const response = await $fetch<{ success: boolean; data: Album[] }>(
      '/api/admin/album'
    );

    if (response.success) {
      const currentIds = new Set(galleryList.value.map((g) => Number(g.id)));
      availableGalleries.value = response.data.filter(
        (gallery) => !currentIds.has(Number(gallery.id))
      );
    }
    showGalleryDialog.value = true;
  } catch (error) {
    console.error('Error fetching available galleries:', error);
    toast.error('Failed to fetch available galleries');
  } finally {
    isLoadingAvailableGalleries.value = false;
  }
};

// Open event attachment dialog
const openEventDialog = async () => {
  try {
    isLoadingAvailableEvents.value = true;
    const response = await $fetch<{
      success: boolean;
      data: DepartmentEvent[];
    }>('/api/admin/announcement/type/event', {
      query: {
        filterStatus: 'active',
      },
    });

    if (response.success) {
      const currentIds = new Set(eventList.value.map((e) => e.id));
      availableEvents.value = response.data.filter(
        (event) => !currentIds.has(event.id)
      );
    }
    showEventDialog.value = true;
  } catch (error) {
    console.error('Error fetching available events:', error);
    toast.error('Failed to fetch available events');
  } finally {
    isLoadingAvailableEvents.value = false;
  }
};

// Attach a gallery to a club/committee
const attachGallery = async () => {
  if (!selectedGallery.value) return;

  try {
    const updatedIds = [
      ...galleryList.value.map((g) => Number(g.id)),
      Number(selectedGallery.value.id),
    ];
    await updateGalleries(updatedIds);

    // Update local state directly instead of fetching
    galleryList.value = [...galleryList.value, selectedGallery.value];

    // Update the form value - use IDs array instead of objects
    setFieldValue(
      'galleries',
      galleryList.value.map((g) => Number(g.id))
    );

    showGalleryDialog.value = false;
    selectedGallery.value = null;
    gallerySearchQuery.value = '';
    toast.success('Gallery attached successfully');
  } catch (error) {
    console.error('Error attaching gallery:', error);
    toast.error('Failed to attach gallery');
  }
};

// Detach a gallery from a club/committee
const detachGallery = async (galleryId: string | number) => {
  if (!currentItemId.value) return;

  try {
    const updatedIds = galleryList.value
      .filter((g) => Number(g.id) !== Number(galleryId))
      .map((g) => Number(g.id));

    await updateGalleries(updatedIds);

    // Update local state directly instead of fetching
    galleryList.value = galleryList.value.filter(
      (g) => Number(g.id) !== Number(galleryId)
    );

    // Also update the form value - use IDs array instead of objects
    setFieldValue(
      'galleries',
      galleryList.value.map((g) => Number(g.id))
    );

    toast.success('Gallery detached successfully');
  } catch (error) {
    console.error('Error detaching gallery:', error);
    toast.error('Failed to detach gallery');
  }
};

// Attach an event to a club/committee
const attachEvent = async () => {
  if (!selectedEvent.value) return;

  try {
    const updatedIds = [
      ...eventList.value.map((e) => e.id),
      selectedEvent.value.id,
    ];
    await updateEvents(updatedIds);

    // Update local state directly instead of fetching
    eventList.value = [...eventList.value, selectedEvent.value];

    // Also update the form value - use IDs array instead of objects
    setFieldValue(
      'events',
      eventList.value.map((e) => e.id)
    );

    showEventDialog.value = false;
    selectedEvent.value = null;
    eventSearchQuery.value = '';
    toast.success('Event attached successfully');
  } catch (error) {
    console.error('Error attaching event:', error);
    toast.error('Failed to attach event');
  }
};

// Detach an event from a club/committee
const detachEvent = async (eventId: number) => {
  if (!currentItemId.value) return;

  try {
    const updatedIds = eventList.value
      .filter((e) => e.id !== eventId)
      .map((e) => e.id);

    await updateEvents(updatedIds);

    // Update local state directly instead of fetching
    eventList.value = eventList.value.filter((e) => e.id !== eventId);

    // Also update the form value - use IDs array instead of objects
    setFieldValue(
      'events',
      eventList.value.map((e) => e.id)
    );

    toast.success('Event detached successfully');
  } catch (error) {
    console.error('Error detaching event:', error);
    toast.error('Failed to detach event');
  }
};

// Open edit form
const handleEdit = async (item: ClubCommitteeResponse) => {
  resetFormState();

  // Set form values
  setFieldValue('title', item.title);
  setFieldValue('overview', item.overview);
  setFieldValue('slug', item.slug);
  setFieldValue('image', item.image);
  setFieldValue('type', item.type);
  setFieldValue('attachments', item.attachments || []);
  setFieldValue('linkButton', item.linkButton);

  // Set gallery and event IDs in the form
  setFieldValue('galleries', item.galleries || []);
  setFieldValue('events', item.events || []);

  // Set other state values
  primaryImage.value = item.image;
  showLinkButton.value = !!item.linkButton;
  currentItemId.value = item.id;
  isEditMode.value = true;
  showForm.value = true;

  // Load detailed gallery data if we have gallery IDs
  if (item.galleries && item.galleries.length > 0) {
    isLoadingGalleries.value = true;
    try {
      const response = await $fetch<{ success: boolean; data: Album[] }>(
        '/api/admin/album'
      );
      if (response.success) {
        // Filter to only include the galleries we need
        const galleryIdsSet = new Set(item.galleries.map((id) => Number(id)));
        galleryList.value = response.data.filter((gallery) =>
          galleryIdsSet.has(Number(gallery.id))
        );
      }
    } catch (error) {
      console.error('Error fetching gallery details:', error);
      toast.error('Failed to fetch gallery details');
    } finally {
      isLoadingGalleries.value = false;
    }
  } else {
    galleryList.value = [];
  }

  // Load detailed event data if we have event IDs
  if (item.events && item.events.length > 0) {
    isLoadingEvents.value = true;
    try {
      const response = await $fetch<{
        success: boolean;
        data: DepartmentEvent[];
      }>('/api/admin/announcement/type/event', {
        query: {
          filterStatus: 'all',
        },
      });
      if (response.success) {
        // Filter to only include the events we need
        const eventIdsSet = new Set(item.events.map((id) => Number(id)));
        eventList.value = response.data.filter((event) =>
          eventIdsSet.has(Number(event.id))
        );
      }
    } catch (error) {
      console.error('Error fetching event details:', error);
      toast.error('Failed to fetch event details');
    } finally {
      isLoadingEvents.value = false;
    }
  } else {
    eventList.value = [];
  }
};

// Show delete confirmation dialog
const openDeleteDialog = (item: ClubCommitteeResponse) => {
  itemToDelete.value = item;
  deleteState.value = 'idle';
  showDeleteDialog.value = true;
};

// Execute delete when confirmed
const handleDeleteConfirm = async () => {
  if (!itemToDelete.value) return;

  deleteState.value = 'loading';

  try {
    // Call delete API
    await $fetch(`/api/admin/clubs-and-committees/${itemToDelete.value.id}`, {
      method: 'DELETE',
    });

    // Remove item from local state
    clubsAndCommittees.value = clubsAndCommittees.value.filter(
      (item) => item.id !== itemToDelete.value?.id
    );

    deleteState.value = 'success';
    toast.success('Item deleted successfully');

    // Close dialog after success message is shown
    setTimeout(() => {
      showDeleteDialog.value = false;
      itemToDelete.value = null;
    }, 1500);
  } catch (error) {
    console.error('Error deleting item:', error);
    deleteState.value = 'error';
    toast.error('Failed to delete item');
  }
};

// Function to generate slug from title
const generateSlugFromTitle = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .replace(/[^\w-]+/g, '') // Remove special characters except underscores and hyphens
    .replace(/--+/g, '_') // Replace multiple hyphens with single underscore
    .replace(/^-+/, '') // Trim hyphens from start
    .replace(/-+$/, ''); // Trim hyphens from end
};

// Handle form submission
const handleFormSubmit = async () => {
  // const isButtonLinkValid: boolean =
  //   (values.linkButton?.externalLink?.length ?? 0) > 0 ||
  //   (values.linkButton?.internalLink?.length ?? 0) > 0;
  // //If linkButton is there, make sure that the link is also there
  // if (!isButtonLinkValid) {
  //   toast.error('Button link is required');
  //   return;
  // }

  isSubmitting.value = true;

  try {
    if (isEditMode.value && currentItemId.value) {
      // Update existing item
      const response = await $fetch(
        `/api/admin/clubs-and-committees/${currentItemId.value}`,
        {
          method: 'PUT',
          body: values,
        }
      );

      // Update the item in the local state
      const index = clubsAndCommittees.value.findIndex(
        (item) => item.id === currentItemId.value
      );

      if (index !== -1 && response.data) {
        clubsAndCommittees.value[index] = {
          ...clubsAndCommittees.value[index],
          ...response.data,
        } as ClubCommitteeResponse;
      }

      toast.success('Item updated successfully');
    } else {
      // Create new item
      const response = await $fetch('/api/admin/clubs-and-committees', {
        method: 'POST',
        body: values,
      });

      // CHECK IF SLUG IS UNIQUE
      if (response && response.data && response.data.id) {
        const slug = generateSlugFromTitle(values.title || '');
        if (clubsAndCommittees.value.some((item) => item.slug === slug)) {
          toast.error('Slug must be unique. Try changing the title.');
          return;
        }
      }

      // Add the new item to the local state with the returned ID
      if (response && response.data && response.data.id) {
        const newItem = {
          ...values,
          id: response.data.id,
          // Ensure required fields are set for ClubCommitteeResponse type
          overview: values.overview || '',
          slug: generateSlugFromTitle(values.title || ''),
          type: values.type || 'club',
          image: values.image || null,
          attachments: values.attachments || null,
          linkButton: values.linkButton || null,
          galleries: values.galleries || null,
          events: values.events || null,
        } as ClubCommitteeResponse;

        clubsAndCommittees.value.push(newItem);
        toast.success('Item created successfully');
      }
    }

    resetFormState();
    showForm.value = false;

    // Refresh the data to ensure we have the latest from the server
    fetchClubsAndCommittees();
  } catch (error) {
    console.error('Error saving item:', error);
    toast.error('Failed to save item');
  } finally {
    isSubmitting.value = false;
  }
};

// Upload primary image
async function uploadPrimaryImage(file: File | undefined) {
  if (!file) {
    primaryImage.value = null;
    setFieldValue('image', null);
    return;
  }

  try {
    // Upload file to server
    const formData = new FormData();
    formData.append('files', file);
    formData.append('prefix', 'club-and-committee');
    formData.append('title', file.name);

    const response = await $fetch<FileData[]>('/api/blob/upload', {
      method: 'POST',
      body: formData,
    });

    if (response && response.length > 0) {
      const fileData: FileData = response[0]!;
      primaryImage.value = fileData;
      setFieldValue('image', fileData);
    }
  } catch (err) {
    console.error('Failed to upload primary file:', err);
    toast.error('Failed to upload primary image');
  }
}

// Upload attachment files
const uploadFiles = async (
  files: { file: File; title?: string }[],
  isTitleEdit: boolean = false
) => {
  if (!files.length) return;
  const formData = new FormData();

  // Add each file to FormData
  files.forEach(({ file, title }) => {
    formData.append('files', file);
    formData.append('prefix', 'club-and-committee');
    formData.append('title', title || file.name);
    formData.append('type', file.type);
  });

  try {
    const response = await $fetch<FileData[]>('/api/blob/upload', {
      method: 'POST',
      body: formData,
    });

    if (response && Array.isArray(response)) {
      const uploadedFiles = response.map((file) => ({
        pathname: file.pathname,
        title: file.title,
        type: file.type,
        prefix: 'club-and-committee',
      }));
      // If this is a title edit, remove the existing file with the same title
      if (isTitleEdit) {
        attachedFiles.value = attachedFiles.value.filter(
          (existingFile) =>
            !uploadedFiles.some(
              (newFile) =>
                newFile.pathname.split('-')[0] ===
                existingFile.pathname?.split('-')[0]
            )
        );
      }

      // Append new files to existing ones
      attachedFiles.value = [...attachedFiles.value, ...uploadedFiles];
      setFieldValue('attachments', [...attachedFiles.value]);
      toast.success(`Successfully uploaded ${uploadedFiles.length} file(s)`);
    }
  } catch (error) {
    console.error('Error uploading files:', error);
    toast.error('Failed to upload files');
  }
};

// Remove attachment file
function handleFileRemoval(fileTitle: string) {
  const currentAttachments = values.attachments || [];
  const filteredAttachments = currentAttachments.filter(
    (file) => file.title !== fileTitle
  );
  setFieldValue('attachments', filteredAttachments);
  toast.success('File removed successfully');
}

// Handle link button updates
const handleLinkButtonUpdate = (value: any) => {
  if (!value || !value.style || !value.title) {
    setFieldValue('linkButton', null);
    return;
  }

  setFieldValue('linkButton', {
    style: value.style || 'primary',
    title: value.title,
    type: value.type || 'link',
    newTab: value.newTab || false,
    externalLink: value.externalLink || null,
    internalLink: value.internalLink || null,
    icon: value.icon || null,
  });
};

// Computed prop for link button to handle undefined case
const linkButtonValue = computed({
  get: () => values.linkButton || null,
  set: (val) => handleLinkButtonUpdate(val),
});

// Load initial data
onMounted(() => {
  fetchClubsAndCommittees();
});
</script>

<template>
  <div class="w-full">
    <!-- List View -->
    <div v-if="!showForm" class="space-y-6">
      <!-- Header and actions -->
      <div class="flex flex-wrap items-center justify-between gap-2">
        <h2 class="text-2xl font-bold tracking-tight">Clubs and Committees</h2>
        <div class="flex items-center gap-2">
          <!-- Replace regular select with Shadcn Select -->
          <Select v-model="filterType">
            <SelectTrigger class="w-[160px]">
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="club">Clubs</SelectItem>
              <SelectItem value="committee">Committees</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="default" @click="handleCreateNew">
            <Plus class="h-4 w-4 mr-1" />
            Add New
          </Button>
        </div>
      </div>

      <!-- Loading state -->
      <div v-if="isLoading" class="flex justify-center py-12">
        <Loader2 class="h-8 w-8 animate-spin text-gray-400" />
      </div>

      <!-- Empty state -->
      <div
        v-else-if="filteredItems.length === 0"
        class="p-12 flex flex-col items-center justify-center border rounded-lg bg-muted/50"
      >
        <p class="text-gray-500 mb-4">No clubs or committees found.</p>
        <Button variant="outline" @click="handleCreateNew">
          <Plus class="h-4 w-4 mr-1" />
          Create your first one
        </Button>
      </div>

      <!-- Items list -->
      <div v-else class="grid grid-cols-1 gap-4">
        <div
          v-for="item in filteredItems"
          :key="item.id"
          class="flex bg-card flex-col md:flex-row p-4 border rounded-lg hover:bg-muted/30 transition-colors duration-200 shadow-sm"
        >
          <!-- Image thumbnail -->
          <div
            class="w-full md:w-48 h-32 mb-4 md:mb-0 md:mr-4 overflow-hidden rounded-md shrink-0"
          >
            <NuxtImg
              v-if="item?.image?.pathname"
              :src="getPreviewUrl(item?.image?.pathname)"
              :alt="item.title"
              class="w-full h-full object-cover"
            />
            <div
              v-else
              class="w-full h-full bg-muted flex items-center justify-center text-muted-foreground"
            >
              No image
            </div>
          </div>

          <!-- Content -->
          <div class="flex-1">
            <div
              class="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-2"
            >
              <div>
                <h3 class="text-lg font-medium">{{ item.title }}</h3>
                <div class="flex items-center gap-2">
                  <Badge
                    :variant="item.type === 'club' ? 'default' : 'secondary'"
                    class="mt-1"
                  >
                    {{ item.type }}
                  </Badge>
                  <span class="text-xs text-gray-500 mt-1"
                    >Slug: {{ item.slug }}</span
                  >
                </div>
              </div>
              <div class="flex space-x-2">
                <Button size="sm" variant="outline" @click="handleEdit(item)">
                  <Edit2 class="h-4 w-4 mr-1" />
                  Edit
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  @click="openDeleteDialog(item)"
                >
                  <Trash2 class="h-4 w-4 mr-1" />
                  Delete
                </Button>
              </div>
            </div>
            <p class="text-sm text-gray-600 line-clamp-2">
              {{ item.overview }}
            </p>

            <!-- Additional info -->
            <div
              class="flex flex-wrap gap-x-4 gap-y-1 mt-3 text-xs text-gray-500"
            >
              <div v-if="item.attachments?.length">
                <span class="font-medium">Attachments:</span>
                {{ item.attachments.length }}
              </div>
              <div v-if="item.galleries?.length">
                <span class="font-medium">Galleries:</span>
                {{ item.galleries.length }}
              </div>
              <div v-if="item.events?.length">
                <span class="font-medium">Events:</span>
                {{ item.events.length }}
              </div>
              <div v-if="item.linkButton">
                <span class="font-medium">Link Button:</span>
                {{ item.linkButton.title }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Form View -->
    <div v-else class="space-y-6 bg-card p-6 rounded-lg">
      <!-- Form header -->
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold">
          {{ isEditMode ? 'Edit' : 'Create' }} Club/Committee
        </h2>
        <Button variant="outline" @click="showForm = false"> Cancel </Button>
      </div>

      <!-- Form using validation schema -->
      <Form
        :validation-schema="validationSchema"
        :initial-values="initialValues"
        class="space-y-6"
      >
        <!-- Form fields -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Title field -->
          <FormField v-slot="{ field, errorMessage }" name="title">
            <FormItem>
              <FormLabel
                >Title<span class="text-destructive">*</span></FormLabel
              >
              <FormControl>
                <Input
                  v-bind="field"
                  :model-value="field.value"
                  placeholder="Enter title"
                  :disabled="isSubmitting"
                  @update:model-value="
                    (value) => {
                      setFieldValue('title', value as string);
                      const slugVal = generateSlugFromTitle(value as string);
                      setFieldValue('slug', slugVal);
                    }
                  "
                />
              </FormControl>
              <FormMessage>{{ errorMessage }}</FormMessage>
              <p v-if="!errorMessage" class="text-sm text-gray-500">
                The name of the club or committee
              </p>
            </FormItem>
          </FormField>

          <!-- Type field - Replace with Shadcn Select -->
          <FormField v-slot="{ field, errorMessage }" name="type">
            <FormItem>
              <FormLabel>Type<span class="text-destructive">*</span></FormLabel>
              <Select
                :model-value="field.value"
                :disabled="isSubmitting"
                @update:model-value="
                  (value) =>
                    setFieldValue('type', value as 'club' | 'committee')
                "
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="club">Club</SelectItem>
                  <SelectItem value="committee">Committee</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage>{{ errorMessage }}</FormMessage>
              <p v-if="!errorMessage" class="text-sm text-gray-500">
                Select the type of organization
              </p>
            </FormItem>
          </FormField>
        </div>

        <!-- Image upload -->
        <div class="space-y-4">
          <AdminBaseDropzone
            :model-value="values.image"
            :disabled="isSubmitting"
            :is-multiple-allowed="false"
            :file-types="['image']"
            title="Primary Image"
            description="Upload the primary image for the club/committee"
            :existing-files="primaryImage ? [primaryImage] : []"
            @files-selected="
              (files) => uploadPrimaryImage(files ? files[0]?.file : undefined)
            "
            @file-removed="
              () => {
                primaryImage = null;
                setFieldValue('image', null);
              }
            "
          />
          <FormField v-slot="{ errorMessage }" name="image">
            <FormItem>
              <FormMessage>{{ errorMessage }}</FormMessage>
            </FormItem>
          </FormField>
        </div>

        <!-- Overview field -->
        <FormField v-slot="{ field, errorMessage }" name="overview">
          <FormItem>
            <FormLabel
              >Overview<span class="text-destructive">*</span></FormLabel
            >
            <FormControl>
              <AdminBaseTextEditor
                v-bind="field"
                :model-value="field.value"
                placeholder="Enter overview content"
                :disabled="isSubmitting"
                @update:model-value="
                  (value) => setFieldValue('overview', value as string)
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
            <p v-if="!errorMessage" class="text-sm text-gray-500">
              Provide a detailed description (minimum 100 characters)
            </p>
          </FormItem>
        </FormField>

        <!-- File attachments -->
        <div class="space-y-4">
          <AdminBaseDropzone
            :model-value="values.attachments"
            :disabled="isSubmitting"
            :is-multiple-allowed="true"
            :file-types="['pdf', 'image', 'text', 'video', 'audio']"
            title="Attachments"
            description="Upload related documents or files (optional)"
            :existing-files="values.attachments || []"
            @files-selected="(files) => uploadFiles(files)"
            @file-removed="handleFileRemoval"
          />
          <FormField v-slot="{ errorMessage }" name="attachments">
            <FormItem>
              <FormMessage>{{ errorMessage }}</FormMessage>
            </FormItem>
          </FormField>
        </div>

        <!-- Link Button -->
        <div class="space-y-4">
          <h3 class="text-sm font-medium">Link Button</h3>
          <AdminTemplateButton
            v-model="linkButtonValue"
            :disabled="isSubmitting"
            :show-button="showLinkButton"
            @update:show-button="showLinkButton = $event"
          />
          <FormField v-slot="{ errorMessage }" name="linkButton">
            <FormItem>
              <FormMessage>{{ errorMessage }}</FormMessage>
            </FormItem>
          </FormField>
        </div>

        <!-- Galleries & Events selection -->
        <div class="grid grid-cols-1 gap-6">
          <!-- Galleries Section -->
          <div class="space-y-4">
            <div class="flex items-center justify-between mb-2">
              <Label class="text-base font-medium">Galleries</Label>
              <Button
                variant="outline"
                size="sm"
                type="button"
                @click="openGalleryDialog"
              >
                <PlusCircle class="h-4 w-4 mr-2" />
                Attach Gallery
              </Button>
            </div>

            <!-- Loading State -->
            <div v-if="isLoadingGalleries" class="flex justify-center py-8">
              <div
                class="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"
              ></div>
            </div>

            <!-- Empty State -->
            <div
              v-else-if="!galleryList.length"
              class="flex flex-col items-center justify-center p-8 border-2 border-dashed rounded-lg"
            >
              <div class="rounded-full bg-primary/10 p-4 mb-4">
                <Camera class="h-8 w-8 text-primary" />
              </div>
              <h3 class="text-lg font-semibold mb-2">No Galleries Attached</h3>
              <p class="text-sm text-muted-foreground max-w-sm text-center">
                Attach galleries to display in the club/committee page.
              </p>
            </div>

            <!-- Gallery Grid -->
            <div
              v-else
              class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
            >
              <Card
                v-for="gallery in galleryList"
                :key="gallery.id"
                class="group relative overflow-hidden border-none bg-background shadow-sm hover:shadow-md transition-all duration-300"
              >
                <!-- Gallery Cover -->
                <div class="aspect-square bg-muted relative">
                  <div v-if="gallery.primaryImage" class="w-full h-full">
                    <img
                      :src="getPreviewUrl(gallery.primaryImage.pathname)"
                      :alt="gallery.title"
                      class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                  </div>
                  <div
                    v-else
                    class="w-full h-full flex flex-col items-center justify-center bg-muted/50"
                  >
                    <ImageIcon class="h-10 w-10 text-muted-foreground/50" />
                    <span class="text-xs text-muted-foreground mt-2"
                      >No images</span
                    >
                  </div>

                  <!-- Overlay Content -->
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  />
                  <div
                    class="absolute inset-x-0 bottom-0 p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20"
                  >
                    <div class="flex items-center justify-between">
                      <div class="max-w-[85%]">
                        <h3 class="font-semibold text-white truncate">
                          {{ gallery.title }}
                        </h3>
                        <p class="text-sm text-white/80">
                          {{ gallery.count || 0 }} photos
                        </p>
                      </div>
                      <Button
                        variant="secondary"
                        size="icon"
                        class="p-2 bg-white/20 hover:bg-white/30 backdrop-blur-sm"
                        @click="detachGallery(gallery.id)"
                      >
                        <Trash2 class="h-4 w-4 text-white" />
                      </Button>
                    </div>
                  </div>
                </div>

                <!-- Mobile/SEO Friendly Content -->
                <CardContent class="p-4 sm:hidden">
                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="font-semibold truncate">
                        {{ gallery.title }}
                      </h3>
                      <p class="text-sm text-muted-foreground">
                        {{ gallery.count || 0 }} photos
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      class="h-8 w-8 text-destructive"
                      @click="detachGallery(gallery.id)"
                    >
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <!-- Events Section -->
          <div class="space-y-4">
            <div class="flex items-center justify-between mb-2">
              <Label class="text-base font-medium">Events</Label>
              <Button
                variant="outline"
                size="sm"
                type="button"
                @click="openEventDialog"
              >
                <PlusCircle class="h-4 w-4 mr-2" />
                Attach Event
              </Button>
            </div>

            <!-- Loading State -->
            <div v-if="isLoadingEvents" class="flex justify-center py-8">
              <div
                class="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"
              ></div>
            </div>

            <!-- Empty State -->
            <div
              v-else-if="!eventList.length"
              class="flex flex-col items-center justify-center p-8 border-2 border-dashed rounded-lg"
            >
              <div class="rounded-full bg-primary/10 p-4 mb-4">
                <CalendarIcon class="h-8 w-8 text-primary" />
              </div>
              <h3 class="text-lg font-semibold mb-2">No Events Attached</h3>
              <p class="text-sm text-muted-foreground max-w-sm text-center">
                Attach events to display in the club/committee page.
              </p>
            </div>

            <!-- Events List -->
            <div v-else class="grid gap-4">
              <Card
                v-for="event in eventList"
                :key="event.id"
                class="group transition-all hover:shadow-md"
              >
                <CardHeader
                  class="flex flex-row items-center justify-between pb-2 space-y-0"
                >
                  <CardTitle class="text-base font-semibold">
                    {{ event.title }}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="icon"
                    class="text-destructive"
                    @click="detachEvent(event.id)"
                  >
                    <Trash2 class="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent class="space-y-3">
                  <div class="text-sm text-muted-foreground line-clamp-2">
                    {{ event.content }}
                  </div>
                  <div class="flex flex-wrap gap-4 text-sm">
                    <Badge :variant="event.isActive ? 'default' : 'secondary'">
                      {{ event.isActive ? 'Active' : 'Inactive' }}
                    </Badge>
                    <Badge variant="outline"
                      >Priority: {{ event.priority }}</Badge
                    >
                  </div>
                  <div
                    class="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-muted-foreground"
                  >
                    <div class="flex items-center gap-2">
                      <CalendarIcon class="h-4 w-4" />
                      <span
                        >Event Date:
                        {{ formatEventDate(event.eventDate) }}</span
                      >
                    </div>
                    <div class="flex items-center gap-2">
                      <ClockIcon class="h-4 w-4" />
                      <span
                        >Scheduled:
                        {{ formatEventDate(event.scheduledAt) }}</span
                      >
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        <!-- Form actions -->
        <div class="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            class="w-24"
            :disabled="isSubmitting"
            @click="showForm = false"
          >
            Cancel
          </Button>
          <Button
            :disabled="isSubmitting"
            class="w-24"
            type="button"
            @click="handleFormSubmit"
          >
            <Loader2 v-if="isSubmitting" class="mr-2 h-4 w-4 animate-spin" />
            {{ isEditMode ? 'Update' : 'Create' }}
          </Button>
        </div>
      </Form>
    </div>

    <!-- Delete Confirmation Dialog -->
    <AdminMenuDeleteDialog
      :is-open="showDeleteDialog"
      :is-submitting="deleteState === 'loading'"
      :delete-state="deleteState"
      :menu-title="itemToDelete?.title || ''"
      :description="`Are you sure you want to delete the ${itemToDelete?.type || ''} '${itemToDelete?.title || ''}'? This action cannot be undone.`"
      @update:is-open="showDeleteDialog = $event"
      @confirm="handleDeleteConfirm"
    />

    <!-- Gallery Attachment Dialog -->
    <Dialog :open="showGalleryDialog" @update:open="showGalleryDialog = false">
      <DialogContent class="sm:max-w-[900px]">
        <DialogHeader>
          <DialogTitle> Attach Gallery </DialogTitle>
          <DialogDescription>
            Select a gallery to attach to this club/committee. You can search
            galleries by title.
          </DialogDescription>
        </DialogHeader>

        <!-- Search Input -->
        <div class="relative">
          <Search
            class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"
          />
          <Input
            v-model="gallerySearchQuery"
            class="pl-9"
            placeholder="Search galleries by title..."
          />
        </div>
        <div class="grid gap-4 py-4 max-h-[500px] overflow-y-auto">
          <!-- Loading State -->
          <div
            v-if="isLoadingAvailableGalleries"
            class="flex justify-center py-8"
          >
            <div
              class="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"
            ></div>
          </div>

          <!-- Empty State -->
          <div
            v-else-if="!filteredGalleries.length"
            class="flex flex-col items-center justify-center py-8 text-center"
          >
            <div class="rounded-full bg-muted p-3 mb-4">
              <ImageIcon class="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 class="font-semibold mb-1">No Galleries Available</h3>
            <p class="text-sm text-muted-foreground">
              {{
                gallerySearchQuery
                  ? 'No galleries match your search.'
                  : 'Create new galleries in the gallery management section.'
              }}
            </p>
          </div>

          <!-- Gallery Grid -->
          <div
            v-else
            class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 px-4"
          >
            <Card
              v-for="gallery in filteredGalleries"
              :key="gallery.id"
              class="group relative overflow-hidden border-none bg-background shadow-sm hover:shadow-md transition-all duration-300"
              :class="{
                'ring-2 ring-primary ring-offset-2':
                  selectedGallery?.id === gallery.id,
                'hover:ring-2 hover:ring-primary/50 hover:ring-offset-2':
                  selectedGallery?.id !== gallery.id,
              }"
              @click="selectedGallery = gallery"
            >
              <!-- Gallery Cover -->
              <div class="aspect-square bg-muted relative">
                <div v-if="gallery.primaryImage" class="w-full h-full">
                  <img
                    :src="getPreviewUrl(gallery.primaryImage.pathname)"
                    :alt="gallery.title"
                    class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                </div>
                <div
                  v-else
                  class="w-full h-full flex flex-col items-center justify-center bg-muted/50"
                >
                  <ImageIcon class="h-10 w-10 text-muted-foreground/50" />
                  <span class="text-xs text-muted-foreground mt-2"
                    >No images</span
                  >
                </div>

                <!-- Selection Indicator -->
                <div
                  v-if="selectedGallery?.id === gallery.id"
                  class="absolute top-2 right-2 h-6 w-6 bg-primary rounded-full flex items-center justify-center"
                >
                  <Check class="h-4 w-4 text-primary-foreground" />
                </div>
              </div>

              <CardContent class="p-4">
                <h3 class="font-semibold truncate mb-1">{{ gallery.title }}</h3>
                <p class="text-sm text-muted-foreground">
                  {{ gallery.count || 0 }} photos
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showGalleryDialog = false">
            Cancel
          </Button>
          <Button :disabled="!selectedGallery" @click="attachGallery">
            Attach Selected Gallery
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Event Attachment Dialog -->
    <Dialog :open="showEventDialog" @update:open="showEventDialog = false">
      <DialogContent class="sm:max-w-[900px]">
        <DialogHeader>
          <DialogTitle>Attach Event</DialogTitle>
          <DialogDescription>
            Select an event to attach to this club/committee. You can search
            events by title.
          </DialogDescription>
        </DialogHeader>

        <!-- Search Input -->
        <div class="relative">
          <Search
            class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"
          />
          <Input
            v-model="eventSearchQuery"
            class="pl-9"
            placeholder="Search events by title..."
          />
        </div>

        <div class="grid gap-4 py-4 max-h-[500px] overflow-y-auto">
          <!-- Loading State -->
          <div v-if="isLoadingAvailableEvents" class="flex justify-center py-8">
            <div
              class="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"
            ></div>
          </div>

          <!-- Empty State -->
          <div
            v-else-if="!filteredEvents.length"
            class="flex flex-col items-center justify-center py-8 text-center"
          >
            <div class="rounded-full bg-muted p-3 mb-4">
              <CalendarIcon class="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 class="font-semibold mb-1">No Events Available</h3>
            <p class="text-sm text-muted-foreground">
              {{
                eventSearchQuery
                  ? 'No events match your search.'
                  : 'Create new events in the event management section.'
              }}
            </p>
          </div>

          <!-- Events Grid -->
          <div v-else class="grid gap-4 px-4">
            <Card
              v-for="event in filteredEvents"
              :key="event.id"
              class="group transition-all hover:shadow-md cursor-pointer"
              :class="{
                'ring-2 ring-primary ring-offset-2':
                  selectedEvent?.id === event.id,
                'hover:ring-2 hover:ring-primary/50 hover:ring-offset-2':
                  selectedEvent?.id !== event.id,
              }"
              @click="selectedEvent = event"
            >
              <CardHeader class="pb-2">
                <div class="flex items-center justify-between">
                  <CardTitle class="text-base font-semibold">
                    {{ event.title }}
                  </CardTitle>
                  <Badge :variant="event.isActive ? 'default' : 'secondary'">
                    {{ event.isActive ? 'Active' : 'Inactive' }}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent class="space-y-3">
                <p class="text-sm text-muted-foreground line-clamp-2">
                  {{ event.content }}
                </p>
                <div class="flex flex-wrap gap-2 text-sm">
                  <Badge variant="outline"
                    >Priority: {{ event.priority }}</Badge
                  >
                  <span class="text-muted-foreground">
                    {{ formatEventDate(event.eventDate) }}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showEventDialog = false">
            Cancel
          </Button>
          <Button :disabled="!selectedEvent" @click="attachEvent">
            Attach Selected Event
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>
