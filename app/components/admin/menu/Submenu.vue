<script setup lang="ts">
import type { MenuWithRelations } from '~~/server/database/tables/menu';
import { ExternalLink, Plus, PlusCircle, Trash2, Save } from 'lucide-vue-next';
import { toast } from 'vue-sonner';
import { watch, onMounted } from 'vue';

const props = defineProps<{
  menuData: MenuWithRelations;
}>();

const emit = defineEmits<{
  (e: 'refresh'): void;
}>();
const isDialogOpen = ref(false);
const isSubmitting = ref(false);
const showDeleteDialog = ref(false);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');
const submenuToDelete = ref<MenuWithRelations | null>(null);
const updatingPriority = ref<Record<number, boolean>>({});
const priorities = ref<Record<number, number>>({});

// Ensure all submenus have a priority value when mounted
onMounted(() => {
  initializePriorities();
});

// Watch for changes in menuData.submenus
watch(
  () => props.menuData.submenus,
  () => {
    initializePriorities();
  },
  { immediate: true }
);

// Initialize priority values from submenus
function initializePriorities() {
  if (!props.menuData?.submenus) return;

  props.menuData.submenus.forEach((submenu: MenuWithRelations) => {
    // Initialize if not already set
    if (priorities.value[submenu.id] === undefined) {
      priorities.value[submenu.id] = submenu.priority ?? 0;
    }
  });
}

async function handleSubmit(values: any) {
  isSubmitting.value = true;
  const toastId = toast.loading('Creating submenu...');

  try {
    const response = await $fetch('/api/admin/menu/add-submenu', {
      method: 'POST',
      body: values,
    });

    if (response) {
      toast.success('Submenu created successfully', { id: toastId });
      isDialogOpen.value = false;
      emit('refresh');
    }
  } catch (err: any) {
    const errorMessage =
      err.data?.message || err.message || 'Failed to create submenu';
    toast.error(errorMessage, { id: toastId });
  } finally {
    isSubmitting.value = false;
  }
}

async function confirmDelete(submenu: MenuWithRelations) {
  submenuToDelete.value = submenu;
  showDeleteDialog.value = true;
}

async function handleDelete() {
  if (!submenuToDelete.value) return;

  deleteState.value = 'loading';

  try {
    await $fetch(`/api/admin/menu/remove?id=${submenuToDelete.value.id}`, {
      method: 'DELETE',
    });
    deleteState.value = 'success';

    // Close dialog and refresh after a short delay
    setTimeout(() => {
      showDeleteDialog.value = false;
      deleteState.value = 'idle';
      submenuToDelete.value = null;
      emit('refresh');
    }, 1000);
  } catch (err: any) {
    deleteState.value = 'error';
    toast.error(err.message || 'Failed to delete submenu');
  }
}

async function updatePriority(id: number) {
  const priority = priorities.value[id];
  if (priority === undefined || isNaN(priority) || priority < 0) return;

  updatingPriority.value[id] = true;
  const toastId = toast.loading('Updating priority...');

  try {
    await $fetch('/api/admin/menu/update-priority', {
      method: 'POST',
      body: { id, priority },
    });

    toast.success('Priority updated successfully', { id: toastId });
    emit('refresh');
  } catch (err: any) {
    const errorMessage =
      err.data?.message || err.message || 'Failed to update priority';
    toast.error(errorMessage, { id: toastId });
  } finally {
    updatingPriority.value[id] = false;
  }
}
</script>

<template>
  <Card>
    <CardHeader class="flex flex-row items-center justify-between">
      <div>
        <CardTitle>Submenu Management</CardTitle>
        <CardDescription> Manage and organize submenus </CardDescription>
      </div>
      <Button variant="outline" @click="isDialogOpen = true">
        <PlusCircle class="mr-2 h-4 w-4" />
        Add Submenu
      </Button>
    </CardHeader>
    <CardContent>
      <div v-if="menuData.submenus?.length" class="space-y-4">
        <div
          v-for="submenu in menuData.submenus"
          :key="submenu.id"
          class="flex items-center justify-between border rounded-md p-4"
        >
          <div class="flex items-center gap-2">
            <span>{{ submenu.title }}</span>
            <Badge variant="secondary">
              {{ submenu?.slug?.length ? `/${submenu.slug}` : 'No route path' }}
            </Badge>
          </div>
          <div class="flex items-center gap-2">
            <Badge :variant="submenu.isActive ? 'default' : 'destructive'">
              {{ submenu.isActive ? 'Active' : 'Inactive' }}
            </Badge>
            <div class="flex items-center gap-2 ml-4">
              <span class="text-xs text-muted-foreground"> Priority </span>
              <div class="flex items-center gap-1">
                <input
                  v-model.number="priorities[submenu.id]"
                  type="number"
                  class="w-16 h-8 rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  min="0"
                />
                <Button
                  size="sm"
                  variant="outline"
                  class="h-8 px-2"
                  :disabled="updatingPriority[submenu.id]"
                  @click="updatePriority(submenu.id)"
                >
                  <Save v-if="!updatingPriority[submenu.id]" class="h-4 w-4" />
                  <span v-else class="h-4 w-4 animate-spin">↻</span>
                </Button>
              </div>
            </div>
            <NuxtLink
              :to="`/admin/menus/menu-item?id=${submenu.id}`"
              target="_blank"
            >
              <Button class="ml-2" variant="ghost" size="sm">
                <ExternalLink class="h-4 w-4" />
              </Button>
            </NuxtLink>
            <Button
              variant="ghost"
              size="sm"
              @click.stop="confirmDelete(submenu)"
            >
              <Trash2 class="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      <div v-else class="text-center py-8">
        <div
          class="rounded-full bg-background border border-primary p-3 mx-auto w-fit mb-4"
        >
          <Plus class="h-6 w-6 text-primary" />
        </div>
        <h3 class="font-semibold mb-2">No Submenus Found</h3>
        <p class="text-muted-foreground mb-4">
          Create submenus to organize your content.
        </p>
        <Button variant="outline" @click="isDialogOpen = true">
          <PlusCircle class="mr-2 h-4 w-4" />
          Add Submenu
        </Button>
      </div>
    </CardContent>

    <!-- Add Submenu Dialog -->
    <Dialog :open="isDialogOpen" @update:open="isDialogOpen = false">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Submenu</DialogTitle>
          <DialogDescription>
            Create a new submenu under this menu.
          </DialogDescription>
        </DialogHeader>

        <AdminMenuSubmenuForm
          :is-submitting="isSubmitting"
          :parent-id="menuData.id"
          @submit="handleSubmit"
          @cancel="isDialogOpen = false"
        />
      </DialogContent>
    </Dialog>

    <!-- Add Delete Dialog -->
    <AdminMenuDeleteDialog
      v-if="submenuToDelete"
      v-model:is-open="showDeleteDialog"
      :is-submitting="deleteState === 'loading'"
      :delete-state="deleteState"
      :menu-title="submenuToDelete.title"
      :description="
        submenuToDelete
          ? `Are you sure you want to delete submenu '${submenuToDelete.title}'? This action cannot be undone.\n All submenus and featured items will be deleted.`
          : ''
      "
      @confirm="handleDelete"
    />
  </Card>
</template>
