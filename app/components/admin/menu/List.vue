<!-- components/menu/MenuList.vue -->
<script setup lang="ts">
import type { Menu } from '~~/server/database/tables/menu';
import { Plus, PlusCircle } from 'lucide-vue-next';

defineProps<{
  menus: Menu[];
}>();

const emit = defineEmits<{
  (e: 'addMenu'): void;
  (e: 'menuClick', menu: Menu): void;
}>();
</script>

<template>
  <!-- Empty State -->

  <div
    v-if="menus.length === 0"
    class="flex flex-col items-center justify-center py-12 text-center"
  >
    <div
      class="rounded-full bg-background border border-primary p-3 mx-auto w-fit mb-4"
    >
      <Plus class="h-6 w-6 text-primary" />
    </div>
    <h3 class="text-lg font-semibold mb-2">No menus found</h3>
    <p class="text-muted-foreground mb-4">
      Create your first menu by clicking the "Add Menu" button.
    </p>
    <Button variant="outline" @click="emit('addMenu')">
      <PlusCircle class="h-4 w-4" />
      Add Menu
    </Button>
  </div>

  <!-- Menu Grid -->
  <div
    v-else
    class="grid gap-4 lg:grid-cols-3 md:grid-cols-2 xl:grid-cols-4 md:gap-8"
  >
    <AdminMenuCard
      v-for="menu in menus"
      :key="menu.id"
      :menu="menu"
      @click="emit('menuClick', menu)"
    />
  </div>
</template>
