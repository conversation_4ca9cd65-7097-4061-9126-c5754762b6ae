<script setup lang="ts">
import type { Menu } from '~~/server/database/tables/menu';
import { ExternalLink } from 'lucide-vue-next';

defineProps<{
  menu: Menu;
}>();

const emit = defineEmits<{
  (e: 'click', menu: Menu): void;
}>();
</script>

<template>
  <Card
    class="group transition-all !cursor-pointer hover:shadow-md"
    @click="emit('click', menu)"
  >
    <CardHeader
      class="flex flex-row items-center justify-between pb-2 space-y-0"
    >
      <CardTitle class="text-base font-semibold group-hover:text-primary">
        {{ menu.title }}
      </CardTitle>
      <div class="flex items-center gap-2">
        <ExternalLink
          class="h-4 w-4 text-muted-foreground transition-transform group-hover:translate-x-1"
        />
      </div>
    </CardHeader>
    <CardContent class="space-y-3">
      <span class="flex items-center gap-1 text-sm text-muted-foreground">
        {{
          menu?.slug && menu.slug?.length > 0 ? `/${menu.slug}` : 'No route set'
        }}
      </span>
      <div class="flex items-center justify-between rounded-lg bg-muted/50 p-2">
        <span class="text-sm font-medium">Route Enabled:</span>
        <Badge :variant="menu.routeEnabled ? 'default' : 'secondary'">
          {{ menu.routeEnabled ? 'Yes' : 'No' }}
        </Badge>
      </div>
      <div class="flex items-center justify-between rounded-lg bg-muted/50 p-2">
        <span class="text-sm font-medium">Status:</span>
        <Badge :variant="menu.isActive ? 'default' : 'secondary'">
          {{ menu.isActive ? 'Active' : 'Inactive' }}
        </Badge>
      </div>
    </CardContent>
  </Card>
</template>
