<script setup lang="ts">
import { Check, Loader2 } from 'lucide-vue-next';

const props = defineProps<{
  isOpen: boolean;
  isSubmitting: boolean;
  type?: 'menu' | 'notice' | 'update' | 'event';
  menuTitle: string;
  description?: string;
  deleteState: 'idle' | 'loading' | 'success' | 'error';
}>();

const emit = defineEmits<{
  (e: 'update:isOpen', value: boolean): void;
  (e: 'confirm'): void;
}>();

// Prevent closing dialog during deletion
function handleClose(value: boolean) {
  if (props.deleteState === 'loading' || props.deleteState === 'success')
    return;
  emit('update:isOpen', value);
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="handleClose">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>
          Delete
          {{
            type === 'notice'
              ? 'Notice'
              : type === 'update'
                ? 'Update'
                : type === 'event'
                  ? 'Event'
                  : 'Menu'
          }}
        </DialogTitle>
        <DialogDescription
          :class="{
            'text-destructive':
              deleteState !== 'success' && deleteState !== 'error',
            'text-green-600': deleteState === 'success',
          }"
        >
          <template v-if="deleteState === 'success'">
            {{
              type === 'notice'
                ? `Notice ${menuTitle} `
                : type === 'update'
                  ? `Update ${menuTitle} `
                  : type === 'event'
                    ? `Event ${menuTitle} `
                    : `Menu ${menuTitle} `
            }}
            has been successfully deleted.
          </template>
          <template v-else-if="deleteState === 'error'">
            An error occurred while deleting the
            {{
              type === 'notice'
                ? 'notice'
                : type === 'update'
                  ? 'update'
                  : type === 'event'
                    ? 'event'
                    : 'menu'
            }}. Try again.
          </template>
          <template v-else-if="deleteState === 'loading'">
            Deleting
            {{
              type === 'notice'
                ? 'Notice'
                : type === 'update'
                  ? 'Update'
                  : type === 'event'
                    ? 'Event'
                    : 'Menu'
            }}...
          </template>
          <template v-else>
            {{
              description ||
              `Are you sure you want to delete "${
                type === 'notice'
                  ? 'notice'
                  : type === 'update'
                    ? 'update'
                    : type === 'event'
                      ? 'event'
                      : menuTitle
              }"? This action cannot be undone.\n All submenus and
            featured items will be deleted.`
            }}
          </template>
        </DialogDescription>
      </DialogHeader>
      <DialogFooter class="gap-2 sm:gap-0">
        <Button
          variant="ghost"
          :disabled="deleteState === 'loading' || deleteState === 'success'"
          @click="handleClose(false)"
        >
          Cancel
        </Button>
        <Button
          variant="destructive"
          :disabled="deleteState === 'loading' || deleteState === 'success'"
          @click="emit('confirm')"
        >
          <template v-if="deleteState === 'loading'">
            <Loader2 class="mr-2 h-4 w-4 animate-spin" />
            Deleting...
          </template>
          <template v-else-if="deleteState === 'success'">
            <Check class="mr-2 h-4 w-4" />
            Deleted
          </template>
          <template v-else>
            Delete
            {{
              type === 'notice'
                ? 'Notice'
                : type === 'update'
                  ? 'Update'
                  : type === 'event'
                    ? 'Event'
                    : 'Menu'
            }}
          </template>
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
