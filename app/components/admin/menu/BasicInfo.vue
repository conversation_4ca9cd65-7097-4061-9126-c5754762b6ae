// components/menu/MenuBasicInfo.vue
<script setup lang="ts">
import type { MenuWithRelations } from '~~/server/database/tables/menu';
import { Edit, Save, Trash2, X } from 'lucide-vue-next';
import { Form, useForm } from 'vee-validate';
import { z } from 'zod';
import { toTypedSchema } from '@vee-validate/zod';

const props = defineProps<{
  menuData: MenuWithRelations;
  isEditing: boolean;
  isSubmitting: boolean;
  formValues: any;
  isMenu: boolean;
}>();

const emit = defineEmits<{
  (e: 'save', values: any): void;
  (e: 'toggleEdit'): void;
  (e: 'delete'): void;
}>();

const menuSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  menuName: z.string().optional(),
  isActive: z.boolean(),
  slug: z.string().optional(),
});

const formSchema = toTypedSchema(menuSchema);

const { values: formValues } = useForm<typeof formSchema>({
  initialValues: props.formValues,
  validationSchema: formSchema,
});
</script>

<template>
  <Form
    v-slot="{ setFieldValue: formSlotSetFieldValue, values: currentValues }"
    :validation-schema="formSchema"
    :initial-values="formValues"
  >
    <Card>
      <CardHeader class="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription class="mt-1 text-sm">
            Configure the basic settings for this menu item
          </CardDescription>
        </div>
        <div class="flex gap-2">
          <Button
            v-if="!isEditing"
            variant="outline"
            @click="emit('toggleEdit')"
          >
            <Edit class="h-4 w-4" />
            Edit
          </Button>
          <Button
            v-else
            variant="outline"
            type="button"
            @click="emit('toggleEdit')"
          >
            <X class="h-4 w-4" />
            Cancel
          </Button>
          <Button
            v-if="isEditing"
            type="button"
            variant="destructive"
            @click="emit('delete')"
          >
            <Trash2 class="h-4 w-4" />
            Delete
          </Button>
        </div>
      </CardHeader>
      <CardContent class="space-y-4">
        <!-- Title Field -->
        <FormField v-slot="{ field }" name="title" :validate-on-blur="true">
          <FormItem>
            <FormLabel>Title</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="field.value"
                placeholder="Enter title"
                :disabled="!isEditing"
                @update:model-value="
                  (value) => {
                    formSlotSetFieldValue('title', value);
                  }
                "
              />
            </FormControl>
            <FormDescription>
              This is the title that will be displayed on the page
            </FormDescription>
            <FormMessage />
          </FormItem>
        </FormField>
        <!-- Menu Name Field -->
        <FormField v-slot="{ field }" name="menuName">
          <FormItem>
            <FormLabel>Menu Name</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="field.value"
                placeholder="Enter menu name"
                :disabled="!isEditing"
                @update:model-value="
                  (value) => {
                    formSlotSetFieldValue('menuName', value);
                  }
                "
              />
            </FormControl>
            <FormDescription>
              A shorter name used to show in the menus. If not specified, the
              title will be used instead.
            </FormDescription>
            <FormMessage />
          </FormItem>
        </FormField>

        <!-- Status Field -->
        <FormField v-slot="{ field }" name="isActive">
          <FormItem
            class="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"
          >
            <div class="space-y-0.5">
              <FormLabel>Status</FormLabel>
              <FormDescription> Enable or disable this menu </FormDescription>
            </div>
            <FormControl>
              <Switch
                id="menu-item-status"
                :disabled="!isEditing"
                :checked="field.value"
                @update:checked="
                  (() => {
                    const newValue = !field.value;
                    formSlotSetFieldValue('isActive', newValue);
                  })()
                "
              />
            </FormControl>
          </FormItem>
        </FormField>

        <!-- Route Enabled Field -->
        <!-- <FormField name="routeEnabled">
          <FormItem
            class="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"
          >
            <div class="space-y-0.5">
              <FormLabel>Enable Route</FormLabel>
              <FormDescription> Enable routing for this menu </FormDescription>
            </div>
            <FormControl>
              <Switch
                id="menu-item-route-enabled"
                :disabled="true"
                :checked="isMenu ? false : true"
              />
            </FormControl>
          </FormItem>
        </FormField> -->
        <!-- Route Path Field -->
        <FormField v-slot="{ field }" name="slug">
          <FormItem>
            <FormLabel>Route Path</FormLabel>
            <FormControl>
              <AdminBaseRouteInput
                v-bind="field"
                :model-value="field.value"
                placeholder="example-route"
                :disabled="!isEditing"
                @update:model-value="field['onUpdate:modelValue']"
              />
            </FormControl>
            <FormDescription>
              The URL path for this menu (e.g., /about-us)
            </FormDescription>
            <FormMessage />
          </FormItem>
        </FormField>
      </CardContent>
      <CardFooter>
        <Button
          v-if="isEditing"
          type="submit"
          :disabled="isSubmitting"
          @click="
            emit('save', {
              ...currentValues,
              routeEnabled: isMenu ? false : true,
            })
          "
        >
          <Save class="h-4 w-4" />
          {{ isSubmitting ? 'Saving...' : 'Save Changes' }}
        </Button>
      </CardFooter>
    </Card>
  </Form>
</template>
