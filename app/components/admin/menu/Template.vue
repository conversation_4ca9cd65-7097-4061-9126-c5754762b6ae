// components/menu/MenuTemplate.vue
<script setup lang="ts">
import type { MenuWithRelations } from '~~/server/database/tables/menu';

defineProps<{
  menuData: MenuWithRelations;
}>();
const emit = defineEmits<{
  (e: 'fetchMenuData'): void;
  (e: 'toggleTemplateDialog'): void;
}>();
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle>Page Template</CardTitle>
      <CardDescription class="flex justify-between items-center gap-2">
        Configure the template for this menu's page
        <!-- // Show the current path -->
        <Badge variant="outline" class="text-sm">
          {{ '/about-us' }}
        </Badge>
      </CardDescription>
    </CardHeader>
    <CardContent>
      <template v-if="menuData">
        <AdminTemplateManager
          :menu-id="menuData?.id"
          :template-id="menuData?.template?.id"
          @created="emit('fetchMenuData')"
        />
      </template>
    </CardContent>
  </Card>
</template>
