<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod';
import { Loader2 } from 'lucide-vue-next';
import { Form } from 'vee-validate';
import { addSubmenuSchema } from '~~/shared/schema/menu/create';

const props = defineProps<{
  isSubmitting: boolean;
  parentId: number;
}>();

const emit = defineEmits<{
  (e: 'submit', values: any): void;
  (e: 'cancel'): void;
}>();

const formSchema = toTypedSchema(addSubmenuSchema);

const initialValues = {
  menuId: props.parentId,
  title: '',
  menuName: '',
  isActive: true,
  routeEnabled: true,
  slug: '',
};
</script>

<template>
  <Form
    :validation-schema="formSchema"
    :initial-values="initialValues"
    @submit="emit('submit', $event)"
  >
    <div class="grid gap-4 pb-4">
      <!-- Title Field -->
      <FormField v-slot="{ field, errorMessage }" name="title">
        <FormItem>
          <FormLabel>Title <span class="text-destructive">*</span></FormLabel>
          <FormControl>
            <Input
              v-bind="field"
              :model-value="field.value"
              placeholder="Enter title"
              :disabled="isSubmitting"
              @update:model-value="field.onChange"
            />
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- Menu Name Field -->
      <FormField v-slot="{ field, errorMessage }" name="menuName">
        <FormItem>
          <FormLabel
            >Menu Name <span class="text-destructive">*</span></FormLabel
          >
          <FormControl>
            <Input
              v-bind="field"
              :model-value="field.value"
              placeholder="Enter menu name"
              :disabled="isSubmitting"
              @update:model-value="field.onChange"
            />
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- Route Enabled Field -->
      <!-- <FormField v-slot="{ field }" name="routeEnabled">
        <FormItem
          class="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"
        >
          <div class="space-y-0.5">
            <FormLabel>Enable Route</FormLabel>
            <FormDescription> Enable routing for this submenu </FormDescription>
          </div>
          <FormControl>
            <Switch
              :checked="field.value"
              :disabled="isSubmitting"
              @update:checked="field.onChange"
            />
          </FormControl>
        </FormItem>
      </FormField> -->

      <!-- Route Path Field -->
      <FormField v-slot="{ field, errorMessage }" name="slug">
        <FormItem>
          <FormLabel>Route Path</FormLabel>
          <FormControl>
            <AdminBaseRouteInput
              v-bind="field"
              :model-value="field.value"
              placeholder="example-route"
              :disabled="isSubmitting"
              @update:model-value="field.onChange"
            />
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- Status Field -->
      <FormField v-slot="{ field }" name="isActive">
        <FormItem
          class="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"
        >
          <div class="space-y-0.5">
            <FormLabel>Status</FormLabel>
            <FormDescription> Enable or disable this submenu </FormDescription>
          </div>
          <FormControl>
            <Switch
              :checked="field.value"
              :disabled="isSubmitting"
              @update:checked="field.onChange"
            />
          </FormControl>
        </FormItem>
      </FormField>
    </div>

    <DialogFooter>
      <Button
        type="button"
        variant="ghost"
        :disabled="isSubmitting"
        @click="emit('cancel')"
      >
        Cancel
      </Button>
      <Button type="submit" :disabled="isSubmitting">
        <Loader2 v-if="isSubmitting" class="mr-2 h-4 w-4 animate-spin" />
        {{ isSubmitting ? 'Creating...' : 'Create Submenu' }}
      </Button>
    </DialogFooter>
  </Form>
</template>
