<script setup lang="ts">
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { toast } from 'vue-sonner';
import type { PaginationResponse } from '~~/shared/schema/pagination';
import { Loader2, AlertTriangle, Check } from 'lucide-vue-next';
import { UploadForm } from '.';

// Define the file upload data type
type FileUploadData = {
  id: number;
  title: string;
  type: string;
  file: {
    pathname: string;
    title: string;
    type: string;
    prefix?: string;
  };
};

// Function to format file type for display
const formatFileType = (type: string): string => {
  // Capitalize first letter and handle special cases
  switch (type.toLowerCase()) {
    case 'image':
      return 'Image';
    case 'document':
      return 'Document';
    case 'video':
      return 'Video';
    case 'audio':
      return 'Audio';
    case 'text':
      return 'Text';
    case 'other':
      return 'Other';
    default:
      // Capitalize first letter
      return type.charAt(0).toUpperCase() + type.slice(1);
  }
};

// Define the file upload response type
type FileUploadsResponse = {
  success: boolean;
  data: FileUploadData[];
  pagination: PaginationResponse;
};

const props = defineProps<{
  fileType?: string;
}>();

// Map file type to accepted file types for the dropzone
const acceptedFileTypes = computed(() => {
  if (!props.fileType || props.fileType === 'all') {
    return ['pdf', 'image', 'text', 'video', 'audio'] as const;
  }

  switch (props.fileType) {
    case 'document':
      return ['pdf', 'text'] as const;
    case 'image':
      return ['image'] as const;
    case 'video':
      return ['video'] as const;
    case 'audio':
      return ['audio'] as const;
    case 'other':
      return ['pdf', 'image', 'text', 'video', 'audio'] as const; // Allow all but will be filtered by backend
    default:
      // Handle the case where fileType is not one of the expected values
      return ['pdf', 'image', 'text', 'video', 'audio'] as const;
  }
});

const columns = [
  { key: 'title', label: 'Title' },
  { key: 'type', label: 'Type' },
  { key: 'link', label: 'Link' },
  { key: 'actions', label: 'Actions' },
];

const isDialogOpen = ref(false);
const selectedFile = ref<FileUploadData | null>(null);
const isSubmitting = ref(false);
const currentPage = ref(1);
const itemsPerPage = ref(10);
const filePagination = ref<PaginationResponse>({
  totalPages: 0,
  page: 0,
  limit: 0,
  totalItems: 0,
  hasPrevPage: false,
  hasNextPage: false,
});

// Add delete dialog state
const showDeleteDialog = ref(false);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

// Fetch file uploads
const fetchUrl = computed(() => {
  let url = '/api/admin/file-uploads';
  if (props.fileType) {
    url += `?type=${props.fileType}`;
  }
  if (currentPage.value > 1) {
    url += props.fileType ? '&' : '?';
    url += `page=${currentPage.value}`;
  }
  if (itemsPerPage.value !== 10) {
    url += props.fileType || currentPage.value > 1 ? '&' : '?';
    url += `limit=${itemsPerPage.value}`;
  }
  return url;
});

const {
  data: fileUploadsResponse,
  error: fileUploadsError,
  status: fileUploadsStatus,
  execute: fetchFileUploads,
} = useFetch<FileUploadsResponse>(fetchUrl);

const fileUploads = ref<FileUploadData[]>([]);

watch(fileUploadsResponse, (newResponse) => {
  if (!fileUploadsError.value && newResponse) {
    fileUploads.value = newResponse?.data || [];
    filePagination.value = newResponse?.pagination;
  }
});

const handleEdit = (file: FileUploadData) => {
  selectedFile.value = file;
  isDialogOpen.value = true;
};

const handleDelete = async (file: FileUploadData) => {
  selectedFile.value = file;
  showDeleteDialog.value = true;
};

const confirmDelete = async () => {
  if (!selectedFile.value) return;

  deleteState.value = 'loading';
  try {
    await $fetch(`/api/admin/file-uploads/${selectedFile.value.id}`, {
      method: 'DELETE',
    });
    deleteState.value = 'success';

    // Close dialog and refresh after a short delay
    setTimeout(async () => {
      showDeleteDialog.value = false;
      deleteState.value = 'idle';
      selectedFile.value = null;
      await fetchFileUploads();
    }, 1000);
  } catch (error) {
    console.error('Error deleting file:', error);
    deleteState.value = 'error';
    toast.error('Failed to delete file');
  }
};

const handleAdd = () => {
  selectedFile.value = null;
  isDialogOpen.value = true;
};

const handleFormSubmit = async (values: any) => {
  try {
    isSubmitting.value = true;
    if (selectedFile.value) {
      await $fetch(`/api/admin/file-uploads/${selectedFile.value.id}`, {
        method: 'PUT',
        body: values,
      });
      toast.success('File updated successfully');
    } else {
      await $fetch('/api/admin/file-uploads', {
        method: 'POST',
        body: values,
      });
      toast.success('File created successfully');
    }
    isDialogOpen.value = false;
    await fetchFileUploads();
  } catch (error) {
    console.error('Error saving file:', error);
    toast.error('Failed to save file');
  } finally {
    isSubmitting.value = false;
  }
};

const handleFormCancel = () => {
  isDialogOpen.value = false;
};

const handleCopyLink = (file: FileUploadData) => {
  if (file.file?.pathname) {
    const url = `${window.location.origin}/api/files/${file.file.pathname}`;
    navigator.clipboard.writeText(url);
    toast.success('Link copied to clipboard');
  }
};

const handlePageChange = async (page: number) => {
  currentPage.value = page;
  await fetchFileUploads();
};

const handleLimitChange = async (limit: number) => {
  itemsPerPage.value = limit;
  currentPage.value = 1; // Reset to first page when changing limit
  await fetchFileUploads();
};

// Initialize
onMounted(() => {
  fetchFileUploads();
});
</script>

<template>
  <Card>
    <CardHeader
      class="flex flex-row items-center justify-between space-y-0 pb-2"
    >
      <CardTitle>File Uploads</CardTitle>
      <Button size="sm" @click="handleAdd">Add File</Button>
    </CardHeader>
    <CardContent>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead v-for="column in columns" :key="column.key">
              {{ column.label }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-if="fileUploadsStatus === 'pending'">
            <TableCell colspan="4" class="text-center py-4">
              <div class="flex justify-center">
                <Loader2 class="h-6 w-6 animate-spin text-primary" />
              </div>
              <span class="text-sm text-muted-foreground mt-2"
                >Loading files...</span
              >
            </TableCell>
          </TableRow>
          <TableRow v-else-if="fileUploadsError">
            <TableCell colspan="4" class="text-center py-4 text-destructive">
              Error loading files. Please try again.
            </TableCell>
          </TableRow>
          <TableRow v-else-if="fileUploads.length === 0">
            <TableCell
              colspan="4"
              class="text-center py-4 text-muted-foreground"
            >
              No files found.
            </TableCell>
          </TableRow>
          <TableRow v-for="file in fileUploads" :key="file.id">
            <TableCell>{{ file.title }}</TableCell>
            <TableCell>{{ formatFileType(file.type) }}</TableCell>
            <TableCell>
              <Button
                type="button"
                variant="outline"
                size="sm"
                :disabled="isSubmitting"
                aria-label="Copy Link"
                @click="handleCopyLink(file)"
              >
                Copy Link
              </Button>
            </TableCell>
            <TableCell>
              <div class="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Edit file"
                  @click="handleEdit(file)"
                >
                  {{ isSubmitting ? 'Editing...' : 'Edit' }}
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Delete file"
                  @click="handleDelete(file)"
                >
                  {{ isSubmitting ? 'Deleting...' : 'Delete' }}
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <!-- Pagination -->
      <div
        v-if="filePagination.totalPages > 1"
        class="flex justify-between items-center mt-4"
      >
        <div class="flex items-center space-x-2">
          <span class="text-sm text-muted-foreground">
            Showing
            {{ (filePagination.page - 1) * filePagination.limit + 1 }} to
            {{
              Math.min(
                filePagination.page * filePagination.limit,
                filePagination.totalItems
              )
            }}
            of {{ filePagination.totalItems }} files
          </span>
          <select
            v-model="itemsPerPage"
            class="border rounded px-2 py-1 text-sm"
            @change="handleLimitChange(itemsPerPage)"
          >
            <option :value="10">10 per page</option>
            <option :value="25">25 per page</option>
            <option :value="50">50 per page</option>
          </select>
        </div>
        <div class="flex space-x-1">
          <Button
            variant="outline"
            size="sm"
            :disabled="!filePagination.hasPrevPage"
            @click="handlePageChange(filePagination.page - 1)"
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            :disabled="!filePagination.hasNextPage"
            @click="handlePageChange(filePagination.page + 1)"
          >
            Next
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>

  <!-- Add/Edit Dialog -->
  <Dialog :open="isDialogOpen" @update:open="isDialogOpen = $event">
    <DialogContent class="sm:max-w-3xl">
      <DialogHeader>
        <DialogTitle>
          {{ selectedFile ? 'Edit File' : 'Add New File' }}
        </DialogTitle>
      </DialogHeader>
      <UploadForm
        :initial-values="selectedFile"
        :is-submitting="isSubmitting"
        :accepted-file-types="acceptedFileTypes"
        @submit="handleFormSubmit"
        @cancel="handleFormCancel"
      />
    </DialogContent>
  </Dialog>

  <!-- Delete Confirmation Dialog -->
  <Dialog :open="showDeleteDialog" @update:open="showDeleteDialog = $event">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Delete File</DialogTitle>
      </DialogHeader>
      <div v-if="deleteState === 'idle'">
        <p>
          Are you sure you want to delete the file "{{ selectedFile?.title }}"?
          This action cannot be undone.
        </p>
      </div>
      <div
        v-else-if="deleteState === 'loading'"
        class="flex flex-col items-center py-4"
      >
        <Loader2 class="h-8 w-8 animate-spin text-primary" />
        <p class="mt-2">Deleting file...</p>
      </div>
      <div
        v-else-if="deleteState === 'success'"
        class="flex flex-col items-center py-4"
      >
        <Check class="h-8 w-8 text-green-500" />
        <p class="mt-2">File deleted successfully!</p>
      </div>
      <div
        v-else-if="deleteState === 'error'"
        class="flex flex-col items-center py-4"
      >
        <AlertTriangle class="h-8 w-8 text-destructive" />
        <p class="mt-2 text-destructive">
          Failed to delete file. Please try again.
        </p>
      </div>
      <DialogFooter v-if="deleteState === 'idle'">
        <Button variant="outline" @click="showDeleteDialog = false"
          >Cancel</Button
        >
        <Button variant="destructive" @click="confirmDelete">Delete</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
