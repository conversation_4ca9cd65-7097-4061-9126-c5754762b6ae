<script setup lang="ts">
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import { z } from 'zod';
import { toast } from 'vue-sonner';
import { fileSchema } from '~~/shared/schema';

// Define the file upload schema
const fileUploadSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  type: z.string().min(1, 'Type is required'),
  file: fileSchema.required(),
});

type FileUploadData = {
  id: number;
  title: string;
  type: string;
  file: {
    pathname: string;
    title: string;
    type: string;
    prefix?: string;
  };
};

const props = defineProps<{
  initialValues?: FileUploadData | null;
  isSubmitting?: boolean;
  acceptedFileTypes?: readonly ('pdf' | 'image' | 'text' | 'video' | 'audio')[];
}>();

// Default to all file types if not specified
const fileTypes = computed(() => {
  // Convert to array to avoid readonly issues
  return props.acceptedFileTypes
    ? [...props.acceptedFileTypes]
    : ['pdf', 'image', 'text', 'video', 'audio'];
});

const emit = defineEmits<{
  submit: [values: z.infer<typeof fileUploadSchema>];
  cancel: [];
}>();

// Use appropriate schema based on whether we're editing or creating
const validationSchema = toTypedSchema(fileUploadSchema);

const initialValues = computed(() => {
  return {
    title: props.initialValues?.title ?? '',
    type: props.initialValues?.type ?? '',
    file: props.initialValues?.file
      ? {
          pathname: props.initialValues.file.pathname,
          title: props.initialValues.file.title,
          type: props.initialValues.file.type,
          prefix: props.initialValues.file.prefix,
        }
      : undefined,
  };
});

const { values, errors, setFieldValue, resetForm } = useForm<
  z.infer<typeof fileUploadSchema>
>({
  validationSchema,
  initialValues: initialValues.value,
});

const handleSubmit = () => {
  emit('submit', values);
};

const handleCancel = () => {
  resetForm();
  emit('cancel');
};

// Add type for file upload
interface FileUpload {
  file: File;
  title?: string;
}

const primaryFile = ref<
  | {
      pathname: string;
      title: string;
      type: string;
      prefix?: string;
    }
  | undefined
>(
  props.initialValues?.file
    ? {
        pathname: props.initialValues.file.pathname,
        title: props.initialValues.file.title,
        type: props.initialValues.file.type,
        prefix: props.initialValues.file.prefix,
      }
    : undefined
);

// Function to determine file type category based on MIME type
const getFileTypeCategory = (mimeType: string): string => {
  if (mimeType.startsWith('image/')) {
    return 'image';
  } else if (mimeType.startsWith('video/')) {
    return 'video';
  } else if (mimeType.startsWith('audio/')) {
    return 'audio';
  } else if (mimeType === 'application/pdf') {
    return 'document';
  } else if (
    mimeType === 'text/plain' ||
    mimeType === 'text/html' ||
    mimeType === 'text/css' ||
    mimeType === 'text/javascript'
  ) {
    return 'text';
  } else if (
    mimeType === 'application/msword' ||
    mimeType ===
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
    mimeType === 'application/vnd.ms-excel' ||
    mimeType ===
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    mimeType === 'application/vnd.ms-powerpoint' ||
    mimeType ===
      'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  ) {
    return 'document';
  } else {
    return 'other';
  }
};

const handleFileUpload = async (files: FileUpload[]) => {
  const file = files[0]?.file;
  if (!file) {
    primaryFile.value = undefined;
    // Use null instead of undefined to avoid type issues
    setFieldValue('file', null as any);
    return;
  }

  // Automatically determine file type from the file's MIME type
  const fileTypeCategory = getFileTypeCategory(file.type);
  setFieldValue('type', fileTypeCategory);

  // If no title is set, use the file name without extension
  if (!values.title) {
    const fileName = file.name.split('.').slice(0, -1).join('.') || file.name;
    setFieldValue('title', fileName);
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', fileTypeCategory);
    form.append('title', values.title || file.name.split('.')[0] || 'File');

    const response = await $fetch<
      { pathname: string; title: string; type: string; prefix: string }[]
    >('/api/blob/upload-files', {
      method: 'POST',
      body: form,
    });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: response[0]?.prefix || '',
    };
    primaryFile.value = fileData;
    setFieldValue('file', fileData);
  } catch (err) {
    console.error('Failed to upload file:', err);
    toast.error('Failed to upload file');
  }
};

const handleFileRemove = () => {
  primaryFile.value = undefined;
  // Use null instead of undefined to avoid type issues
  setFieldValue('file', null as any);
};
</script>

<template>
  <Form @submit="handleSubmit">
    <div class="space-y-4">
      <!-- Title -->
      <FormField v-slot="{ componentField }" name="title">
        <FormItem>
          <FormLabel>Title</FormLabel>
          <FormControl>
            <Input
              v-bind="componentField"
              placeholder="Enter file title"
              :disabled="props.isSubmitting"
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <!-- Type (hidden, automatically determined) -->
      <input type="hidden" name="type" :value="values.type" />

      <!-- File Upload -->
      <AdminBaseDropzone
        :is-multiple-allowed="false"
        :file-types="
          fileTypes as ('pdf' | 'image' | 'text' | 'video' | 'audio')[]
        "
        title="File"
        :existing-files="primaryFile ? [primaryFile] : []"
        :description="`Upload a file (${fileTypes.join(', ')})`"
        :is-submitting="props.isSubmitting"
        @files-selected="(files) => handleFileUpload(files)"
        @file-removed="handleFileRemove"
      />
      <!-- Show error of file -->
      <div v-if="errors.file" class="text-red-500 text-xs -mt-4">
        {{ errors.file }}
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-2 pt-4">
        <Button
          type="button"
          variant="outline"
          :disabled="props.isSubmitting"
          @click="handleCancel"
        >
          Cancel
        </Button>
        <Button type="submit" :disabled="props.isSubmitting">
          {{ props.isSubmitting ? 'Saving...' : 'Save' }}
        </Button>
      </div>
    </div>
  </Form>
</template>
