<script setup lang="ts">
import type { DownloadData } from '~/types/admin';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from 'vue-sonner';
import type { PaginationResponse } from '~~/shared/schema/pagination';
import type { IQACDownloadsResponse } from '~/types/admin/iqac';

const props = defineProps<{ param: string }>();

const columns = [
  { key: 'title', label: 'Title' },
  { key: 'link', label: 'Link' },
  { key: 'actions', label: 'Actions' },
];

const isDialogOpen = ref(false);
const selectedDownload = ref<DownloadData | null>(null);
const isSubmitting = ref(false);
const currentPage = ref(1);
const downloadPagination = ref<PaginationResponse>({
  totalPages: 0,
  page: 0,
  limit: 0,
  totalItems: 0,
  hasPrevPage: false,
  hasNextPage: false,
});

// Add delete dialog state
const showDeleteDialog = ref(false);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

const {
  data: downloadResponse,
  error: downloadError,
  status: downloadListStatus,
  execute: fetchDownloadList,
} = useFetch<IQACDownloadsResponse>(`/api/admin/iqac/download/${props.param}`);

const downloadList = ref<DownloadData[]>([]);

watch(downloadResponse, (newDownloadResponse) => {
  if (!downloadError.value && newDownloadResponse) {
    downloadList.value = newDownloadResponse?.downloads || [];
    downloadPagination.value = newDownloadResponse?.pagination;
  }
});

const handleEdit = (download: DownloadData) => {
  selectedDownload.value = download;
  isDialogOpen.value = true;
};

const handleDelete = async (download: DownloadData) => {
  selectedDownload.value = download;
  showDeleteDialog.value = true;
};

const confirmDelete = async () => {
  if (!selectedDownload.value) return;

  deleteState.value = 'loading';
  try {
    await $fetch(
      `/api/admin/iqac/download/${props.param}/${selectedDownload.value.id}`,
      {
        method: 'DELETE',
      }
    );
    deleteState.value = 'success';

    // Close dialog and refresh after a short delay
    setTimeout(async () => {
      showDeleteDialog.value = false;
      deleteState.value = 'idle';
      selectedDownload.value = null;
      await fetchDownloadList();
    }, 1000);
  } catch (error) {
    console.error('Error deleting download:', error);
    deleteState.value = 'error';
    toast.error('Failed to delete download');
  }
};

const handleAdd = () => {
  selectedDownload.value = null;
  isDialogOpen.value = true;
};

const handleFormSubmit = async (values: any) => {
  try {
    isSubmitting.value = true;
    if (selectedDownload.value) {
      await $fetch(
        `/api/admin/iqac/download/${props.param}/${selectedDownload.value.id}`,
        {
          method: 'PUT',
          body: values,
        }
      );
      toast.success('Download updated successfully');
    } else {
      await $fetch(`/api/admin/iqac/download/${props.param}`, {
        method: 'POST',
        body: values,
      });
      toast.success('Download created successfully');
    }
    isDialogOpen.value = false;
    await fetchDownloadList();
  } catch (error) {
    console.error('Error saving download:', error);
    toast.error('Failed to save download');
  } finally {
    isSubmitting.value = false;
  }
};

const handleFormCancel = () => {
  isDialogOpen.value = false;
};

const handleDownload = (download: DownloadData) => {
  if (download.file?.pathname) {
    window.open(`/api/files/${download.file.pathname}`, '_blank');
  }
};

const handlePageChange = async (page: number) => {
  currentPage.value = page;
  await fetchDownloadList();
};
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader
      class="px-2 pt-0 pb-4 flex flex-row items-center justify-between"
    >
      <CardTitle>Downloads</CardTitle>
      <Button
        type="button"
        variant="default"
        size="sm"
        :disabled="isSubmitting"
        @click="handleAdd"
      >
        Add Download
      </Button>
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead v-for="column in columns" :key="column.key">
              {{ column.label }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-if="downloadListStatus === 'pending'">
            <TableCell colspan="4" class="text-center py-4">
              Loading downloads data...
            </TableCell>
          </TableRow>
          <TableRow v-else-if="!downloadList.length">
            <TableCell colspan="4" class="text-center py-4">
              No downloads found.
            </TableCell>
          </TableRow>
          <TableRow v-for="download in downloadList" :key="download.id">
            <TableCell>{{ download.title }}</TableCell>
            <TableCell>
              <div class="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Download"
                  @click="handleDownload(download)"
                >
                  Copy Link
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Edit download"
                  @click="handleEdit(download)"
                >
                  {{ isSubmitting ? 'Editing...' : 'Edit' }}
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Delete download"
                  @click="handleDelete(download)"
                >
                  {{ isSubmitting ? 'Deleting...' : 'Delete' }}
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <!-- Pagination -->
      <div
        v-if="downloadPagination.totalPages > 1"
        class="mt-4 flex justify-center"
      >
        <div class="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            :disabled="!downloadPagination.hasPrevPage"
            @click="handlePageChange(currentPage - 1)"
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            :disabled="!downloadPagination.hasNextPage"
            @click="handlePageChange(currentPage + 1)"
          >
            Next
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>

  <Dialog :open="isDialogOpen" @update:open="isDialogOpen = $event">
    <DialogContent class="sm:max-w-3xl">
      <DialogHeader>
        <DialogTitle>
          {{ selectedDownload ? 'Edit Download' : 'Add New Download' }}
        </DialogTitle>
      </DialogHeader>
      <AdminIqacDownloadForm
        :initial-values="selectedDownload"
        :is-submitting="isSubmitting"
        @submit="handleFormSubmit"
        @cancel="handleFormCancel"
      />
    </DialogContent>
  </Dialog>

  <!-- Delete Confirmation Dialog -->
  <AdminMenuDeleteDialog
    v-if="selectedDownload"
    v-model:is-open="showDeleteDialog"
    :is-submitting="deleteState === 'loading'"
    :delete-state="deleteState"
    menu-title="download"
    type="menu"
    :description="`Are you sure you want to delete download '${selectedDownload.title}'? This action cannot be undone.`"
    @confirm="confirmDelete"
  />
</template>
