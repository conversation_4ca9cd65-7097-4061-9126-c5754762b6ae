<script setup lang="ts">
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Form } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import { Loader2 } from 'lucide-vue-next';
import { DialogFooter } from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { iconOptions } from '~/constants/admin/icons';
import {
  createQuickLinkSchema,
  type CreateQuickLinkSchema,
} from '~~/shared/schema/quick-link/create';

defineProps<{
  isSubmitting: boolean;
}>();

const emit = defineEmits<{
  (e: 'submit', values: CreateQuickLinkSchema): void;
  (e: 'cancel'): void;
}>();

const formSchema = toTypedSchema(createQuickLinkSchema);

const initialValues = {
  title: '',
  link: '',
  icon: '',
};

const handleSubmit = (values: any) => {
  emit('submit', values);
};
</script>

<template>
  <Form
    :validation-schema="formSchema"
    :initial-values="initialValues"
    @submit="handleSubmit"
  >
    <div class="grid gap-4 pb-4">
      <!-- Title Field -->
      <FormField v-slot="{ field, errorMessage }" name="title">
        <FormItem>
          <FormLabel
            >Service Title <span class="text-destructive">*</span></FormLabel
          >
          <FormControl>
            <Input
              v-bind="field"
              :model-value="field.value"
              placeholder="Enter service title"
              :disabled="isSubmitting"
              @update:model-value="field.onChange"
            />
          </FormControl>
          <FormDescription>Display name for the e-service</FormDescription>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- Link Field -->
      <FormField v-slot="{ field, errorMessage }" name="link">
        <FormItem>
          <FormLabel
            >Service Link <span class="text-destructive">*</span></FormLabel
          >
          <FormControl>
            <Input
              v-bind="field"
              :model-value="field.value"
              type="url"
              placeholder="Enter service URL"
              :disabled="isSubmitting"
              @update:model-value="field.onChange"
            />
          </FormControl>
          <FormDescription
            >URL where the service can be accessed</FormDescription
          >
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- Icon Field -->
      <FormField v-slot="{ field, errorMessage }" name="icon">
        <FormItem>
          <FormLabel>Icon</FormLabel>
          <FormControl>
            <Select
              :model-value="field.value"
              :disabled="isSubmitting"
              @update:model-value="field.onChange"
            >
              <SelectTrigger>
                <SelectValue placeholder="Select icon" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">
                  <div class="flex items-center gap-2">
                    <Icon name="i-lucide-x" class="size-4" />
                    <div class="flex items-center gap-2">None</div>
                  </div>
                </SelectItem>
                <SelectItem
                  v-for="option in iconOptions"
                  :key="option.icon"
                  :value="option.icon"
                >
                  <div class="flex items-center gap-2">
                    <Icon :name="option.icon" class="size-4" />
                    {{ option.label }}
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </FormControl>
          <FormDescription>Icon to display with the quick link</FormDescription>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>
    </div>

    <DialogFooter>
      <Button
        type="button"
        variant="ghost"
        :disabled="isSubmitting"
        @click="emit('cancel')"
      >
        Cancel
      </Button>
      <Button type="submit" :disabled="isSubmitting">
        <Loader2 v-if="isSubmitting" class="mr-2 h-4 w-4 animate-spin" />
        {{ isSubmitting ? 'Creating...' : 'Create Quick Link' }}
      </Button>
    </DialogFooter>
  </Form>
</template>
