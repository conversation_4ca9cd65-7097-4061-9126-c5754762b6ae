<script setup lang="ts">
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { AlertCircle, PlusCircle } from 'lucide-vue-next';
import type { QuickLink } from '~~/server/database/tables/quick-link';
import type { CreateEServiceSchema } from '~~/shared/schema/e-service/create';

const isDialogOpen = ref(false);
const selectedQuickLink = ref<QuickLink | null>(null);
const showDeleteDialog = ref(false);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

const {
  quickLinks,
  isLoading,
  fetchError,
  isSubmitting,
  createQuickLink,
  updateQuickLink,
  deleteQuickLink,
} = useQuickLinks();

const columns = [
  { key: 'title', label: 'Title' },
  { key: 'link', label: 'Link' },
  { key: 'icon', label: 'Icon' },
  { key: 'actions', label: 'Actions' },
];

const handleAdd = () => {
  selectedQuickLink.value = null;
  isDialogOpen.value = true;
};

const handleEdit = (quickLink: QuickLink) => {
  selectedQuickLink.value = quickLink;
  isDialogOpen.value = true;
};

const handleDelete = (quickLink: QuickLink) => {
  selectedQuickLink.value = quickLink;
  showDeleteDialog.value = true;
};

const onDelete = async () => {
  if (!selectedQuickLink.value?.id) return;
  deleteState.value = 'loading';

  const success = await deleteQuickLink(selectedQuickLink.value.id);

  if (success) {
    deleteState.value = 'success';
    setTimeout(() => {
      showDeleteDialog.value = false;
      deleteState.value = 'idle';
      selectedQuickLink.value = null;
    }, 1000);
  } else {
    deleteState.value = 'error';
  }
};

const handleFormSubmit = async (values: CreateEServiceSchema) => {
  let success;

  if (selectedQuickLink.value?.id) {
    success = await updateQuickLink(selectedQuickLink.value.id, values);
  } else {
    success = await createQuickLink(values);
  }

  if (success) {
    isDialogOpen.value = false;
    selectedQuickLink.value = null;
  }
};

const handleFormCancel = () => {
  isDialogOpen.value = false;
  selectedQuickLink.value = null;
};
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <AdminLayoutLoading />
    </div>

    <!-- Fetch Error State -->
    <Alert v-else-if="fetchError" variant="destructive" class="my-4">
      <AlertCircle class="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{{ fetchError }}</AlertDescription>
    </Alert>

    <!-- Content -->
    <Card v-else class="w-full p-6 px-0 border-none shadow-none">
      <CardHeader
        class="px-2 pt-0 pb-4 flex items-center justify-between flex-row"
      >
        <CardTitle> List</CardTitle>
        <Dialog :open="isDialogOpen" @update:open="isDialogOpen = $event">
          <DialogTrigger as-child>
            <Button variant="outline" @click="handleAdd">
              <PlusCircle class="h-4 w-4 mr-2" />
              Add Service
            </Button>
          </DialogTrigger>
          <DialogContent class="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {{
                  selectedQuickLink
                    ? 'Edit Quick Link'
                    : 'Create New Quick Link'
                }}
              </DialogTitle>
              <DialogDescription>
                {{
                  selectedQuickLink
                    ? 'Edit existing quick link details.'
                    : 'Add a new quick link to your list. Fill in the details below.'
                }}
              </DialogDescription>
            </DialogHeader>
            <AdminQuickLinkForm
              :initial-values="selectedQuickLink"
              :is-submitting="isSubmitting"
              @submit="handleFormSubmit"
              @cancel="handleFormCancel"
            />
          </DialogContent>
        </Dialog>
      </CardHeader>

      <CardContent class="px-2 pt-0 pb-4">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead v-for="column in columns" :key="column.key">
                {{ column.label }}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-if="!quickLinks.length">
              <TableCell colspan="3" class="text-center py-4">
                No quick links found.
              </TableCell>
            </TableRow>
            <TableRow v-for="quickLink in quickLinks" :key="quickLink.id">
              <TableCell>{{ quickLink.title }}</TableCell>
              <TableCell>
                <a
                  :href="quickLink.link ?? ''"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="text-blue-600 hover:underline"
                  tabindex="0"
                >
                  {{ quickLink.link }}
                </a>
              </TableCell>
              <TableCell>
                <div
                  v-if="quickLink.icon"
                  class="flex items-center gap-2 bg-[#82acc5] rounded-md p-2 w-12 h-12"
                >
                  <Icon
                    :name="quickLink.icon"
                    class="size-8 m-auto text-white"
                  />
                </div>
              </TableCell>
              <TableCell>
                <div class="flex space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    :disabled="isSubmitting"
                    aria-label="Edit quick link"
                    @click="handleEdit(quickLink)"
                  >
                    Edit
                  </Button>
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    :disabled="isSubmitting"
                    aria-label="Delete quick link"
                    @click="handleDelete(quickLink)"
                  >
                    Delete
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>

    <!-- Delete Dialog -->
    <AdminMenuDeleteDialog
      v-if="selectedQuickLink"
      v-model:is-open="showDeleteDialog"
      :is-submitting="isSubmitting"
      :menu-title="selectedQuickLink.title"
      :delete-state="deleteState"
      :description="`Are you sure you want to delete quick link '${selectedQuickLink.title}'? This action cannot be undone.`"
      @confirm="onDelete"
    />
  </div>
</template>
