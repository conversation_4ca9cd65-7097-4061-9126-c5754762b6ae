// components/Admin/Gallery/Albums.vue
<script setup lang="ts">
import { PlusCircle, AlertCircle } from 'lucide-vue-next';

const {
  albums,
  isLoading,
  error,
  currentAlbum,
  deleteState,
  showCreateDialog,
  showDeleteDialog,
  createAlbum,
  deleteAlbum,
  fetchAlbums,
} = useAlbums();

onMounted(() => {
  fetchAlbums();
});

function handleCreateAlbum(name: string) {
  createAlbum({ name });
}
</script>

<template>
  <!-- Loading State -->
  <div v-if="isLoading" class="flex justify-center py-8">
    <AdminLayoutLoading />
  </div>

  <!-- Error State -->
  <Alert v-else-if="error" variant="destructive">
    <AlertCircle class="h-4 w-4" />
    <AlertTitle>Error</AlertTitle>
    <AlertDescription>{{ error }}</AlertDescription>
  </Alert>

  <!-- Content -->
  <Card v-else class="border-none">
    <CardHeader>
      <div class="flex items-center justify-between">
        <div>
          <CardTitle>Photo Albums</CardTitle>
          <CardDescription
            >Manage your photo albums and gallery</CardDescription
          >
        </div>

        <Dialog v-model:open="showCreateDialog">
          <DialogTrigger as-child>
            <Button variant="outline">
              <PlusCircle class="h-4 w-4 mr-2" />
              Create Album
            </Button>
          </DialogTrigger>
          <DialogContent class="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Create New Album</DialogTitle>
              <DialogDescription>
                Enter a name for your new photo album
              </DialogDescription>
            </DialogHeader>
            <AdminGalleryForm @submit="handleCreateAlbum" />
          </DialogContent>
        </Dialog>
      </div>
    </CardHeader>

    <CardContent>
      <AdminGalleryAlbumList
        :albums="albums"
        @delete="
          currentAlbum = $event;
          showDeleteDialog = true;
        "
      />
    </CardContent>
  </Card>

  <!-- Delete Dialog -->
  <AdminMenuDeleteDialog
    v-if="currentAlbum"
    v-model:is-open="showDeleteDialog"
    :is-submitting="isLoading"
    menu-title="album"
    :delete-state="deleteState"
    :description="'Are you sure you want to delete this album? All photos in this album will be deleted. This action cannot be undone.'"
    @confirm="deleteAlbum(currentAlbum.id)"
  />
</template>
