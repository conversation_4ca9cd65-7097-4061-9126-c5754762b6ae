<!-- components/Admin/Gallery/AlbumList.vue -->
<script setup lang="ts">
import { Camera, Trash2, Image as ImageIcon } from 'lucide-vue-next';
import type { Album } from '~~/shared/schema/album/get';

defineProps<{
  albums: Album[];
}>();

const emit = defineEmits<{
  (e: 'delete', album: Album): void;
}>();
</script>

<template>
  <div class="space-y-6">
    <!-- Empty State -->
    <div
      v-if="!albums.length"
      class="flex flex-col items-center justify-center py-16 border-2 border-dashed rounded-lg"
    >
      <div class="rounded-full bg-primary/10 p-4 mb-4">
        <Camera class="h-8 w-8 text-primary" />
      </div>
      <h3 class="text-lg font-semibold mb-2">No Albums Found</h3>
      <p class="text-sm text-muted-foreground max-w-sm text-center">
        Get started by creating your first photo album. Click the "Create Album"
        button above.
      </p>
    </div>

    <!-- Album Grid -->
    <div
      v-else
      class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
    >
      <NuxtLink
        v-for="album in albums"
        :key="album.id"
        :to="`/admin/gallery/${album.id}`"
        class="group relative block"
      >
        <div
          class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg z-10"
        />

        <Card
          class="overflow-hidden border-none bg-background shadow-sm hover:shadow-md transition-all duration-300"
        >
          <!-- Album Cover -->
          <div class="aspect-square bg-muted relative">
            <NuxtImg
              v-if="album.primaryImage"
              :src="getPreviewUrl(album.primaryImage.pathname)"
              class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105 bg-white"
              :alt="album.title"
            />
            <div
              v-else
              class="w-full h-full flex flex-col items-center justify-center bg-muted/50"
            >
              <ImageIcon class="h-10 w-10 text-muted-foreground/50" />
              <span class="text-xs text-muted-foreground mt-2"
                >No cover image</span
              >
            </div>

            <!-- Overlay Content -->
            <div
              class="absolute inset-x-0 bottom-0 p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20"
            >
              <div class="flex items-center justify-between">
                <div class="max-w-[85%]">
                  <h3 class="font-semibold text-white truncate">
                    {{ album.title }}
                  </h3>
                  <p class="text-sm text-white/80">
                    {{ album.count || 0 }} photos
                  </p>
                </div>
                <Button
                  variant="secondary"
                  size="icon"
                  class="p-2 bg-white/20 hover:bg-white/30 backdrop-blur-sm"
                  @click.prevent="emit('delete', album)"
                >
                  <Trash2 class="h-4 w-4 text-white" />
                </Button>
              </div>
            </div>
          </div>

          <!-- Mobile/SEO Friendly Content -->
          <CardContent class="p-4 sm:hidden">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="font-semibold truncate">{{ album.title }}</h3>
                <p class="text-sm text-muted-foreground">
                  {{ album.count || 0 }} photos
                </p>
              </div>
              <Button
                variant="ghost"
                size="icon"
                class="h-8 w-8 text-destructive"
                @click.prevent="emit('delete', album)"
              >
                <Trash2 class="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </NuxtLink>
    </div>
  </div>
</template>

<style scoped>
/* Optional: Add a subtle scaling effect on hover */
.group:hover .card {
  transform: translateY(-2px);
}
</style>
