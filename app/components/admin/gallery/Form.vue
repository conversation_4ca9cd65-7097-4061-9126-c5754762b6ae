// components/Admin/Gallery/AlbumForm.vue
<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod';
import { Form } from 'vee-validate';
import { z } from 'zod';

const emit = defineEmits<{
  (e: 'submit', title: string): void;
}>();

// Form validation schema
const schema = toTypedSchema(
  z.object({
    title: z
      .string()
      .min(1, 'Album name is required')
      .max(50, 'Album name cannot exceed 50 characters')
      .refine(
        (title) => /^[a-zA-Z0-9\s-_]+$/.test(title),
        'Album name can only contain letters, numbers, spaces, hyphens, and underscores'
      ),
  })
);

// Initial form values
const initialValues = {
  title: '',
};

const onSubmit = (values: any) => {
  emit('submit', values?.title);
};
</script>

<template>
  <Form
    :validation-schema="schema"
    :initial-values="initialValues"
    @submit="onSubmit"
  >
    <div class="space-y-4">
      <FormField v-slot="{ field, errorMessage }" name="title">
        <FormItem>
          <FormLabel>Album Name</FormLabel>
          <FormControl>
            <Input
              v-bind="field"
              placeholder="Enter album name"
              autocomplete="off"
            />
          </FormControl>
          <FormDescription>
            Choose a unique name for your album using letters, numbers, spaces,
            hyphens, or underscores.
          </FormDescription>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <div class="flex justify-end gap-2">
        <DialogClose as-child>
          <Button type="button" variant="ghost">Cancel</Button>
        </DialogClose>
        <Button type="submit">Create Album</Button>
      </div>
    </div>
  </Form>
</template>
