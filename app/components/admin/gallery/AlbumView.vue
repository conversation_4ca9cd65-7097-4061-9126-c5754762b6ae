<!-- //components/Admin/Gallery/AlbumView.vue -->
<script setup lang="ts">
import {
  Upload,
  Trash2,
  X,
  Maximize2,
  ChevronLeft,
  ChevronRight,
  Camera,
} from 'lucide-vue-next';
import { toast } from 'vue-sonner';

import type { AlbumData, AlbumPhoto } from '~~/shared/schema/album/get';

const props = defineProps<{
  albumId: string;
  initialData?: AlbumData;
  isUploading: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:photos', files: File[]): void;
  (e: 'delete-photos', photos: number[]): void;
}>();

// State management
const photos = ref<AlbumPhoto[]>(props.initialData?.photos ?? []);
const isLoading = ref(false);
const uploadProgress = ref(0);
const selectedPhotos = ref<Set<number>>(new Set());
const showDeleteDialog = ref(false);
const showUploadDialog = ref(false);
const previewPhoto = ref<AlbumPhoto | null>(null);
const uploadQueue = ref<File[]>([]);

// Preview navigation
const currentPreviewIndex = computed(() => {
  if (!previewPhoto.value) return -1;
  return photos.value.findIndex((p) => p.id === previewPhoto.value?.id);
});

// Selection mode
const isSelectionMode = computed(() => selectedPhotos.value.size > 0);

// Handle file selection
function handleFilesSelected(files: { file: File; title?: string }[]) {
  uploadQueue.value = files.map((file) => file.file);
  showUploadDialog.value = true;
}

// Handle file upload
async function handleUpload() {
  if (!uploadQueue.value.length) return;
  emit('update:photos', uploadQueue.value);
}

watch(
  () => props.initialData,
  (newData) => {
    photos.value = newData?.photos ?? [];
  }
);

watch(
  () => props.isUploading,
  (newData, oldData) => {
    if (!newData && oldData) {
      showUploadDialog.value = false;
      uploadQueue.value = [];
    }
  }
);
// Photo selection
function togglePhotoSelection(photoId: number) {
  if (selectedPhotos.value.has(photoId)) {
    selectedPhotos.value.delete(photoId);
  } else {
    selectedPhotos.value.add(photoId);
  }
}

// Bulk delete
async function deleteSelectedPhotos() {
  try {
    emit('delete-photos', Array.from(selectedPhotos.value));
    await new Promise((resolve) => setTimeout(resolve, 1000));
    photos.value = photos.value.filter((p) => !selectedPhotos.value.has(p.id));
    selectedPhotos.value.clear();
  } catch (err: any) {
    toast.error(err?.message ?? 'Failed to delete photos');
  }
}

// Preview navigation
function navigatePreview(direction: 'prev' | 'next') {
  const currentIndex = currentPreviewIndex.value;
  const newIndex = direction === 'prev' ? currentIndex - 1 : currentIndex + 1;

  if (newIndex >= 0 && newIndex < photos.value.length) {
    previewPhoto.value = photos.value[newIndex]!;
  }
}

const containerWidth = ref(0);
// const photoRefs = ref<Map<string, HTMLElement>>(new Map());

// Update container width on mount and resize
onMounted(() => {
  updateContainerWidth();
  window.addEventListener('resize', updateContainerWidth);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateContainerWidth);
});

function updateContainerWidth() {
  const container = document.querySelector('.photos-grid');
  if (container) {
    containerWidth.value = container.clientWidth;
  }
}

// Add loading state for images
const loadedImages = ref<Set<number>>(new Set());

function handleImageLoad(photoId: number) {
  loadedImages.value.add(photoId);
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header Card -->
    <Card
      class="border-none bg-background/80 backdrop-blur-sm sticky top-0 z-30"
    >
      <CardHeader>
        <div
          class="flex flex-col sm:flex-row sm:items-center justify-between gap-4"
        >
          <div>
            <CardTitle class="text-lg">{{
              initialData?.title || 'Album Title'
            }}</CardTitle>
            <CardDescription class="text-sm">
              {{ photos.length }} photos in this album
            </CardDescription>
          </div>

          <div class="flex flex-wrap items-center gap-2">
            <Button
              v-if="isSelectionMode"
              variant="destructive"
              class="w-full sm:w-auto"
              @click="showDeleteDialog = true"
            >
              <Trash2 class="h-4 w-4 mr-2" />
              Delete Selected ({{ selectedPhotos.size }})
            </Button>

            <Button
              v-if="isSelectionMode"
              variant="outline"
              class="w-full sm:w-auto"
              @click="selectedPhotos.clear()"
            >
              <X class="h-4 w-4 mr-2" />
              Cancel Selection
            </Button>

            <Dialog v-model:open="showUploadDialog">
              <DialogTrigger as-child>
                <Button variant="default" class="w-full sm:w-auto">
                  <Upload class="h-4 w-4 mr-2" />
                  Upload Photos
                </Button>
              </DialogTrigger>
              <DialogContent class="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Upload Photos</DialogTitle>
                  <DialogDescription>
                    Add new photos to your album
                  </DialogDescription>
                </DialogHeader>

                <div class="space-y-4">
                  <!-- Upload Dropzone -->
                  <AdminBaseDropzone
                    :is-multiple-allowed="true"
                    :file-types="['image']"
                    title="Drop photos here"
                    description="Or click to select files"
                    @files-selected="handleFilesSelected"
                  />

                  <!-- Upload Progress -->
                  <div v-if="isUploading" class="space-y-2">
                    <Progress :value="uploadProgress" />
                    <p class="text-sm text-center text-muted-foreground">
                      Uploading... {{ Math.round(uploadProgress) }}%
                    </p>
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="ghost" @click="showUploadDialog = false">
                    Cancel
                  </Button>
                  <Button
                    :disabled="!uploadQueue.length || isUploading"
                    @click="handleUpload"
                  >
                    {{ isUploading ? 'Uploading...' : 'Upload' }}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
    </Card>

    <!-- Photos Grid -->
    <div
      v-if="photos.length"
      class="photos-grid grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4"
    >
      <div
        v-for="photo in photos"
        :key="photo.id"
        class="group relative aspect-square bg-muted/30 rounded-lg overflow-hidden"
        :class="{
          'ring-2 ring-primary ring-offset-2': selectedPhotos.has(photo.id),
          'opacity-0': !loadedImages.has(photo.id),
        }"
      >
        <!-- Loading Skeleton -->
        <div
          v-if="!loadedImages.has(photo.id)"
          class="absolute inset-0 bg-muted animate-pulse"
        />

        <!-- Image -->
        <NuxtImg
          :src="getPreviewUrl(photo.pathname)"
          :alt="photo.title || ''"
          class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105 bg-white"
          @load="handleImageLoad(photo.id)"
          @click="
            isSelectionMode
              ? togglePhotoSelection(photo.id)
              : (previewPhoto = photo)
          "
        />

        <!-- Overlay -->
        <div
          class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300"
          :class="{ 'opacity-100': selectedPhotos.has(photo.id) }"
        >
          <!-- Actions -->
          <div class="absolute top-3 right-3 flex items-center gap-2">
            <Button
              v-if="!isSelectionMode"
              variant="secondary"
              size="icon"
              class="h-8 w-8 bg-primary/70 hover:bg-primary backdrop-blur-sm"
              @click.stop="previewPhoto = photo"
            >
              <Maximize2 class="h-4 w-4 text-white" />
            </Button>
            <Button
              variant="secondary"
              size="icon"
              class="h-8 w-8"
              :class="[
                selectedPhotos.has(photo.id)
                  ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                  : 'bg-primary/70 hover:bg-primary backdrop-blur-sm',
              ]"
              @click.stop="togglePhotoSelection(photo.id)"
            >
              <template v-if="selectedPhotos.has(photo.id)">
                <X class="h-4 w-4" />
              </template>
              <template v-else>
                <Trash2 class="h-4 w-4 text-white" />
              </template>
            </Button>
          </div>

          <!-- Photo Info -->
          <div class="absolute bottom-0 left-0 right-0 p-3">
            <h3 class="text-white font-medium truncate text-sm">
              {{ photo.title || 'Untitled' }}
            </h3>
          </div>
        </div>

        <!-- Selection Indicator -->
        <div
          v-if="selectedPhotos.has(photo.id)"
          class="absolute top-3 left-3 h-6 w-6 bg-primary rounded-full flex items-center justify-center"
        >
          <span class="text-primary-foreground text-xs font-medium">
            {{ Array.from(selectedPhotos).indexOf(photo.id) + 1 }}
          </span>
        </div>
      </div>
    </div>
    <Card
      v-else
      class="flex flex-col items-center justify-center py-8 text-center border-none"
    >
      <div class="rounded-full bg-muted p-3 mb-4">
        <Camera class="h-6 w-6 text-muted-foreground" />
      </div>
      <h3 class="font-semibold mb-1">No Photos Found</h3>
      <p class="text-sm text-muted-foreground">
        Get started by creating your first photo album. Click the "Upload
        Photos" button above.
      </p>
    </Card>

    <!-- Photo Preview Dialog -->
    <Dialog :open="!!previewPhoto" @update:open="previewPhoto = null">
      <DialogContent
        class="max-w-[95vw] max-h-[95vh] p-0 overflow-hidden bg-black/95 border-none"
      >
        <div class="relative w-full h-[90vh]">
          <!-- Navigation Buttons -->
          <Button
            v-if="currentPreviewIndex > 0"
            variant="ghost"
            size="icon"
            class="absolute left-4 top-1/2 -translate-y-1/2 z-10 h-12 w-12 rounded-full bg-black/50 hover:bg-black/70"
            @click="navigatePreview('prev')"
          >
            <ChevronLeft class="h-8 w-8 text-white" />
          </Button>

          <Button
            v-if="currentPreviewIndex < photos.length - 1"
            variant="ghost"
            size="icon"
            class="absolute right-4 top-1/2 -translate-y-1/2 z-10 h-12 w-12 rounded-full bg-black/50 hover:bg-black/70"
            @click="navigatePreview('next')"
          >
            <ChevronRight class="h-8 w-8 text-white" />
          </Button>

          <!-- Preview Image -->
          <NuxtImg
            v-if="previewPhoto"
            :src="getPreviewUrl(previewPhoto?.pathname)"
            :alt="previewPhoto.title || ''"
            class="w-full h-full object-contain"
          />

          <!-- Close Button -->
          <Button
            variant="ghost"
            size="icon"
            class="absolute top-4 right-4 h-10 w-10 rounded-full bg-black/50 hover:bg-black/70"
            @click="previewPhoto = null"
          >
            <X class="h-6 w-6 text-white" />
          </Button>

          <!-- Photo Info -->
          <div
            class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent"
          >
            <h3 class="text-white font-medium text-base">
              {{ previewPhoto?.title || 'Untitled' }}
            </h3>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    <!-- Delete Confirmation Dialog -->
    <AdminMenuDeleteDialog
      v-if="selectedPhotos.size"
      v-model:is-open="showDeleteDialog"
      :is-submitting="isLoading"
      menu-title="photos"
      :delete-state="'idle'"
      :description="`Are you sure you want to delete ${selectedPhotos.size} selected photos? This action cannot be undone.`"
      @confirm="deleteSelectedPhotos"
    />
  </div>
</template>

<style scoped>
.photos-grid {
  container-type: inline-size;
}

@container (min-width: 768px) {
  .photos-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@container (min-width: 1024px) {
  .photos-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

.photo-preview-enter-active,
.photo-preview-leave-active {
  transition: all 0.3s ease;
}

.photo-preview-enter-from,
.photo-preview-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
</style>
