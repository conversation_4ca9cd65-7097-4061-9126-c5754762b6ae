<script setup lang="ts">
import { toast } from 'vue-sonner';
import { useForm } from 'vee-validate';
import { landingPageConfigurationSchema } from '~~/shared/schema/section/landing-page/update';
import type { LandingPageConfiguration } from '~~/shared/schema/section/landing-page/update';
import type { fileSchema } from '~~/shared/schema';
import { toTypedSchema } from '@vee-validate/zod';

const props = defineProps<{
  modelValue: LandingPageConfiguration['campusDetails'];
}>();

const emit = defineEmits<{
  (
    e: 'update:model-value',
    value: LandingPageConfiguration['campusDetails']
  ): void;
}>();

// Create a schema for just the campus details section
const campusDetailsSchema = landingPageConfigurationSchema.shape.campusDetails;
const formSchema = toTypedSchema(campusDetailsSchema);

const initialValues = {
  title: props.modelValue?.title || '',
  description: props.modelValue?.description || '',
  image: props.modelValue?.image || undefined,
  callToAction: props.modelValue?.callToAction || undefined,
};

const primaryFile = ref<typeof fileSchema._type | undefined>(
  props.modelValue?.image
);

const { values, setFieldValue } = useForm({
  validationSchema: formSchema,
  initialValues,
});

const handleFileUpload = async (file: File | undefined) => {
  if (!file) {
    primaryFile.value = undefined;
    setFieldValue('image', undefined);
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'landing');
    form.append('title', 'Campus Image');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', {
      method: 'POST',
      body: form,
    });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
    };
    primaryFile.value = fileData;
    handleValueChange(fileData, 'image');
  } catch (err) {
    console.error('Failed to upload primary file:', err);
    toast.error('Failed to upload primary file');
  }
};

const handleValueChange = (value: any, key: keyof typeof initialValues) => {
  setFieldValue(key, value);
  emit('update:model-value', { ...values, [key]: value });
};
</script>

<template>
  <AccordionItem value="campus">
    <AccordionTrigger class="font-bold">Campus Details</AccordionTrigger>
    <AccordionContent>
      <Form
        :initial-values="initialValues"
        :validation-schema="formSchema"
        class="flex flex-col gap-4 pt-4"
      >
        <FormField v-slot="{ field, errorMessage }" name="title">
          <FormItem>
            <FormLabel>Title<span class="text-destructive">*</span></FormLabel>
            <FormControl>
              <Input
                :model-value="field.value"
                v-bind="field"
                placeholder="Enter the section title"
                @update:model-value="
                  (newValue) => handleValueChange(newValue, 'title')
                "
              />
            </FormControl>
            <FormDescription>
              The title of the section that will be displayed to users
            </FormDescription>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <FormField v-slot="{ field, errorMessage }" name="description">
          <FormItem>
            <FormLabel
              >Description<span class="text-destructive">*</span></FormLabel
            >
            <FormControl>
              <Textarea
                :model-value="field.value"
                v-bind="field"
                placeholder="Enter description content"
                @update:model-value="
                  (newValue) => handleValueChange(newValue, 'description')
                "
              />
            </FormControl>
            <FormDescription>
              The description of the campus that will be displayed to users
            </FormDescription>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <AdminBaseDropzone
          class="pt-2"
          :is-multiple-allowed="false"
          :file-types="['image']"
          title="Campus Image"
          :existing-files="primaryFile ? [primaryFile] : []"
          description="Upload campus image. Only one file is allowed."
          @files-selected="(files) => handleFileUpload(files[0]?.file)"
          @file-removed="() => (primaryFile = undefined)"
        />

        <AdminTemplateButton
          :disable-style="true"
          :hide-border="true"
          :model-value="values.callToAction"
          :show-button="
            values.callToAction !== null && values.callToAction !== undefined
          "
          @update:model-value="
            (newValue) => handleValueChange(newValue, 'callToAction')
          "
        />
      </Form>
    </AccordionContent>
  </AccordionItem>
</template>
