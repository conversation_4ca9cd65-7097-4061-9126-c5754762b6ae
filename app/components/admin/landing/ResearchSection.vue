<script setup lang="ts">
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import { landingPageConfigurationSchema } from '~~/shared/schema/section/landing-page/update';
import type { LandingPageConfiguration } from '~~/shared/schema/section/landing-page/update';

const props = defineProps<{
  modelValue: LandingPageConfiguration['research'];
}>();

const emit = defineEmits<{
  (e: 'update:model-value', value: LandingPageConfiguration['research']): void;
}>();

// Create a schema for just the research section
const researchSchema = landingPageConfigurationSchema.shape.research;
const formSchema = toTypedSchema(researchSchema);

const initialValues = {
  title: props.modelValue?.title || '',
  description: props.modelValue?.description || '',
  callToAction: props.modelValue?.callToAction || undefined,
};

const { values, setFieldValue } = useForm({
  validationSchema: formSchema,
  initialValues,
});

const handleValueChange = (value: any, key: keyof typeof initialValues) => {
  setFieldValue(key, value);
  emit('update:model-value', { ...values, [key]: value });
};
</script>

<template>
  <AccordionItem value="research">
    <AccordionTrigger class="font-bold">Research</AccordionTrigger>
    <AccordionContent>
      <Form
        :initial-values="initialValues"
        :validation-schema="formSchema"
        class="flex flex-col gap-4 pt-4"
      >
        <FormField v-slot="{ field, errorMessage }" name="title">
          <FormItem>
            <FormLabel>Title<span class="text-destructive">*</span></FormLabel>
            <FormControl>
              <Input
                :model-value="field.value"
                v-bind="field"
                placeholder="Enter the section title"
                @update:model-value="
                  (newValue) => handleValueChange(newValue, 'title')
                "
              />
            </FormControl>
            <FormDescription>
              The title of the section that will be displayed to users
            </FormDescription>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <FormField v-slot="{ field, errorMessage }" name="description">
          <FormItem>
            <FormLabel
              >Description<span class="text-destructive">*</span></FormLabel
            >
            <FormControl>
              <Textarea
                :model-value="field.value"
                v-bind="field"
                placeholder="Enter description content"
                @update:model-value="
                  (newValue) => handleValueChange(newValue, 'description')
                "
              />
            </FormControl>
            <FormDescription>
              The description of the research that will be displayed to users
            </FormDescription>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <AdminTemplateButton
          :disable-style="true"
          :hide-border="true"
          :model-value="values.callToAction"
          :show-button="
            values.callToAction !== null && values.callToAction !== undefined
          "
          @update:model-value="
            (newValue) => handleValueChange(newValue, 'callToAction')
          "
        />
      </Form>
    </AccordionContent>
  </AccordionItem>
</template>
