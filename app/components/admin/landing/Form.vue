<script setup lang="ts">
import { Loader2 } from 'lucide-vue-next';
import type {
  StatItem,
  LandingPageConfiguration,
} from '~~/shared/schema/section/landing-page/update';
import type {
  BUTTON_STYLES,
  BUTTON_TYPES,
} from '~~/server/database/tables/enums';

// Define types for our utility functions
type ButtonStyle = (typeof BUTTON_STYLES)[number];
type LinkType = 'internal' | 'external';
type ButtonType = (typeof BUTTON_TYPES)[number];

type CallToActionButton = {
  enabled: boolean;
  buttonText: string;
  icon?: string;
  newTab: boolean;
  buttonStyle: ButtonStyle;
  linkType: LinkType;
  internalLink?: string;
  externalLink?: string;
};

type LinkButton = {
  title: string;
  icon?: string | null;
  style: ButtonStyle;
  type: ButtonType;
  internalLink?: string | null;
  externalLink?: string | null;
  newTab: boolean;
};

const { landingPageData, isLoading, fetchLandingPage, updateLandingPage } =
  useLandingPage();

// Convert LandingPageConfiguration stats to the format expected by StatsSection
const convertStatsToAdapter = (stats: LandingPageConfiguration['stats']) => {
  if (!stats || !stats.items) {
    return [];
  }

  return stats.items.map((item) => ({
    icon: item.icon || '',
    value: item.value || 0,
    title: item.title || '',
  }));
};

// Convert adapter stats back to LandingPageConfiguration format
const convertAdapterToStats = (adapterStats: StatItem[]) => {
  return {
    items: adapterStats.map((item) => ({
      icon: item.icon,
      value: item.value,
      title: item.title,
    })),
  };
};

// Convert LandingPageConfiguration sectionVisibility to toggles format
// const convertVisibilityToToggles = (
//   visibility: LandingPageConfiguration['sectionVisibility']
// ) => {
//   return [
//     {
//       id: 1,
//       title: 'programsWeOffer',
//       isActive: visibility.programsWeOffer,
//       type: 'menu',
//       menuId: null,
//     },
//     {
//       id: 2,
//       title: 'events',
//       isActive: visibility.events,
//       type: 'event',
//       menuId: null,
//     },
//     {
//       id: 3,
//       title: 'gallery',
//       isActive: visibility.gallery,
//       type: 'gallery',
//       menuId: null,
//     },
//   ];
// };

// Convert toggles format back to LandingPageConfiguration sectionVisibility
// const convertTogglesToVisibility = (toggles: ToggleItem[]) => {
//   return {
//     programsWeOffer:
//       toggles.find((t) => t.title === 'programsWeOffer')?.isActive || false,
//     events: toggles.find((t) => t.title === 'events')?.isActive || false,
//     gallery: toggles.find((t) => t.title === 'gallery')?.isActive || false,
//   };
// };

// Utility function to convert from AdminTemplateButton's linkButtonSchema to CallToActionButtonSchema
const normalizeCallToActionFromComponent = (
  buttonData: LinkButton | null
): CallToActionButton => {
  if (!buttonData) {
    return {
      enabled: false,
      buttonText: 'Learn More',
      newTab: false,
      buttonStyle: 'primary',
      linkType: 'internal',
      internalLink: '/',
      icon: '',
    };
  }

  // Determine link type based on which link is provided
  const linkType = buttonData.internalLink ? 'internal' : 'external';

  // For buttonText, ensure it's never empty if enabled is true
  const buttonText = buttonData.title?.trim() || '';

  // Only set the link that matches the linkType
  const internalLink =
    linkType === 'internal' ? buttonData.internalLink || '/' : '';

  // Only set externalLink if linkType is external
  const externalLink =
    linkType === 'external' && buttonData.externalLink?.trim()
      ? buttonData.externalLink.startsWith('http')
        ? buttonData.externalLink
        : `https://${buttonData.externalLink}`
      : undefined;

  return {
    enabled: true,
    buttonText: buttonText || 'Learn More', // Default text to prevent empty text validation errors
    newTab: buttonData.newTab || false,
    buttonStyle: (buttonData.style as ButtonStyle) || 'primary',
    linkType,
    internalLink,
    externalLink,
    icon: buttonData.icon || '',
  };
};

// Helper function to create a valid callToAction object with required fields
const createValidCallToAction = (
  isEnabled: boolean = false
): CallToActionButton => {
  return {
    enabled: isEnabled,
    buttonText: 'Learn More',
    newTab: false,
    buttonStyle: 'primary',
    linkType: 'internal',
    internalLink: '/',
    icon: '',
  };
};

// Validates and fixes all callToAction fields to ensure they pass Zod validation
const validateCallToActionFields = (
  data: LandingPageConfiguration
): LandingPageConfiguration => {
  // Create a shallow copy
  const validated = { ...data };

  // Helper function to validate a single callToAction
  const validateSingleCallToAction = (
    cta: CallToActionButton | undefined | null
  ): CallToActionButton => {
    // If null or undefined, return a default valid object
    if (!cta) {
      return createValidCallToAction(false);
    }

    // Create a new object for the callToAction with all required fields
    const validatedCta: CallToActionButton = {
      // Ensure all required fields have values
      enabled: typeof cta.enabled === 'boolean' ? cta.enabled : false,
      buttonText: cta.buttonText?.trim() || 'Learn More',
      newTab: typeof cta.newTab === 'boolean' ? cta.newTab : false,
      buttonStyle: ['primary', 'secondary', 'outline'].includes(
        cta.buttonStyle as string
      )
        ? (cta.buttonStyle as ButtonStyle)
        : 'primary',
      linkType: ['internal', 'external'].includes(cta.linkType as string)
        ? (cta.linkType as LinkType)
        : 'internal',
      // Initialize with default values
      internalLink: '/',
      icon: cta.icon || '',
    };

    // Handle links based on linkType
    if (validatedCta.linkType === 'internal') {
      validatedCta.internalLink = cta.internalLink?.trim() || '/';
      // Don't set externalLink when linkType is internal
    } else {
      // For external link, ensure it's a valid URL
      let externalLink = cta.externalLink?.trim() || '';

      // Validate URL format - must start with http:// or https://
      if (!externalLink) {
        externalLink = 'https://example.com';
      } else if (!externalLink.match(/^https?:\/\//)) {
        // If URL doesn't start with http:// or https://, add https://
        externalLink = `https://${externalLink}`;
      }

      // Try to create a URL object to validate it further
      try {
        new URL(externalLink);
        validatedCta.externalLink = externalLink;
      } catch {
        // If URL is invalid even after our fixes, use the default
        validatedCta.externalLink = 'https://example.com';
      }
    }

    return validatedCta;
  };

  // Ensure principalsMessage has a valid callToAction
  if (!validated.principalsMessage) {
    validated.principalsMessage = {
      title: '',
      name: '',
      designation: '',
      message: '',
      callToAction: createValidCallToAction(),
    };
  } else {
    validated.principalsMessage = {
      ...validated.principalsMessage,
      callToAction: validateSingleCallToAction(
        validated.principalsMessage.callToAction
      ),
    };
  }

  // Ensure campusDetails has a valid callToAction
  if (!validated.campusDetails) {
    validated.campusDetails = {
      title: '',
      description: '',
      callToAction: createValidCallToAction(),
    };
  } else {
    validated.campusDetails = {
      ...validated.campusDetails,
      callToAction: validateSingleCallToAction(
        validated.campusDetails.callToAction
      ),
    };
  }

  // Ensure research has a valid callToAction
  if (!validated.research) {
    validated.research = {
      title: '',
      description: '',
      callToAction: createValidCallToAction(),
    };
  } else {
    validated.research = {
      ...validated.research,
      callToAction: validateSingleCallToAction(validated.research.callToAction),
    };
  }

  return validated;
};

const isError = ref({
  principalMessage: false,
  campusDetails: false,
  research: false,
  stats: false,
  sectionVisibility: false,
});

const principalMessage = ref<
  LandingPageConfiguration['principalsMessage'] | null
>(null);

const campusDetails = ref<LandingPageConfiguration['campusDetails'] | null>(
  null
);

const research = ref<LandingPageConfiguration['research'] | null>(null);

const stats = ref<LandingPageConfiguration['stats'] | null>(null);

// const sectionVisibility = ref<
//   LandingPageConfiguration['sectionVisibility'] | null
// >(null);
// Adapter state for child components
const statsAdapter = ref<StatItem[]>([]);
// const togglesAdapter = ref<ToggleItem[]>([]);

watch(
  landingPageData,
  () => {
    principalMessage.value = landingPageData.value.principalsMessage;
    campusDetails.value = landingPageData.value.campusDetails;
    research.value = landingPageData.value.research;
    stats.value = landingPageData.value.stats;
    // sectionVisibility.value = landingPageData.value.sectionVisibility;
    statsAdapter.value = convertStatsToAdapter(landingPageData.value.stats);
    // togglesAdapter.value = convertVisibilityToToggles(
    //   landingPageData.value.sectionVisibility
    // );
  },
  { deep: true, immediate: true }
);

// Add a validation function to check all form sections
const validateForm = (): boolean => {
  // Check if any section has errors
  const hasErrors = !Object.values(isError).some((error) => error);

  // Check if all required sections are present
  const hasAllSections =
    principalMessage.value &&
    campusDetails.value &&
    research.value &&
    stats.value;
  // && sectionVisibility.value;

  return !hasErrors && !!hasAllSections;
};

// Then modify the onSubmit function to use this validation
const onSubmit = async () => {
  if (validateForm()) {
    try {
      // Create submission data with all form values
      const submissionData: LandingPageConfiguration = {
        // Create default empty objects if values are null
        principalsMessage: {
          title: principalMessage.value?.title || '',
          name: principalMessage.value?.name || '',
          designation: principalMessage.value?.designation || '',
          message: principalMessage.value?.message || '',
          callToAction:
            principalMessage.value?.callToAction || createValidCallToAction(),
          // Include the image field if it exists
          image: principalMessage.value?.image,
        },
        campusDetails: {
          title: campusDetails.value?.title || '',
          description: campusDetails.value?.description || '',
          callToAction:
            campusDetails.value?.callToAction || createValidCallToAction(),
          // Include the image field if it exists
          image: campusDetails.value?.image,
        },
        research: {
          title: research.value?.title || '',
          description: research.value?.description || '',
          callToAction:
            research.value?.callToAction || createValidCallToAction(),
        },
        stats: stats.value || { items: [] },
        // sectionVisibility: sectionVisibility.value || {
        //   programsWeOffer: false,
        //   events: false,
        //   gallery: false,
        // },
      };

      // Validate and fix callToAction fields before submission
      const validatedData = validateCallToActionFields(submissionData);

      await updateLandingPage(validatedData);
    } catch (error: any) {
      // Error is already handled in the useLandingPage composable
      console.error('Form submission failed:', error);
    }
  }
};

// Helper function to update nested form fields
const updateFormSection = (
  section: keyof LandingPageConfiguration,
  value: any
) => {
  if (section === 'principalsMessage') {
    // Create a new object to avoid reference issues
    const updatedValue = { ...value };

    // Normalize the callToAction field if it exists
    if (updatedValue.callToAction) {
      updatedValue.callToAction = normalizeCallToActionFromComponent(
        updatedValue.callToAction as LinkButton
      );
    }
    principalMessage.value = updatedValue;
  } else if (section === 'campusDetails') {
    // Create a new object to avoid reference issues
    const updatedValue = { ...value };

    // Normalize the callToAction field if it exists
    if (updatedValue.callToAction) {
      updatedValue.callToAction = normalizeCallToActionFromComponent(
        updatedValue.callToAction as LinkButton
      );
    }
    campusDetails.value = updatedValue;
  } else if (section === 'research') {
    // Create a new object to avoid reference issues
    const updatedValue = { ...value };

    // Normalize the callToAction field if it exists
    if (updatedValue.callToAction) {
      updatedValue.callToAction = normalizeCallToActionFromComponent(
        updatedValue.callToAction as LinkButton
      );
    }
    research.value = updatedValue;
  } else if (section === 'stats') {
    statsAdapter.value = value;
    stats.value = convertAdapterToStats(value);
  }
  // else if (section === 'sectionVisibility') {
  //   togglesAdapter.value = value;
  //   sectionVisibility.value = convertTogglesToVisibility(value);
  // }
};

// Handle stats updates from the StatsSection component
const handleStatsUpdate = (updatedStats: StatItem[]) => {
  statsAdapter.value = updatedStats;
  const formattedStats = convertAdapterToStats(updatedStats);
  stats.value = formattedStats;
};

// Handle toggles updates from the SectionVisibility component
// const handleTogglesUpdate = (updatedToggles: ToggleItem[]) => {
//   togglesAdapter.value = updatedToggles;
//   const formattedVisibility = convertTogglesToVisibility(updatedToggles);
//   sectionVisibility.value = formattedVisibility;
// };

const onCancel = () => {
  fetchLandingPage();
};

onMounted(() => {
  fetchLandingPage();
});
</script>

<template>
  <div class="space-y-6">
    <Card class="p-6 border-none">
      <div
        v-if="isLoading && !landingPageData.principalsMessage.title"
        class="flex justify-center p-6"
      >
        <Loader2 class="h-8 w-8 animate-spin" />
        <span class="ml-2">Loading landing page data...</span>
      </div>

      <Accordion v-else type="single" collapsible class="w-full">
        <AdminLandingPrincipalMessageSection
          v-if="principalMessage"
          :model-value="principalMessage"
          @update:model-value="
            (value) => updateFormSection('principalsMessage', value)
          "
          @error="isError.principalMessage = true"
        />
        <AdminLandingCampusDetailsSection
          v-if="campusDetails"
          :model-value="campusDetails"
          @update:model-value="
            (value) => updateFormSection('campusDetails', value)
          "
          @error="isError.campusDetails = true"
        />
        <AdminLandingResearchSection
          v-if="research"
          :model-value="research"
          @update:model-value="(value) => updateFormSection('research', value)"
          @error="isError.research = true"
        />
        <AdminLandingStatsSection
          v-if="stats"
          :model-value="statsAdapter"
          @update:model-value="handleStatsUpdate"
          @error="isError.stats = true"
        />
        <!-- <AdminLandingSectionVisibility
          v-if="sectionVisibility"
          :model-value="togglesAdapter"
          @update:model-value="handleTogglesUpdate"
          @error="isError.sectionVisibility = true"
        /> -->
      </Accordion>

      <div class="flex justify-end mt-6 space-x-2">
        <Button variant="outline" @click="onCancel">Cancel</Button>
        <Button
          :disabled="isLoading || Object.values(isError).some((error) => error)"
          @click="onSubmit()"
        >
          <Loader2 v-if="isLoading" class="mr-2 h-4 w-4 animate-spin" />
          {{ isLoading ? 'Saving...' : 'Save Changes' }}
        </Button>
      </div>
    </Card>
  </div>
</template>
