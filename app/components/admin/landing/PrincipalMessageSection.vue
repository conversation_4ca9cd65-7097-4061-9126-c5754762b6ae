<script setup lang="ts">
import { toast } from 'vue-sonner';
import { useForm } from 'vee-validate';
import { landingPageConfigurationSchema } from '~~/shared/schema/section/landing-page/update';
import type { LandingPageConfiguration } from '~~/shared/schema/section/landing-page/update';
import type { fileSchema } from '~~/shared/schema';
import { toTypedSchema } from '@vee-validate/zod';

const props = defineProps<{
  modelValue: LandingPageConfiguration['principalsMessage'];
}>();

const emit = defineEmits<{
  (
    e: 'update:model-value',
    value: LandingPageConfiguration['principalsMessage']
  ): void;
}>();

// Create a schema for just the principal's message section
const principalMessageSchema =
  landingPageConfigurationSchema.shape.principalsMessage;
const formSchema = toTypedSchema(principalMessageSchema);

const initialValues = {
  title: props.modelValue?.title || '',
  name: props.modelValue?.name || '',
  designation: props.modelValue?.designation || '',
  message: props.modelValue?.message || '',
  image: props.modelValue?.image || undefined,
  callToAction: props.modelValue?.callToAction || undefined,
};

const primaryFile = ref<typeof fileSchema._type | undefined>(
  props.modelValue?.image
);

const { values, setFieldValue } = useForm({
  validationSchema: formSchema,
  initialValues,
});

const handleFileUpload = async (file: File | undefined) => {
  if (!file) {
    primaryFile.value = undefined;
    setFieldValue('image', undefined);
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'landing');
    form.append('title', 'Principal Image');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', {
      method: 'POST',
      body: form,
    });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
    };
    primaryFile.value = fileData;
    handleValueChange(fileData, 'image');
  } catch (err) {
    console.error('Failed to upload primary file:', err);
    toast.error('Failed to upload primary file');
  }
};

const handleValueChange = (value: any, key: keyof typeof initialValues) => {
  setFieldValue(key, value);
  emit('update:model-value', { ...values, [key]: value });
};
</script>

<template>
  <AccordionItem value="principal">
    <AccordionTrigger class="font-bold">Principal's Message</AccordionTrigger>
    <AccordionContent>
      <Form
        :initial-values="initialValues"
        :validation-schema="formSchema"
        class="flex flex-col gap-4 pt-4"
      >
        <FormField v-slot="{ field, errorMessage }" name="title">
          <FormItem>
            <FormLabel>Title<span class="text-destructive">*</span></FormLabel>
            <FormControl>
              <Input
                :model-value="field.value"
                v-bind="field"
                placeholder="Enter the section title"
                @update:model-value="
                  (newValue) => handleValueChange(newValue, 'title')
                "
              />
            </FormControl>
            <FormDescription>
              The title of the section that will be displayed to users
            </FormDescription>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <div class="grid md:grid-cols-2 gap-4">
          <FormField v-slot="{ field, errorMessage }" name="name">
            <FormItem>
              <FormLabel>Name<span class="text-destructive">*</span></FormLabel>
              <FormControl>
                <Input
                  :model-value="field.value"
                  v-bind="field"
                  placeholder="Principal's name"
                  @update:model-value="
                    (newValue) => handleValueChange(newValue, 'name')
                  "
                />
              </FormControl>
              <FormDescription>
                The name of the principal that will be displayed to users
              </FormDescription>
              <FormMessage>{{ errorMessage }}</FormMessage>
            </FormItem>
          </FormField>

          <FormField v-slot="{ field, errorMessage }" name="designation">
            <FormItem>
              <FormLabel
                >Designation<span class="text-destructive">*</span></FormLabel
              >
              <FormControl>
                <Input
                  :model-value="field.value"
                  v-bind="field"
                  placeholder="Principal's designation"
                  @update:model-value="
                    (newValue) => handleValueChange(newValue, 'designation')
                  "
                />
              </FormControl>
              <FormDescription>
                The designation of the principal that will be displayed to users
              </FormDescription>
              <FormMessage>{{ errorMessage }}</FormMessage>
            </FormItem>
          </FormField>
        </div>

        <FormField v-slot="{ field, errorMessage }" name="message">
          <FormItem>
            <FormLabel
              >Message<span class="text-destructive">* </span></FormLabel
            >
            <FormControl>
              <Textarea
                :model-value="field.value"
                v-bind="field"
                placeholder="Enter message content"
                @update:model-value="
                  (newValue) => handleValueChange(newValue, 'message')
                "
              />
            </FormControl>
            <FormDescription>
              The message of the principal that will be displayed to users
            </FormDescription>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <AdminBaseDropzone
          class="pt-2"
          :is-multiple-allowed="false"
          :file-types="['image']"
          title="Principal's Image"
          :existing-files="primaryFile ? [primaryFile] : []"
          description="Upload principal's file. Only one file is allowed."
          @files-selected="(files) => handleFileUpload(files[0]?.file)"
          @file-removed="() => (primaryFile = undefined)"
        />

        <AdminTemplateButton
          :disable-style="true"
          :hide-border="true"
          :model-value="values.callToAction"
          :show-button="
            values.callToAction !== null && values.callToAction !== undefined
          "
          @update:model-value="
            (newValue) => handleValueChange(newValue, 'callToAction')
          "
        />
      </Form>
    </AccordionContent>
  </AccordionItem>
</template>
