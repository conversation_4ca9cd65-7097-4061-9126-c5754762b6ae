<script setup lang="ts">
// Define the ToggleItem type to match what's expected by the parent component
export type ToggleItem = {
  id: number;
  title: string;
  isActive: boolean;
  type: string;
  menuId: number | null;
};

const props = defineProps<{
  modelValue: ToggleItem[];
}>();

const emit = defineEmits<{
  (e: 'update:model-value', value: ToggleItem[]): void;
  (e: 'error'): void;
}>();

const getToggleByType = (type: string) => {
  return props.modelValue.find((toggle: ToggleItem) => toggle.type === type);
};

const showPrograms = ref(getToggleByType('menu')?.isActive ?? false);
const showEvents = ref(getToggleByType('event')?.isActive ?? false);
const showGallery = ref(getToggleByType('gallery')?.isActive ?? false);

watch([showPrograms, showEvents, showGallery], () => {
  const programsToUpdate = props.modelValue.find(
    (toggle: ToggleItem) => toggle.type === 'menu'
  )!;
  const eventsToUpdate = props.modelValue.find(
    (toggle: ToggleItem) => toggle.type === 'event'
  )!;
  const galleryToUpdate = props.modelValue.find(
    (toggle: ToggleItem) => toggle.type === 'gallery'
  )!;

  const updatedPrograms = {
    ...programsToUpdate,
    isActive: showPrograms.value,
  };

  const updatedEvents = {
    ...eventsToUpdate,
    isActive: showEvents.value,
  };

  const updatedGallery = {
    ...galleryToUpdate,
    isActive: showGallery.value,
  };

  const updatedToggle = [updatedPrograms, updatedEvents, updatedGallery];

  emit('update:model-value', updatedToggle);
});
</script>

<template>
  <AccordionItem value="visibility">
    <AccordionTrigger class="font-bold">Section Visibility</AccordionTrigger>
    <AccordionContent>
      <div class="space-y-4 pt-4">
        <div
          class="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"
        >
          <div class="space-y-0.5">
            <Label>Programs We Offer</Label>
            <div>Toggle visibility of the programs we offer section</div>
          </div>
          <div class="flex items-center space-x-2">
            <Switch
              :id="`toggle-programs-we-offer`"
              v-model="showPrograms"
              :checked="showPrograms"
              @update:checked="showPrograms = $event"
            />
            <Label :for="`toggle-programs-we-offer`">
              {{ showPrograms ? 'Visible' : 'Hidden' }}
            </Label>
          </div>
        </div>
        <div
          class="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"
        >
          <div class="space-y-0.5">
            <Label>Events</Label>
            <div>Toggle visibility of the news & events section</div>
          </div>
          <div class="flex items-center space-x-2">
            <Switch
              :id="`toggle-events`"
              v-model="showEvents"
              :checked="showEvents"
              @update:checked="showEvents = $event"
            />
            <Label :for="`toggle-events`">
              {{ showEvents ? 'Visible' : 'Hidden' }}
            </Label>
          </div>
        </div>

        <div
          class="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"
        >
          <div class="space-y-0.5">
            <Label>Gallery</Label>
            <div>Toggle visibility of the gallery section</div>
          </div>
          <div class="flex items-center space-x-2">
            <Switch
              :id="`toggle-gallery`"
              v-model="showGallery"
              :checked="showGallery"
              @update:checked="showGallery = $event"
            />
            <Label :for="`toggle-gallery`">
              {{ showGallery ? 'Visible' : 'Hidden' }}
            </Label>
          </div>
        </div>
      </div>
    </AccordionContent>
  </AccordionItem>
</template>
