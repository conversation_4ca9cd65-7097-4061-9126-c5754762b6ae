<script setup lang="ts">
import { iconOptions } from '~/constants/admin/icons';
import { z } from 'zod';
import type { StatItem } from '~~/shared/schema/section/landing-page/update';

const props = defineProps<{
  modelValue: StatItem[];
}>();

const emit = defineEmits<{
  (e: 'update:model-value', value: StatItem[]): void;
  (e: 'error'): void;
}>();

// Create a validation schema for stats items
const statItemSchema = z.object({
  icon: z.string().min(1, 'Icon is required'),
  value: z.number().min(1, 'Value must be at least 1'),
  title: z.string().min(1, 'Title is required'),
});

// Initialize stats list from props
const statsList = ref<StatItem[]>([]);

// Track validation state
const hasErrors = ref(false);

// Validate all stats items
const validateStats = () => {
  let isValid = true;
  // Validate each stat item
  for (const stat of statsList.value) {
    try {
      statItemSchema.parse(stat);
    } catch {
      console.log('stat', stat);
      isValid = false;
      break;
    }
  }

  hasErrors.value = !isValid;
  if (!isValid) {
    emit('error');
  }

  return isValid;
};

// Watch for changes in the modelValue prop
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && Array.isArray(newValue)) {
      statsList.value = [...newValue];
      validateStats();
    } else {
      // If no stats provided, initialize with one empty stat
      statsList.value = [
        {
          icon: '',
          value: 0,
          title: '',
        },
      ];
      validateStats();
    }
  },
  { immediate: true, deep: true }
);
// Handle value changes
const handleValueChange = (
  value: any,
  index: number,
  field: keyof StatItem
) => {
  const updatedStats = [...statsList.value];

  // Ensure we have a properly typed object
  updatedStats[index] = {
    icon: updatedStats[index]?.icon || '',
    value: updatedStats[index]?.value || 0,
    title: updatedStats[index]?.title || '',
    ...updatedStats[index],
    [field]: value,
  };

  statsList.value = updatedStats;
  emit('update:model-value', updatedStats);

  // Validate after update
  validateStats();
};
</script>

<template>
  <AccordionItem value="stats">
    <AccordionTrigger class="font-bold">Stats</AccordionTrigger>
    <AccordionContent>
      <div class="flex flex-col gap-4 pt-4">
        <div
          v-for="(stat, index) in statsList"
          :key="index"
          class="grid md:grid-cols-3 gap-4 p-4 border rounded relative"
        >
          <div class="space-y-2">
            <Label>Icon<span class="text-destructive">*</span></Label>
            <Select
              :model-value="stat.icon"
              @update:model-value="
                (value) => handleValueChange(value, index, 'icon')
              "
            >
              <SelectTrigger :class="{ 'border-red-500': !stat.icon }">
                <SelectValue placeholder="Select icon" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="option in iconOptions"
                  :key="option.icon"
                  :value="option.icon"
                >
                  <div class="flex items-center gap-2">
                    <Icon :name="option.icon" />
                    {{ option.label }}
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            <p v-if="!stat.icon" class="text-sm text-red-500">
              Icon is required
            </p>
          </div>

          <div class="space-y-2">
            <Label>Value<span class="text-destructive">*</span></Label>
            <Input
              :model-value="stat.value"
              type="number"
              placeholder="Enter stat value"
              :class="{ 'border-red-500': stat.value < 1 }"
              @update:model-value="
                (value) => handleValueChange(Number(value), index, 'value')
              "
            />
            <p v-if="stat.value < 1" class="text-sm text-red-500">
              Value must be at least 1
            </p>
          </div>

          <div class="space-y-2">
            <Label>Title<span class="text-destructive">*</span></Label>
            <Input
              :model-value="stat.title"
              placeholder="Enter stat title"
              :class="{ 'border-red-500': !stat.title }"
              @update:model-value="
                (value) => handleValueChange(value, index, 'title')
              "
            />
            <p v-if="!stat.title" class="text-sm text-red-500">
              Title is required
            </p>
          </div>
        </div>

        <div
          v-if="hasErrors"
          class="mt-4 p-3 bg-red-50 border border-red-200 rounded text-red-600"
        >
          <p>Please fix the validation errors above before saving.</p>
        </div>
      </div>
    </AccordionContent>
  </AccordionItem>
</template>
