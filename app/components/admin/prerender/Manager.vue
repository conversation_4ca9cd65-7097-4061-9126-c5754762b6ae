<script setup lang="ts">
import { AlertCircle, RefreshCw, CheckCircle2 } from 'lucide-vue-next';
import { z } from 'zod';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
// Define types locally to avoid import errors
interface PrerenderResponse {
  success: boolean;
  message: string;
  taskKey?: string;
  error?: string;
}

interface PrerenderStatusData {
  key: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  routes?: string[];
  all?: boolean;
  createdAt?: number;
  processingStartedAt?: number;
  completedAt?: number;
  failedAt?: number;
  deploymentId?: string;
  deploymentUrl?: string;
  error?: string;
}

interface PrerenderStatusResponse {
  success: boolean;
  data?: PrerenderStatusData;
  message?: string;
  error?: string;
}

const isLoading = ref(false);
const isCheckingStatus = ref(false);
const result = ref<PrerenderResponse | null>(null);
const statusResult = ref<PrerenderStatusData | null>(null);
const showResult = ref(false);
const error = ref<string | null>(null);
const currentTaskKey = ref<string | null>(null);
const statusCheckInterval = ref<NodeJS.Timeout | null>(null);

// Define simplified form schema - we don't need any inputs now
const formSchema = toTypedSchema(z.object({}));

// Initialize form
const { handleSubmit, resetForm } = useForm({
  validationSchema: formSchema,
  initialValues: {},
});

// Function to check the status of a pre-rendering task
async function checkPrerenderStatus(key: string) {
  if (!key) return;

  try {
    isCheckingStatus.value = true;

    const response = await $fetch<PrerenderStatusResponse>(
      `/api/admin/prerender/status?key=${key}`
    );

    if (response.success && response.data) {
      statusResult.value = response.data;

      // If the task is completed or failed, clear the interval
      if (
        response.data.status === 'completed' ||
        response.data.status === 'failed'
      ) {
        if (statusCheckInterval.value) {
          clearInterval(statusCheckInterval.value);
          statusCheckInterval.value = null;
        }

        if (response.data.status === 'failed') {
          error.value = response.data.error || 'Pre-rendering failed';
        }
      }
    } else {
      console.error('Failed to get pre-rendering status:', response);
    }
  } catch (err: any) {
    console.error('Error checking pre-rendering status:', err);
  } finally {
    isCheckingStatus.value = false;
  }
}

// Handle form submission
const onSubmit = handleSubmit(async () => {
  try {
    isLoading.value = true;
    error.value = null;
    showResult.value = false;
    statusResult.value = null;

    // Clear any existing interval
    if (statusCheckInterval.value) {
      clearInterval(statusCheckInterval.value);
      statusCheckInterval.value = null;
    }

    // Make API request - no body needed
    const response = await $fetch<PrerenderResponse>('/api/admin/prerender', {
      method: 'POST',
    });

    result.value = response;
    showResult.value = true;

    if (!response.success) {
      error.value = response.error || 'Unknown error occurred';
    } else if (response.taskKey) {
      // Use the task key from the response
      currentTaskKey.value = response.taskKey;

      // Start checking the status periodically
      if (currentTaskKey.value) {
        await checkPrerenderStatus(currentTaskKey.value);
      }
      statusCheckInterval.value = setInterval(() => {
        if (currentTaskKey.value) {
          checkPrerenderStatus(currentTaskKey.value);
        }
      }, 5000); // Check every 5 seconds
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to trigger pre-rendering';
    console.error('Pre-rendering error:', err);
  } finally {
    isLoading.value = false;
  }
});

// Reset the form and results
function resetAll() {
  resetForm();
  result.value = null;
  statusResult.value = null;
  showResult.value = false;
  error.value = null;
  currentTaskKey.value = null;

  // Clear any existing interval
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value);
    statusCheckInterval.value = null;
  }
}

// Clean up on component unmount
onBeforeUnmount(() => {
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value);
  }
});
</script>

<template>
  <div class="space-y-6">
    <div class="flex flex-col space-y-2">
      <h2 class="text-2xl font-bold tracking-tight">Pre-render Manager</h2>
      <p class="text-muted-foreground">
        Pre-render pages to improve performance and SEO. This process will build
        static HTML files for the specified routes.
      </p>
    </div>

    <Card>
      <CardHeader>
        <CardTitle>Pre-render Configuration</CardTitle>
        <CardDescription>
          Specify which pages to pre-render or select "Pre-render All Pages" to
          pre-render all public pages.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form class="space-y-6" @submit.prevent="onSubmit">
          <div class="space-y-4">
            <p class="text-muted-foreground">
              Clicking the button below will pre-render all public pages except
              admin routes. This process may take several minutes to complete.
            </p>
          </div>

          <!-- Error Message -->
          <Alert v-if="error" variant="destructive">
            <AlertCircle class="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{{ error }}</AlertDescription>
          </Alert>

          <!-- Success Message -->
          <Alert v-if="showResult && result?.success" variant="success">
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>
              {{ result?.message }}
              <div class="mt-2">
                <p class="font-semibold">
                  All public pages will be pre-rendered.
                </p>
              </div>
            </AlertDescription>
          </Alert>

          <!-- Status Message -->
          <Alert
            v-if="statusResult"
            :variant="
              statusResult.status === 'failed' ? 'destructive' : 'default'
            "
            class="mt-4"
          >
            <AlertTitle class="flex items-center">
              <RefreshCw
                v-if="
                  statusResult.status === 'pending' ||
                  statusResult.status === 'processing'
                "
                class="mr-2 h-4 w-4 animate-spin"
              />
              <CheckCircle2
                v-else-if="statusResult.status === 'completed'"
                class="mr-2 h-4 w-4 text-green-500"
              />
              <AlertCircle v-else class="mr-2 h-4 w-4" />
              Pre-rendering Status:
              {{
                statusResult.status.charAt(0).toUpperCase() +
                statusResult.status.slice(1)
              }}
            </AlertTitle>
            <AlertDescription>
              <div class="mt-2 space-y-2">
                <p v-if="statusResult.status === 'pending'">
                  Waiting to start pre-rendering...
                </p>
                <p v-if="statusResult.status === 'processing'">
                  Pre-rendering in progress...
                </p>
                <p v-if="statusResult.status === 'completed'">
                  Pre-rendering completed successfully!
                </p>
                <p v-if="statusResult.status === 'failed'">
                  Pre-rendering failed: {{ statusResult.error }}
                </p>

                <div v-if="statusResult.deploymentId" class="mt-2">
                  <p class="font-semibold">Deployment Information:</p>
                  <p class="text-sm">
                    Deployment ID: {{ statusResult.deploymentId }}
                  </p>
                  <p v-if="statusResult.deploymentUrl" class="text-sm">
                    Preview URL:
                    <a
                      :href="statusResult.deploymentUrl"
                      target="_blank"
                      class="text-blue-500 hover:underline"
                      >{{ statusResult.deploymentUrl }}</a
                    >
                  </p>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          <!-- Action Buttons -->
          <div class="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              :disabled="isLoading"
              @click="resetAll"
            >
              Reset
            </Button>
            <Button type="submit" :disabled="isLoading">
              <RefreshCw v-if="isLoading" class="mr-2 h-4 w-4 animate-spin" />
              <span v-if="isLoading">Processing...</span>
              <span v-else>Start Pre-rendering</span>
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Pre-rendering Information</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div>
          <h3 class="font-semibold mb-2">What is Pre-rendering?</h3>
          <p class="text-muted-foreground">
            Pre-rendering generates static HTML files for your pages at build
            time, which improves:
          </p>
          <ul class="list-disc pl-5 mt-2 text-muted-foreground">
            <li>Page load performance</li>
            <li>Search engine optimization (SEO)</li>
            <li>Server resource usage</li>
          </ul>
        </div>

        <div>
          <h3 class="font-semibold mb-2">When to Use Pre-rendering</h3>
          <p class="text-muted-foreground">
            Pre-render pages after making significant content changes that
            should be reflected in the static HTML. Common scenarios include:
          </p>
          <ul class="list-disc pl-5 mt-2 text-muted-foreground">
            <li>After updating important content on the homepage</li>
            <li>After adding new programs or courses</li>
            <li>After updating department information</li>
            <li>
              Before major traffic events (admissions period, results
              announcements)
            </li>
          </ul>
        </div>

        <div>
          <h3 class="font-semibold mb-2">Important Notes</h3>
          <ul class="list-disc pl-5 text-muted-foreground">
            <li>Pre-rendering may take several minutes to complete</li>
            <li>The website will remain accessible during pre-rendering</li>
            <li>
              New content will be visible after the pre-rendering process
              completes
            </li>
            <li>Admin pages are never pre-rendered</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
