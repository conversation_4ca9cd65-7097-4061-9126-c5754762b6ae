<!-- //components/Admin/Base/Dropzone.vue -->
<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useDropZone } from '@vueuse/core';
import {
  File,
  FileAudio,
  FileImage,
  FileText,
  FileVideo,
  PencilLine,
  Save,
  UploadCloud,
  X,
} from 'lucide-vue-next';
import { toast } from 'vue-sonner';
import type { fileSchema } from '~~/shared/schema';
import { getPreviewUrl as getGlobalPreviewUrl } from '@/lib/url';

interface FileEntry {
  file: File | null;
  title?: string;
  editingTitle: boolean;
  pathname?: string;
  type: string;
  isExisting: boolean;
}

const props = defineProps<{
  isMultipleAllowed: boolean;
  fileTypes: ('image' | 'pdf' | 'text' | 'audio' | 'video')[];
  defaultTitle?: string;
  title: string;
  description: string;
  enableTitles?: boolean;
  existingFiles?: (typeof fileSchema._type)[];
  isSubmitting?: boolean;
}>();

const emit = defineEmits<{
  (
    e: 'filesSelected',
    files: { file: File; title?: string }[],
    isTitleEdit: boolean
  ): void;
  (e: 'fileRemoved', title: string): void;
  (
    e: 'titleChanged',
    newTitle: string | undefined,
    identifier: string | undefined,
    sectionIndex: number
  ): void;
}>();

// Computed
const acceptedFileTypes = computed(() => {
  const mimeTypes: string[] = [];
  props.fileTypes.forEach((type) => {
    switch (type) {
      case 'image':
        mimeTypes.push('image/*');
        break;
      case 'pdf':
        mimeTypes.push('application/pdf');
        break;
      case 'text':
        mimeTypes.push('text/*');
        break;
      case 'audio':
        mimeTypes.push('audio/*');
        break;
      case 'video':
        mimeTypes.push('video/*');
        break;
    }
  });
  return mimeTypes;
});

// State
const files = ref<FileEntry[]>([]);
const filePreviewUrls = ref<Map<string, string>>(new Map());
const dropZoneRef = ref<HTMLElement | null>(null);
const fileInputRef = ref<HTMLInputElement | null>(null);

// Add constant for max file size (5MB in bytes)
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

// Initialize with existing files
onMounted(() => {
  if (props.existingFiles?.length) {
    files.value = props.existingFiles.map((file) => ({
      file: null,
      title: file.title || file.pathname.split('/').pop(),
      editingTitle: false,
      pathname: file.pathname,
      type: file.type,
      isExisting: true,
    }));
  }
});

// Drop zone handling
const { isOverDropZone } = useDropZone(dropZoneRef, {
  onDrop: (files: File[] | null) => {
    if (files) {
      const validFiles = files.filter((file) =>
        acceptedFileTypes.value.some((type) => {
          if (type.endsWith('/*')) {
            const baseType = type.split('/')[0];
            return file.type.startsWith(`${baseType}/`);
          }
          return file.type === type;
        })
      );
      handleFiles(validFiles);
    }
  },
});

// File handling methods
function handleFileSelect(event: Event) {
  const input = event.target as HTMLInputElement;
  if (input.files) {
    handleFiles(Array.from(input.files));
    input.value = ''; // Reset input for same file selection
  }
}

function isFileAlreadyUploaded(newFile: File): boolean {
  return files.value.some((existingEntry) => {
    // Check for duplicate file names/sizes
    if (
      existingEntry.file?.name === newFile.name &&
      existingEntry.file?.size === newFile.size
    ) {
      return true; // Remove the file removal logic from here
    }

    // Check for duplicate pathnames
    const newPathname = newFile.name;
    const existingPathname =
      existingEntry.pathname || existingEntry.file?.name || existingEntry.title;

    return existingPathname === newPathname;
  });
}

function emitFiles(isTitleEdit: boolean = false) {
  const newFiles = files.value
    .filter((entry) => !entry.isExisting && entry.file)
    .map((entry) => ({
      file: entry.file!,
      title: entry.title,
    }));

  emit('filesSelected', newFiles, isTitleEdit);
}

async function handleFiles(newFiles: File[]) {
  if (!props.isMultipleAllowed) {
    files.value = [];
    filePreviewUrls.value.clear();
    newFiles = newFiles.slice(0, 1);
  }

  const rejectedFiles: { name: string; reason: string }[] = [];
  const validFiles: File[] = [];

  // Process each file
  for (const file of newFiles) {
    // Check if file is already uploaded
    if (await isFileAlreadyUploaded(file)) {
      rejectedFiles.push({
        name: file.name,
        reason: 'File already exists',
      });
      continue; // Skip this file
    }

    // Rest of validation checks
    if (file.size > MAX_FILE_SIZE) {
      rejectedFiles.push({
        name: file.name,
        reason: 'File exceeds maximum size of 5MB',
      });
      continue;
    }

    if (file.type === 'image/svg+xml') {
      rejectedFiles.push({
        name: file.name,
        reason: 'SVG files are not allowed',
      });
      continue;
    }

    const isValidType = acceptedFileTypes.value.some((type) => {
      if (type.endsWith('/*')) {
        const baseType = type.split('/')[0];
        return file.type.startsWith(`${baseType}/`);
      }
      return file.type === type;
    });

    if (!isValidType) {
      rejectedFiles.push({ name: file.name, reason: 'Invalid file type' });
      continue;
    }

    validFiles.push(file);
  }

  // Show error messages for rejected files
  if (rejectedFiles.length > 0) {
    toast.error(
      rejectedFiles.map(({ name, reason }) => `${name}: ${reason}`).join('\n')
    );
  }

  // Only process valid files that aren't duplicates
  validFiles.forEach((file) => {
    const entry: FileEntry = {
      file,
      title: props.defaultTitle || file.name.split('.')[0],
      editingTitle: false,
      type: file.type,
      isExisting: false,
    };
    files.value.push(entry);

    if (isImageFile(file)) {
      const previewUrl = URL.createObjectURL(file);
      filePreviewUrls.value.set(file.name, previewUrl);
    }
  });

  // Only emit if there are valid files to process
  if (validFiles.length > 0) {
    emitFiles();
  }
}

function removeFile(index: number) {
  const fileEntry = files.value[index];
  if (!fileEntry) return;

  if (fileEntry.file && filePreviewUrls.value.has(fileEntry.file.name)) {
    URL.revokeObjectURL(filePreviewUrls.value.get(fileEntry.file.name)!);
    filePreviewUrls.value.delete(fileEntry.file.name);
  }

  // Emit removed file info
  if (fileEntry.isExisting) {
    emit('fileRemoved', fileEntry.title || fileEntry.pathname!);
  }

  files.value.splice(index, 1);
  emitFiles();
}
// Add new ref for storing the original title before editing
const originalTitle = ref('');

// Modify toggleTitleEdit to store original title
function toggleTitleEdit(index: number) {
  if (!files.value[index]!.editingTitle) {
    // Store original title when entering edit mode
    originalTitle.value = files.value[index]!.title || '';
  } else {
    // Emit files when saving changes
    emitFiles(true);
  }
  files.value[index]!.editingTitle = !files.value[index]!.editingTitle;
}

// Add function to cancel editing
function cancelTitleEdit(index: number) {
  // Restore original title
  files.value[index]!.title = originalTitle.value;
  files.value[index]!.editingTitle = false;
}

// Utility methods
function openFilePicker() {
  if (props.isSubmitting) return;
  fileInputRef.value?.click();
}

function isImageFile(file: File | { type: string }) {
  return file.type.startsWith('image/');
}

function getFileIcon(fileType: string) {
  if (fileType.startsWith('image/')) return FileImage;
  if (fileType.startsWith('audio/')) return FileAudio;
  if (fileType.startsWith('video/')) return FileVideo;
  if (fileType.includes('pdf') || fileType.includes('text')) return FileText;
  return File;
}

function getPreviewUrl(entry: FileEntry): string {
  if (entry.isExisting) return getGlobalPreviewUrl(entry.pathname || '');
  return entry.file ? filePreviewUrls.value.get(entry.file.name) || '' : '';
}

function formatFileSize(bytes: number) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
}
</script>

<template>
  <div class="flex flex-col gap-3">
    <!-- Header -->
    <div class="space-y-3">
      <div class="flex items-center justify-between">
        <Label>{{ title }}</Label>
      </div>
      <p class="text-xs text-muted-foreground">
        {{
          description ??
          `Accepted types: ${props.fileTypes.join(', ')}. Maximum file size: 5MB`
        }}
      </p>
    </div>

    <!-- Drop Zone -->
    <div
      ref="dropZoneRef"
      class="border-2 bg-border/20 border-dashed rounded-lg p-4 text-center transition-colors border-border/50"
      :class="[
        isOverDropZone ? 'border-primary bg-primary/5' : 'border-border',
        props.isSubmitting ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
      ]"
      @dragover.prevent
      @drop.prevent
      @click="openFilePicker"
    >
      <div class="flex flex-col items-center">
        <UploadCloud class="h-6 w-6 text-muted-foreground" />
        <div class="text-sm text-muted-foreground mt-2 mb-1">
          Drop files here or
          <Button
            type="button"
            variant="link"
            class="px-1 h-auto p-0 font-bold"
            :disabled="props.isSubmitting"
          >
            browse
          </Button>
        </div>
        <p class="text-xs text-muted-foreground/50">
          {{ fileTypes.join(', ') }}
        </p>
      </div>
      <input
        ref="fileInputRef"
        type="file"
        :multiple="isMultipleAllowed"
        :accept="acceptedFileTypes.join(',')"
        class="hidden"
        :disabled="props.isSubmitting"
        @change="handleFileSelect"
      />
    </div>
    <!-- Unified File List -->
    <div v-if="files.length > 0" class="space-y-2">
      <div
        v-for="(fileEntry, index) in files"
        :key="index"
        class="flex flex-col gap-2 p-2 border rounded-lg bg-muted/10"
      >
        <div class="flex items-center gap-2">
          <!-- Preview/Icon -->
          <div class="h-10 w-10 shrink-0">
            <NuxtImg
              v-if="isImageFile({ type: fileEntry.type })"
              :src="getPreviewUrl(fileEntry)"
              class="h-full w-full object-cover rounded"
              alt="Preview"
            />
            <div
              v-else
              class="h-full w-full rounded bg-muted flex items-center justify-center"
            >
              <component
                :is="getFileIcon(fileEntry.type)"
                class="h-5 w-5 text-muted-foreground"
              />
            </div>
          </div>

          <!-- File Info -->

          <div class="flex-1 min-w-0">
            <div v-if="props.enableTitles" class="flex items-center gap-2">
              <div
                v-if="!fileEntry.editingTitle"
                class="text-sm font-medium truncate flex items-center gap-2"
              >
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  class="h-6 w-6 hover:bg-muted"
                  :disabled="props.isSubmitting"
                  @click="toggleTitleEdit(index)"
                >
                  <PencilLine class="h-3 w-3" />
                </Button>
                <p class="text-sm font-medium truncate">
                  {{
                    fileEntry.title ||
                    fileEntry.file?.name ||
                    fileEntry.pathname
                  }}
                </p>
              </div>
              <div
                v-else
                class="flex items-center gap-2 w-full bg-background rounded-md border shadow-sm"
              >
                <Input
                  v-model="fileEntry.title"
                  class="h-8 text-sm border-0 focus-visible:ring-1"
                  placeholder="Enter title"
                  @keyup.enter="toggleTitleEdit(index)"
                  @keyup.esc="cancelTitleEdit(index)"
                />
                <div class="flex items-center pr-2 gap-1">
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    class="h-6 w-6 hover:bg-muted"
                    :disabled="props.isSubmitting"
                    @click="toggleTitleEdit(index)"
                  >
                    <Save class="h-3 w-3 text-primary" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    class="h-6 w-6 hover:bg-muted"
                    :disabled="props.isSubmitting"
                    @click="cancelTitleEdit(index)"
                  >
                    <X class="h-3 w-3 text-destructive" />
                  </Button>
                </div>
              </div>
            </div>
            <p v-else class="text-sm font-medium truncate">
              {{
                fileEntry.title || fileEntry.file?.name || fileEntry.pathname
              }}
            </p>
            <p class="text-xs text-muted-foreground">
              <span v-if="fileEntry.isExisting">Existing file</span>
              <span v-else-if="fileEntry.file">
                {{ formatFileSize(fileEntry.file.size) }}
              </span>
            </p>
          </div>

          <!-- Remove Button -->
          <Button
            type="button"
            variant="ghost"
            size="icon"
            class="h-6 w-6"
            :disabled="props.isSubmitting"
            @click="removeFile(index)"
          >
            <X class="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
