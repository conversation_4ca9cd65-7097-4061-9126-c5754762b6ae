<script setup lang="ts">
import type { HTMLAttributes } from 'vue';
import { cn } from '@/lib/utils';
import { useVModel } from '@vueuse/core';

const props = defineProps<{
  defaultValue?: string | number;
  modelValue: string | number;
  disabled?: boolean;
  placeholder?: string;
  id?: string;
  class?: HTMLAttributes['class'];
}>();

const emits = defineEmits<{
  (e: 'update:model-value', payload: string | number): void;
}>();

const modelValue = useVModel(props, 'modelValue', emits, {
  passive: true,
  defaultValue: props.defaultValue,
});
</script>

<template>
  <div :class="cn('relative w-full items-center', props.class)">
    <Input
      :id="id"
      v-model="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :class="cn('pl-10', props.class)"
      @update:model-value="
        (() => {
          emits('update:model-value', $event);
        })()
      "
    />
    <span
      :class="
        cn(
          'absolute start-0 inset-y-0 flex items-center justify-center px-3 text-white rounded-l-md',
          disabled ? ' bg-primary/50' : 'bg-primary'
        )
      "
      aria-hidden="true"
    >
      /
    </span>
  </div>
</template>
