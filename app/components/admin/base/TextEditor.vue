<script setup lang="ts">
import { QuillEditor } from '@vueup/vue-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.css';

interface Props {
  modelValue: string;
  disabled?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:model-value': [value: string];
}>();
const inputValue = ref(props.modelValue);

watch(
  () => props.modelValue,
  (value) => {
    inputValue.value = value;
  }
);
</script>

<template>
  <ClientOnly>
    <QuillEditor
      v-if="disabled"
      v-model:content="inputValue"
      content-type="html"
      theme="snow"
      read-only
      :disabled="disabled"
      @update:content="emit('update:model-value', $event)"
    />
    <QuillEditor
      v-else
      v-model:content="inputValue"
      content-type="html"
      theme="snow"
      :disabled="disabled"
      @update:content="emit('update:model-value', $event)"
    />
  </ClientOnly>
</template>
