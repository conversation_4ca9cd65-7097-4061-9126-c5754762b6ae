<script setup lang="ts">
import {
  DateFormatter,
  type DateValue,
  getLocalTimeZone,
  parseDate,
  toZoned,
} from '@internationalized/date';
import { CalendarIcon } from '@radix-icons/vue';
import { ref, watch } from 'vue';

const props = defineProps<{
  dateValue: Date | undefined;
}>();

const emit = defineEmits<{
  (e: 'update:dateValue', value: Date | undefined): void;
}>();

const df = new DateFormatter('en-US', {
  dateStyle: 'long',
});
function convertToDateValue(
  date: Date | null | undefined
): DateValue | undefined {
  if (!date) return undefined;
  // Use the local date components to create the date value
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const calendarDate = parseDate(
    `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
  );
  return toZoned(calendarDate, getLocalTimeZone());
}

function convertToDate(dateValue: DateValue | undefined): Date | undefined {
  if (!dateValue) return undefined;
  return dateValue.toDate(getLocalTimeZone());
}

// Create a ref that's properly typed as DateValue
const value = ref<DateValue | undefined>(convertToDateValue(props.dateValue));

// Watch for prop changes
watch(
  () => props.dateValue,
  (newValue) => {
    if (newValue !== value.value) {
      value.value = convertToDateValue(newValue);
    }
  }
);

function updateValue(val: DateValue | undefined) {
  if (val && typeof val === 'object' && 'toDate' in val) {
    const dateInLocalTZ = val.toDate(getLocalTimeZone());
    const year = dateInLocalTZ.getFullYear();
    const month = String(dateInLocalTZ.getMonth() + 1).padStart(2, '0');
    const day = String(dateInLocalTZ.getDate()).padStart(2, '0');
    const date = `${year}-${month}-${day}`;

    const newValue = toZoned(parseDate(date), getLocalTimeZone());
    value.value = newValue;
    emit('update:dateValue', convertToDate(newValue));
  } else {
    value.value = undefined;
    emit('update:dateValue', undefined);
  }
}

function formatDate(date: DateValue | undefined): string {
  if (!date) return 'Pick a date';
  try {
    return df.format(date.toDate(getLocalTimeZone()));
  } catch (e) {
    console.error('Error formatting date:', e);
    return 'Invalid date';
  }
}
</script>

<template>
  <Popover>
    <PopoverTrigger as-child>
      <Button
        variant="outline"
        class="w-full justify-start text-left font-normal border-input text-foreground"
      >
        <CalendarIcon class="mr-2 h-4 w-4" />
        {{ formatDate(value as any) }}
      </Button>
    </PopoverTrigger>
    <PopoverContent class="w-auto p-0">
      <Calendar
        v-model="value as any"
        initial-focus
        @update:model-value="updateValue"
      />
    </PopoverContent>
  </Popover>
</template>
