<script setup lang="ts">
import { Loader2, Mail, Lock, AlertCircle } from 'lucide-vue-next';

interface FormState {
  email: string;
  password: string;
  error: string | null;
}

interface LoginResponse {
  success: boolean;
  [key: string]: any;
}

const formState = reactive<FormState>({
  email: '',
  password: '',
  error: null,
});

const isLoading = ref(false);
const showPassword = ref(false);
const { fetch: fetchUserSession } = useUserSession();

const isValidEmail = computed(
  () =>
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formState.email) || formState.email === ''
);

const isFormValid = computed(
  () => isValidEmail.value && formState.password.length > 0
);

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

const handleSubmit = async (event: Event) => {
  event.preventDefault();

  if (!isFormValid.value) {
    formState.error = 'Please enter a valid college email and password';
    return;
  }

  formState.error = null;
  isLoading.value = true;

  try {
    const response = await $fetch<LoginResponse>('/api/admin/login', {
      method: 'POST',
      body: {
        username: formState.email, // Using email as username
        password: formState.password,
      },
    });

    if (response.success) {
      // Refresh user session data
      await fetchUserSession();
      // Redirect to admin dashboard
      navigateTo('/admin/dashboard');
    } else {
      formState.error = 'Authentication failed. Please try again.';
    }
  } catch (error: any) {
    console.error('Login error:', error);
    formState.error =
      error?.data?.message ||
      'Invalid credentials. Please verify your college administrator account.';
  } finally {
    isLoading.value = false;
  }
};
</script>

<template>
  <form class="space-y-6" @submit="handleSubmit">
    <!-- Error message with improved visibility -->
    <div
      v-if="formState.error"
      class="p-4 rounded-lg bg-destructive/15 border border-destructive/30 text-destructive flex items-start space-x-3"
      role="alert"
      aria-live="assertive"
    >
      <AlertCircle class="h-5 w-5 flex-shrink-0 mt-0.5" />
      <span class="text-sm font-medium">{{ formState.error }}</span>
    </div>

    <!-- Email field with improved styling -->
    <div class="space-y-2.5">
      <Label for="email" class="text-sm font-semibold text-foreground">
        Email
      </Label>
      <div class="relative">
        <div class="absolute left-3.5 top-1/2 -translate-y-1/2 text-primary/70">
          <Mail class="h-[18px] w-[18px]" />
        </div>
        <Input
          id="email"
          v-model="formState.email"
          type="email"
          placeholder="<EMAIL>"
          :disabled="isLoading"
          class="pl-11 py-6 h-12 text-base border-muted-foreground/20 bg-white dark:bg-zinc-900 shadow-sm"
          :class="[
            {
              'ring-destructive border-destructive/50 focus:border-destructive':
                formState.email && !isValidEmail,
            },
          ]"
          auto-capitalize="none"
          auto-complete="email"
          auto-correct="off"
          aria-describedby="email-error"
        />
      </div>
      <div
        v-if="formState.email && !isValidEmail"
        id="email-error"
        class="text-xs font-medium text-destructive mt-1 pl-1"
      >
        Please enter a valid college email address
      </div>
    </div>

    <!-- Password field with improved styling and show/hide functionality -->
    <div class="space-y-2.5">
      <div>
        <Label for="password" class="text-sm font-semibold text-foreground">
          Password
        </Label>
      </div>
      <div class="relative">
        <div class="absolute left-3.5 top-1/2 -translate-y-1/2 text-primary/70">
          <Lock class="h-[18px] w-[18px]" />
        </div>
        <Input
          id="password"
          v-model="formState.password"
          :type="showPassword ? 'text' : 'password'"
          placeholder="Enter your password"
          :disabled="isLoading"
          class="pl-11 pr-11 py-6 h-12 text-base border-muted-foreground/20 bg-white dark:bg-zinc-900 shadow-sm"
          auto-complete="current-password"
        />
        <button
          type="button"
          class="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-primary focus:outline-none"
          tabindex="0"
          aria-label="Toggle password visibility"
          @click="togglePasswordVisibility"
        >
          <svg
            v-if="showPassword"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-5 h-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88"
            />
          </svg>
          <svg
            v-else
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-5 h-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"
            />
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Submit button with improved styling -->
    <Button
      type="submit"
      class="w-full mt-2 font-medium transition-all py-6 h-12 text-base shadow-sm bg-primary hover:bg-primary/90"
      :disabled="isLoading || !isFormValid"
      :class="{ 'opacity-90 hover:opacity-100': isFormValid && !isLoading }"
    >
      <div class="flex items-center justify-center">
        <Loader2 v-if="isLoading" class="mr-2 h-5 w-5 animate-spin" />
        <span>{{ isLoading ? 'Authenticating...' : 'Access CMS' }}</span>
      </div>
    </Button>
  </form>
</template>

<style scoped></style>
