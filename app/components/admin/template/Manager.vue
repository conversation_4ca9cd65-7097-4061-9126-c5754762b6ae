<!-- components/TemplateManager.vue -->
<script setup lang="ts">
import {
  AlertCircle,
  ArrowLeft,
  Eye,
  Save,
  PlusCircle,
  Trash2,
} from 'lucide-vue-next';
import { useTemplateManager } from '~/composables/useTemplateManager';
import type {
  ProfileCardConfig,
  CardType,
  StandardCardConfig,
  VisionCardConfig,
} from '~/types/admin';
import { CARD_LAYOUTS_LIST } from '~/constants/admin/cards';
import type { ButtonStyle, CardLayout } from '~~/server/database/tables/enums';
import type {
  ContentConfig,
  FileConfig,
  ButtonConfig,
} from '~~/server/helper/section';
import type { CreateSectionInput } from '~~/shared/schema/section/create';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { toast } from 'vue-sonner';

const props = defineProps<{
  menuId: number;
  templateId: number;
}>();

const previewCard = ref(false);

const {
  currentSection,
  isLoading,
  error,
  fetchSection,
  selectCardType,
  selectTemplate,
  goBack,
  createSection,
  updateSection,
  addSection,
  removeSection,
} = useTemplateManager();

// Delete dialog state
const isDeleteDialogOpen = ref(false);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

// Revert dialog state
const isRevertDialogOpen = ref(false);

// Fetch sections when component is mounted
onMounted(async () => {
  try {
    await fetchSection(props.templateId);

    // If no sections were found, we'll show a button to create one
    // The user will need to click the button to initialize a new section
  } catch (err) {
    console.error('Failed to fetch sections:', err);
  }
});

// Initialize a new section if none exists
const initializeNewSection = () => {
  addSection();
};

// Open delete confirmation dialog
const openDeleteDialog = () => {
  deleteState.value = 'idle';
  isDeleteDialogOpen.value = true;
};

// Open revert confirmation dialog
const openRevertDialog = () => {
  isRevertDialogOpen.value = true;
};

// Handle delete confirmation
const handleDeleteConfirm = async () => {
  if (!currentSection.value) return;

  try {
    deleteState.value = 'loading';
    await removeSection(currentSection.value.id);
    deleteState.value = 'success';

    // Close dialog after success (with a small delay for better UX)
    setTimeout(() => {
      isDeleteDialogOpen.value = false;
      deleteState.value = 'idle';
      // Reset currentSection to undefined to show the "No template configuration found" page
      currentSection.value = undefined;
    }, 1500);
  } catch (err) {
    console.error('Failed to delete section:', err);
    deleteState.value = 'error';
  }
};

// Handle revert confirmation
const confirmRevert = () => {
  // Reset the current section to undefined to go back to the initial state
  currentSection.value = undefined;
  isRevertDialogOpen.value = false;
};

const generateKey = () => Math.random().toString(36).substring(7);

const createButton = (
  title: string = '',
  style: ButtonStyle = 'primary'
): ButtonConfig => ({
  title,
  style,
  type: 'link',
  internalLink: null,
  externalLink: null,
  icon: null,
  newTab: false,
});

const createImage = (): FileConfig => ({
  type: 'image',
  pathname: '',
  title: '',
});

const createContent = (): ContentConfig => ({
  title: '',
  content: '',
});

const createConfiguration = (layout: CardLayout): any => {
  const key = generateKey();

  switch (layout) {
    case 'standard':
      return {
        layout: 'standard',
        title: '',
        content1: createContent(),
        image: createImage(),
        linkButton: createButton('View Details'),
        files: [],
        downloadButton: createButton('Download', 'secondary'),
        key,
      };
    case 'vision':
    case 'profile':
      return {
        layout,
        content: createContent(),
        image: createImage(),
        key,
      };
    default:
      throw new Error(`Unsupported layout: ${layout}`);
  }
};

const updateCardData = (
  newValues: any
): ProfileCardConfig | StandardCardConfig | VisionCardConfig => {
  if (newValues?.layout == 'profile') {
    const updatedValues: ProfileCardConfig = {
      layout: 'profile',
      key: newValues?.key,
      content1: {
        title: newValues?.content1?.title?.length
          ? newValues?.content1?.title
          : 'null',
        content: newValues?.content1?.content?.length
          ? newValues?.content1?.content
          : 'null',
      },
      content2: {
        title: newValues?.content2?.title?.length
          ? newValues?.content2?.title
          : 'null',
        content: newValues?.content2?.content?.length
          ? newValues?.content2?.content
          : 'null',
      },
      image: newValues.image?.pathname ? newValues.image : null,
      linkButton: newValues.linkButton
        ? {
            ...newValues.linkButton,
            style: newValues.linkButton.style as string,
            newTab: newValues.linkButton.newTab || false,
          }
        : null,
      downloadButton: newValues.downloadButton
        ? {
            ...newValues.downloadButton,
            style: newValues.downloadButton.style as string,
            newTab: newValues.downloadButton.newTab || false,
          }
        : null,
    };
    return updatedValues;
  } else if (newValues?.layout == 'vision') {
    const updatedValues: VisionCardConfig = {
      layout: 'vision',
      key: newValues?.key,
      image: newValues.image?.pathname ? newValues.image : null,
      content1: {
        title: newValues?.content1?.title?.length
          ? newValues?.content1?.title
          : 'null',
        content: newValues?.content1?.content?.length
          ? newValues?.content1?.content
          : 'null',
      },
    };
    return updatedValues;
  } else {
    const updatedValues: StandardCardConfig = {
      layout: 'standard',
      key: newValues?.key,
      title: newValues?.title?.length ? newValues?.title : 'null',
      content1: {
        title: newValues?.content1?.title?.length
          ? newValues?.content1?.title
          : 'null',
        content: newValues?.content1?.content?.length
          ? newValues?.content1?.content
          : 'null',
      },
      image: newValues.image?.pathname ? newValues.image : null,
      linkButton: newValues.linkButton
        ? {
            ...newValues.linkButton,
            style: newValues.linkButton.style as string,
            newTab: newValues.linkButton.newTab || false,
          }
        : null,
      files: Array.isArray(newValues.files) ? newValues.files : [],
      downloadButton: newValues.downloadButton
        ? {
            ...newValues.downloadButton,
            style: newValues.downloadButton.style as string,
            newTab: newValues.downloadButton.newTab || false,
          }
        : null,
    };
    return updatedValues;
  }
};

const handleSubmit = async () => {
  if (!currentSection.value) return;

  try {
    // Show loading state
    isLoading.value = true;
    error.value = null;

    if (currentSection.value.cardType === 'single') {
      // Log the configuration before updating to debug
      console.log(
        'Configuration before updateCardData:',
        currentSection.value.configuration
      );

      // Check if files exist in the configuration
      if (currentSection.value.configuration.files) {
        console.log(
          'Files in configuration before update:',
          currentSection.value.configuration.files
        );
      }

      const updatedConfig = updateCardData(
        currentSection.value.configuration
      ) as any;

      // Log the updated configuration
      console.log('Updated configuration after updateCardData:', updatedConfig);

      const sectionData: Partial<CreateSectionInput> = {
        type: 'card',
        priority: 0, // Always priority 0 since we only have one section
        configuration: updatedConfig,
      };

      if (currentSection.value.templateId) {
        await updateSection(currentSection.value.id, sectionData);
      } else {
        await createSection(props.templateId, sectionData);
      }
    } else if (currentSection.value.cardType === 'list') {
      // For card list type
      const cardListConfig = {
        title: currentSection.value.configuration.title || '',
        description: currentSection.value.configuration.description || '',
        maxCards: currentSection.value.configuration.maxCards || 10,
        minCards: currentSection.value.configuration.minCards || 1,
        layout: currentSection.value.layout,
        cards:
          currentSection.value.configuration.cards.map((card: any) =>
            updateCardData(card)
          ) || [],
      };

      const sectionData: Partial<CreateSectionInput> = {
        type: 'cardList', // Updated to match API expectation
        priority: 0, // Always priority 0 since we only have one section
        configuration: cardListConfig,
      };

      if (currentSection.value.templateId) {
        await updateSection(currentSection.value.id, sectionData);
      } else {
        await createSection(props.templateId, sectionData);
      }
    } else {
      // Default case for other section types
      const cardConfig = createConfiguration(currentSection.value.layout);

      const sectionData: Partial<CreateSectionInput> = {
        type: 'card',
        priority: 0, // Always priority 0 since we only have one section
        configuration: cardConfig,
      };

      if (currentSection.value.templateId) {
        await updateSection(currentSection.value.id, sectionData);
      } else {
        await createSection(props.templateId, sectionData);
      }
    }

    // Show success message
    toast.success('Template saved successfully');
  } catch (err) {
    console.error('Failed to save section:', err);
    error.value =
      err instanceof Error ? err.message : 'Failed to save template';
    toast.error('Failed to save template');
  } finally {
    isLoading.value = false;
  }
};

const updateCurrentSection = (value: any) => {
  // Log files specifically to debug
  if (value?.files) {
    console.log('Files in updateCurrentSection:', value.files);
  }

  if (!currentSection.value) return;

  // Update the configuration of the current section
  currentSection.value.configuration = value;
  console.log(
    '🚀 ~ updateCurrentSection ~ currentSection.value:',
    currentSection.value
  );

  // For card lists, ensure the layout is properly set
  if (currentSection.value.cardType === 'list' && value.layout) {
    // Make sure the layout in the configuration matches the section layout
    currentSection.value.layout = value.layout;

    // Ensure all cards have the correct layout
    if (value.cards && Array.isArray(value.cards)) {
      value.cards.forEach((card: any) => {
        if (card) {
          card.layout = value.layout;
        }
      });
    }
  }
};
</script>

<template>
  <div class="space-y-6">
    <!-- Error Alert -->
    <Alert v-if="error" variant="destructive">
      <AlertCircle class="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{{ error }}</AlertDescription>
    </Alert>
    <!-- Template Management Header -->
    <div class="flex items-center justify-between">
      <h2 class="text-lg font-semibold">Template</h2>
      <div class="flex gap-2">
        <!-- Create Template Button (shown when no section exists) -->
        <Button
          v-if="!currentSection && !isLoading"
          size="sm"
          class="min-w-[160px]"
          @click="initializeNewSection"
        >
          <PlusCircle class="h-4 w-4 mr-1" />
          Create Template
        </Button>
        <div class="flex gap-2">
          <!-- Delete Button (shown only when a section exists and has been saved) -->
          <Button
            v-if="currentSection && currentSection.templateId"
            size="sm"
            variant="destructive"
            class="min-w-[120px]"
            :disabled="isLoading"
            @click="openDeleteDialog"
          >
            <Trash2 class="h-4 w-4 mr-1" />
            Delete
          </Button>
          <!-- Revert Button (shown when a section exists but hasn't been saved yet) -->
          <Button
            v-if="currentSection && !currentSection.templateId"
            size="sm"
            variant="destructive"
            class="min-w-[120px]"
            :disabled="isLoading"
            @click="openRevertDialog"
          >
            <ArrowLeft class="h-4 w-4 mr-1" />
            Revert
          </Button>
          <!-- Save Button (shown when a section exists) -->
          <Button
            v-if="currentSection"
            size="sm"
            class="min-w-[120px]"
            :disabled="isLoading"
            @click="handleSubmit"
          >
            <template v-if="isLoading">
              <div
                class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"
              ></div>
              Saving...
            </template>
            <template v-else>
              <Save class="h-4 w-4 mr-1" />
              Save
            </template>
          </Button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <div
        class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"
      ></div>
    </div>

    <!-- No Section State - Empty State Message -->
    <div
      v-else-if="!currentSection && !isLoading"
      class="flex flex-col items-center justify-center py-12 space-y-4"
    >
      <div class="text-center space-y-3">
        <h3 class="text-lg font-medium">No template configuration found</h3>
        <p class="text-muted-foreground">
          Click the "Create Template" button above to get started
        </p>
        <Button
          v-if="!currentSection && !isLoading"
          size="sm"
          class="min-w-[160px]"
          variant="outline"
          @click="initializeNewSection"
        >
          <PlusCircle class="h-4 w-4 mr-1" />
          Create Template
        </Button>
      </div>
    </div>

    <!-- Current Section Content -->
    <div v-else-if="currentSection" class="pt-4">
      <!-- Type Selection -->
      <div v-if="currentSection.step === 'type'" class="grid grid-cols-2 gap-4">
        <Card
          v-for="type in ['single', 'list']"
          :key="type"
          class="cursor-pointer hover:border-primary"
          :class="{ 'border-primary': currentSection.cardType === type }"
          @click="selectCardType(type as CardType)"
        >
          <CardHeader>
            <CardTitle>{{
              type === 'single' ? 'Single Card' : 'Cards List'
            }}</CardTitle>
            <CardDescription>
              {{
                type === 'single'
                  ? 'Create a single card component'
                  : 'Create a list of cards'
              }}
            </CardDescription>
          </CardHeader>
        </Card>
      </div>

      <!-- Template Selection -->
      <div v-if="currentSection.step === 'template'" class="space-y-6">
        <Button variant="outline" :disabled="isLoading" @click="goBack">
          <ArrowLeft class="mr-1 h-4 w-4" /> Back
        </Button>

        <div class="mb-4">
          <h3 class="text-lg font-medium mb-2">
            {{
              currentSection.cardType === 'single'
                ? 'Select Card Layout'
                : 'Select Card Type for List'
            }}
          </h3>
          <p class="text-muted-foreground">
            {{
              currentSection.cardType === 'single'
                ? 'Choose the layout for your single card'
                : 'All cards in this list will use the same layout type'
            }}
          </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card
            v-for="layout in CARD_LAYOUTS_LIST"
            :key="layout"
            class="cursor-pointer hover:border-primary"
            :class="{ 'border-primary': currentSection.layout === layout }"
            @click="selectTemplate(layout)"
          >
            <CardHeader>
              <CardTitle class="capitalize">{{ layout }}</CardTitle>
            </CardHeader>
            <CardContent>
              <div
                class="h-32 bg-muted rounded-lg flex items-center justify-center"
              >
                <span class="text-sm text-muted-foreground"
                  >{{ layout }} preview</span
                >
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- Form -->
      <div v-if="currentSection.step === 'form'" class="space-y-6">
        <div class="flex justify-between items-center gap-2">
          <Button
            v-if="currentSection && !currentSection.templateId"
            variant="outline"
            :disabled="isLoading"
            @click="goBack"
          >
            <ArrowLeft class="mr-1 h-4 w-4" /> Back
          </Button>
          <span v-else class="text-base">
            {{
              currentSection?.layout === 'profile'
                ? 'Profile Card'
                : currentSection?.layout === 'vision'
                  ? 'Vision Card'
                  : 'Profile Card '
            }}
          </span>
          <div class="flex justify-end items-center gap-2">
            <!-- Show the card type and layouts -->
            <span
              v-if="currentSection.cardType === 'single'"
              class="text-xs bg-teal-500 px-2 py-1 font-bold rounded-full text-white w-fit h-fit"
            >
              {{ currentSection.cardType }} - {{ currentSection.layout }}
            </span>
            <!-- Preview Button -->
            <Button
              variant="outline"
              size="sm"
              class="min-w-[120px]"
              :disabled="isLoading"
              @click="previewCard = true"
            >
              <Eye class="h-4 w-4 mr-1" />
              <span>Preview</span>
            </Button>
          </div>
        </div>

        <!-- Single Card Forms -->
        <AdminTemplateFormProfileCard
          v-if="
            currentSection.cardType === 'single' &&
            currentSection.layout === 'profile'
          "
          v-model="currentSection.configuration"
          :disabled="isLoading"
          @update:model-value="updateCurrentSection"
        />
        <AdminTemplateFormStandardCard
          v-else-if="
            currentSection.cardType === 'single' &&
            currentSection.layout === 'standard'
          "
          v-model="currentSection.configuration"
          :disabled="isLoading"
          @update:model-value="updateCurrentSection"
        />
        <AdminTemplateFormVisionCard
          v-else-if="
            currentSection.cardType === 'single' &&
            currentSection.layout === 'vision'
          "
          v-model="currentSection.configuration"
          :disabled="isLoading"
          @update:model-value="updateCurrentSection"
        />
        <!-- Card List Form -->
        <AdminTemplateFormCardList
          v-else-if="currentSection.cardType === 'list'"
          v-model="currentSection.configuration"
          :disabled="isLoading"
          @update:model-value="updateCurrentSection"
        />
      </div>
    </div>
    <div class="flex justify-end items-center gap-2 pb-8">
      <!-- Delete Button (shown only when a section exists and has been saved) -->
      <Button
        v-if="currentSection && currentSection.templateId"
        size="sm"
        variant="destructive"
        class="min-w-[120px]"
        :disabled="isLoading"
        @click="openDeleteDialog"
      >
        <Trash2 class="h-4 w-4 mr-1" />
        Delete
      </Button>
      <!-- Revert Button (shown when a section exists but hasn't been saved yet) -->
      <Button
        v-if="currentSection && !currentSection.templateId"
        size="sm"
        variant="destructive"
        class="min-w-[120px]"
        :disabled="isLoading"
        @click="openRevertDialog"
      >
        <ArrowLeft class="h-4 w-4 mr-1" />
        Revert
      </Button>
      <!-- Save Button (shown when a section exists) -->
      <Button
        v-if="currentSection"
        size="sm"
        class="min-w-[120px]"
        :disabled="isLoading"
        @click="handleSubmit"
      >
        <template v-if="isLoading">
          <div
            class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"
          ></div>
          Saving...
        </template>
        <template v-else>
          <Save class="h-4 w-4 mr-1" />
          Save
        </template>
      </Button>
    </div>

    <!-- Preview Dialog -->
    <Dialog v-model:open="previewCard">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Preview Card</DialogTitle>
          <DialogDescription>
            {{
              `Preview of the card with the layout \"${currentSection?.layout}\"`
            }}
          </DialogDescription>
        </DialogHeader>
        <div class="flex justify-center items-center">
          <HomeTemplateCardProfile
            v-if="currentSection?.configuration?.layout === 'profile'"
            :data="{
              title: currentSection?.configuration?.title || 'Title',
              subtitle1:
                currentSection?.configuration?.content1?.title || 'Subtitle 1',
              subtitle2:
                currentSection?.configuration?.content2?.title || 'Subtitle 2',
              content1:
                currentSection?.configuration?.content1?.content || 'Content 1',
              content2:
                currentSection?.configuration?.content2?.content || 'Content 2',
              layout: currentSection?.configuration?.layout || 'profile',
              image: currentSection?.configuration?.image || {
                pathname: '',
                title: '',
                type: 'image',
                prefix: '',
              },
              linkButton: currentSection?.configuration?.linkButton,
              downloadButton: currentSection?.configuration?.downloadButton,
            }"
          />
          <HomeTemplateCardStandard
            v-if="currentSection?.configuration?.layout === 'standard'"
            :data="{
              title: currentSection?.configuration?.title || 'Title',
              subtitle: currentSection?.configuration?.subtitle || 'Subtitle',
              content: currentSection?.configuration?.content || 'Content',
              image: currentSection?.configuration?.image || {
                pathname: '',
                title: '',
                type: 'image',
                prefix: '',
              },
              linkButton: currentSection?.configuration?.linkButton,
              downloadButton: currentSection?.configuration?.downloadButton,
              layout: 'standard',
            }"
          />
          <HomeTemplateCardVision
            v-if="currentSection?.configuration?.layout === 'vision'"
            :data="{
              title:
                currentSection?.configuration?.content1?.title || 'Our Vision',
              description:
                currentSection?.configuration?.content1?.content ||
                'To groom intellectually competent, professionally efficient, morally upright, physically able and socially responsible persons through holistic and innovative education in the Don Bosco way.',
              image: currentSection?.configuration?.image || {
                pathname: '',
                title: '',
                type: 'image',
                prefix: '',
              },
              layout: 'vision',
            }"
          />
        </div>
        <DialogFooter class="gap-2 sm:gap-0">
          <Button variant="ghost" @click="previewCard = false">
            <ArrowLeft class="mr-2 h-4 w-4" />
            Back
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Delete Confirmation Dialog -->
    <Dialog v-model:open="isDeleteDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Template</DialogTitle>
          <DialogDescription
            :class="{
              'text-destructive':
                deleteState !== 'success' && deleteState !== 'error',
              'text-green-600': deleteState === 'success',
            }"
          >
            <template v-if="deleteState === 'success'">
              Template has been successfully deleted.
            </template>
            <template v-else-if="deleteState === 'error'">
              An error occurred while deleting the template. Please try again.
            </template>
            <template v-else-if="deleteState === 'loading'">
              Deleting template...
            </template>
            <template v-else>
              Are you sure you want to delete this template? This action cannot
              be undone.
            </template>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter class="gap-2 sm:gap-0">
          <Button
            variant="ghost"
            :disabled="deleteState === 'loading' || deleteState === 'success'"
            @click="isDeleteDialogOpen = false"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            :disabled="deleteState === 'loading' || deleteState === 'success'"
            @click="handleDeleteConfirm"
          >
            <template v-if="deleteState === 'loading'">
              <div
                class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"
              ></div>
              Deleting...
            </template>
            <template v-else-if="deleteState === 'success'">
              <div class="mr-2 h-4 w-4 text-white">✓</div>
              Deleted
            </template>
            <template v-else> Delete Template </template>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Revert Confirmation Dialog -->
    <Dialog v-model:open="isRevertDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Revert Changes</DialogTitle>
          <DialogDescription class="text-destructive">
            Are you sure you want to revert your changes? This action cannot be
            undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter class="gap-2 sm:gap-0">
          <Button variant="ghost" @click="isRevertDialogOpen = false">
            Cancel
          </Button>
          <Button variant="outline" @click="confirmRevert">
            <ArrowLeft class="mr-2 h-4 w-4" />
            Revert Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>
