<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod';
import { useForm } from 'vee-validate';
import { iconOptions } from '~/constants/admin/icons';
import { linkButtonSchema } from '~~/shared/schema';

const props = defineProps<{
  modelValue: typeof linkButtonSchema._type | null;
  showButton: boolean;
  disabled?: boolean;
  hideBorder?: boolean;
  disableStyle?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:model-value', value: typeof linkButtonSchema._type | null): void;
  (e: 'update:showButton', value: boolean): void;
}>();

const showButton = ref(props.showButton);
const linkType = ref(props.modelValue?.internalLink ? 'internal' : 'external');

const formSchema = toTypedSchema(linkButtonSchema);

const initialValues: typeof linkButtonSchema._type = {
  title: props.modelValue?.title || '',
  internalLink: props.modelValue?.internalLink ?? '',
  externalLink: props.modelValue?.externalLink ?? '',
  icon: props.modelValue?.icon ?? '',
  type: props.modelValue?.type ?? 'link',
  style: props.modelValue?.style ?? 'primary',
  newTab: props.modelValue?.newTab ?? false,
};

const { values, setFieldValue } = useForm({
  validationSchema: formSchema,
  initialValues,
});

// Watch showButton changes to update model value
watch(showButton, (newValue) => {
  emit('update:showButton', newValue);
  if (!newValue) {
    emit('update:model-value', null);
  } else {
    emit('update:model-value', transformButton(initialValues));
  }
});

// Update the appropriate link type when changed
function handleLinkTypeChange(value: string) {
  linkType.value = value;
  if (props.modelValue) {
    const updatedButton = {
      ...props.modelValue,
      internalLink: value === 'internal' ? '' : undefined,
      externalLink: value === 'external' ? '' : undefined,
    };

    emit('update:model-value', transformButton(updatedButton));
  }
}

const handleValueChange = (value: any, key: any) => {
  setFieldValue(key, value);
  emit(
    'update:model-value',
    transformButton(values as typeof linkButtonSchema._type)
  );
};
</script>

<template>
  <div
    class="bg-[#824eec]/10 p-4 rounded-md"
    :class="{ 'border-t-0 pt-0': hideBorder, 'border-t pt-6': !hideBorder }"
  >
    <div class="flex items-center justify-between mb-4">
      <div>
        <h4 class="font-medium">Call to Action Button</h4>
        <p class="text-sm text-muted-foreground">
          Add a button to your content
        </p>
      </div>
      <Switch
        v-model="showButton"
        :checked="showButton"
        :disabled="disabled"
        @update:checked="showButton = $event"
      />
    </div>

    <div v-if="showButton" class="space-y-4">
      <Form :validation-schema="formSchema" :initial-values="initialValues">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField v-slot="{ field, errorMessage }" name="title">
            <FormItem>
              <FormLabel
                >Button Text <span class="text-destructive">*</span></FormLabel
              >
              <FormControl>
                <Input
                  v-bind="field"
                  :model-value="field.value"
                  :disabled="disabled"
                  placeholder="Enter button text"
                  @update:model-value="
                    (newValue) => handleValueChange(newValue, 'title')
                  "
                />
              </FormControl>
              <FormMessage>{{ errorMessage }}</FormMessage>
              <FormDescription>
                The text displayed on the button
              </FormDescription>
            </FormItem>
          </FormField>

          <FormField
            v-slot="{ value, errorMessage, handleChange }"
            type="checkbox"
            name="newTab"
          >
            <FormItem>
              <FormLabel>New Tab</FormLabel>
              <FormControl>
                <div
                  class="flex items-center gap-2 h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm"
                >
                  <Checkbox
                    :checked="value"
                    :disabled="disabled"
                    @update:checked="
                      (newValue) => {
                        handleChange(newValue);
                        handleValueChange(newValue, 'newTab');
                      }
                    "
                  />

                  <div class="text-sm">Open in new tab</div>
                </div>
              </FormControl>
              <FormDescription>
                Should the link open in a new tab?
              </FormDescription>
              <FormMessage>{{ errorMessage }}</FormMessage>
            </FormItem>
          </FormField>

          <FormField v-slot="{ field, errorMessage }" name="icon">
            <FormItem>
              <FormLabel>Icon</FormLabel>
              <FormControl>
                <Select
                  :model-value="values.icon ? values.icon : field.value"
                  @update:model-value="
                    (value) => handleValueChange(value, 'icon')
                  "
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select icon" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      <div class="flex items-center gap-2">
                        <Icon name="i-lucide-x" />
                        <div class="flex items-center gap-2">None</div>
                      </div>
                    </SelectItem>
                    <SelectItem
                      v-for="option in iconOptions"
                      :key="option.icon"
                      :value="option.icon"
                    >
                      <div class="flex items-center gap-2">
                        <Icon :name="option.icon" />
                        {{ option.label }}
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage>{{ errorMessage }}</FormMessage>
              <FormDescription>
                The icon displayed on the button
              </FormDescription>
            </FormItem>
          </FormField>
          <FormField v-slot="{ field, errorMessage }" name="style">
            <FormItem>
              <FormLabel>Button Style</FormLabel>
              <Select
                :model-value="field.value"
                :disabled="disabled || disableStyle"
                @update:model-value="
                  (newValue) => handleValueChange(newValue, 'style')
                "
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select button style" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="primary"> Primary </SelectItem>
                  <SelectItem value="secondary"> Secondary </SelectItem>
                  <SelectItem value="outline"> Outline </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage>{{ errorMessage }}</FormMessage>
              <FormDescription>
                Choose the visual style of the button
              </FormDescription>
            </FormItem>
          </FormField>
          <div name="linkType">
            <div class="space-y-2">
              <Label>Link Type</Label>
              <Select
                :model-value="linkType"
                :disabled="disabled"
                @update:model-value="handleLinkTypeChange"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select link type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="internal"> Internal Link </SelectItem>
                  <SelectItem value="external"> External Link </SelectItem>
                </SelectContent>
              </Select>
              <div class="text-xs text-muted-foreground">
                Choose the type of link for the button
              </div>
            </div>
          </div>

          <FormField
            v-if="linkType === 'internal'"
            v-slot="{ field, errorMessage }"
            name="internalLink"
          >
            <FormItem>
              <FormLabel
                >Internal Link
                <span class="text-destructive">*</span></FormLabel
              >
              <FormControl>
                <Input
                  v-bind="field"
                  :model-value="field.value"
                  :disabled="disabled"
                  placeholder="Enter internal link path (e.g., /about)"
                  @update:model-value="
                    (newValue) => handleValueChange(newValue, 'internalLink')
                  "
                />
              </FormControl>
              <FormMessage>{{ errorMessage }}</FormMessage>
              <FormDescription>
                The internal link for the button
              </FormDescription>
            </FormItem>
          </FormField>

          <FormField
            v-if="linkType === 'external'"
            v-slot="{ field, errorMessage }"
            name="externalLink"
          >
            <FormItem>
              <FormLabel
                >External Link
                <span class="text-destructive">*</span></FormLabel
              >
              <FormControl>
                <Input
                  v-bind="field"
                  :model-value="field.value"
                  :disabled="disabled"
                  placeholder="Enter full URL (e.g., https://example.com)"
                  @update:model-value="
                    (newValue) => handleValueChange(newValue, 'externalLink')
                  "
                />
              </FormControl>
              <FormMessage>{{ errorMessage }}</FormMessage>
              <FormDescription>
                The external link for the button
              </FormDescription>
            </FormItem>
          </FormField>
        </div>
      </Form>
    </div>
  </div>
</template>
