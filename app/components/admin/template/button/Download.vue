<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod';
import { useForm } from 'vee-validate';
import { toast } from 'vue-sonner';
import { iconOptions } from '~/constants/admin/icons';
import type { FileData } from '~/types/home';
import { downloadButtonSchema } from '~~/shared/schema';

type DownloadButtonType = typeof downloadButtonSchema._type;

const props = defineProps<{
  modelValue: DownloadButtonType | null;
  showButton: boolean;
  disabled?: boolean;
  hideBorder?: boolean;
  disableStyle?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:model-value', value: DownloadButtonType | null): void;
  (e: 'update:showButton', value: boolean): void;
}>();

const showButton = ref(props.showButton);

const formSchema = toTypedSchema(downloadButtonSchema);

const initialValues: DownloadButtonType = {
  title: props.modelValue?.title || '',
  icon: props.modelValue?.icon || '',
  type: 'download',
  style: props.modelValue?.style || 'primary',
  file: props.modelValue?.file || {
    pathname: '',
    type: '',
    title: '',
  },
  newTab: props.modelValue?.newTab || false,
};

const primaryFile = ref<FileData | null>(
  props.modelValue?.file
    ? {
        pathname: props.modelValue.file.pathname,
        type: props.modelValue.file.type,
        prefix: 'download',
        title: props.modelValue.file.title || '',
      }
    : null
);

const { values, setFieldValue } = useForm({
  validationSchema: formSchema,
  initialValues,
});

// Transform download button data before emitting
const transformDownloadButton = (
  button: DownloadButtonType
): DownloadButtonType => {
  const formattedButton = {
    title: button.title,
    style: button.style || 'primary',
    icon: button.icon,
    type: 'download',
    file: button.file,
    newTab: button.newTab,
  };

  // If icon is none, remove it from formattedButton
  if (button.icon === 'none' || !button.icon) {
    delete formattedButton.icon;
  }

  return formattedButton as DownloadButtonType;
};

// Watch showButton changes to update model value
watch(showButton, (newValue) => {
  emit('update:showButton', newValue);
  if (!newValue) {
    emit('update:model-value', null);
  } else {
    emit(
      'update:model-value',
      transformDownloadButton(values as DownloadButtonType)
    );
  }
});

const handleValueChange = (value: any, key: keyof DownloadButtonType) => {
  setFieldValue(key, value);
  emit(
    'update:model-value',
    transformDownloadButton(values as DownloadButtonType)
  );
};

const handleFileUpload = async (file: File | undefined) => {
  if (!file) {
    primaryFile.value = null;
    setFieldValue('file', undefined);
    return;
  }
  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'download');
    form.append('title', 'File To Download');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', {
      method: 'POST',
      body: form,
    });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'download',
    };
    primaryFile.value = fileData;
    handleValueChange(fileData, 'file');
  } catch (err) {
    console.error('Failed to upload primary file:', err);
    toast.error('Failed to upload primary file');
  }
};
</script>

<template>
  <div
    class="bg-[#4e98ec]/10 p-4 rounded-md"
    :class="{
      'border-t-0 pt-0': hideBorder,
      'border-t pt-6': !hideBorder,
    }"
  >
    <div class="flex items-center justify-between mb-4">
      <div>
        <h4 class="font-medium">Download Button</h4>
        <p class="text-sm text-muted-foreground">
          Add a download button to your content
        </p>
      </div>
      <Switch
        v-model="showButton"
        :checked="showButton"
        :disabled="disabled"
        @update:checked="showButton = $event"
      />
    </div>

    <div v-if="showButton" class="space-y-4">
      <Form :validation-schema="formSchema" :initial-values="initialValues">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <FormField v-slot="{ field, errorMessage }" name="title">
            <FormItem>
              <FormLabel
                >Button Text <span class="text-destructive">*</span></FormLabel
              >
              <FormControl>
                <Input
                  v-bind="field"
                  :model-value="field.value"
                  :disabled="disabled"
                  placeholder="Enter button text"
                  @update:model-value="
                    (newValue: any) => handleValueChange(newValue, 'title')
                  "
                />
              </FormControl>
              <FormMessage>{{ errorMessage }}</FormMessage>
              <FormDescription>
                The text displayed on the button
              </FormDescription>
            </FormItem>
          </FormField>

          <FormField v-slot="{ field, errorMessage }" name="icon">
            <FormItem>
              <FormLabel>Icon</FormLabel>
              <FormControl>
                <Select
                  :model-value="field.value"
                  @update:model-value="
                    (value: any) => handleValueChange(value, 'icon')
                  "
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select icon" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      <div class="flex items-center gap-2">
                        <Icon name="i-lucide-x" />
                        <div class="flex items-center gap-2">None</div>
                      </div>
                    </SelectItem>
                    <SelectItem
                      v-for="option in iconOptions"
                      :key="option.icon"
                      :value="option.icon"
                    >
                      <div class="flex items-center gap-2">
                        <Icon :name="option.icon" />
                        {{ option.label }}
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage>{{ errorMessage }}</FormMessage>
              <FormDescription>
                The icon displayed on the button
              </FormDescription>
            </FormItem>
          </FormField>

          <FormField v-slot="{ field, errorMessage }" name="style">
            <FormItem>
              <FormLabel>Button Style</FormLabel>
              <Select
                :model-value="field.value"
                :disabled="disabled || disableStyle"
                @update:model-value="
                  (newValue: any) => handleValueChange(newValue, 'style')
                "
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select button style" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="primary"> Primary </SelectItem>
                  <SelectItem value="secondary"> Secondary </SelectItem>
                  <SelectItem value="outline"> Outline </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage>{{ errorMessage }}</FormMessage>
              <FormDescription>
                Choose the visual style of the button
              </FormDescription>
            </FormItem>
          </FormField>
        </div>
        <AdminBaseDropzone
          :is-multiple-allowed="false"
          :file-types="['pdf', 'text', 'image']"
          title="File To Download"
          :existing-files="primaryFile ? [primaryFile] : []"
          description="Upload the file that will be downloaded when the button is clicked"
          :disabled="disabled"
          @files-selected="(files: any) => handleFileUpload(files[0]?.file)"
          @file-removed="() => (primaryFile = undefined)"
        />
      </Form>
    </div>
  </div>
</template>
