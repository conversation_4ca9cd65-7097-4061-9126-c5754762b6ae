<script setup lang="ts">
import { useForm } from 'vee-validate';
import type { ProfileCardConfig } from '~/types/admin';
import { toast } from 'vue-sonner';
import type { FileData } from '~/types/home';
import { toTypedSchema } from '@vee-validate/zod';
import { z } from 'zod';
import {
  fileSchema,
  linkButtonSchema,
  downloadButtonSchema,
} from '~~/shared/schema';
import type { ButtonStyle } from '~~/server/database/tables';

const props = defineProps<{
  modelValue: ProfileCardConfig;
  disabled?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: ProfileCardConfig): void;
}>();

// Define validation schema directly
const profileCardSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content1: z
    .object({
      title: z.string().min(1, 'Title is required').optional(),
      content: z.string().min(1, 'Content is required').optional(),
    })
    .optional(),
  content2: z
    .object({
      title: z.string().min(1, 'Subtitle is required').optional(),
      content: z.string().min(1, 'Subtitle content is required').optional(),
    })
    .optional(),
  image: fileSchema.nullable(),
  linkButton: linkButtonSchema.nullable().optional(),
  downloadButton: downloadButtonSchema.nullable().optional(),
  layout: z.literal('profile'),
  key: z.string(),
});

const formSchema = toTypedSchema(profileCardSchema);
const primaryImage = ref<FileData | null>(props.modelValue.image || null);

const initialValues = {
  content1: {
    title:
      props.modelValue.content1?.title !== 'null'
        ? props.modelValue.content1?.title
        : '',
    content:
      props.modelValue.content1?.content !== 'null'
        ? props.modelValue.content1?.content
        : '',
  },
  content2: {
    title:
      props.modelValue.content2?.title !== 'null'
        ? props.modelValue.content2?.title
        : '',
    content:
      props.modelValue.content2?.content !== 'null'
        ? props.modelValue.content2?.content
        : '',
  },
  image: props.modelValue.image || null,
  linkButton: props.modelValue.linkButton
    ? {
        ...props.modelValue.linkButton,
        style: props.modelValue.linkButton.style as ButtonStyle,
      }
    : null,
  downloadButton: props.modelValue.downloadButton
    ? {
        ...props.modelValue.downloadButton,
        style: props.modelValue.downloadButton.style as ButtonStyle,
        newTab: props.modelValue.downloadButton.newTab || false,
      }
    : null,
  key: props.modelValue.key || '',
  layout: props.modelValue.layout || 'profile',
};

const showLinkButton = ref(!!props.modelValue.linkButton);
const { values: formValues, setFieldValue } = useForm<typeof initialValues>({
  initialValues,
  validationSchema: formSchema,
});
const emitUpdatedValues = (newValues: typeof formValues) => {
  const updatedValues: ProfileCardConfig = {
    layout: 'profile',
    key: newValues?.key,
    content1: {
      title: newValues?.content1?.title?.length
        ? newValues?.content1?.title
        : 'null',
      content: newValues?.content1?.content?.length
        ? newValues?.content1?.content
        : 'null',
    },
    content2: {
      title: newValues?.content2?.title?.length
        ? newValues?.content2?.title
        : 'null',
      content: newValues?.content2?.content?.length
        ? newValues?.content2?.content
        : 'null',
    },
    image: primaryImage.value || {
      pathname: '',
      title: '',
      type: '',
      prefix: 'profile',
    },
    linkButton: newValues.linkButton
      ? {
          ...newValues.linkButton,
          style: newValues.linkButton.style as string,
          newTab: newValues.linkButton.newTab || false,
        }
      : null,
    downloadButton: newValues.downloadButton
      ? {
          ...newValues.downloadButton,
          style: newValues.downloadButton.style as string,
          newTab: newValues.downloadButton.newTab || false,
        }
      : null,
  };
  console.log('🚀 ~ emitUpdatedValues ~ updatedValues:', updatedValues);
  emit('update:modelValue', updatedValues);
};

async function uploadPrimaryImage(file: File | undefined) {
  if (!file) {
    primaryImage.value = null;
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'profile');
    form.append('title', 'Primary Image');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', { method: 'POST', body: form });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'profile',
    };
    primaryImage.value = fileData;
    emitUpdatedValues(formValues);
  } catch (err) {
    console.error('Failed to upload primary file:', err);
    toast.error('Failed to upload primary image');
  }
}

watch(formValues, (newValues) => {
  emitUpdatedValues(newValues);
});
</script>

<template>
  <Form
    class="space-y-6"
    :validation-schema="formSchema"
    :initial-values="initialValues"
  >
    <div
      class="bg-amber-50 border border-amber-200 text-amber-800 px-4 py-3 rounded-md mb-3 flex items-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
          clip-rule="evenodd"
        />
      </svg>
      <span
        >An image is required for profile cards. Please upload an image.</span
      >
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Subtitle (content2.title) -->
      <FormField v-slot="{ field, errorMessage }" name="content1.title">
        <FormItem>
          <FormLabel>Title 1</FormLabel>
          <FormControl>
            <Input
              :model-value="field.value"
              v-bind="field"
              :disabled="disabled"
              placeholder="Enter title"
              @update:model-value="
                (value: any) => setFieldValue('content1.title', value as never)
              "
            />
          </FormControl>
          <FormDescription>
            The title for the first section of the profile card
          </FormDescription>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- Content (content1.content) -->
      <FormField v-slot="{ field, errorMessage }" name="content1.content">
        <FormItem>
          <FormLabel>Subtitle 1</FormLabel>
          <FormControl>
            <Input
              :model-value="field.value"
              v-bind="field"
              :disabled="disabled"
              placeholder="Enter content"
              @update:model-value="
                (value: any) =>
                  setFieldValue('content1.content', value as never)
              "
            />
          </FormControl>
          <FormDescription>
            The content for the first section of the profile card
          </FormDescription>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Subtitle Content (content2.title) -->
      <FormField v-slot="{ field, errorMessage }" name="content2.title">
        <FormItem>
          <FormLabel>Title 2</FormLabel>
          <FormControl>
            <Input
              :model-value="field.value"
              v-bind="field"
              :disabled="disabled"
              placeholder="Enter title"
              @update:model-value="
                (value: any) => setFieldValue('content2.title', value as never)
              "
            />
          </FormControl>
          <FormDescription>
            The title for the second section of the profile card
          </FormDescription>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- Content (content2.content) -->
      <FormField v-slot="{ field, errorMessage }" name="content2.content">
        <FormItem>
          <FormLabel>Subtitle 2</FormLabel>
          <FormControl>
            <Input
              :model-value="field.value"
              v-bind="field"
              :disabled="disabled"
              placeholder="Enter content"
              @update:model-value="
                (value: any) =>
                  setFieldValue('content2.content', value as never)
              "
            />
          </FormControl>
          <FormDescription>
            The content for the second section of the profile card
          </FormDescription>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>
    </div>

    <!-- Image -->
    <div class="space-y-4">
      <AdminBaseDropzone
        :model-value="formValues.image"
        :disabled="disabled"
        :is-multiple-allowed="false"
        :file-types="['image']"
        title="Card Image *"
        description="Upload the card image (required for profile cards)"
        :existing-files="primaryImage ? [primaryImage] : []"
        @files-selected="
          (files: any) => uploadPrimaryImage(files ? files[0]?.file : undefined)
        "
        @file-removed="() => (primaryImage = null)"
      />
    </div>

    <!-- Link Button -->
    <div class="space-y-4">
      <h3 class="text-sm font-medium">Link Button</h3>
      <AdminTemplateButton
        v-model="formValues.linkButton"
        :disabled="disabled"
        :show-button="showLinkButton"
        @update:show-button="showLinkButton = $event"
        @update:model-value="
          (value: any) => {
            setFieldValue('linkButton', value as never);
          }
        "
      />
    </div>
  </Form>
</template>
