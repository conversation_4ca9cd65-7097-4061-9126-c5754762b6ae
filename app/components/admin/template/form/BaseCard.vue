<script setup lang="ts">
import type { BaseCardFormData } from '~/types/admin/forms';

const props = defineProps<{
  modelValue: BaseCardFormData;
  disabled?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: BaseCardFormData): void;
}>();

const form = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});
</script>

<template>
  <form class="space-y-4" @submit.prevent>
    <div class="grid gap-4 md:grid-cols-2">
      <!-- Title -->
      <div class="space-y-2">
        <Label for="title">Title</Label>
        <Input
          id="title"
          v-model="form.title"
          type="text"
          placeholder="Enter title"
          :disabled="disabled"
          required
        />
      </div>

      <!-- Priority -->
      <div class="space-y-2">
        <Label for="priority">Priority</Label>
        <Input
          id="priority"
          v-model="form.priority"
          type="number"
          min="0"
          :disabled="disabled"
          required
        />
      </div>
    </div>

    <!-- Description -->
    <div class="space-y-2">
      <Label for="description">Description</Label>
      <Textarea
        id="description"
        v-model="form.description"
        placeholder="Enter description"
        :disabled="disabled"
      />
    </div>

    <!-- Key -->
    <div class="space-y-2">
      <Label for="key">Key</Label>
      <Input
        id="key"
        v-model="form.key"
        type="text"
        placeholder="Enter key"
        :disabled="disabled"
        required
      />
    </div>

    <!-- Image Upload -->
    <div class="space-y-2">
      <Label for="image">Image</Label>
      <div
        v-if="form.image && form.image.pathname"
        class="mt-2 p-2 border rounded"
      >
        <p class="text-sm">{{ form.image.title || 'Image' }}</p>
        <p class="text-xs text-muted-foreground">{{ form.image.pathname }}</p>
      </div>
      <p v-else class="text-sm text-muted-foreground mt-1">
        Image will be handled by the specific card component
      </p>
    </div>

    <slot />
  </form>
</template>
