<script setup lang="ts">
import type { VisionCardConfig } from '~/types/admin';
import { toTypedSchema } from '@vee-validate/zod';
import { useForm } from 'vee-validate';
import { toast } from 'vue-sonner';

import type { FileData } from '~/types/home';

// Import the fileSchema directly since we need it for validation
import { fileSchema } from '~~/shared/schema';
import { z } from 'zod';
// Update props to use the new type
const props = defineProps<{
  modelValue: VisionCardConfig;
  disabled?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: VisionCardConfig): void;
}>();

// Define validation schema directly
const visionCardSchema = z.object({
  layout: z.literal('vision'),
  content1: z.object({
    title: z.string().min(1, 'Title is required'),
    content: z.string().min(1, 'Content is required'),
  }),
  image: fileSchema.nullable(),
  key: z.string(),
});

const formSchema = toTypedSchema(visionCardSchema);
const primaryImage = ref<FileData | null>(props.modelValue.image || null);

const initialValues = {
  layout: 'vision',
  content1: {
    title: props.modelValue?.content1?.title || '',
    content: props.modelValue?.content1?.content || '',
  },
  image: props.modelValue.image || null,
  key: props.modelValue.key || 'vision',
};

const { values: formValues, setFieldValue } = useForm<typeof initialValues>({
  initialValues,
  validationSchema: formSchema,
});

const emitUpdatedValues = (newValues: typeof formValues) => {
  const updatedValues: VisionCardConfig = {
    ...newValues,
    image: primaryImage.value || {
      pathname: '',
      title: '',
      type: '',
      prefix: 'vision',
    },
    layout: 'vision',
  };
  emit('update:modelValue', updatedValues);
};

async function uploadPrimaryImage(file: File | undefined) {
  if (!file) {
    primaryImage.value = null;
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'vision');
    form.append('title', 'Vision Image');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', { method: 'POST', body: form });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'vision',
    };
    primaryImage.value = fileData;
    emitUpdatedValues(formValues);
  } catch (err) {
    console.error('Failed to upload primary file:', err);
    toast.error('Failed to upload primary image');
  }
}

watch(formValues, (newValues) => {
  emitUpdatedValues(newValues);
});
</script>

<template>
  <Form
    class="space-y-6"
    :validation-schema="formSchema"
    :initial-values="initialValues"
  >
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Title -->
      <FormField
        v-slot="{ field, errorMessage }"
        name="content1.title"
        required
      >
        <FormItem>
          <FormLabel for="content1.title"
            >Title <span class="text-destructive">*</span></FormLabel
          >
          <FormControl>
            <Input
              id="content1.title"
              v-model="field.value"
              type="text"
              placeholder="Enter title"
              :disabled="disabled"
              @update:model-value="
                (value: any) => {
                  setFieldValue('content1.title', value as never);
                }
              "
            />
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>
    </div>

    <!-- Content Body -->
    <FormField
      v-slot="{ field, errorMessage }"
      name="content1.content"
      required
    >
      <FormItem>
        <FormLabel for="content1.content"
          >Content <span class="text-destructive">*</span></FormLabel
        >
        <FormControl>
          <Textarea
            id="content1.content"
            v-model="field.value"
            placeholder="Enter content"
            :disabled="disabled"
            required
            rows="4"
            @update:model-value="
              (value: any) => {
                setFieldValue('content1.content', value as never);
              }
            "
          />
        </FormControl>
        <FormMessage>{{ errorMessage }}</FormMessage>
      </FormItem>
    </FormField>

    <!-- Image -->
    <div class="space-y-4">
      <AdminBaseDropzone
        :model-value="formValues.image"
        :disabled="disabled"
        :is-multiple-allowed="false"
        :file-types="['image']"
        title="Vision Image"
        description="Upload the vision image (required)"
        :existing-files="primaryImage ? [primaryImage] : []"
        @files-selected="
          (files: any) => uploadPrimaryImage(files ? files[0]?.file : undefined)
        "
        @file-removed="() => (primaryImage = null)"
      />
    </div>
  </Form>
</template>
