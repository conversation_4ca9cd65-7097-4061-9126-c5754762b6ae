<script setup lang="ts">
import { PlusCircle, X, ChevronDown, ChevronUp, Edit } from 'lucide-vue-next';
import type { CardLayout } from '~~/server/database/tables/enums';
import { toast } from 'vue-sonner';
import { z } from 'zod';
import { toTypedSchema } from '@vee-validate/zod';
import { useForm } from 'vee-validate';
import { fileSchema } from '~~/shared/schema';
import type {
  CardListConfig,
  ProfileCardConfig,
  StandardCardConfig,
  VisionCardConfig,
} from '~/types/admin';

const props = defineProps<{
  modelValue: CardListConfig;
  disabled?: boolean;
}>();
const emit = defineEmits<{
  (e: 'update:modelValue', value: CardListConfig): void;
  (e: 'submit'): void;
}>();

// Define validation schema that matches the structure in @shared/schema/section/create.ts
const cardSchema = z.object({
  layout: z.enum(['standard', 'profile', 'vision']),
  title: z.string().min(1, 'Title is required').optional(),
  content1: z
    .object({
      title: z.string().optional(),
      content: z.string().optional(),
    })
    .optional(),
  content2: z
    .object({
      title: z.string().optional(),
      content: z.string().optional(),
    })
    .optional(),
  image: fileSchema.nullable(),
  key: z.string(),
});

const cardListSchema = z.object({
  title: z.string().min(1, 'List title is required'),
  description: z.string().optional(),
  maxCards: z.number().min(1, 'Maximum cards must be at least 1'),
  minCards: z.number().min(0, 'Minimum cards must be at least 0'),
  layout: z.string(),
  cards: z.array(cardSchema),
});

const formSchema = toTypedSchema(cardListSchema);

// Initialize form with default values
const initialValues: CardListConfig = {
  title: props.modelValue.title || '',
  description: props.modelValue.description || '',
  maxCards: props.modelValue.maxCards || 10,
  minCards: props.modelValue.minCards || 1,
  layout: props.modelValue.layout || 'standard',
  cards: props.modelValue.cards || [],
};

const { values: formValues, setFieldValue } = useForm<CardListConfig>({
  initialValues,
  validationSchema: formSchema,
});

// Track which card is currently being edited
const editingCardIndex = ref<number | null>(null);
const expandedCards = ref<Set<number>>(new Set());

// Function to update a card
const updateCard = (
  index: number,
  newCard: ProfileCardConfig | StandardCardConfig | VisionCardConfig
) => {
  setFieldValue(`cards.${index}`, newCard);
  emitUpdatedValues({ ...formValues, cards: formValues.cards });
};

// Function to toggle card expansion
const toggleCardExpansion = (index: number) => {
  if (expandedCards.value.has(index)) {
    expandedCards.value.delete(index);
  } else {
    expandedCards.value.add(index);
  }
};

// Function to start editing a card
const startEditingCard = (index: number) => {
  editingCardIndex.value = index;
};

// Function to stop editing a card
const stopEditingCard = () => {
  // Ensure the form is updated with the latest card data
  emitUpdatedValues(formValues);
  editingCardIndex.value = null;
};

// Function to start adding a new card
const startAddingCard = () => {
  // Create a new card directly
  createNewCard();
};

// Function to create a new card based on the list's layout
const createNewCard = () => {
  let newCard:
    | ProfileCardConfig
    | StandardCardConfig
    | VisionCardConfig
    | null = null;

  // Add layout-specific properties based on the form's layout
  const layout = formValues.layout;

  // Create appropriate card type based on layout
  if (layout === 'profile') {
    newCard = {
      content1: {
        title: '',
        content: '',
      },
      content2: {
        title: '',
        content: '',
      },
      key: `card_${Date.now()}`,
      image: {
        type: 'image',
        pathname: '',
        title: '',
        prefix: 'profile',
      },
      linkButton: null,
      downloadButton: null,
      layout: 'profile',
    };
  } else if (layout === 'standard') {
    newCard = {
      title: '',
      content1: {
        title: '',
        content: '',
      },
      image: {
        type: 'image',
        pathname: '',
        title: '',
        prefix: 'standard',
      },
      linkButton: null,
      downloadButton: null,
      layout: 'standard',
      key: `card_${Date.now()}`,
    };
  } else if (layout === 'vision') {
    newCard = {
      content1: {
        title: '',
        content: '',
      },
      image: {
        type: 'image',
        pathname: '',
        title: '',
        prefix: 'vision',
      },
      key: `card_${Date.now()}`,
      layout: 'vision',
    };
  } else {
    // Handle unknown layout type
    toast.error(`Unknown layout type: ${layout}`);
    return;
  }

  // Create a new array with the added card (for reactivity)
  const updatedCards = [...formValues.cards, newCard];

  // Update the field value
  setFieldValue('cards', updatedCards as never);

  // Emit the updated form value to parent
  emitUpdatedValues({ ...formValues, cards: updatedCards });

  // Expand the new card
  expandedCards.value.add(updatedCards.length - 1);

  // Start editing the newly added card
  editingCardIndex.value = updatedCards.length - 1;
};

// Function to remove a card
const removeCard = (index: number) => {
  // Create a new array without the removed card (for reactivity)
  const updatedCards = [...formValues.cards];
  updatedCards.splice(index, 1);

  // Update the field value
  setFieldValue('cards', updatedCards as never);

  // Emit the updated form value
  emitUpdatedValues({ ...formValues, cards: updatedCards });

  // If we were editing this card, stop editing
  if (editingCardIndex.value === index) {
    editingCardIndex.value = null;
  } else if (
    editingCardIndex.value !== null &&
    editingCardIndex.value > index
  ) {
    // Adjust the editing index if we removed a card before the one being edited
    editingCardIndex.value--;
  }

  // Remove from expanded cards set if it was expanded
  if (expandedCards.value.has(index)) {
    expandedCards.value.delete(index);
  }

  // Update indices in expandedCards set for cards after the removed one
  const newExpandedCards = new Set<number>();
  expandedCards.value.forEach((cardIndex) => {
    if (cardIndex < index) {
      newExpandedCards.add(cardIndex);
    } else if (cardIndex > index) {
      newExpandedCards.add(cardIndex - 1);
    }
  });
  expandedCards.value = newExpandedCards;
};

// Function to emit updated values to parent
const emitUpdatedValues = (newValues: typeof formValues) => {
  const updatedValues: CardListConfig = {
    ...newValues,
    title: newValues.title,
    description: newValues.description,
    maxCards: newValues.maxCards,
    minCards: newValues.minCards,
    layout: newValues.layout as CardLayout,
    cards: newValues.cards,
  };
  emit('update:modelValue', updatedValues);
};

// Function to handle form submission
const handleSubmit = () => {
  // Validation now happens automatically through vee-validate
  if (formValues.cards.length === 0) {
    toast.error('Please add at least one card');
    return;
  }

  // Make sure we emit the final form value before submitting
  emitUpdatedValues(formValues);
  emit('submit');
};

// Function to get card type display name
const getCardTypeDisplayName = (type: CardLayout) => {
  switch (type) {
    case 'standard':
      return 'Standard Card';
    case 'profile':
      return 'Profile Card';
    case 'vision':
      return 'Vision Card';
    default:
      return type;
  }
};

// Function to get card preview text based on type
const getCardPreviewText = (
  card: ProfileCardConfig | StandardCardConfig | VisionCardConfig,
  index: number
) => {
  if (!card) {
    return `Card ${index + 1}`;
  }

  if (card.layout === 'profile') {
    return card.content1?.title || `Profile Card ${index + 1}`;
  } else if (card.layout === 'standard') {
    return card.title || `Standard Card ${index + 1}`;
  } else if (card.layout === 'vision') {
    return card.content1?.title || `Vision Card ${index + 1}`;
  } else {
    return `Card ${index + 1}`;
  }
};

// Watch for changes to the form values and emit updates
watch(
  formValues,
  (newValues) => {
    emitUpdatedValues(newValues);
  },
  { deep: true }
);
</script>

<template>
  <Form
    class="space-y-6"
    :validation-schema="formSchema"
    :initial-values="initialValues"
  >
    <!-- List Settings -->
    <div class="grid gap-4 md:grid-cols-2">
      <!-- Title -->
      <div class="space-y-2 col-span-2">
        <FormField v-slot="{ field, errorMessage }" name="title" required>
          <FormItem>
            <FormLabel for="title"
              >List Title <span class="text-destructive">*</span></FormLabel
            >
            <FormControl>
              <Input
                id="title"
                v-model="field.value"
                type="text"
                placeholder="Enter list title"
                :disabled="disabled"
                required
                @update:model-value="
                  (value) => {
                    setFieldValue('title', value as never);
                  }
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
      </div>

      <!-- Min Cards -->
      <div class="space-y-2">
        <FormField v-slot="{ field, errorMessage }" name="minCards" required>
          <FormItem>
            <FormLabel for="minCards"
              >Minimum Cards <span class="text-destructive">*</span></FormLabel
            >
            <FormControl>
              <Input
                id="minCards"
                v-model="field.value"
                type="number"
                min="0"
                :disabled="disabled"
                required
                @update:model-value="
                  (value) => {
                    setFieldValue('minCards', Number(value) as never);
                  }
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
      </div>

      <!-- Max Cards -->
      <div class="space-y-2">
        <FormField v-slot="{ field, errorMessage }" name="maxCards" required>
          <FormItem>
            <FormLabel for="maxCards"
              >Maximum Cards <span class="text-destructive">*</span></FormLabel
            >
            <FormControl>
              <Input
                id="maxCards"
                v-model="field.value"
                type="number"
                min="1"
                :disabled="disabled"
                required
                @update:model-value="
                  (value) => {
                    setFieldValue('maxCards', Number(value) as never);
                  }
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
      </div>
    </div>

    <!-- Description -->
    <div class="space-y-2">
      <FormField v-slot="{ field, errorMessage }" name="description">
        <FormItem>
          <FormLabel for="description">List Description</FormLabel>
          <FormControl>
            <Textarea
              id="description"
              v-model="field.value"
              placeholder="Enter list description"
              :disabled="disabled"
              @update:model-value="
                (value) => {
                  setFieldValue('description', value as never);
                }
              "
            />
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>
    </div>

    <!-- Cards Section -->
    <div class="space-y-4">
      <div class="flex flex-col space-y-2">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <Label>Cards</Label>
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            :disabled="disabled || editingCardIndex !== null"
            @click="startAddingCard"
          >
            <PlusCircle class="h-4 w-4 mr-1" />
            Add Card
          </Button>
        </div>
        <div class="bg-muted p-3 rounded-md flex items-center justify-between">
          <div class="flex items-center gap-2">
            <span class="text-sm font-medium">Card Type:</span>
            <Badge>{{
              getCardTypeDisplayName(formValues.layout as CardLayout)
            }}</Badge>
          </div>
          <p class="text-xs text-muted-foreground">
            All cards in this list will use this layout
          </p>
        </div>
      </div>

      <!-- Cards List -->
      <div
        v-if="formValues.cards && formValues.cards.length > 0"
        class="space-y-4"
      >
        <div
          v-for="(card, index) in formValues.cards"
          :key="card.key || index"
          class="border rounded-lg overflow-hidden"
        >
          <!-- Card Header -->
          <div
            class="p-4 bg-muted flex items-center justify-between cursor-pointer"
            @click="toggleCardExpansion(index)"
          >
            <div class="flex items-center gap-2">
              <span class="font-medium">{{
                getCardPreviewText(card, index)
              }}</span>
            </div>
            <div class="flex items-center gap-2">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                class="h-8 w-8"
                :disabled="disabled || editingCardIndex !== null"
                aria-label="Edit card"
                @click.stop="startEditingCard(index)"
              >
                <Edit class="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                class="h-8 w-8"
                :disabled="disabled || editingCardIndex !== null"
                aria-label="Remove card"
                @click.stop="removeCard(index)"
              >
                <X class="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                class="h-8 w-8"
                aria-label="Toggle card expansion"
                @click.stop="toggleCardExpansion(index)"
              >
                <ChevronDown v-if="!expandedCards.has(index)" class="h-4 w-4" />
                <ChevronUp v-else class="h-4 w-4" />
              </Button>
            </div>
          </div>

          <!-- Card Content (Expanded View) -->
          <div v-if="expandedCards.has(index)" class="p-4 border-t">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm font-medium mb-1">Title</p>
                <p class="text-sm">
                  {{ getCardPreviewText(card, index) || 'No title' }}
                </p>
              </div>
              <div>
                <p class="text-sm font-medium mb-1">Image</p>
                <p class="text-sm">
                  {{ card.image?.pathname ? 'Image uploaded' : 'No image' }}
                </p>
              </div>
            </div>
          </div>

          <!-- Card Edit Form -->
          <div v-if="editingCardIndex === index" class="p-4 border-t">
            <div v-if="disabled" class="flex justify-center py-4">
              <div
                class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"
              ></div>
            </div>
            <template v-else>
              <!-- Standard Card Form -->
              <AdminTemplateFormStandardCard
                v-if="formValues.layout === 'standard'"
                v-model="formValues.cards[index] as StandardCardConfig"
                :disabled="disabled"
                @update:model-value="updateCard(index, $event)"
              />

              <AdminTemplateFormProfileCard
                v-else-if="formValues.layout === 'profile'"
                v-model="formValues.cards[index] as ProfileCardConfig"
                :disabled="disabled"
                @update:model-value="updateCard(index, $event)"
              />

              <AdminTemplateFormVisionCard
                v-else-if="formValues.layout === 'vision'"
                v-model="formValues.cards[index] as VisionCardConfig"
                :disabled="disabled"
                @update:model-value="updateCard(index, $event)"
              />
              <div class="flex justify-end mt-4">
                <Button type="button" @click="stopEditingCard"> Done </Button>
              </div>
            </template>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div
        v-else
        class="border rounded-lg p-8 flex flex-col items-center justify-center text-center"
      >
        <div class="mb-4 p-3 rounded-full bg-muted">
          <PlusCircle class="h-6 w-6 text-muted-foreground" />
        </div>
        <h3 class="text-lg font-medium mb-1">No cards added</h3>
        <p class="text-muted-foreground mb-4">
          Add cards to your
          {{
            getCardTypeDisplayName(
              formValues.layout as CardLayout
            ).toLowerCase()
          }}
          list
        </p>
        <Button type="button" @click="startAddingCard">
          <PlusCircle class="h-4 w-4 mr-1" />
          Add Card
        </Button>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end">
      <Button
        :disabled="
          disabled ||
          !formValues.cards ||
          formValues.cards.length === 0 ||
          editingCardIndex !== null
        "
        @click="handleSubmit"
      >
        <template v-if="disabled">
          <div
            class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"
          ></div>
          Saving...
        </template>
        <template v-else> Save Cards List </template>
      </Button>
    </div>
  </Form>
</template>
