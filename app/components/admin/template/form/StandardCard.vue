<script setup lang="ts">
import type { StandardCardConfig } from '~/types/admin';
import type { FileData } from '~/types/home';
import { toTypedSchema } from '@vee-validate/zod';
import { useForm } from 'vee-validate';
import type { ButtonStyle } from '~~/server/database/tables';
import { toast } from 'vue-sonner';
import { z } from 'zod';
import {
  fileSchema,
  linkButtonSchema,
  downloadButtonSchema,
} from '~~/shared/schema';

// Update props to use the new type
const props = defineProps<{
  modelValue: StandardCardConfig;
  disabled?: boolean;
}>();

const emit =
  defineEmits<(e: 'update:modelValue', value: StandardCardConfig) => void>();

// Define validation schema directly
const standardCardSchema = z.object({
  layout: z.literal('standard'),
  title: z.string().min(1, 'Title is required'),
  content1: z.object({
    title: z.string().min(1, 'Content title is required'),
    content: z.string().min(1, 'Content is required'),
  }),
  image: fileSchema,
  linkButton: linkButtonSchema.nullable(),
  downloadButton: downloadButtonSchema.nullable(),
  files: fileSchema.array().optional(),
  key: z.string(),
});
const formSchema = toTypedSchema(standardCardSchema);
const primaryImage = ref<FileData | null>(props.modelValue.image || null);
const attachments = ref<FileData[]>(props.modelValue.files || []);
const initialValues = {
  title: props.modelValue.title || '',
  content1: {
    title: props.modelValue.content1?.title || '',
    content: props.modelValue.content1?.content || '',
  },
  image: props.modelValue.image || null,
  linkButton: props.modelValue.linkButton
    ? {
        ...props.modelValue.linkButton,
        style: props.modelValue.linkButton.style as ButtonStyle,
      }
    : null,
  downloadButton: props.modelValue.downloadButton
    ? {
        ...props.modelValue.downloadButton,
        style: props.modelValue.downloadButton.style as ButtonStyle,
        newTab: props.modelValue.downloadButton.newTab || false,
      }
    : null,
  files: props.modelValue.files || [],
  key: props.modelValue.key || '',
  layout: props.modelValue.layout || 'standard',
};
const showLinkButton = ref(!!props.modelValue.linkButton);
const { values: formValues, setFieldValue } = useForm<typeof initialValues>({
  initialValues,
  validationSchema: formSchema,
});

const emitUpdatedValues = (newValues: typeof formValues) => {
  // Make sure to include the attachments in the updated values, but filter out the primary image
  const primaryImagePathname = primaryImage.value?.pathname || '';

  // Filter out the primary image from attachments
  const filteredAttachments = attachments.value.filter(
    (file) => file.pathname !== primaryImagePathname
  );

  const updatedValues: StandardCardConfig = {
    ...newValues,
    image: primaryImage.value || {
      pathname: '',
      title: '',
      type: '',
      prefix: 'standard',
    },
    files: filteredAttachments, // Only include non-primary image files
    layout: props.modelValue.layout || 'standard',
    linkButton: newValues.linkButton
      ? {
          ...newValues.linkButton,
          style: newValues.linkButton.style as string,
          newTab: newValues.linkButton.newTab || false,
        }
      : null,
    downloadButton: newValues.downloadButton
      ? {
          ...newValues.downloadButton,
          style: newValues.downloadButton.style as string,
          newTab: newValues.downloadButton.newTab || false,
        }
      : null,
  };
  console.log('Emitting updated values with files:', updatedValues.files);
  emit('update:modelValue', updatedValues);
};

async function uploadPrimaryImage(file: File | undefined) {
  if (!file) {
    primaryImage.value = null;
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'standard');
    form.append('title', 'Primary Image');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', { method: 'POST', body: form });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'standard',
    };
    primaryImage.value = fileData;
    emitUpdatedValues(formValues);
  } catch (err) {
    console.error('Failed to upload primary file:', err);
    toast.error('Failed to upload primary image');
  }
}

const uploadFiles = async (files: { file: File; title?: string }[]) => {
  if (!files.length) return;
  const formData = new FormData();

  // Add each file to FormData
  for (const { file, title } of files) {
    formData.append('files', file);
    formData.append('prefix', 'standard'); // Changed from 'announcement' to 'standard' for consistency
    formData.append('title', title || file.name);
    formData.append('type', file.type);
  }

  try {
    const response = await $fetch<FileData[]>('/api/blob/upload', {
      method: 'POST',
      body: formData,
    });

    if (response && Array.isArray(response)) {
      const uploadedFiles = response.map((file) => ({
        pathname: file.pathname,
        title: file.title,
        type: file.type,
        prefix: 'standard', // Changed from 'announcement' to 'standard' for consistency
      }));

      // Get the primary image pathname to filter it out
      const primaryImagePathname = primaryImage.value?.pathname || '';

      // Filter out any files that match the primary image
      const newFilesWithoutPrimary = uploadedFiles.filter(
        (file) => file.pathname !== primaryImagePathname
      );

      // Append new files to existing ones
      attachments.value = [...attachments.value, ...newFilesWithoutPrimary];

      // Update the form field value too
      setFieldValue('files', attachments.value as never);

      // Emit the updated values
      emitUpdatedValues(formValues);
      toast.success(`Successfully uploaded ${uploadedFiles.length} file(s)`);
    }
  } catch (error) {
    console.error('Error uploading files:', error);
    toast.error('Failed to upload files');
  }
};

// Update file removal handler
function handleFileRemoval(fileTitle: string) {
  const fileToRemove = attachments.value.find(
    (file) => file.title === fileTitle
  );
  if (!fileToRemove) return;

  // Check if this is the primary image
  const isPrimaryImage = primaryImage.value?.pathname === fileToRemove.pathname;

  if (isPrimaryImage) {
    // Don't allow removing the primary image from attachments
    toast.error('Cannot remove primary image from attachments');
    return;
  }

  // Filter out the file to remove
  attachments.value = attachments.value.filter(
    (file) => file.title !== fileTitle
  );

  // Update the form field value too
  setFieldValue('files', attachments.value as never);

  // Emit the updated values
  emitUpdatedValues(formValues);
  toast.success('File removed successfully');
}

watch(formValues, (newValues) => {
  emitUpdatedValues(newValues);
});
</script>

<template>
  <Form
    class="space-y-6"
    :validation-schema="formSchema"
    :initial-values="initialValues"
  >
    <div
      class="bg-amber-50 border border-amber-200 text-amber-800 px-4 py-3 rounded-md mb-3 flex items-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
          clip-rule="evenodd"
        />
      </svg>
      <span
        >An image is required for standard cards. Please upload an image.</span
      >
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Title -->
      <FormField v-slot="{ field, errorMessage }" name="title" required>
        <FormItem>
          <FormLabel for="title"
            >Title <span class="text-destructive">*</span></FormLabel
          >
          <FormControl>
            <Input
              id="title"
              v-model="field.value"
              type="text"
              placeholder="Enter title"
              :disabled="disabled"
              @update:model-value="
                (value: any) => {
                  setFieldValue('title', value as never);
                }
              "
            />
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- Content Title -->
      <FormField
        v-slot="{ field, errorMessage }"
        name="content1.title"
        required
      >
        <FormItem>
          <FormLabel for="content1.title"
            >Content Title <span class="text-destructive">*</span></FormLabel
          >
          <FormControl>
            <Input
              id="content1.title"
              v-model="field.value"
              type="text"
              placeholder="Enter content title"
              :disabled="disabled"
              required
              @update:model-value="
                (value: any) => {
                  setFieldValue('content1.title', value as never);
                }
              "
            />
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>
    </div>

    <!-- Content Body -->
    <FormField
      v-slot="{ field, errorMessage }"
      name="content1.content"
      required
    >
      <FormItem>
        <FormLabel for="content1.content"
          >Content <span class="text-destructive">*</span></FormLabel
        >
        <FormControl>
          <AdminBaseTextEditor
            id="content1.content"
            v-model="field.value"
            placeholder="Enter content"
            :disabled="disabled"
            required
            rows="4"
            @update:model-value="
              (value: string | number) => {
                setFieldValue('content1.content', value as never);
              }
            "
          />
        </FormControl>
        <FormMessage>{{ errorMessage }}</FormMessage>
      </FormItem>
    </FormField>
    <!-- Image -->
    <div class="space-y-4">
      <AdminBaseDropzone
        :model-value="formValues.image"
        :disabled="disabled"
        :is-multiple-allowed="false"
        :file-types="['image']"
        title="Card Image"
        description="Upload the card image (required)"
        :existing-files="primaryImage ? [primaryImage] : []"
        @files-selected="
          (files: any) => uploadPrimaryImage(files ? files[0]?.file : undefined)
        "
        @file-removed="() => (primaryImage = null)"
      />
    </div>

    <div class="space-y-4">
      <AdminBaseDropzone
        :model-value="formValues.files"
        :disabled="disabled"
        :is-multiple-allowed="true"
        :file-types="['image', 'pdf']"
        title="Attach Files"
        description="Upload the files to attach"
        :existing-files="attachments"
        @files-selected="(files) => uploadFiles(files)"
        @file-removed="handleFileRemoval"
      />
    </div>

    <!-- Link Button -->
    <div class="space-y-4">
      <h3 class="text-sm font-medium">Link Button</h3>
      <AdminTemplateButton
        v-model="formValues.linkButton"
        :disabled="disabled"
        :show-button="showLinkButton"
        @update:show-button="showLinkButton = $event"
        @update:model-value="
          (value: any) => {
            setFieldValue('linkButton', value as never);
          }
        "
      />
    </div>
  </Form>
</template>
