<script setup lang="ts">
import type {
  AnnouncementType,
  AnnouncementWithRelations,
} from '~~/server/database/tables';
import { AlertCircle, PlusCircle } from 'lucide-vue-next';

const props = defineProps<{
  type: AnnouncementType;
}>();

const {
  announcements,
  isLoading,
  error,
  isSubmitting,
  showCreateEdit,
  actionState,
  showDeleteDialog,
  currentAnnouncement,
  deleteState,
  fetchAnnouncements,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement,
} = useAnnouncement();

// Filter state
const filterStatus = ref<'all' | 'active' | 'inactive' | 'expired'>('all');

// Computed filtered announcements
// Filter handler
function handleFilterChange(status: 'all' | 'active' | 'inactive' | 'expired') {
  filterStatus.value = status;
  fetchAnnouncements(props.type, filterStatus.value);
}

onMounted(() => {
  fetchAnnouncements(props.type, filterStatus.value);
});

function handleEdit(announcement: AnnouncementWithRelations) {
  currentAnnouncement.value = announcement;
  showCreateEdit.value = true;
  actionState.value = 'edit';
}

async function handleSubmit(values: any) {
  const transformedValues = {
    ...values,
    button: values?.button ? values.button : null,
  };

  if (actionState.value === 'edit' && currentAnnouncement.value?.id) {
    await updateAnnouncement(
      {
        ...transformedValues,
        id: currentAnnouncement.value.id,
      },
      props.type
    );
  } else {
    await createAnnouncement(transformedValues, props.type);
  }
}
</script>

<template>
  <!-- Loading State -->
  <div v-if="isLoading" class="flex justify-center py-8">
    <AdminLayoutLoading />
  </div>

  <!-- Error State -->
  <Alert v-else-if="error" variant="destructive">
    <AlertCircle class="h-4 w-4" />
    <AlertTitle>Error</AlertTitle>
    <AlertDescription>{{ error }}</AlertDescription>
  </Alert>

  <!-- Content -->
  <Card v-else class="border-none">
    <CardHeader>
      <div class="flex flex-wrap items-center justify-between gap-2">
        <!-- Filters -->
        <div class="flex items-center space-x-2">
          <Button
            v-for="status in ['all', 'active', 'inactive', 'expired']"
            :key="status"
            :variant="filterStatus === status ? 'default' : 'outline'"
            class="capitalize"
            :class="[
              filterStatus === status && 'bg-primary text-primary-foreground',
            ]"
            @click="
              handleFilterChange(
                status as 'all' | 'active' | 'inactive' | 'expired'
              )
            "
          >
            {{ status }}
          </Button>
        </div>

        <!-- Filters - Active Inactive Expired -->

        <div class="flex items-center space-x-2">
          <Dialog
            :open="showCreateEdit"
            @update:open="
              actionState = null;
              showCreateEdit = false;
            "
          >
            <DialogTrigger as-child>
              <Button
                variant="outline"
                @click="
                  showCreateEdit = true;
                  currentAnnouncement = null;
                  actionState = 'create';
                "
              >
                <PlusCircle class="h-4 w-4" />
                Create
                {{
                  type === 'notice'
                    ? 'Notice'
                    : type === 'update'
                      ? 'Update'
                      : 'Event'
                }}
              </Button>
            </DialogTrigger>
            <DialogContent class="sm:max-w-[900px] max-h-[90vh]">
              <DialogHeader>
                <DialogTitle>
                  {{ actionState === 'edit' ? 'Edit' : 'Create' }}
                  {{
                    type === 'notice'
                      ? 'Notice'
                      : type === 'update'
                        ? 'Update'
                        : 'Event'
                  }}
                </DialogTitle>
                <DialogDescription>
                  Add a new
                  {{
                    type === 'notice'
                      ? 'notice'
                      : type === 'update'
                        ? 'update'
                        : 'event'
                  }}
                  to your list. Fill in the details below.
                </DialogDescription>
              </DialogHeader>
              <AdminAnnouncementForm
                :type="type"
                :announcement="currentAnnouncement"
                :is-submitting="isSubmitting"
                @submit="handleSubmit"
                @cancel="
                  showCreateEdit = false;
                  actionState = null;
                "
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </CardHeader>
    <CardContent>
      <AdminAnnouncementList
        :announcements="announcements"
        @edit="handleEdit($event)"
        @delete="
          currentAnnouncement = $event;
          showDeleteDialog = true;
          actionState = 'delete';
        "
      />
    </CardContent>
  </Card>

  <!-- Delete Dialog -->
  <AdminMenuDeleteDialog
    v-if="currentAnnouncement"
    v-model:is-open="showDeleteDialog"
    :is-submitting="isSubmitting"
    menu-title="announcement"
    :delete-state="deleteState"
    :type="type"
    :description="`Are you sure you want to delete this ${type === 'notice' ? 'notice' : type === 'update' ? 'update' : 'event'}? This action cannot be undone.`"
    @confirm="deleteAnnouncement(type)"
  />
</template>

<style scoped>
.card {
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
}
</style>
