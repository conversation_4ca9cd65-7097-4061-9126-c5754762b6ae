<script setup lang="ts">
import type { AnnouncementWithRelations } from '~~/server/database/tables';
import {
  Bell,
  CalendarIcon,
  ClockIcon,
  Edit,
  LinkIcon,
  Trash2,
} from 'lucide-vue-next';
import type { AnnouncementListData } from '~/types/home';

const props = defineProps<{
  announcements: AnnouncementWithRelations[];
}>();

const emit = defineEmits<{
  (e: 'edit' | 'delete', announcement: AnnouncementWithRelations): void;
}>();

// Computed properties for filtering announcements
const updateAnnouncements = computed(() =>
  props.announcements.filter((a) => a.announcementType === 'update')
);

const nonUpdateAnnouncements = computed(() =>
  props.announcements.filter((a) => a.announcementType !== 'update')
);

function handleDelete(announcement: AnnouncementWithRelations) {
  emit('delete', announcement);
}

function formatEventDate(date: number | null) {
  if (!date) return 'Not set';
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

const showPreview = ref(false);
const selectedAnnouncement = ref<AnnouncementWithRelations | null>(null);

const transformAnnouncement = (
  announcement: AnnouncementWithRelations
): AnnouncementListData => {
  const value: any = {
    id: announcement.id.toString(),
    title: announcement.title,
    date: announcement.eventDate ? new Date(announcement.eventDate) : null,
    description: announcement.content,
  };
  if (announcement.announcementType === 'event') {
    value.primaryImage = {
      pathname: announcement.files[0]?.file.pathname || '',
      title: announcement.files[0]?.file.title || '',
      type: announcement.files[0]?.file.type || '',
      prefix: 'announcement',
    };
  }
  return value;
};
</script>

<template>
  <div class="space-y-4">
    <div
      v-if="!announcements.length"
      class="flex flex-col items-center justify-center py-8 text-center"
    >
      <div class="rounded-full bg-muted p-3 mb-4">
        <Bell class="h-6 w-6 text-muted-foreground" />
      </div>
      <h3 class="font-semibold mb-1">No Announcements Found</h3>
      <p class="text-sm text-muted-foreground">
        Create your first announcement to get started.
      </p>
    </div>

    <div v-else>
      <!-- Updates Section -->
      <div v-if="updateAnnouncements.length > 0" class="mb-8">
        <div class="grid gap-3">
          <div
            v-for="announcement in updateAnnouncements"
            :key="announcement.id"
            class="flex items-center justify-between p-4 bg-muted/50 rounded-lg hover:bg-muted/70 transition-all group"
          >
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-3">
                <p
                  class="font-medium whitespace-normal max-w-full line-clamp-2"
                  v-html="announcement.content"
                />
              </div>
              <div
                class="mt-1 flex items-center gap-4 text-sm text-muted-foreground"
              >
                <span class="flex items-center gap-1">
                  <CalendarIcon class="h-3.5 w-3.5" />
                  {{ formatEventDate(announcement.scheduledAt) }}
                </span>
                <Badge
                  :variant="announcement.isActive ? 'default' : 'secondary'"
                  class="text-xs"
                >
                  {{ announcement.isActive ? 'Active' : 'Inactive' }}
                </Badge>
              </div>
            </div>
            <div
              class="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <Button
                variant="ghost"
                size="icon"
                @click="emit('edit', announcement)"
              >
                <Edit class="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                class="text-destructive"
                @click="handleDelete(announcement)"
              >
                <Trash2 class="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- Notices and Events Section -->
      <div v-if="nonUpdateAnnouncements.length > 0">
        <h3
          v-if="updateAnnouncements.length > 0"
          class="text-lg font-semibold mb-4"
        >
          Notices & Events
        </h3>
        <div class="grid gap-4">
          <Card
            v-for="announcement in nonUpdateAnnouncements"
            :key="announcement.id"
            class="transition-all hover:shadow-md"
          >
            <CardHeader
              class="flex flex-row items-center justify-between pb-2 space-y-0"
            >
              <CardTitle class="text-base font-semibold">
                {{ announcement.title }}
              </CardTitle>
              <div class="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  @click="
                    () => {
                      showPreview = true;
                      selectedAnnouncement = announcement;
                    }
                  "
                >
                  <Icon name="i-heroicons-viewfinder-circle" class="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  @click="emit('edit', announcement)"
                >
                  <Edit class="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  class="text-destructive"
                  @click="handleDelete(announcement)"
                >
                  <Trash2 class="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent class="space-y-3">
              <div
                class="text-sm text-muted-foreground line-clamp-2"
                v-html="announcement.content"
              />
              <div class="flex flex-wrap gap-4 text-sm">
                <Badge
                  :variant="announcement.isActive ? 'default' : 'secondary'"
                >
                  {{ announcement.isActive ? 'Active' : 'Inactive' }}
                </Badge>
                <Badge v-if="announcement.announcementType" variant="outline">
                  Priority: {{ announcement.priority }}
                </Badge>
                <Badge variant="outline">
                  Type: {{ announcement.announcementType }}
                </Badge>
              </div>
              <div
                class="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-muted-foreground"
              >
                <div class="flex items-center gap-2">
                  <CalendarIcon class="h-4 w-4" />
                  <span
                    >Scheduled:
                    {{ formatEventDate(announcement.scheduledAt) }}</span
                  >
                </div>
                <div class="flex items-center gap-2">
                  <ClockIcon class="h-4 w-4" />
                  <span
                    >Expires:
                    {{ formatEventDate(announcement.expiresAt) }}</span
                  >
                </div>
              </div>
              <div
                v-if="announcement.button"
                class="flex items-center gap-2 text-sm"
              >
                <LinkIcon class="h-4 w-4" />
                <a
                  :href="
                    announcement.button?.externalLink ??
                    announcement.button?.internalLink ??
                    ''
                  "
                  class="text-primary hover:underline"
                  :target="announcement.button?.newTab ? '_blank' : '_self'"
                >
                  {{ announcement.button.title }}
                </a>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>

    <Dialog :open="showPreview" @update:open="showPreview = false">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{{ selectedAnnouncement?.title }}</DialogTitle>
          <DialogDescription class="border rounded-lg p-4">
            <HomeEventsCard
              v-if="selectedAnnouncement"
              :event-data="transformAnnouncement(selectedAnnouncement)"
            />
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button @click="showPreview = false">Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>
