<script setup lang="ts">
import type {
  AnnouncementType,
  AnnouncementWithRelations,
} from '~~/server/database/tables';
import { ANNOUNCEMENT_TYPES } from '~~/server/database/tables/enums';
import { toTypedSchema } from '@vee-validate/zod';
import { Loader2 } from 'lucide-vue-next';
import { Form } from 'vee-validate';
import { toast } from 'vue-sonner';
import type { fileSchema } from '~~/shared/schema';
import { createAnnouncementTemplateSchema } from '~~/shared/schema/announcement/create';
import { z } from 'zod';

// Add type guard for announcement type
const isUpdateType = computed(() => props.type === 'update');

const props = defineProps<{
  announcement: AnnouncementWithRelations | null;
  isSubmitting: boolean;
  type: AnnouncementType;
}>();
const emit = defineEmits<{
  (e: 'submit', values: any): void;
  (e: 'cancel'): void;
}>();

// Update file handling
const attachedFiles = ref<(typeof fileSchema._type)[]>(
  props.announcement?.files
    ?.map((fileRelation) => ({
      pathname: fileRelation.file.pathname,
      title: fileRelation.file.title || undefined,
      type: fileRelation.file.type,
      prefix: 'announcement',
    }))
    .filter((file) => file.title !== 'Primary Image') || []
);

const primaryFile = ref<typeof fileSchema._type | null>(
  props.announcement?.files?.find(
    (fileRelation) => fileRelation.file.title === 'Primary Image'
  )
    ? {
        pathname: props.announcement.files.find(
          (f) => f.file.title === 'Primary Image'
        )!.file.pathname,
        title: 'Primary Image',
        type: props.announcement.files.find(
          (f) => f.file.title === 'Primary Image'
        )!.file.type,
        prefix: 'announcement',
      }
    : null
);

const formSchema = computed(() => {
  if (isUpdateType.value) {
    return toTypedSchema(
      z.object({
        title: z.string().min(1, 'Title is required'),
        link: z.string().optional(),
        content: z.string().min(50, 'Content is required'),
        isActive: z.boolean(),
        scheduledAt: z.date({ required_error: 'Schedule date is required' }),
        expiresAt: z.date({ required_error: 'Expiry date is required' }),
        announcementType: z.enum(ANNOUNCEMENT_TYPES),
      })
    );
  }
  return toTypedSchema(createAnnouncementTemplateSchema as any);
});

// Initialize button structure if showButton is true
const showButton = ref(!!props.announcement?.button);
const defaultButton = {
  title: '',
  url: '',
  style: 'primary',
  type: 'link',
  newTab: props.announcement?.button?.newTab ?? false,
};
const initialValues = {
  title: props.announcement?.title || (isUpdateType.value ? 'View' : ''),
  content: props.announcement?.content || '',
  link: props.announcement?.link || '',
  isActive: props.announcement?.isActive ?? true,
  eventDate: props.announcement?.eventDate
    ? new Date(props.announcement.eventDate)
    : null,
  scheduledAt: props.announcement?.scheduledAt
    ? new Date(props.announcement.scheduledAt)
    : new Date(),
  expiresAt: props.announcement?.expiresAt
    ? new Date(props.announcement.expiresAt)
    : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
  priority: props.announcement?.priority || 0,
  announcementType: props.type,
  button: showButton.value
    ? { ...props.announcement?.button, type: 'link' }
    : null,
};

const formRef = ref();

// Watch showButton changes to update button field
watch(showButton, (newValue) => {
  if (formRef.value) {
    formRef.value.setFieldValue('button', newValue ? defaultButton : null);
  }
});

// Update upload functions
async function uploadPrimaryImage(file: File | undefined) {
  if (!file) {
    primaryFile.value = null;
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'announcement');
    form.append('title', 'Primary Image');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', { method: 'POST', body: form });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'announcement',
    };
    primaryFile.value = fileData;
  } catch (err) {
    console.error('Failed to upload primary file:', err);
    toast.error('Failed to upload primary image');
  }
}

const uploadFiles = async (
  files: { file: File; title?: string }[],
  isTitleEdit: boolean = false
) => {
  if (!files.length) return;
  const formData = new FormData();

  // Add each file to FormData
  files.forEach(({ file, title }) => {
    formData.append('files', file);
    formData.append('prefix', 'announcement');
    formData.append('title', title || file.name);
    formData.append('type', file.type);
  });

  try {
    const response = await $fetch<(typeof fileSchema._type)[]>(
      '/api/blob/upload',
      { method: 'POST', body: formData }
    );

    if (response && Array.isArray(response)) {
      const uploadedFiles = response.map((file) => ({
        pathname: file.pathname,
        title: file.title,
        type: file.type,
        prefix: 'announcement',
      }));
      // If this is a title edit, remove the existing file with the same title
      if (isTitleEdit) {
        attachedFiles.value = attachedFiles.value.filter(
          (existingFile) =>
            !uploadedFiles.some(
              (newFile) =>
                newFile.pathname.split('-')[0] ===
                existingFile.pathname?.split('-')[0]
            )
        );
      }

      // Append new files to existing ones
      attachedFiles.value = [...attachedFiles.value, ...uploadedFiles];
      toast.success(`Successfully uploaded ${uploadedFiles.length} file(s)`);
    }
  } catch (error) {
    console.error('Error uploading files:', error);
    toast.error('Failed to upload files');
  }
};

// Update file removal handler
function handleFileRemoval(fileTitle: string) {
  const fileToRemove = attachedFiles.value.find(
    (file) => file.title === fileTitle
  );
  if (!fileToRemove) return;

  attachedFiles.value = attachedFiles.value.filter(
    (file) => file.title !== fileTitle
  );
  toast.success('File removed successfully');
}

function onSubmit(values: any) {
  // Check if the content is empty
  if (values.content.trim() === '') {
    toast.error('Content is required');
    return;
  }
  if (props.type === 'update') {
    if (values.content.trim().length < 50) {
      toast.error('Content must be at least 50 characters');
      return;
    }
    if (values.content.trim().length > 150) {
      toast.error('Content must be less than 150 characters');
      return;
    }
  }
  if (values.title.trim() === '') {
    toast.error('Title is required');
    return;
  }
  const transformedValues = {
    ...values,
    title: values.title || (isUpdateType.value ? 'View' : ''),
    // Ensure dates are Date objects or null
    scheduledAt: values.scheduledAt ? new Date(values.scheduledAt) : null,
    expiresAt: values.expiresAt ? new Date(values.expiresAt) : null,
    eventDate: values.eventDate ? new Date(values.eventDate) : null,
    files: [
      ...(primaryFile.value ? [primaryFile.value] : []),
      ...attachedFiles.value,
    ],
    button: showButton.value ? values.button : null,
  };

  if (!showButton.value) {
    delete transformedValues.button;
  }
  emit('submit', transformedValues);
}
</script>
<template>
  <Form
    ref="formRef"
    v-slot="{ values, setFieldValue }"
    :validation-schema="formSchema"
    :initial-values="initialValues"
    :announcement="announcement"
    :is-submitting="isSubmitting"
    :type="type"
    @submit="onSubmit"
  >
    <div class="space-y-6">
      <!-- Basic Information -->
      <div class="space-y-4">
        <div v-if="isUpdateType" class="space-y-4">
          <FormField v-slot="{ field, errorMessage }" name="content">
            <FormItem>
              <FormLabel
                >Content<span class="text-destructive">*</span></FormLabel
              >
              <FormControl>
                <AdminBaseTextEditor
                  :model-value="field.value"
                  placeholder="Enter announcement content"
                  @update:model-value="setFieldValue('content', $event)"
                />
              </FormControl>
              <FormDescription>
                The main content of your
                {{
                  type === 'notice'
                    ? 'notice'
                    : type === 'update'
                      ? 'update'
                      : 'event'
                }}. Supports rich text formatting.
              </FormDescription>
              <FormMessage>{{ errorMessage }}</FormMessage>
            </FormItem>
          </FormField>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField v-slot="{ field, errorMessage }" name="title">
              <FormItem class="w-full">
                <FormLabel>Button Title</FormLabel>
                <FormControl>
                  <Input
                    :model-value="field.value"
                    v-bind="field"
                    :placeholder="`Enter ${type === 'notice' ? 'notice' : type === 'update' ? 'button' : 'event'} title`"
                  />
                </FormControl>
                <FormDescription>
                  The title of your
                  {{
                    type === 'notice'
                      ? 'notice'
                      : type === 'update'
                        ? 'button'
                        : 'event'
                  }}
                  that will be displayed to users
                </FormDescription>
                <FormMessage>{{ errorMessage }}</FormMessage>
              </FormItem>
            </FormField>

            <!-- Link field for update type -->
            <FormField
              v-if="isUpdateType"
              v-slot="{ field, errorMessage }"
              name="link"
            >
              <FormItem class="w-full">
                <FormLabel>Button Link</FormLabel>
                <FormControl>
                  <Input
                    :model-value="field.value"
                    v-bind="field"
                    placeholder="Enter button link"
                  />
                </FormControl>
                <FormDescription> The link for the button </FormDescription>
                <FormMessage>{{ errorMessage }}</FormMessage>
              </FormItem>
            </FormField>
          </div>
        </div>

        <FormField
          v-if="!isUpdateType"
          v-slot="{ field, errorMessage }"
          name="title"
        >
          <FormItem>
            <FormLabel>Title <span class="text-destructive">*</span></FormLabel>
            <FormControl>
              <Input
                :model-value="field.value"
                v-bind="field"
                placeholder="Enter announcement title"
              />
            </FormControl>
            <FormDescription>
              The title of your
              {{
                type === 'notice'
                  ? 'notice'
                  : type === 'update'
                    ? 'update'
                    : 'event'
              }}
              that will be displayed to users
            </FormDescription>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <FormField
          v-if="!isUpdateType"
          v-slot="{ field, errorMessage }"
          name="content"
        >
          <FormItem>
            <FormLabel
              >Content<span class="text-destructive">*</span></FormLabel
            >
            <FormControl>
              <AdminBaseTextEditor
                :model-value="field.value"
                placeholder="Enter announcement content"
                @update:model-value="setFieldValue('content', $event)"
              />
            </FormControl>
            <FormDescription>
              The main content of your
              {{
                type === 'notice'
                  ? 'notice'
                  : type === 'update'
                    ? 'update'
                    : 'event'
              }}. Supports rich text formatting.
            </FormDescription>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <div v-if="!isUpdateType" class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField v-slot="{ field }" name="priority">
            <FormItem>
              <FormLabel>Priority</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  :model-value="field.value"
                  v-bind="field"
                  min="0"
                  :value="field.value"
                  placeholder="Enter priority number"
                />
              </FormControl>
              <FormDescription>
                Higher priority
                {{
                  type === 'notice'
                    ? 'notice'
                    : type === 'update'
                      ? 'update'
                      : 'event'
                }}
                appear first
              </FormDescription>
            </FormItem>
          </FormField>

          <div class="space-y-2">
            <Label>Event Date</Label>
            <AdminBaseDatePicker
              :date-value="values.eventDate"
              @update:date-value="setFieldValue('eventDate', $event)"
            />
            <p class="text-xs text-muted-foreground">
              When is this
              {{
                type === 'notice'
                  ? 'notice'
                  : type === 'update'
                    ? 'update'
                    : 'event'
              }}
              event?
            </p>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label>Schedule Date<span class="text-destructive">*</span></Label>
            <AdminBaseDatePicker
              :date-value="values.scheduledAt"
              @update:date-value="setFieldValue('scheduledAt', $event)"
            />
            <p class="text-xs text-muted-foreground">
              When should this
              {{
                type === 'notice'
                  ? 'notice'
                  : type === 'update'
                    ? 'update'
                    : 'event'
              }}
              be published?
            </p>
          </div>
          <div class="space-y-2">
            <Label>Expiry Date<span class="text-destructive">*</span></Label>
            <AdminBaseDatePicker
              :date-value="values.expiresAt"
              @update:date-value="setFieldValue('expiresAt', $event)"
            />
            <p class="text-xs text-muted-foreground">
              When should this
              {{
                type === 'notice'
                  ? 'notice'
                  : type === 'update'
                    ? 'update'
                    : 'event'
              }}
              expire?
            </p>
          </div>
        </div>

        <FormField v-if="!isUpdateType" v-slot="{ field }" name="isActive">
          <FormItem
            class="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"
          >
            <div class="space-y-0.5">
              <FormLabel>Active Status</FormLabel>
              <FormDescription>
                Enable or disable this
                {{
                  type === 'notice'
                    ? 'notice'
                    : type === 'update'
                      ? 'update'
                      : 'event'
                }}
              </FormDescription>
            </div>
            <FormControl>
              <Switch :checked="field.value" @update:checked="field.onChange" />
            </FormControl>
          </FormItem>
        </FormField>
        <AdminBaseDropzone
          v-if="!isUpdateType"
          class="pt-2"
          :is-multiple-allowed="false"
          :file-types="['image']"
          title="Attach Primary Image"
          default-title="Primary Image"
          :existing-files="primaryFile ? [primaryFile] : []"
          :description="`Attach primary image to your ${type === 'notice' ? 'notice' : type === 'update' ? 'update' : 'event'}. Only one image is allowed.`"
          @files-selected="
            (files) => uploadPrimaryImage(files ? files[0]?.file : undefined)
          "
          @file-removed="() => (primaryFile = null)"
        />
        <AdminBaseDropzone
          v-if="!isUpdateType"
          class="pt-2"
          :is-multiple-allowed="true"
          :file-types="['image', 'pdf']"
          title="Attach Files"
          :enable-titles="true"
          :existing-files="attachedFiles"
          :description="`Attach image or PDF files to your ${type === 'notice' ? 'notice' : type === 'update' ? 'update' : 'event'}. Accepted types: ${['image', 'pdf']?.join(', ')}`"
          @files-selected="uploadFiles"
          @file-removed="handleFileRemoval"
        />
      </div>

      <!-- Button Configuration -->
      <AdminTemplateButton
        v-if="!isUpdateType"
        :disable-style="true"
        :show-button="showButton"
        :model-value="values.button"
        @update:model-value="setFieldValue('button', $event)"
        @update:show-button="showButton = $event"
      />
    </div>
    <div class="flex justify-end gap-2 mt-6">
      <Button
        type="button"
        variant="ghost"
        :disabled="isSubmitting"
        @click="emit('cancel')"
      >
        Cancel
      </Button>
      <Button type="submit" :disabled="isSubmitting">
        <Loader2 v-if="isSubmitting" class="mr-2 h-4 w-4 animate-spin" />
        {{ announcement?.id ? 'Update' : 'Create' }}
        {{
          type === 'notice' ? 'Notice' : type === 'update' ? 'Update' : 'Event'
        }}
      </Button>
    </div>
  </Form>
</template>
