<script setup lang="ts">
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import TeamMemberForm from './CompositionForm.vue';
import TitleForm from './TitleForm.vue';
import type {
  IQAC_TEAM_MEMBER_RESPONSE,
  IQACTeamTitleResponse,
} from '~~/app/types/admin/iqac';
import { toast } from 'vue-sonner';

const {
  // Team Members
  teamMembers,
  isLoading,
  fetchTeamMembers,
  createTeamMember,
  updateTeamMember,
  deleteTeamMember,
  showDeleteDialog,
  deleteState,

  // Team Titles
  teamTitles,
  titlesLoading,
  fetchTeamTitles,
  createTeamTitle,
  updateTeamTitle,
  deleteTeamTitle,
  showTitleDeleteDialog,
  titleDeleteState,
} = useIqac();

// Tab management
const activeTab = ref('members');

// Team member columns
const teamMemberColumns = [
  { key: 'title', label: 'Title' },
  { key: 'name', label: 'Name' },
  { key: 'designation', label: 'Designation' },
  { key: 'priority', label: 'Priority' },
  { key: 'actions', label: 'Actions' },
];

// Title columns
const titleColumns = [
  { key: 'title', label: 'Title' },
  { key: 'priority', label: 'Priority' },
  { key: 'count', label: 'Team Members Count' },
  { key: 'actions', label: 'Actions' },
];

// Dialog states
const isTeamMemberDialogOpen = ref(false);
const isTitleDialogOpen = ref(false);
const selectedTeamMember = ref<IQAC_TEAM_MEMBER_RESPONSE | null>(null);
const selectedTeamTitle = ref<IQACTeamTitleResponse | null>(null);
const isSubmitting = ref(false);

// Get count of team members for each title
const titleMemberCounts = computed(() => {
  const countMap = new Map<string, number>();

  if (teamMembers.value.length > 0) {
    teamMembers.value.forEach((member) => {
      if (!countMap.has(member.title)) {
        countMap.set(member.title, 1);
      } else {
        countMap.set(member.title, countMap.get(member.title)! + 1);
      }
    });
  }

  return countMap;
});

// Load team members and titles when component mounts
onMounted(async () => {
  await Promise.all([fetchTeamMembers(), fetchTeamTitles()]);
});

// Team member functions
const handleEditTeamMember = (member: IQAC_TEAM_MEMBER_RESPONSE) => {
  selectedTeamMember.value = member;
  isTeamMemberDialogOpen.value = true;
};

const handleDeleteTeamMember = async (member: IQAC_TEAM_MEMBER_RESPONSE) => {
  selectedTeamMember.value = member;
  showDeleteDialog.value = true;
};

const confirmDeleteTeamMember = async () => {
  if (!selectedTeamMember.value) return;

  try {
    await deleteTeamMember(selectedTeamMember.value.id);
  } catch (error) {
    console.error('Error deleting team member:', error);
    toast.error('Failed to delete team member');
  }
};

const handleAddTeamMember = () => {
  selectedTeamMember.value = null;
  isTeamMemberDialogOpen.value = true;
};

const handleTeamMemberFormSubmit = async (values: any) => {
  try {
    isSubmitting.value = true;
    if (selectedTeamMember.value) {
      await updateTeamMember(selectedTeamMember.value.id, values);
    } else {
      await createTeamMember(values);
    }
    isTeamMemberDialogOpen.value = false;
  } catch (error) {
    console.error('Error saving team member:', error);
    toast.error('Failed to save team member');
  } finally {
    isSubmitting.value = false;
  }
};

const handleTeamMemberFormCancel = () => {
  isTeamMemberDialogOpen.value = false;
};

// Title functions
const handleEditTitle = (title: IQACTeamTitleResponse) => {
  selectedTeamTitle.value = title;
  isTitleDialogOpen.value = true;
};

const handleDeleteTitle = (title: IQACTeamTitleResponse) => {
  selectedTeamTitle.value = title;
  showTitleDeleteDialog.value = true;
};

const confirmDeleteTitle = async () => {
  if (!selectedTeamTitle.value) return;

  try {
    await deleteTeamTitle(selectedTeamTitle.value.id);
  } catch (error) {
    console.error('Error deleting team title:', error);
    toast.error('Failed to delete team title');
  }
};

const handleAddTitle = () => {
  selectedTeamTitle.value = null;
  isTitleDialogOpen.value = true;
};

const handleTitleFormSubmit = async (values: any) => {
  try {
    isSubmitting.value = true;

    if (selectedTeamTitle.value) {
      await updateTeamTitle(selectedTeamTitle.value.id, {
        title: values.title,
        priority: values.priority,
      });
    } else {
      await createTeamTitle({
        title: values.title,
        priority: values.priority,
      });
    }

    isTitleDialogOpen.value = false;
    await Promise.all([fetchTeamTitles(), fetchTeamMembers()]);
  } catch (error) {
    console.error('Error saving title:', error);
    toast.error('Failed to save title');
  } finally {
    isSubmitting.value = false;
  }
};

const handleTitleFormCancel = () => {
  isTitleDialogOpen.value = false;
};
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader
      class="px-2 pt-0 pb-4 flex flex-row items-center justify-between"
    >
      <CardTitle>IQAC Team Management</CardTitle>
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <Tabs v-model="activeTab" class="w-full">
        <TabsList class="w-full mb-4">
          <TabsTrigger value="members" class="flex-1">Team Members</TabsTrigger>
          <TabsTrigger value="attachments" class="flex-1"
            >Attachments</TabsTrigger
          >
          <TabsTrigger value="titles" class="flex-1">Titles</TabsTrigger>
        </TabsList>

        <!-- Titles Tab -->
        <TabsContent value="titles">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium">Title Management</h3>
            <Button
              type="button"
              variant="default"
              size="sm"
              :disabled="isSubmitting"
              @click="handleAddTitle"
            >
              Add Title
            </Button>
          </div>

          <div v-if="titlesLoading" class="flex justify-center py-8">
            Loading...
          </div>
          <Table v-else>
            <TableHeader>
              <TableRow>
                <TableHead v-for="column in titleColumns" :key="column.key">
                  {{ column.label }}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="titlesLoading">
                <TableCell colspan="4" class="text-center py-4">
                  Loading title data...
                </TableCell>
              </TableRow>
              <TableRow v-else-if="!teamTitles.length">
                <TableCell colspan="4" class="text-center py-4">
                  No titles found.
                </TableCell>
              </TableRow>
              <TableRow v-for="title in teamTitles" :key="title.id">
                <TableCell>{{ title.title }}</TableCell>
                <TableCell>{{ title.priority }}</TableCell>
                <TableCell>{{
                  titleMemberCounts.get(title.title) || 0
                }}</TableCell>
                <TableCell>
                  <div class="flex space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      :disabled="isSubmitting"
                      aria-label="Edit title"
                      @click="handleEditTitle(title)"
                    >
                      {{ isSubmitting ? 'Editing...' : 'Edit' }}
                    </Button>
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      :disabled="isSubmitting"
                      aria-label="Delete title"
                      @click="handleDeleteTitle(title)"
                    >
                      {{ isSubmitting ? 'Deleting...' : 'Delete' }}
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TabsContent>

        <!-- Team Members Tab -->
        <TabsContent value="members">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium">Team Members Management</h3>
            <Button
              type="button"
              variant="default"
              size="sm"
              :disabled="isSubmitting"
              @click="handleAddTeamMember"
            >
              Add Team Member
            </Button>
          </div>

          <div v-if="isLoading" class="flex justify-center py-8">
            Loading...
          </div>
          <Table v-else>
            <TableHeader>
              <TableRow>
                <TableHead
                  v-for="column in teamMemberColumns"
                  :key="column.key"
                >
                  {{ column.label }}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="isLoading">
                <TableCell colspan="6" class="text-center py-4">
                  Loading team members data...
                </TableCell>
              </TableRow>
              <TableRow v-else-if="!teamMembers.length">
                <TableCell colspan="6" class="text-center py-4">
                  No team members found.
                </TableCell>
              </TableRow>
              <TableRow v-for="member in teamMembers" :key="member.id">
                <TableCell>{{ member.title }}</TableCell>
                <TableCell>{{ member.name }}</TableCell>
                <TableCell>{{ member.designation }}</TableCell>
                <TableCell>{{ member.priority }}</TableCell>
                <TableCell>
                  <div class="flex space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      :disabled="isSubmitting"
                      aria-label="Edit team member"
                      @click="handleEditTeamMember(member)"
                    >
                      {{ isSubmitting ? 'Editing...' : 'Edit' }}
                    </Button>
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      :disabled="isSubmitting"
                      aria-label="Delete team member"
                      @click="handleDeleteTeamMember(member)"
                    >
                      {{ isSubmitting ? 'Deleting...' : 'Delete' }}
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TabsContent>

        <!-- Attachments Tab -->
        <TabsContent value="attachments">
          <AdminIqacDownload param="team_members_attachments" />
        </TabsContent>
      </Tabs>
    </CardContent>
  </Card>

  <!-- Team Member Dialog -->
  <Dialog
    :open="isTeamMemberDialogOpen"
    @update:open="isTeamMemberDialogOpen = $event"
  >
    <DialogContent class="sm:max-w-3xl">
      <DialogHeader>
        <DialogTitle>
          {{ selectedTeamMember ? 'Edit Team Member' : 'Add New Team Member' }}
        </DialogTitle>
      </DialogHeader>
      <TeamMemberForm
        :initial-values="selectedTeamMember"
        :is-submitting="isSubmitting"
        :team-titles="teamTitles"
        @submit="handleTeamMemberFormSubmit"
        @cancel="handleTeamMemberFormCancel"
      />
    </DialogContent>
  </Dialog>

  <!-- Title Dialog -->
  <Dialog :open="isTitleDialogOpen" @update:open="isTitleDialogOpen = $event">
    <DialogContent class="sm:max-w-3xl">
      <DialogHeader>
        <DialogTitle>
          {{ selectedTeamTitle ? 'Edit Title' : 'Add New Title' }}
        </DialogTitle>
      </DialogHeader>
      <TitleForm
        :initial-title="selectedTeamTitle?.title"
        :initial-priority="selectedTeamTitle?.priority"
        :is-submitting="isSubmitting"
        @submit="handleTitleFormSubmit"
        @cancel="handleTitleFormCancel"
      />
    </DialogContent>
  </Dialog>

  <!-- Delete Team Member Confirmation Dialog -->
  <AdminMenuDeleteDialog
    v-if="selectedTeamMember"
    v-model:is-open="showDeleteDialog"
    :is-submitting="deleteState === 'loading'"
    :delete-state="deleteState"
    menu-title="team member"
    type="menu"
    :description="`Are you sure you want to delete team member '${selectedTeamMember.name}'? This action cannot be undone.`"
    @confirm="confirmDeleteTeamMember"
  />

  <!-- Delete Team Title Confirmation Dialog -->
  <AdminMenuDeleteDialog
    v-if="selectedTeamTitle"
    v-model:is-open="showTitleDeleteDialog"
    :is-submitting="titleDeleteState === 'loading'"
    :delete-state="titleDeleteState"
    menu-title="team title"
    type="menu"
    :description="`Are you sure you want to delete team title '${selectedTeamTitle.title}'? This action cannot be undone.`"
    @confirm="confirmDeleteTitle"
  />
</template>
