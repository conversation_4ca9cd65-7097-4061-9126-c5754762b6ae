<script setup lang="ts">
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import type {
  IQAC_TEAM_MEMBER_RESPONSE,
  IQACTeamTitleResponse,
} from '~~/app/types/admin/iqac';
import type { TeamMember } from '~~/shared/schema/iqac/team-members/create';
import type { UpdateTeamMember } from '~~/shared/schema/iqac/team-members/update';
import { teamMemberSchema } from '~~/shared/schema/iqac/team-members/create';
import { updateTeamMemberSchema } from '~~/shared/schema/iqac/team-members/update';

const props = defineProps<{
  initialValues?: IQAC_TEAM_MEMBER_RESPONSE | null;
  isSubmitting?: boolean;
  teamTitles: IQACTeamTitleResponse[];
}>();

const emit = defineEmits<{
  submit: [values: TeamMember | UpdateTeamMember];
  cancel: [];
}>();

// Use appropriate schema based on whether we're editing or creating
const validationSchema = toTypedSchema(
  props.initialValues ? updateTeamMemberSchema : teamMemberSchema
);

const initialValues = computed(() => {
  if (props.initialValues) {
    return {
      id: props.initialValues.id,
      title: props.initialValues.title,
      titleOrder: props.initialValues.titleOrder || 0,
      name: props.initialValues.name,
      designation: props.initialValues.designation,
      priority: props.initialValues.priority || 0,
    };
  }

  return {
    title: '',
    titleOrder: 0,
    name: '',
    designation: '',
    priority: 0,
  };
});

const { values, errors, setFieldValue, resetForm } = useForm<
  TeamMember | UpdateTeamMember
>({
  validationSchema,
  initialValues: initialValues.value,
});

const handleSubmit = () => {
  emit('submit', values);
};

const handleCancel = () => {
  resetForm();
  emit('cancel');
};

type NumericFields = 'titleOrder' | 'priority';

const handleNumericInput = (
  value: string | number | null,
  field: NumericFields
) => {
  const numValue = value === null || value === '' ? 0 : Number(value);
  setFieldValue(field, numValue);
};
</script>

<template>
  <Form
    :validation-schema="validationSchema"
    :initial-values="initialValues"
    class="space-y-6"
  >
    <div class="grid gap-6">
      <div class="grid grid-cols-2 gap-4">
        <FormField v-slot="{ field, errorMessage }" name="title">
          <FormItem>
            <FormLabel>Title</FormLabel>
            <Select
              v-bind="field"
              :model-value="field.value"
              :disabled="props.isSubmitting"
              @update:model-value="
                (value: any) => {
                  setFieldValue('title', value as string);
                  setFieldValue(
                    'titleOrder',
                    props.teamTitles.find((title) => title.title === value)
                      ?.priority || 0
                  );
                }
              "
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select course type" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem
                  v-for="item in teamTitles"
                  :key="item.id"
                  :value="item.title"
                >
                  {{ item.title }}
                </SelectItem>
              </SelectContent>
            </Select>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
        <FormField v-slot="{ field, errorMessage }" name="name">
          <FormItem>
            <FormLabel>Name</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="field.value"
                placeholder="Enter name"
                :disabled="props.isSubmitting"
                @update:model-value="
                  (value: any) => setFieldValue('name', value as string)
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
      </div>

      <div class="grid grid-cols-2 gap-4">
        <FormField v-slot="{ field, errorMessage }" name="designation">
          <FormItem>
            <FormLabel>Designation</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="field.value"
                placeholder="Enter designation"
                :disabled="props.isSubmitting"
                @update:model-value="
                  (value: any) => setFieldValue('designation', value as string)
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
        <FormField v-slot="{ field, errorMessage }" name="priority">
          <FormItem>
            <FormLabel>Priority</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="String(field.value || '')"
                type="number"
                min="0"
                placeholder="Enter priority"
                :disabled="props.isSubmitting"
                @update:model-value="
                  (value: any) => handleNumericInput(value, 'priority')
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
      </div>

      <div class="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          :disabled="props.isSubmitting"
          @click="handleCancel"
        >
          Cancel
        </Button>
        <Button
          :disabled="props.isSubmitting || Object.keys(errors).length > 0"
          @click="handleSubmit"
        >
          {{
            props.isSubmitting
              ? 'Saving...'
              : props.initialValues
                ? 'Update'
                : 'Create'
          }}
        </Button>
      </div>
    </div>
  </Form>
</template>
