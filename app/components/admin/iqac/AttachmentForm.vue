<script setup lang="ts">
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toTypedSchema } from '@vee-validate/zod';
import type { CreateAttachment } from '~~/shared/schema/department/files/create';
import type { UpdateAttachment } from '~~/shared/schema/department/files/update';
import { createAttachmentSchema } from '~~/shared/schema/department/files/create';
import { updateAttachmentSchema } from '~~/shared/schema/department/files/update';
import { toast } from 'vue-sonner';
import type { IQACAttachmentData, LinkButtonConfig } from '~/types/admin';
import type { FileData } from '~/types/home';

const props = defineProps<{
  attachmentData?: IQACAttachmentData | null;
  isSubmitting?: boolean;
}>();

const emit = defineEmits<{
  submit: [values: CreateAttachment | UpdateAttachment];
  cancel: [];
}>();

// Use appropriate schema based on whether we're editing or creating
const validationSchema = toTypedSchema(
  props.attachmentData ? updateAttachmentSchema : createAttachmentSchema
);

const attachment = ref<{
  title: string;
  files: {
    pathname: string;
    title: string;
    type: string;
    prefix: string;
  }[];
  linkButton: LinkButtonConfig | null;
  showLinkButton: boolean;
}>({
  title: props.attachmentData?.title ?? '',
  files:
    props.attachmentData?.files.map((file) => ({
      pathname: file.pathname,
      title: file.title,
      type: file.type,
      prefix: file.prefix,
    })) ?? [],
  linkButton: props.attachmentData?.linkButton ?? null,
  showLinkButton: !!props.attachmentData?.linkButton,
});

const handleCancel = () => {
  attachment.value = {
    title: '',
    files: [],
    linkButton: null,
    showLinkButton: false,
  };
  emit('cancel');
};

const handleSubmit = () => {
  // Validate attachment has title and files
  if (!attachment.value.title) {
    toast.error('Attachment must have a title');
    return;
  }

  if (attachment.value.files.length === 0) {
    toast.error('Attachment must have at least one file');
    return;
  }

  const transformedValue = {
    type: 'attachment' as const,
    title: attachment.value.title,
    files: attachment.value.files,
    linkButton: attachment.value.linkButton,
  };

  emit('submit', transformedValue);
};

const uploadFiles = async (
  files: { file: File; title?: string }[],
  isTitleEdit: boolean = false
) => {
  if (!files.length) return;
  const formData = new FormData();

  // Add each file to FormData
  files.forEach(({ file, title }) => {
    formData.append('files', file);
    formData.append('prefix', 'announcement');
    formData.append('title', title || file.name);
    formData.append('type', file.type);
  });

  try {
    const response = await $fetch<FileData[]>('/api/blob/upload', {
      method: 'POST',
      body: formData,
    });

    if (response && Array.isArray(response)) {
      const uploadedFiles = response.map((file) => ({
        pathname: file.pathname,
        title: file.title,
        type: file.type,
        prefix: 'announcement',
      }));

      // If this is a title edit, remove the existing file with the same title
      if (isTitleEdit) {
        attachment.value.files = attachment.value.files.filter(
          (existingFile) =>
            !uploadedFiles.some(
              (newFile) =>
                newFile.pathname.split('-')[0] ===
                existingFile.pathname?.split('-')[0]
            )
        );
      }

      // Append new files to existing ones
      attachment.value.files = [...attachment.value.files, ...uploadedFiles];
      toast.success(`Successfully uploaded ${uploadedFiles.length} file(s)`);
    }
  } catch (error) {
    console.error('Error uploading files:', error);
    toast.error('Failed to upload files');
  }
};

const handleFileRemoval = (fileTitle: string) => {
  attachment.value.files = attachment.value.files.filter(
    (file) => file.title !== fileTitle
  );
  toast.success('File removed successfully');
};
</script>

<template>
  <Form
    :validation-schema="validationSchema"
    :initial-values="attachment"
    class="space-y-6"
  >
    <div class="grid gap-6">
      <FormField v-slot="{ errorMessage }" name="title">
        <FormItem>
          <FormLabel>Title</FormLabel>
          <FormControl>
            <Input
              v-model="attachment.title"
              placeholder="Enter attachment title"
              :disabled="isSubmitting"
            />
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- File Upload -->
      <AdminBaseDropzone
        :is-multiple-allowed="true"
        :file-types="['image', 'text', 'pdf']"
        title="Attachment Files"
        :existing-files="attachment?.files"
        description="Upload the attachment files in PDF, Image or Text format"
        :is-submitting="isSubmitting"
        @files-selected="(files: any) => uploadFiles(files)"
        @file-removed="(title: any) => handleFileRemoval(title)"
      />

      <!-- Link Button -->
      <div class="space-y-4">
        <h3 class="text-sm font-medium">Link Button</h3>
        <AdminTemplateButton
          v-model="attachment.linkButton"
          :disabled="isSubmitting"
          :show-button="attachment.linkButton"
          @update:show-button="attachment.showLinkButton = $event"
          @update:model-value="(value: any) => (attachment.linkButton = value)"
        />
      </div>
    </div>

    <div class="flex justify-end space-x-4 mt-4">
      <Button
        type="button"
        variant="outline"
        :disabled="isSubmitting"
        @click="handleCancel"
      >
        Cancel
      </Button>
      <Button
        :disabled="
          isSubmitting || !attachment.title || attachment.files.length === 0
        "
        @click="handleSubmit"
      >
        {{ isSubmitting ? 'Saving...' : attachmentData ? 'Update' : 'Create' }}
      </Button>
    </div>
  </Form>
</template>
