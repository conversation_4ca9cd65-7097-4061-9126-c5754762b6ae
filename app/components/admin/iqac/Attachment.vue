<script setup lang="ts">
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from 'vue-sonner';
import type { PaginationResponse } from '~~/shared/schema/pagination';
import type {
  IQACAttachmentsResponse,
  IQACAttachmentData,
} from '~/types/admin';

const props = defineProps<{ param: string }>();

const columns = [
  { key: 'title', label: 'Title' },
  { key: 'actions', label: 'Actions' },
];

const isDialogOpen = ref(false);
const selectedAttachment = ref<IQACAttachmentData | null>(null);
const isSubmitting = ref(false);
const currentPage = ref(1);
const attachmentPagination = ref<PaginationResponse>({
  totalPages: 0,
  page: 0,
  limit: 0,
  totalItems: 0,
  hasPrevPage: false,
  hasNextPage: false,
});

// Add delete dialog state
const showDeleteDialog = ref(false);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

const {
  data: attachmentResponse,
  error: attachmentError,
  status: attachmentListStatus,
  execute: fetchAttachmentList,
} = useFetch<IQACAttachmentsResponse>(
  `/api/admin/iqac/attachment/${props.param}`
);

const attachmentList = ref<IQACAttachmentData[]>([]);

watch(attachmentResponse, (newAttachmentResponse) => {
  if (!attachmentError.value && newAttachmentResponse) {
    attachmentList.value = newAttachmentResponse?.attachments || [];
    attachmentPagination.value = newAttachmentResponse?.pagination;
  }
});

const handleEdit = (attachment: IQACAttachmentData) => {
  selectedAttachment.value = attachment;
  isDialogOpen.value = true;
};

const handleDelete = async (attachment: IQACAttachmentData) => {
  selectedAttachment.value = attachment;
  showDeleteDialog.value = true;
};

const confirmDelete = async () => {
  if (!selectedAttachment.value) return;

  deleteState.value = 'loading';
  try {
    await $fetch(
      `/api/admin/iqac/attachment/${props.param}/${selectedAttachment.value.id}`,
      {
        method: 'DELETE',
      }
    );
    deleteState.value = 'success';

    // Close dialog and refresh after a short delay
    setTimeout(async () => {
      showDeleteDialog.value = false;
      deleteState.value = 'idle';
      selectedAttachment.value = null;
      await fetchAttachmentList();
    }, 1000);
  } catch (error) {
    console.error('Error deleting attachment:', error);
    deleteState.value = 'error';
    toast.error('Failed to delete attachment');
  }
};

const handleAdd = () => {
  selectedAttachment.value = null;
  isDialogOpen.value = true;
};

const handleFormSubmit = async (values: any) => {
  try {
    isSubmitting.value = true;
    if (selectedAttachment.value) {
      // For edit: Update the selected attachment
      await $fetch(
        `/api/admin/iqac/attachment/${props.param}/${selectedAttachment.value.id}`,
        {
          method: 'PUT',
          body: values,
        }
      );
      toast.success('Attachment updated successfully');
    } else {
      // For create: Add a new attachment
      await $fetch(`/api/admin/iqac/attachment/${props.param}`, {
        method: 'POST',
        body: values,
      });
      toast.success('Attachment created successfully');
    }

    isDialogOpen.value = false;
    await fetchAttachmentList();
  } catch (error) {
    console.error('Error saving attachment:', error);
    toast.error('Failed to save attachment');
  } finally {
    isSubmitting.value = false;
  }
};

const handleFormCancel = () => {
  isDialogOpen.value = false;
};

const handleAttachmentDownload = (attachment: IQACAttachmentData) => {
  if (attachment.files[0]?.pathname) {
    window.open(`/api/files/${attachment.files[0].pathname}`, '_blank');
  }
};

const handlePageChange = async (page: number) => {
  currentPage.value = page;
  await fetchAttachmentList();
};
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader
      class="px-2 pt-0 pb-4 flex flex-row items-center justify-between"
    >
      <CardTitle>Attachments</CardTitle>
      <Button
        type="button"
        variant="default"
        size="sm"
        :disabled="isSubmitting"
        @click="handleAdd"
      >
        Add Attachment
      </Button>
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead v-for="column in columns" :key="column.key">
              {{ column.label }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-if="attachmentListStatus === 'pending'">
            <TableCell colspan="4" class="text-center py-4">
              Loading attachments data...
            </TableCell>
          </TableRow>
          <TableRow v-else-if="!attachmentList.length">
            <TableCell colspan="4" class="text-center py-4">
              No attachments found.
            </TableCell>
          </TableRow>
          <TableRow v-for="attachment in attachmentList" :key="attachment.id">
            <TableCell>{{ attachment.title }}</TableCell>
            <TableCell>
              <div class="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Attachment Download"
                  @click="handleAttachmentDownload(attachment)"
                >
                  Download
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Edit attachment"
                  @click="handleEdit(attachment)"
                >
                  {{ isSubmitting ? 'Editing...' : 'Edit' }}
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Delete attachment"
                  @click="handleDelete(attachment)"
                >
                  {{ isSubmitting ? 'Deleting...' : 'Delete' }}
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <!-- Pagination -->
      <div
        v-if="attachmentPagination.totalPages > 1"
        class="mt-4 flex justify-center"
      >
        <div class="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            :disabled="!attachmentPagination.hasPrevPage"
            @click="handlePageChange(currentPage - 1)"
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            :disabled="!attachmentPagination.hasNextPage"
            @click="handlePageChange(currentPage + 1)"
          >
            Next
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>

  <Dialog :open="isDialogOpen" @update:open="isDialogOpen = $event">
    <DialogContent class="sm:max-w-3xl">
      <DialogHeader>
        <DialogTitle>
          {{ selectedAttachment ? 'Edit Attachment' : 'Add New Attachment' }}
        </DialogTitle>
      </DialogHeader>
      <AdminIqacAttachmentForm
        :attachment-data="selectedAttachment ?? undefined"
        :is-submitting="isSubmitting"
        @submit="handleFormSubmit"
        @cancel="handleFormCancel"
      />
    </DialogContent>
  </Dialog>

  <!-- Delete Confirmation Dialog -->
  <AdminMenuDeleteDialog
    v-if="selectedAttachment"
    v-model:is-open="showDeleteDialog"
    :is-submitting="deleteState === 'loading'"
    :delete-state="deleteState"
    menu-title="attachment"
    type="menu"
    :description="`Are you sure you want to delete attachment '${selectedAttachment.title}'? This action cannot be undone.`"
    @confirm="confirmDelete"
  />
</template>
