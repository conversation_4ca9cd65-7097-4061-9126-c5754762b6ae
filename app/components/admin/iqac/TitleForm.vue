<script setup lang="ts">
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import { z } from 'zod';

// Title schema
const titleSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  priority: z.number(),
});

type TitleFormData = z.infer<typeof titleSchema>;

const props = defineProps<{
  initialTitle?: string | null;
  initialPriority?: number | null;
  isSubmitting?: boolean;
}>();

const emit = defineEmits<{
  submit: [values: TitleFormData];
  cancel: [];
}>();

// Validation schema
const validationSchema = toTypedSchema(titleSchema);

// Initial values
const initialValues = computed<TitleFormData>(() => {
  return {
    title: props.initialTitle || '',
    priority: props.initialPriority || 0,
  };
});

const { values, errors, setFieldValue, resetForm } = useForm<TitleFormData>({
  validationSchema,
  initialValues: initialValues.value,
});

const handleSubmit = () => {
  emit('submit', values);
};

const handleCancel = () => {
  resetForm();
  emit('cancel');
};

const handleNumericInput = (value: string | number | null) => {
  const numValue = value === null || value === '' ? 0 : Number(value);
  setFieldValue('priority', numValue);
};
</script>

<template>
  <Form
    :validation-schema="validationSchema"
    :initial-values="initialValues"
    class="space-y-6"
  >
    <div class="grid gap-6">
      <div class="grid grid-cols-2 gap-4">
        <FormField v-slot="{ field, errorMessage }" name="title">
          <FormItem>
            <FormLabel>Title</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="field.value"
                placeholder="Enter title"
                :disabled="props.isSubmitting"
                @update:model-value="
                  (value: any) => setFieldValue('title', value as string)
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
        <FormField v-slot="{ field, errorMessage }" name="priority">
          <FormItem>
            <FormLabel>Priority</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="String(field.value || '')"
                type="number"
                min="0"
                placeholder="Enter priority"
                :disabled="props.isSubmitting"
                @update:model-value="(value: any) => handleNumericInput(value)"
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
      </div>

      <div class="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          :disabled="props.isSubmitting"
          @click="handleCancel"
        >
          Cancel
        </Button>
        <Button
          :disabled="props.isSubmitting || Object.keys(errors).length > 0"
          @click="handleSubmit"
        >
          {{
            props.isSubmitting
              ? 'Saving...'
              : props.initialTitle
                ? 'Update'
                : 'Create'
          }}
        </Button>
      </div>
    </div>
  </Form>
</template>
