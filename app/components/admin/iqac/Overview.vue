<script setup lang="ts">
import { Loader2 } from 'lucide-vue-next';
import { toast } from 'vue-sonner';
import type { FileData } from '~/types/home';
import type { IQACOverviewResponse } from '~/types/admin/iqac';

interface Props {
  param?: string;
}

const props = withDefaults(defineProps<Props>(), {
  param: '',
  isSubmitting: false,
});

const form = reactive<IQACOverviewResponse>({
  title: '',
  image: null,
  content: '',
  linkButton: null,
  files: [],
});

const showLinkButton = ref(!!form.linkButton);

const contentError = ref('');

const { data: content, status: contentStatus } = useFetch<IQACOverviewResponse>(
  `/api/admin/iqac/overview/${props.param}`
);
const isSubmitting = ref(false);

watch(content, (newContent) => {
  form.content = newContent?.content || '';
  form.title = newContent?.title || '';
  form.image = newContent?.image || null;
  form.linkButton = newContent?.linkButton || null;
  form.files = newContent?.files || [];
});

async function uploadPrimaryImage(file: File | undefined) {
  if (!file) {
    form.image = null;
    return;
  }

  try {
    const formVal = new FormData();
    formVal.append('files', file);
    formVal.append('prefix', 'profile');
    formVal.append('title', 'Primary Image');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', { method: 'POST', body: formVal });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'profile',
    };
    form.image = fileData;
  } catch (err) {
    console.error('Failed to upload primary file:', err);
    toast.error('Failed to upload primary image');
  }
}

const uploadFiles = async (
  files: { file: File; title?: string }[],
  isTitleEdit: boolean = false
) => {
  if (!files.length) return;
  const formData = new FormData();

  // Add each file to FormData
  files.forEach(({ file, title }) => {
    formData.append('files', file);
    formData.append('prefix', 'announcement');
    formData.append('title', title || file.name);
    formData.append('type', file.type);
  });

  try {
    const response = await $fetch<FileData[]>('/api/blob/upload', {
      method: 'POST',
      body: formData,
    });

    if (response && Array.isArray(response)) {
      const uploadedFiles = response.map((file) => ({
        pathname: file.pathname,
        title: file.title,
        type: file.type,
        prefix: 'announcement',
      }));
      // If this is a title edit, remove the existing file with the same title
      if (isTitleEdit) {
        form.files = form.files.filter(
          (existingFile) =>
            !uploadedFiles.some(
              (newFile) =>
                newFile.pathname.split('-')[0] ===
                existingFile.pathname?.split('-')[0]
            )
        );
      }

      // Append new files to existing ones
      form.files = [...form.files, ...uploadedFiles];
      toast.success(`Successfully uploaded ${uploadedFiles.length} file(s)`);
    }
  } catch (error) {
    console.error('Error uploading files:', error);
    toast.error('Failed to upload files');
  }
};

// Update file removal handler
function handleFileRemoval(fileTitle: string) {
  const fileToRemove = form.files.find((file) => file.title === fileTitle);
  if (!fileToRemove) return;

  form.files = form.files.filter((file) => file.title !== fileTitle);
  toast.success('File removed successfully');
}

const handleSubmit = async () => {
  isSubmitting.value = true;
  // Simple validation
  if (!form.content || form.content.trim() === '') {
    contentError.value = 'Content is required';
    return;
  }
  //Min 200 characters
  if (form.content.length < 200) {
    contentError.value = 'Content must be at least 200 characters';
    return;
  }

  contentError.value = '';

  try {
    await $fetch(`/api/admin/iqac/overview/${props.param}`, {
      method: 'POST',
      body: {
        content: form.content,
        title: form.title,
        image: form.image,
        linkButton: form.linkButton,
        files: form.files,
      },
    });

    toast.success('IQAC overview has been saved successfully');
  } catch (error) {
    console.error('Error saving IQAC overview:', error);
    toast.error('Failed to save IQAC overview. Please try again.');
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<template>
  <div class="w-full">
    <div v-if="contentStatus === 'pending'" class="flex justify-center py-8">
      <Loader2 class="h-4 w-4 animate-spin" />
    </div>
    <div v-else class="space-y-6">
      <div class="space-y-2" name="title">
        <Label>Title</Label>
        <Input
          v-model="form.title"
          placeholder="Enter title"
          :disabled="isSubmitting"
        />
        <p class="text-sm text-gray-500">The title of the IQAC overview</p>
      </div>
      <div class="space-y-4">
        <AdminBaseDropzone
          :model-value="form.image"
          :disabled="isSubmitting"
          :is-multiple-allowed="false"
          :file-types="['image']"
          title="Card Image"
          description="Upload the card image (required)"
          :existing-files="form.image ? [form.image] : []"
          @files-selected="
            (files) => uploadPrimaryImage(files ? files[0]?.file : undefined)
          "
          @file-removed="() => (form.image = null)"
        />
      </div>
      <div class="space-y-2" name="content">
        <Label>Content<span class="text-destructive">*</span></Label>
        <AdminBaseTextEditor
          v-model="form.content"
          placeholder="Enter content here"
          :disabled="isSubmitting"
        />
        <p v-if="contentError.length > 0" class="text-sm text-destructive">
          {{ contentError }}
        </p>
        <p class="text-sm text-gray-500">
          The main content that will be displayed in the IQAC section
        </p>
      </div>
      <div class="space-y-4">
        <AdminBaseDropzone
          :model-value="form.files"
          :disabled="isSubmitting"
          :is-multiple-allowed="true"
          :file-types="['pdf', 'image', 'text', 'video', 'audio']"
          title="Upload Files"
          description="Upload the files (optional)"
          :existing-files="form.files"
          @files-selected="(files) => uploadFiles(files)"
          @file-removed="handleFileRemoval"
        />
      </div>
      <div class="space-y-4">
        <h3 class="text-sm font-medium">Link Button</h3>
        <AdminTemplateButton
          v-model="form.linkButton"
          :disabled="isSubmitting"
          :show-button="showLinkButton || form.linkButton"
          @update:show-button="showLinkButton = $event"
          @update:model-value="
            (value) => {
              form.linkButton = value as never;
            }
          "
        />
      </div>

      <div class="flex justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          class="w-24"
          :disabled="isSubmitting"
          @click="
            () => {
              form.content = content?.content || '';
              form.title = content?.title || '';
              form.image = content?.image || null;
              form.linkButton = content?.linkButton || null;
              form.files = content?.files || [];
            }
          "
        >
          Cancel
        </Button>
        <Button :disabled="isSubmitting" class="w-24" @click="handleSubmit">
          <Loader2 v-if="isSubmitting" class="mr-2 h-4 w-4 animate-spin" />
          Save
        </Button>
      </div>
    </div>
  </div>
</template>
