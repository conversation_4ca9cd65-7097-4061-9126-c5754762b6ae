<script setup lang="ts">
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Form } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import { Loader2 } from 'lucide-vue-next';
import { DialogFooter } from '@/components/ui/dialog';
import {
  createEServiceSchema,
  type CreateEServiceSchema,
} from '~~/shared/schema/e-service/create';

defineProps<{
  isSubmitting: boolean;
}>();

const emit = defineEmits<{
  (e: 'submit', values: CreateEServiceSchema): void;
  (e: 'cancel'): void;
}>();

const formSchema = toTypedSchema(createEServiceSchema);

const initialValues = {
  title: '',
  link: '',
};

const handleSubmit = (values: any) => {
  emit('submit', values);
};
</script>

<template>
  <Form
    :validation-schema="formSchema"
    :initial-values="initialValues"
    @submit="handleSubmit"
  >
    <div class="grid gap-4 pb-4">
      <!-- Title Field -->
      <FormField v-slot="{ field, errorMessage }" name="title">
        <FormItem>
          <FormLabel
            >Service Title <span class="text-destructive">*</span></FormLabel
          >
          <FormControl>
            <Input
              v-bind="field"
              :model-value="field.value"
              placeholder="Enter service title"
              :disabled="isSubmitting"
              @update:model-value="field.onChange"
            />
          </FormControl>
          <FormDescription>Display name for the e-service</FormDescription>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- Link Field -->
      <FormField v-slot="{ field, errorMessage }" name="link">
        <FormItem>
          <FormLabel
            >Service Link <span class="text-destructive">*</span></FormLabel
          >
          <FormControl>
            <Input
              v-bind="field"
              :model-value="field.value"
              type="url"
              placeholder="Enter service URL"
              :disabled="isSubmitting"
              @update:model-value="field.onChange"
            />
          </FormControl>
          <FormDescription
            >URL where the service can be accessed</FormDescription
          >
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>
    </div>

    <DialogFooter>
      <Button
        type="button"
        variant="ghost"
        :disabled="isSubmitting"
        @click="emit('cancel')"
      >
        Cancel
      </Button>
      <Button type="submit" :disabled="isSubmitting">
        <Loader2 v-if="isSubmitting" class="mr-2 h-4 w-4 animate-spin" />
        {{ isSubmitting ? 'Creating...' : 'Create E-Service' }}
      </Button>
    </DialogFooter>
  </Form>
</template>
