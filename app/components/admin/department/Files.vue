<script setup lang="ts">
import type { DepartmentFile } from '~/types/admin/department';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';

interface Column {
  key: string;
  label: string;
}

const props = defineProps<{
  files: DepartmentFile[];
  departmentId: number;
  title: string;
  type?: 'questionBank' | 'project';
}>();

const emit = defineEmits<{
  update: [];
}>();

const columns = computed<Column[]>(() => {
  const titleColumn: Column = { key: 'title', label: 'Title' };
  const actionsColumn: Column = { key: 'actions', label: 'Actions' };

  if (props.type === 'questionBank') {
    return [
      titleColumn,
      { key: 'year', label: 'Year' },
      { key: 'semester', label: '<PERSON>mes<PERSON>' },
      actionsColumn,
    ];
  }

  return [titleColumn, actionsColumn];
});

const handleEdit = async (file: DepartmentFile) => {
  // TODO: Implement edit functionality
  console.log('Edit file:', file.fileId);
};

const handleDelete = async (file: DepartmentFile) => {
  try {
    await $fetch(
      `/api/admin/department/${props.departmentId}/file/${file.fileId}`,
      {
        method: 'DELETE',
      }
    );
    emit('update');
  } catch (error) {
    console.error('Error deleting file:', error);
  }
};

const handleAdd = () => {
  // TODO: Implement add functionality - open modal/form
};

const handleDownload = async (file: DepartmentFile) => {
  if (file.fileId) {
    // TODO: Implement file download functionality
    window.open(`/api/files/${file.fileId}`, '_blank');
  }
};
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader
      class="px-2 pt-0 pb-4 flex flex-row items-center justify-between"
    >
      <CardTitle>{{ title }}</CardTitle>
      <Button @click="handleAdd">Add File</Button>
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead v-for="column in columns" :key="column.key">
              {{ column.label }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow
            v-for="file in files"
            :key="`file-${file.fileId || file.title}`"
          >
            <TableCell>{{ file.title }}</TableCell>
            <TableCell v-if="type === 'questionBank'">
              {{ file.year }}
            </TableCell>
            <TableCell v-if="type === 'questionBank'">
              {{ file.semester }}
            </TableCell>
            <TableCell>
              <div class="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  @click="handleDownload(file)"
                >
                  Download
                </Button>
                <Button variant="outline" size="sm" @click="handleEdit(file)">
                  Edit
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  @click="handleDelete(file)"
                >
                  Delete
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </CardContent>
  </Card>
</template>
