<script setup lang="ts">
import {
  CalendarIcon,
  ClockIcon,
  Trash2,
  PlusCircle,
  Search,
} from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { toast } from 'vue-sonner';
import type { DepartmentEvent } from '~/types/home';

const props = defineProps<{
  departmentId: number;
}>();

const emit = defineEmits<{
  update: [];
}>();

const { eventList, isLoadingEvent, fetchEventList, updateEvent } =
  useDepartment();

const showAttachDialog = ref(false);
const availableEvents = ref<DepartmentEvent[]>([]);
const isLoadingAvailableEvents = ref(false);
const searchQuery = ref('');
const selectedEvent = ref<DepartmentEvent | null>(null);

const filteredEvents = computed(() => {
  if (!searchQuery.value) return availableEvents.value;
  const query = searchQuery.value.toLowerCase();
  return availableEvents.value.filter((event) =>
    event.title.toLowerCase().includes(query)
  );
});

const handleAttach = async () => {
  if (!selectedEvent.value) return;

  try {
    const updatedIds = [
      ...(eventList.value?.map((e) => e.id) || []),
      selectedEvent.value.id,
    ];
    await updateEvent(props.departmentId, updatedIds);
    await fetchEventList(props.departmentId);
    showAttachDialog.value = false;
    selectedEvent.value = null;
    searchQuery.value = '';
    emit('update');
    toast.success('Event attached successfully');
  } catch (error) {
    console.error('Error attaching event:', error);
    toast.error('Failed to attach event');
  }
};

const handleDetach = async (eventId: number) => {
  try {
    const updatedIds =
      eventList.value?.filter((e) => e.id !== eventId).map((e) => e.id) || [];
    await updateEvent(props.departmentId, updatedIds);
    await fetchEventList(props.departmentId);
    emit('update');
    toast.success('Event detached successfully');
  } catch (error) {
    console.error('Error detaching event:', error);
    toast.error('Failed to detach event');
  }
};

const openAttachDialog = async () => {
  try {
    isLoadingAvailableEvents.value = true;
    const response = await $fetch<{
      success: boolean;
      data: DepartmentEvent[];
    }>('/api/admin/announcement/type/event', {
      query: {
        filterStatus: 'active',
      },
    });

    if (response.success) {
      const currentIds = new Set(eventList.value?.map((e) => e.id));
      availableEvents.value = response.data.filter(
        (event) => !currentIds.has(event.id)
      );
    }
    showAttachDialog.value = true;
  } catch (error) {
    console.error('Error fetching available events:', error);
    toast.error('Failed to fetch available events');
  } finally {
    isLoadingAvailableEvents.value = false;
  }
};

const formatEventDate = (date: number | null) => {
  if (!date) return 'Not specified';
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

onMounted(async () => {
  await fetchEventList(props.departmentId);
});
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader class="px-2 pt-0 pb-4">
      <div class="flex items-center justify-between">
        <div>
          <CardTitle>Events</CardTitle>
          <CardDescription>
            Manage department events and activities
          </CardDescription>
        </div>
        <Button @click="openAttachDialog">
          <PlusCircle class="h-4 w-4 mr-2" />
          Attach Event
        </Button>
      </div>
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <div class="space-y-6">
        <!-- Loading State -->
        <div v-if="isLoadingEvent" class="flex justify-center py-8">
          <div
            class="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"
          ></div>
        </div>

        <!-- Empty State -->
        <div
          v-else-if="!eventList?.length"
          class="flex flex-col items-center justify-center py-16 border-2 border-dashed rounded-lg"
        >
          <div class="rounded-full bg-primary/10 p-4 mb-4">
            <CalendarIcon class="h-8 w-8 text-primary" />
          </div>
          <h3 class="text-lg font-semibold mb-2">No Events Attached</h3>
          <p class="text-sm text-muted-foreground max-w-sm text-center">
            Attach events to display them in the department page. Click the
            "Attach Event" button above.
          </p>
        </div>

        <!-- Events List -->
        <div v-else class="grid gap-4">
          <Card
            v-for="event in eventList"
            :key="event.id"
            class="group transition-all hover:shadow-md"
          >
            <CardHeader
              class="flex flex-row items-center justify-between pb-2 space-y-0"
            >
              <CardTitle class="text-base font-semibold">
                {{ event.title }}
              </CardTitle>
              <Button
                variant="ghost"
                size="icon"
                class="text-destructive"
                @click="handleDetach(event.id)"
              >
                <Trash2 class="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent class="space-y-3">
              <div class="text-sm text-muted-foreground line-clamp-2">
                {{ event.content }}
              </div>
              <div class="flex flex-wrap gap-4 text-sm">
                <Badge :variant="event.isActive ? 'default' : 'secondary'">
                  {{ event.isActive ? 'Active' : 'Inactive' }}
                </Badge>
                <Badge variant="outline">Priority: {{ event.priority }}</Badge>
              </div>
              <div
                class="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-muted-foreground"
              >
                <div class="flex items-center gap-2">
                  <CalendarIcon class="h-4 w-4" />
                  <span
                    >Event Date: {{ formatEventDate(event.eventDate) }}</span
                  >
                </div>
                <div class="flex items-center gap-2">
                  <ClockIcon class="h-4 w-4" />
                  <span
                    >Scheduled: {{ formatEventDate(event.scheduledAt) }}</span
                  >
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </CardContent>
  </Card>

  <!-- Attach Event Dialog -->
  <Dialog :open="showAttachDialog" @update:open="showAttachDialog = false">
    <DialogContent class="sm:max-w-[900px]">
      <DialogHeader>
        <DialogTitle>Attach Event</DialogTitle>
        <DialogDescription>
          Select an event to attach to this department. You can search events by
          title.
        </DialogDescription>
      </DialogHeader>

      <!-- Search Input -->
      <div class="relative">
        <Search
          class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"
        />
        <Input
          v-model="searchQuery"
          class="pl-9"
          placeholder="Search events by title..."
        />
      </div>

      <div class="grid gap-4 py-4 max-h-[500px] overflow-y-auto">
        <!-- Loading State -->
        <div v-if="isLoadingAvailableEvents" class="flex justify-center py-8">
          <div
            class="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"
          ></div>
        </div>

        <!-- Empty State -->
        <div
          v-else-if="!filteredEvents.length"
          class="flex flex-col items-center justify-center py-8 text-center"
        >
          <div class="rounded-full bg-muted p-3 mb-4">
            <CalendarIcon class="h-6 w-6 text-muted-foreground" />
          </div>
          <h3 class="font-semibold mb-1">No Events Available</h3>
          <p class="text-sm text-muted-foreground">
            {{
              searchQuery
                ? 'No events match your search.'
                : 'Create new events in the event management section.'
            }}
          </p>
        </div>

        <!-- Events Grid -->
        <div v-else class="grid gap-4 px-4">
          <Card
            v-for="event in filteredEvents"
            :key="event.id"
            class="group transition-all hover:shadow-md cursor-pointer"
            :class="{
              'ring-2 ring-primary ring-offset-2':
                selectedEvent?.id === event.id,
              'hover:ring-2 hover:ring-primary/50 hover:ring-offset-2':
                selectedEvent?.id !== event.id,
            }"
            @click="selectedEvent = event"
          >
            <CardHeader class="pb-2">
              <div class="flex items-center justify-between">
                <CardTitle class="text-base font-semibold">
                  {{ event.title }}
                </CardTitle>
                <Badge :variant="event.isActive ? 'default' : 'secondary'">
                  {{ event.isActive ? 'Active' : 'Inactive' }}
                </Badge>
              </div>
            </CardHeader>
            <CardContent class="space-y-3">
              <p class="text-sm text-muted-foreground line-clamp-2">
                {{ event.content }}
              </p>
              <div class="flex flex-wrap gap-2 text-sm">
                <Badge variant="outline">Priority: {{ event.priority }}</Badge>
                <span class="text-muted-foreground">
                  {{ formatEventDate(event.eventDate) }}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="showAttachDialog = false">
          Cancel
        </Button>
        <Button :disabled="!selectedEvent" @click="handleAttach">
          Attach Selected Event
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
