<script setup lang="ts">
import type { ProjectWorkData } from '~/types/admin';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import ProjectWorkForm from './ProjectWorkForm.vue';
import { toast } from 'vue-sonner';

const props = defineProps<{ departmentId: number }>();

// Add debugging logs

const {
  projectWorkList,
  projectWorkPagination,
  isLoadingProjectWork,
  fetchProjectWorkList,
  createProjectWork,
  updateProjectWork,
  deleteProjectWork,
} = useDepartment();

watch(
  projectWorkList,
  (newValue) => {
    console.log('projectWorkList updated:', newValue);
  },
  { immediate: true }
);

// Add watch to debug loading state
watch(
  isLoadingProjectWork,
  (newValue) => {
    console.log('isLoadingProjectWork updated:', newValue);
  },
  { immediate: true }
);

const columns = [
  { key: 'title', label: 'Title' },
  { key: 'year', label: 'Year' },
  { key: 'semester', label: 'Semester' },
  { key: 'actions', label: 'Actions' },
];

const isDialogOpen = ref(false);
const selectedProjectWork = ref<ProjectWorkData | null>(null);
const isSubmitting = ref(false);
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Add delete dialog state
const showDeleteDialog = ref(false);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

// Update onMounted to include error handling and debugging
onMounted(async () => {
  try {
    await fetchProjectWorkList(
      props.departmentId,
      currentPage.value,
      itemsPerPage.value
    );
  } catch (error) {
    console.error('Error in onMounted:', error);
    toast.error('Failed to load project works');
  }
});

const handleEdit = (projectWork: ProjectWorkData) => {
  selectedProjectWork.value = projectWork;
  isDialogOpen.value = true;
};

const handleDelete = async (projectWork: ProjectWorkData) => {
  selectedProjectWork.value = projectWork;
  showDeleteDialog.value = true;
};

const confirmDelete = async () => {
  if (!selectedProjectWork.value) return;

  deleteState.value = 'loading';
  try {
    await deleteProjectWork(props.departmentId, selectedProjectWork.value.id);
    deleteState.value = 'success';

    // Close dialog and refresh after a short delay
    setTimeout(async () => {
      showDeleteDialog.value = false;
      deleteState.value = 'idle';
      selectedProjectWork.value = null;
      await fetchProjectWorkList(
        props.departmentId,
        currentPage.value,
        itemsPerPage.value
      );
    }, 1000);
  } catch (error) {
    console.error('Error deleting project work:', error);
    deleteState.value = 'error';
    toast.error('Failed to delete project work');
  }
};

const handleAdd = () => {
  selectedProjectWork.value = null;
  isDialogOpen.value = true;
};

const handleFormSubmit = async (values: any) => {
  try {
    isSubmitting.value = true;
    if (selectedProjectWork.value) {
      await updateProjectWork(
        props.departmentId,
        selectedProjectWork.value.id,
        values
      );
      toast.success('Project work updated successfully');
    } else {
      await createProjectWork(props.departmentId, values);
      toast.success('Project work created successfully');
    }
    isDialogOpen.value = false;
    await fetchProjectWorkList(
      props.departmentId,
      currentPage.value,
      itemsPerPage.value
    );
  } catch (error) {
    console.error('Error saving project work:', error);
    toast.error('Failed to save project work');
  } finally {
    isSubmitting.value = false;
  }
};

const handleFormCancel = () => {
  isDialogOpen.value = false;
};

const handleDownload = (projectWork: ProjectWorkData) => {
  if (projectWork.file?.pathname) {
    window.open(`/api/files/${projectWork.file.pathname}`, '_blank');
  }
};

const handlePageChange = async (page: number) => {
  currentPage.value = page;
  await fetchProjectWorkList(props.departmentId, page, itemsPerPage.value);
};
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader
      class="px-2 pt-0 pb-4 flex flex-row items-center justify-between"
    >
      <CardTitle>Project Works</CardTitle>
      <Button
        type="button"
        variant="default"
        size="sm"
        :disabled="isSubmitting"
        @click="handleAdd"
      >
        Add Project Work
      </Button>
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <div v-if="isLoadingProjectWork" class="flex justify-center py-8">
        Loading...
      </div>
      <Table v-else>
        <TableHeader>
          <TableRow>
            <TableHead v-for="column in columns" :key="column.key">
              {{ column.label }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-if="isLoadingProjectWork">
            <TableCell colspan="4" class="text-center py-4">
              Loading project works data...
            </TableCell>
          </TableRow>
          <TableRow v-else-if="!projectWorkList.length">
            <TableCell colspan="4" class="text-center py-4">
              No project works found.
            </TableCell>
          </TableRow>
          <TableRow
            v-for="projectWork in projectWorkList"
            :key="projectWork.id"
          >
            <TableCell>{{ projectWork.title }}</TableCell>
            <TableCell>{{ projectWork.year }}</TableCell>
            <TableCell>{{ projectWork.semester }}</TableCell>
            <TableCell>
              <div class="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Download project work"
                  @click="handleDownload(projectWork)"
                >
                  Download
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Edit project work"
                  @click="handleEdit(projectWork)"
                >
                  {{ isSubmitting ? 'Editing...' : 'Edit' }}
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Delete project work"
                  @click="handleDelete(projectWork)"
                >
                  {{ isSubmitting ? 'Deleting...' : 'Delete' }}
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <!-- Pagination -->
      <div
        v-if="projectWorkPagination.totalPages > 1"
        class="mt-4 flex justify-center"
      >
        <div class="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            :disabled="!projectWorkPagination.hasPreviousPage"
            @click="handlePageChange(currentPage - 1)"
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            :disabled="!projectWorkPagination.hasNextPage"
            @click="handlePageChange(currentPage + 1)"
          >
            Next
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>

  <Dialog :open="isDialogOpen" @update:open="isDialogOpen = $event">
    <DialogContent class="sm:max-w-3xl">
      <DialogHeader>
        <DialogTitle>
          {{
            selectedProjectWork ? 'Edit Project Work' : 'Add New Project Work'
          }}
        </DialogTitle>
      </DialogHeader>
      <ProjectWorkForm
        :department-id="departmentId"
        :initial-values="selectedProjectWork"
        :is-submitting="isSubmitting"
        @submit="handleFormSubmit"
        @cancel="handleFormCancel"
      />
    </DialogContent>
  </Dialog>

  <!-- Delete Confirmation Dialog -->
  <AdminMenuDeleteDialog
    v-if="selectedProjectWork"
    v-model:is-open="showDeleteDialog"
    :is-submitting="deleteState === 'loading'"
    :delete-state="deleteState"
    menu-title="project work"
    type="menu"
    :description="`Are you sure you want to delete project work '${selectedProjectWork.title}'? This action cannot be undone.`"
    @confirm="confirmDelete"
  />
</template>
