<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod';
import { useForm } from 'vee-validate';
import { departmentSchema } from '~~/shared/schema/department/create';
import { useDebounceFn } from '@vueuse/core';
import { toast } from 'vue-sonner';

const props = defineProps<{
  isSubmitting?: boolean;
  type: 'create' | 'edit';
  initialValues?: {
    name: string;
    slug: string;
  };
}>();

const emit = defineEmits<{
  (e: 'submit', values: { name: string; slug: string }): void;
  (e: 'cancel'): void;
}>();

const { validateSlug } = useDepartmentsList();
const isSlugAvailable = ref(true); // Set to true by default for edit mode
const isValidatingSlug = ref(false);
const formSchema = toTypedSchema(departmentSchema);

const { values, errors, isFieldValid, setFieldValue, resetForm } = useForm({
  validationSchema: formSchema,
  initialValues: {
    name: props.initialValues?.name ?? '',
    slug: props.initialValues?.slug ?? '',
  },
});

// Add debounced slug validation
const debouncedValidateSlug = useDebounceFn(async (value: string) => {
  if (
    !value ||
    props.isSubmitting ||
    isValidatingSlug.value ||
    errors.value?.name ||
    errors.value?.slug ||
    !isFieldValid('name') ||
    !isFieldValid('slug')
  )
    return;

  // Skip validation if in edit mode and slug hasn't changed
  if (props.type === 'edit' && props.initialValues?.slug === value) {
    isSlugAvailable.value = true;
    return;
  }

  isValidatingSlug.value = true;

  try {
    const isAvailable = await validateSlug(value);
    isSlugAvailable.value = isAvailable;
    if (!isAvailable) {
      errors.value.slug = 'This route path is already taken';
      toast.error('This route path is already taken');
    }
  } catch (err: any) {
    const errorMessage =
      err.data?.message || err.message || 'Failed to validate slug';
    toast.error(errorMessage);
    isSlugAvailable.value = false;
  } finally {
    isValidatingSlug.value = false;
  }
}, 500);

const onSubmit = () => {
  emit('submit', {
    name: values?.name || '',
    slug: values?.slug || '',
  });
};

const onCancel = () => {
  resetForm();
  emit('cancel');
};

// Watch for initialValues changes and update form
watch(
  () => props.initialValues,
  (newValues) => {
    if (newValues) {
      setFieldValue('name', newValues.name);
      setFieldValue('slug', newValues.slug);
      // In edit mode, set isSlugAvailable to true for initial value
      if (props.type === 'edit') {
        isSlugAvailable.value = true;
      }
      // Only validate the slug in create mode or if it's different from initial value
      else if (props.type === 'create' && newValues.slug) {
        debouncedValidateSlug(newValues.slug);
      }
    }
  }
);

// Reset slug validation when form is reset
watch(
  values,
  (newValues) => {
    if (!newValues.slug) {
      isSlugAvailable.value = props.type === 'edit';
    }
  },
  { deep: true }
);
</script>

<template>
  <Form
    :initial-values="initialValues"
    :validation-schema="formSchema"
    :type="type"
    class="space-y-4"
  >
    <FormField v-slot="{ field, errorMessage }" name="name">
      <FormItem>
        <FormLabel>Department Name</FormLabel>
        <FormControl>
          <Input
            v-bind="field"
            :model-value="field.value"
            placeholder="Enter department name"
            :disabled="isSubmitting"
            @update:model-value="
              (newValue: any) => setFieldValue('name', newValue)
            "
          />
        </FormControl>
        <FormDescription>The name of the department</FormDescription>
        <FormMessage>{{ errorMessage }}</FormMessage>
      </FormItem>
    </FormField>

    <FormField v-slot="{ field, errorMessage }" name="slug">
      <FormItem>
        <FormLabel>Route Path</FormLabel>
        <FormControl>
          <div class="relative">
            <AdminBaseRouteInput
              v-bind="field"
              :model-value="field.value"
              placeholder="example-route"
              :disabled="isSubmitting || isValidatingSlug"
              @update:model-value="
                (newValue: any) => {
                  setFieldValue('slug', newValue);
                  debouncedValidateSlug(newValue);
                }
              "
            />
            <div
              v-if="
                field.value &&
                (type === 'create' || field.value !== initialValues?.slug)
              "
              class="absolute right-2 top-1/2 -translate-y-1/2"
            >
              <span v-if="isValidatingSlug" class="text-muted-foreground">
                <span class="i-lucide-loader-2 animate-spin" />
              </span>
              <span
                v-else-if="isSlugAvailable && !errors.slug"
                class="text-success"
              >
                <span class="i-lucide-check" />
              </span>
              <span v-else-if="errors.slug" class="text-destructive">
                <span class="i-lucide-x" />
              </span>
            </div>
          </div>
        </FormControl>
        <FormDescription
          >The URL path for this menu (e.g., /about-us)</FormDescription
        >
        <FormMessage>{{ errorMessage }}</FormMessage>
      </FormItem>
    </FormField>

    <div class="flex justify-end space-x-2">
      <Button
        type="button"
        variant="outline"
        :disabled="isSubmitting || isValidatingSlug"
        @click="onCancel"
      >
        Cancel
      </Button>
      <Button
        :disabled="
          isSubmitting ||
          isValidatingSlug ||
          errors.name ||
          errors.slug ||
          !isFieldValid('name') ||
          !isFieldValid('slug') ||
          !isSlugAvailable
        "
        @click="onSubmit"
      >
        <slot name="submit-text">
          {{ isSubmitting ? 'Submitting...' : 'Submit' }}
        </slot>
      </Button>
    </div>
  </Form>
</template>
