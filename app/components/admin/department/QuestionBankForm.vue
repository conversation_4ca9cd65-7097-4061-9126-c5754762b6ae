<script setup lang="ts">
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import type { QuestionBankData } from '~/types/admin';
import type { FileData } from '~/types/home';
import type { CreateQuestionBank } from '~~/shared/schema/department/files/create';
import type { UpdateQuestionBank } from '~~/shared/schema/department/files/update';
import { createQuestionBankSchema } from '~~/shared/schema/department/files/create';
import { updateQuestionBankSchema } from '~~/shared/schema/department/files/update';
import { toast } from 'vue-sonner';

const props = defineProps<{
  departmentId: number;
  courseNames: { name: string; id: number }[];
  initialValues?: QuestionBankData | null;
  isSubmitting?: boolean;
}>();

const emit = defineEmits<{
  submit: [values: CreateQuestionBank | UpdateQuestionBank];
  cancel: [];
}>();

// Use appropriate schema based on whether we're editing or creating
const validationSchema = toTypedSchema(
  props.initialValues ? updateQuestionBankSchema : createQuestionBankSchema
);

const initialValues = computed(() => {
  return {
    type: 'question_paper' as const,
    title: props.initialValues?.title ?? '',
    year: props.initialValues?.year ?? new Date().getFullYear(),
    courseId: props.initialValues?.course?.id ?? 1,
    semester: props.initialValues?.semester ?? 1,
    file: props.initialValues?.file
      ? {
          pathname: props.initialValues.file.pathname,
          title: props.initialValues.file.title,
          type: props.initialValues.file.type,
        }
      : undefined,
  };
});

const { values, errors, setFieldValue, resetForm } = useForm<
  CreateQuestionBank | UpdateQuestionBank
>({
  validationSchema,
  initialValues: initialValues.value,
});

const handleSubmit = () => {
  // Check if title is empty
  if (!values.title) {
    toast.error('Title is required');
    return;
  }

  // Check if file is empty
  if (!primaryFile.value) {
    toast.error('File is required');
    return;
  }

  // Check if courseId is empty
  if (!values.courseId) {
    toast.error('Course is required');
    return;
  }

  // Check if year is empty
  if (!values.year) {
    toast.error('Year is required');
    return;
  }

  emit('submit', values);
};

const handleCancel = () => {
  resetForm();
  emit('cancel');
};

const primaryFile = ref<
  | {
      pathname: string;
      title: string;
      type: string;
    }
  | undefined
>(
  props.initialValues?.file
    ? {
        pathname: props.initialValues.file.pathname,
        title: props.initialValues.file.title,
        type: props.initialValues.file.type,
      }
    : undefined
);

const handleFileUpload = async (files: { file: File; title?: string }[]) => {
  const file = files[0]?.file;
  if (!file) {
    primaryFile.value = undefined;
    setFieldValue('file', undefined);
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'question_paper');
    form.append('title', values.title || 'Question Bank');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', {
      method: 'POST',
      body: form,
    });

    const fileData: FileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'question_paper',
    };
    primaryFile.value = fileData;
    setFieldValue('file', fileData);
  } catch (err) {
    console.error('Failed to upload file:', err);
    toast.error('Failed to upload file');
  }
};

const handleFileRemove = () => {
  primaryFile.value = undefined;
  setFieldValue('file', undefined);
};

const handleNumericInput = (
  value: string | number | null,
  field: 'year' | 'semester'
) => {
  const numValue = value === null || value === '' ? 0 : Number(value);
  setFieldValue(field, numValue);
};

// Use computed property to stay in sync with values.courseId
const courseId = computed({
  get: () => String(values.courseId),
  set: (newValue: string) => {
    const id = Number(newValue);
    setFieldValue('courseId', id);
  },
});
</script>

<template>
  <Form
    :validation-schema="validationSchema"
    :initial-values="initialValues"
    class="space-y-6"
  >
    <div class="grid gap-6">
      <div class="grid grid-cols-2 gap-4">
        <FormField v-slot="{ field, errorMessage }" name="title">
          <!-- {{ errors }} -->
          <FormItem>
            <FormLabel>Title</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="field.value"
                placeholder="Enter question bank title"
                :disabled="isSubmitting"
                @update:model-value="
                  (value: any) => setFieldValue('title', value as string)
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <div class="space-y-2">
          <Label>Course</Label>
          <div>
            <Select v-model="courseId" :disabled="isSubmitting">
              <SelectTrigger>
                <SelectValue placeholder="Select a course" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="course in courseNames"
                  :key="course.id"
                  :value="String(course.id)"
                >
                  {{ course.name }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-4">
        <FormField v-slot="{ field, errorMessage }" name="year">
          <FormItem>
            <FormLabel>Year</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="String(field.value || '')"
                type="number"
                min="2000"
                :max="new Date().getFullYear() + 1"
                placeholder="Enter year"
                :disabled="isSubmitting"
                @update:model-value="
                  (value: any) => handleNumericInput(value, 'year')
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <FormField v-slot="{ field, errorMessage }" name="semester">
          <FormItem>
            <FormLabel>Semester</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="String(field.value || '')"
                type="number"
                min="1"
                max="8"
                placeholder="Enter semester"
                :disabled="isSubmitting"
                @update:model-value="
                  (value: any) => handleNumericInput(value, 'semester')
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
      </div>

      <!-- File Upload -->
      <AdminBaseDropzone
        :is-multiple-allowed="false"
        :file-types="['pdf']"
        title="Question Bank File"
        :existing-files="primaryFile ? [primaryFile] : []"
        description="Upload the question bank file in PDF format"
        :is-submitting="isSubmitting"
        @files-selected="(files: any) => handleFileUpload(files)"
        @file-removed="handleFileRemove"
      />
      <!-- Show error of file -->
      <div v-if="errors.file" class="text-red-500 text-xs -mt-4">
        {{ errors.file }}
      </div>

      <div class="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          :disabled="isSubmitting"
          @click="handleCancel"
        >
          Cancel
        </Button>
        <Button :disabled="isSubmitting" @click="handleSubmit">
          {{ isSubmitting ? 'Saving...' : initialValues ? 'Update' : 'Create' }}
        </Button>
      </div>
    </div>
  </Form>
</template>
