<script setup lang="ts">
import {
  Accordion,
  AccordionContent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';

interface Achievement {
  id: number;
  title: string;
  year: number;
  description: string;
  type: 'student' | 'faculty' | 'department';
}

const props = defineProps<{
  departmentId: number;
}>();

const emit = defineEmits<{
  update: [];
}>();
const achievements = ref<Achievement[]>([]);
const achievementTypes = [
  { id: 'student', label: 'Student Achievements' },
  { id: 'faculty', label: 'Faculty Achievements' },
  { id: 'department', label: 'Department Achievements' },
];

const columns = [
  { key: 'title', label: 'Title' },
  { key: 'year', label: 'Year' },
  { key: 'description', label: 'Description' },
  { key: 'actions', label: 'Actions' },
];

const handleEdit = async (achievement: Achievement) => {
  // TODO: Implement edit functionality
  console.log('Edit achievement:', achievement.id);
};

const handleDelete = async (achievement: Achievement) => {
  try {
    await $fetch(
      `/api/admin/department/${props.departmentId}/achievement/${achievement.id}`,
      {
        method: 'DELETE',
      }
    );
    emit('update');
  } catch (error) {
    console.error('Error deleting achievement:', error);
  }
};

const handleAdd = (type: string) => {
  // TODO: Implement add functionality - open modal/form
  console.log('Add achievement of type:', type);
};

const filteredAchievements = computed(() => {
  return achievementTypes.map((type) => ({
    ...type,
    items: achievements.value.filter((a) => a.type === type.id),
  }));
});
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader
      class="px-2 pt-0 pb-4 flex flex-row items-center justify-between"
    >
      <CardTitle>Achievements</CardTitle>
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <Accordion type="single" collapsible class="w-full">
        <AccordionItem
          v-for="section in filteredAchievements"
          :key="section.id"
          :value="section.id"
        >
          <AccordionTrigger class="flex justify-between">
            <span>{{ section.label }}</span>
          </AccordionTrigger>
          <AccordionContent>
            <div class="space-y-4 p-4">
              <div class="flex justify-end">
                <Button @click="handleAdd(section.id)">
                  Add {{ section.label }}
                </Button>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead v-for="column in columns" :key="column.key">
                      {{ column.label }}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow
                    v-for="achievement in section.items"
                    :key="achievement.id"
                  >
                    <TableCell>{{ achievement.title }}</TableCell>
                    <TableCell>{{ achievement.year }}</TableCell>
                    <TableCell>{{ achievement.description }}</TableCell>
                    <TableCell>
                      <div class="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          @click="handleEdit(achievement)"
                        >
                          Edit
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          @click="handleDelete(achievement)"
                        >
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </CardContent>
  </Card>
</template>
