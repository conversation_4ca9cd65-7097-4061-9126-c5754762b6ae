<script setup lang="ts">
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import type { CourseListData } from '~/types/admin';
import type { Course } from '~~/shared/schema/department/course/create';
import type { CourseUpdate } from '~~/shared/schema/department/course/update';
import { courseSchema } from '~~/shared/schema/department/course/create';
import { courseUpdateSchema } from '~~/shared/schema/department/course/update';
import { linkButtonSchema } from '~~/shared/schema';
import { COURSE_TYPES, type CourseType } from '~~/server/database/tables/enums';
import { toast } from 'vue-sonner';
import type { FileData } from '~/types/home';

const props = defineProps<{
  departmentId: number;
  initialValues?: CourseListData | null;
  isSubmitting?: boolean;
}>();

const emit = defineEmits<{
  submit: [values: Course | CourseUpdate];
  cancel: [];
}>();

// Use appropriate schema based on whether we're editing or creating
const validationSchema = computed(() =>
  toTypedSchema(props.initialValues ? courseUpdateSchema : courseSchema)
);

const initialValues = computed(() => {
  return {
    name: props.initialValues?.name ?? '',
    title: props.initialValues?.title ?? 'Eligibility For Admission',
    type: props.initialValues?.type ?? 'ug',
    specialization: props.initialValues?.specialization ?? '',
    durationInMonths: props.initialValues?.durationInMonths ?? 12,
    semesterCount: props.initialValues?.semesterCount ?? 2,
    seatsCount: props.initialValues?.seatsCount ?? 60,
    content: props.initialValues?.content ?? '',
    image: props.initialValues?.image ?? null,
    syllabus: props.initialValues?.syllabus
      ? {
          pathname: props.initialValues.syllabus.pathname,
          title: 'syllabus',
          type: props.initialValues.syllabus.type,
        }
      : null,
    pos: props.initialValues?.pos
      ? {
          pathname: props.initialValues.pos.pathname,
          title: 'program outcome',
          type: props.initialValues.pos.type,
        }
      : null,
    linkButton: props.initialValues?.linkButton
      ? {
          style: props.initialValues.linkButton.style,
          title: props.initialValues.linkButton.title,
          type: props.initialValues.linkButton.type,
          newTab: props.initialValues.linkButton.newTab,
          externalLink: props.initialValues.linkButton.externalLink,
          internalLink: props.initialValues.linkButton.internalLink,
          icon: props.initialValues.linkButton.icon,
        }
      : null,
  };
});

const { values, errors, setFieldValue, resetForm } = useForm<
  Course | CourseUpdate
>({
  validationSchema,
  initialValues: initialValues.value,
});

// Add type for file upload
interface FileUpload {
  file: File;
  title?: string;
}

const primaryImage = ref<FileData | undefined>(
  props.initialValues?.image?.pathname
    ? {
        pathname: props.initialValues.image.pathname,
        title: props.initialValues.image.title || 'Course Image',
        type: props.initialValues.image.type || '',
        prefix: 'course',
      }
    : undefined
);

const primarySyllabus = ref<FileData | undefined>(
  props.initialValues?.syllabus?.pathname
    ? {
        pathname: props.initialValues.syllabus.pathname,
        title: props.initialValues.syllabus.title || 'Syllabus',
        type: props.initialValues.syllabus.type || '',
        prefix: 'syllabus',
      }
    : undefined
);

const primaryPos = ref<FileData | undefined>(
  props.initialValues?.pos?.pathname
    ? {
        pathname: props.initialValues.pos.pathname,
        title: props.initialValues.pos.title || 'Program Outcomes',
        type: props.initialValues.pos.type || '',
        prefix: 'pos',
      }
    : undefined
);

const handleSubmit = () => {
  // Check if title is empty
  if (!values.title) {
    toast.error('Dropdown Title is required');
    return;
  }
  // Check if name is empty
  if (!values.name) {
    toast.error('Course Name is required');
    return;
  }
  // Check if content is empty
  if (!values.content) {
    toast.error('Dropdown Content is required');
    return;
  }
  // Check if image is empty
  if (!primaryImage.value) {
    toast.error('Course Image is required');
    return;
  }
  // Check if syllabus is empty
  if (!primarySyllabus.value) {
    toast.error('Course Syllabus is required');
    return;
  }
  // Check if pos is empty
  if (!primaryPos.value) {
    toast.error('Program Outcomes is required');
    return;
  }

  emit('submit', values);
};

const handleCancel = () => {
  resetForm();
  emit('cancel');
};

// Update handleSyllabusUpload to handle direct file upload
const handleSyllabusUpload = async (files: FileUpload[]) => {
  const file = files[0]?.file;
  if (!file) {
    primarySyllabus.value = undefined;
    setFieldValue('syllabus', null);
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'syllabus');
    form.append('title', 'Course Syllabus');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', {
      method: 'POST',
      body: form,
    });

    const fileData: FileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'syllabus',
    };
    primarySyllabus.value = fileData;
    setFieldValue('syllabus', fileData);
  } catch (err) {
    console.error('Failed to upload syllabus:', err);
    toast.error('Failed to upload syllabus');
  }
};

// Update handlePosUpload to handle direct file upload
const handlePosUpload = async (files: FileUpload[]) => {
  const file = files[0]?.file;
  if (!file) {
    primaryPos.value = undefined;
    setFieldValue('pos', null);
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'pos');
    form.append('title', 'Program Outcomes');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', {
      method: 'POST',
      body: form,
    });

    const fileData: FileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'pos',
    };
    primaryPos.value = fileData;
    setFieldValue('pos', fileData);
  } catch (err) {
    console.error('Failed to upload program outcomes:', err);
    toast.error('Failed to upload program outcomes');
  }
};

// Update remove handlers
const handleSyllabusRemove = () => {
  primarySyllabus.value = undefined;
  setFieldValue('syllabus', null);
};

const handlePosRemove = () => {
  primaryPos.value = undefined;
  setFieldValue('pos', null);
};

// Update showLinkButton to be reactive to linkButton changes
const showLinkButton = computed(() => !!values.linkButton);

const formRef = ref();
watch(
  () => values.linkButton,
  (newValue) => {
    if (formRef.value) {
      formRef.value.setFieldValue('linkButton', newValue ? newValue : null);
    }
  }
);

const handleLinkButtonUpdate = (value: any) => {
  if (!value || !value.style || !value.title) {
    setFieldValue('linkButton', null);
    return;
  }
  setFieldValue('linkButton', {
    style: value.style || 'primary',
    title: value.title,
    type: value.type || 'link',
    newTab: value.newTab || false,
    externalLink: value.externalLink || null,
    internalLink: value.internalLink || null,
    icon: value.icon || null,
  });
};

type FormField = keyof Course | keyof CourseUpdate;
type NumericFields = 'seatsCount' | 'durationInMonths' | 'semesterCount';

const handleNumericInput = (
  value: string | number | null,
  field: NumericFields
) => {
  const numValue = value === null || value === '' ? 0 : Number(value);
  setFieldValue(field, numValue);
};

// Update handleImageUpload to handle direct file upload
const handleImageUpload = async (files: { file: File; title?: string }[]) => {
  const file = files[0]?.file;
  if (!file) {
    primaryImage.value = undefined;
    setFieldValue('image', null);
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'course');
    form.append('title', 'Course Image');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', {
      method: 'POST',
      body: form,
    });

    const fileData: FileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'course',
    };
    primaryImage.value = fileData;
    setFieldValue('image', fileData);
  } catch (err) {
    console.error('Failed to upload course image:', err);
    toast.error('Failed to upload course image');
  }
};

// Update handleImageRemove
const handleImageRemove = () => {
  primaryImage.value = undefined;
  setFieldValue('image', null);
};
</script>

<template>
  <Form
    ref="formRef"
    :validation-schema="validationSchema"
    :initial-values="initialValues"
    class="space-y-6"
  >
    <div class="grid gap-6">
      <!-- Update Image Upload -->
      <AdminBaseDropzone
        :is-multiple-allowed="false"
        :file-types="['image']"
        title="Course Image"
        :existing-files="primaryImage ? [primaryImage] : []"
        description="Upload the course image (JPEG, PNG or WebP)"
        :is-submitting="props.isSubmitting"
        @files-selected="(files: any) => handleImageUpload(files)"
        @file-removed="handleImageRemove"
      />
      <!-- Show error OF IMAGE -->
      <div v-if="errors.image" class="text-red-500 text-xs -mt-4">
        {{ errors.image }}
      </div>

      <FormField v-slot="{ field, errorMessage }" name="name">
        <FormItem>
          <FormLabel>Course Name</FormLabel>
          <FormControl>
            <Input
              v-bind="field"
              :model-value="field.value"
              placeholder="Enter course name"
              :disabled="props.isSubmitting"
              @update:model-value="
                (value: any) => setFieldValue('name', value as string)
              "
            />
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <div class="grid grid-cols-2 gap-4">
        <FormField v-slot="{ field, errorMessage }" name="type">
          <FormItem>
            <FormLabel>Course Type</FormLabel>
            <Select
              v-bind="field"
              :model-value="field.value"
              :disabled="props.isSubmitting"
              @update:model-value="
                (value: any) => setFieldValue('type', value as CourseType)
              "
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select course type" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem
                  v-for="type in COURSE_TYPES"
                  :key="type"
                  :value="type"
                >
                  {{ type.toUpperCase() }}
                </SelectItem>
              </SelectContent>
            </Select>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
        <FormField v-slot="{ field, errorMessage }" name="specialization">
          <FormItem>
            <FormLabel>Specialization (Optional)</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="field.value"
                placeholder="Enter specialization"
                :disabled="props.isSubmitting"
                @update:model-value="
                  (value: any) =>
                    setFieldValue('specialization', value as string)
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
      </div>

      <div class="grid grid-cols-2 gap-4">
        <FormField v-slot="{ field, errorMessage }" name="seatsCount">
          <FormItem>
            <FormLabel>Total Seats</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="String(field.value || '')"
                type="number"
                min="1"
                placeholder="Enter total seats"
                :disabled="props.isSubmitting"
                @update:model-value="
                  (value: any) => handleNumericInput(value, 'seatsCount')
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <FormField v-slot="{ field, errorMessage }" name="durationInMonths">
          <FormItem>
            <FormLabel>Duration (Months)</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="String(field.value || '')"
                type="number"
                min="1"
                placeholder="Enter duration in months"
                :disabled="props.isSubmitting"
                @update:model-value="
                  (value: any) => handleNumericInput(value, 'durationInMonths')
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
      </div>

      <div class="grid grid-cols-2 gap-4">
        <FormField v-slot="{ field, errorMessage }" name="semesterCount">
          <FormItem>
            <FormLabel>Number of Semesters</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="String(field.value || '')"
                type="number"
                min="1"
                placeholder="Enter number of semesters"
                :disabled="props.isSubmitting"
                @update:model-value="
                  (value: any) => handleNumericInput(value, 'semesterCount')
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
        <FormField v-slot="{ field, errorMessage }" name="title">
          <FormItem>
            <FormLabel>Dropdown Title</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="field.value"
                placeholder="Enter the title for dropdown"
                :disabled="props.isSubmitting"
                @update:model-value="
                  (value: any) => setFieldValue('title', value as string)
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
      </div>

      <FormField v-slot="{ field, errorMessage }" name="content">
        <FormItem>
          <FormLabel>Dropdown Content</FormLabel>
          <FormControl>
            <Textarea
              v-bind="field"
              :model-value="field.value"
              placeholder="Enter dropdown content"
              class="min-h-[100px]"
              :disabled="props.isSubmitting"
              @update:model-value="
                (value: any) => setFieldValue('content', value as string)
              "
            />
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- Syllabus Upload -->
      <AdminBaseDropzone
        :is-multiple-allowed="false"
        :file-types="['pdf']"
        title="Course Syllabus"
        :existing-files="primarySyllabus ? [primarySyllabus] : []"
        description="Upload the course syllabus in PDF format"
        :is-submitting="props.isSubmitting"
        @files-selected="(files: any) => handleSyllabusUpload(files)"
        @file-removed="handleSyllabusRemove"
      />
      <!-- Show error OF SYLLABUS -->
      <div v-if="errors.syllabus" class="text-red-500 text-xs -mt-4">
        {{ errors.syllabus }}
      </div>

      <AdminBaseDropzone
        :is-multiple-allowed="false"
        :file-types="['pdf']"
        title="Program Outcomes"
        :existing-files="primaryPos ? [primaryPos] : []"
        description="Upload the program outcomes document in PDF format"
        :is-submitting="props.isSubmitting"
        @files-selected="(files: any) => handlePosUpload(files)"
        @file-removed="handlePosRemove"
      />
      <!-- Show error OF POS -->
      <div v-if="errors.pos" class="text-red-500 text-xs -mt-4">
        {{ errors.pos }}
      </div>

      <!-- Link Button -->
      <AdminTemplateButton
        :show-button="
          showLinkButton && Boolean(values.linkButton?.title?.length)
        "
        :model-value="values.linkButton as typeof linkButtonSchema._type"
        :disabled="props.isSubmitting"
        @update:model-value="handleLinkButtonUpdate"
      />
      <div class="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          :disabled="props.isSubmitting"
          @click="handleCancel"
        >
          Cancel
        </Button>
        <Button
          type="button"
          :disabled="props.isSubmitting"
          @click="handleSubmit"
        >
          {{
            props.isSubmitting
              ? 'Saving...'
              : props.initialValues
                ? 'Update'
                : 'Create'
          }}
        </Button>
      </div>
    </div>
  </Form>
</template>
