<script setup lang="ts">
import type { StudentData } from '~/types/admin';
import type { FileData } from '~/types/home';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';

import { STUDENT_TYPES } from '~~/server/database/tables';
import { createStudentSchema } from '~~/shared/schema/department/student/create';
import { updateStudentSchema } from '~~/shared/schema/department/student/update';
import type { CreateStudentInput } from '~~/shared/schema/department/student/create';
import type { UpdateStudentInput } from '~~/shared/schema/department/student/update';
import Button from '~/components/ui/button/Button.vue';
import { toast } from 'vue-sonner';

const props = defineProps<{
  departmentId: number;
}>();

const {
  topperList,
  isLoadingTopper,
  courseN<PERSON>s,
  fetchCourseN<PERSON>s,
  fetchTopper<PERSON>ist,
  createTopper,
  updateTopper,
  deleteTopper,
} = useDepartment();

// Pagination
const currentPage = ref(1);
const itemsPerPage = ref(20);

// Fetch placements on component mount
onMounted(async () => {
  await fetchCourseNames(props.departmentId);
  await fetchTopperList(
    props.departmentId,
    currentPage.value,
    itemsPerPage.value
  );
});

// Modal state
const isModalOpen = ref(false);
const isSubmitting = ref(false);
const selectedTopper = ref<StudentData | null>(null);

// Delete dialog state
const showDeleteDialog = ref(false);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

// File upload handling
const primaryImage = ref<FileData | null | undefined>(null);

// Columns for the table
const columns = [
  { key: 'name', label: 'Name' },
  { key: 'passOutYear', label: 'Pass Out Year' },
  { key: 'mark', label: 'Mark' },
  { key: 'actions', label: 'Actions' },
];

// Open modal for editing
const handleEdit = (topper: StudentData) => {
  selectedTopper.value = topper;
  // Set image if available
  if (topper.image) {
    primaryImage.value = topper.image;
  } else {
    primaryImage.value = null;
  }

  isModalOpen.value = true;
};

// Open modal for adding
const handleAdd = () => {
  selectedTopper.value = null;
  primaryImage.value = undefined;
  isModalOpen.value = true;
};

// Form validation schema
const formSchema = computed(() => {
  return toTypedSchema(
    selectedTopper.value ? updateStudentSchema : createStudentSchema
  );
});
// Initial form values
const initialValues = computed(() => {
  if (selectedTopper.value) {
    return {
      name: selectedTopper.value.name,
      type: selectedTopper.value.type || STUDENT_TYPES[0],
      courseId: selectedTopper.value.courseId,
      startYear: selectedTopper.value.startYear,
      passOutYear: selectedTopper.value.passOutYear,
      mark: selectedTopper.value.mark,
      placedAt: selectedTopper.value.placedAt || '',
      image: null,
    };
  }

  return {
    name: '',
    type: STUDENT_TYPES[0],
    courseId: 0,
    startYear: new Date().getFullYear(),
    passOutYear: new Date().getFullYear() + 4,
    mark: 0,
    placedAt: '',
    image: undefined,
  };
});

// Form setup
const {
  setFieldValue,
  resetForm,
  errors,
  values: _values,
  handleSubmit: submitForm,
} = useForm<CreateStudentInput | UpdateStudentInput>({
  validationSchema: formSchema,
  initialValues: initialValues.value,
});

// Update form values when initialValues change
watch(initialValues, (newValues) => {
  resetForm({ values: newValues });
});

// Handle image remove
const handleImageRemove = () => {
  primaryImage.value = undefined;
  setFieldValue('image', undefined);
};

// Handle image upload
const handleImageUpload = async (files: { file: File; title?: string }[]) => {
  const file = files[0]?.file;
  if (!file) {
    primaryImage.value = undefined;
    setFieldValue('image', undefined);
    return;
  }

  try {
    const formData = new FormData();
    formData.append('files', file);
    formData.append('prefix', 'student');
    formData.append('title', 'Student Image');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', {
      method: 'POST',
      body: formData,
    });

    const fileData: FileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'student',
    };
    primaryImage.value = fileData;
    setFieldValue('image', fileData);
  } catch (err) {
    console.error('Failed to upload student image:', err);
  }
};

// Handle form submission
const handleSubmit = async () => {
  try {
    // Check if mark is empty
    if (!_values.mark) {
      toast.error('Mark is required');
      return;
    }

    isSubmitting.value = true;
    // Add image to values if available
    if (primaryImage.value) {
      _values.image = primaryImage.value;
    }
    if (selectedTopper.value) {
      await updateTopper(
        props.departmentId,
        selectedTopper.value.id,
        _values as UpdateStudentInput
      );
    } else {
      await createTopper(props.departmentId, _values as CreateStudentInput);
    }

    // Close modal and refresh data
    isModalOpen.value = false;
    await fetchTopperList(
      props.departmentId,
      currentPage.value,
      itemsPerPage.value
    );
  } catch (error) {
    console.error('Error saving topper:', error);
  } finally {
    isSubmitting.value = false;
  }
};

// Open delete confirmation dialog
const handleDeleteClick = (topper: StudentData) => {
  selectedTopper.value = topper;
  showDeleteDialog.value = true;
};

// Handle delete confirmation
const handleDeleteConfirm = async () => {
  if (!selectedTopper.value) return;

  deleteState.value = 'loading';
  try {
    await deleteTopper(props.departmentId, selectedTopper.value.id);
    deleteState.value = 'success';

    // Close dialog and refresh after a short delay
    setTimeout(async () => {
      showDeleteDialog.value = false;
      deleteState.value = 'idle';
      selectedTopper.value = null;
      await fetchTopperList(
        props.departmentId,
        currentPage.value,
        itemsPerPage.value
      );
    }, 1000);
  } catch (error) {
    console.error('Error deleting topper:', error);
    deleteState.value = 'error';
  }
};

// Handle pagination
const handlePageChange = async (page: number) => {
  currentPage.value = page;
  await fetchTopperList(
    props.departmentId,
    currentPage.value,
    itemsPerPage.value
  );
};

// Reset form when modal is closed
watch(isModalOpen, (newValue) => {
  if (!newValue) {
    resetForm();
    primaryImage.value = undefined;
  }
});

// Update form values when selectedTopper changes
watch(selectedTopper, (newValue) => {
  if (newValue) {
    resetForm({
      values: {
        name: newValue.name,
        type: newValue.type || STUDENT_TYPES[0],
        courseId: newValue.courseId,
        startYear: newValue.startYear,
        passOutYear: newValue.passOutYear,
        mark: newValue.mark,
        placedAt: newValue.placedAt || '',
      },
    });

    if (newValue.image) {
      primaryImage.value = newValue.image;
    }
  }
});

// Use computed property to stay in sync with values.courseId
const courseId = computed({
  get: () => String(_values.courseId || ''),
  set: (newValue: string) => {
    const id = Number(newValue);
    setFieldValue('courseId', id);
  },
});
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader
      class="px-2 pt-0 pb-4 flex flex-row items-center justify-between"
    >
      <CardTitle>Placements</CardTitle>
      <Button
        type="button"
        variant="default"
        size="sm"
        :disabled="isSubmitting"
        aria-label="Add placement"
        @click="handleAdd"
      >
        Add Topper
      </Button>
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <div v-if="isLoadingTopper" class="flex justify-center py-8">
        <div
          class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"
        ></div>
      </div>

      <div
        v-else-if="topperList && topperList.length === 0"
        class="text-center py-8 text-gray-500"
      >
        No toppers found. Click "Add Topper" to create one.
      </div>

      <Table v-else>
        <TableHeader>
          <TableRow>
            <TableHead v-for="column in columns" :key="column.key">
              {{ column.label }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="topper in topperList" :key="topper.id">
            <TableCell>{{ topper.name }}</TableCell>
            <TableCell>{{ topper.passOutYear }}</TableCell>
            <TableCell>{{ topper.mark }}</TableCell>
            <TableCell>
              <div class="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Edit topper"
                  @click="handleEdit(topper)"
                >
                  {{ isSubmitting ? 'Editing...' : 'Edit' }}
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Delete topper"
                  @click="handleDeleteClick(topper)"
                >
                  {{ isSubmitting ? 'Deleting...' : 'Delete' }}
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <!-- Pagination -->
      <div v-if="topperList.length > 0" class="flex justify-center mt-4">
        <div class="flex space-x-1">
          <Button
            type="button"
            variant="outline"
            size="sm"
            :disabled="currentPage === 1 || isSubmitting"
            aria-label="Previous page"
            @click="handlePageChange(currentPage - 1)"
          >
            Previous
          </Button>
          <Button
            type="button"
            variant="outline"
            size="sm"
            :disabled="
              currentPage >= topperList.length / itemsPerPage || isSubmitting
            "
            aria-label="Next page"
            @click="handlePageChange(currentPage + 1)"
          >
            Next
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>

  <!-- Add/Edit Placement Modal -->
  <Dialog :open="isModalOpen" @update:open="isModalOpen = $event">
    <DialogContent class="sm:max-w-3xl">
      <DialogHeader>
        <DialogTitle>{{ selectedTopper ? 'Edit' : 'Add' }} Topper</DialogTitle>
      </DialogHeader>

      <Form
        :validation-schema="formSchema"
        :initial-values="initialValues"
        class="space-y-4 mt-4"
        @submit="submitForm(handleSubmit)"
      >
        <!-- Student Image Upload -->
        <AdminBaseDropzone
          :is-multiple-allowed="false"
          :file-types="['image']"
          title="Student Image"
          :existing-files="primaryImage ? [primaryImage] : []"
          description="Upload the student image (JPEG, PNG or WebP)"
          :is-submitting="isSubmitting"
          @files-selected="handleImageUpload"
          @file-removed="handleImageRemove"
        />
        <p v-if="!primaryImage" class="text-xs !mt-1 text-red-500">
          No image selected
        </p>

        <FormField v-slot="{ field, errorMessage }" name="name">
          <FormItem>
            <FormLabel>Student Name</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="field.value"
                placeholder="Enter student name"
                :disabled="isSubmitting"
                @update:model-value="
                  (val: any) => setFieldValue('name', String(val))
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <div class="space-y-2">
          <Label>Course</Label>
          <div>
            <Select v-model="courseId" :disabled="isSubmitting">
              <SelectTrigger>
                <SelectValue placeholder="Select a course" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="course in courseNames"
                  :key="course.id"
                  :value="String(course.id)"
                >
                  {{ course.name }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <FormField v-slot="{ field, errorMessage }" name="startYear">
            <FormItem>
              <FormLabel>Start Year</FormLabel>
              <FormControl>
                <Input
                  v-bind="field"
                  :model-value="field.value"
                  type="number"
                  placeholder="Enter start year"
                  :disabled="isSubmitting"
                  @update:model-value="
                    setFieldValue('startYear', Number($event))
                  "
                />
              </FormControl>
              <FormMessage>{{ errorMessage }}</FormMessage>
            </FormItem>
          </FormField>

          <FormField v-slot="{ field, errorMessage }" name="passOutYear">
            <FormItem>
              <FormLabel>Pass Out Year</FormLabel>
              <FormControl>
                <Input
                  v-bind="field"
                  :model-value="field.value"
                  type="number"
                  placeholder="Enter pass out year"
                  :disabled="isSubmitting"
                  @update:model-value="
                    setFieldValue('passOutYear', Number($event))
                  "
                />
              </FormControl>
              <FormMessage>{{ errorMessage }}</FormMessage>
            </FormItem>
          </FormField>
        </div>

        <FormField v-slot="{ field, errorMessage }" name="mark">
          <FormItem>
            <FormLabel>Mark (%)</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="field.value"
                type="number"
                placeholder="Enter mark percentage"
                :disabled="isSubmitting"
                @update:model-value="setFieldValue('mark', Number($event))"
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            :disabled="isSubmitting"
            @click="isModalOpen = false"
          >
            Cancel
          </Button>
          <Button
            :disabled="
              isSubmitting || Object.keys(errors).length > 0 || !primaryImage
            "
            @click="handleSubmit"
          >
            {{
              isSubmitting ? 'Saving...' : selectedTopper ? 'Update' : 'Create'
            }}
          </Button>
        </DialogFooter>
      </Form>
    </DialogContent>
  </Dialog>

  <!-- Delete Confirmation Dialog -->
  <AdminMenuDeleteDialog
    v-if="selectedTopper"
    v-model:is-open="showDeleteDialog"
    :is-submitting="deleteState === 'loading'"
    :delete-state="deleteState"
    menu-title="topper"
    type="menu"
    :description="`Are you sure you want to delete topper for '${selectedTopper.name}'? This action cannot be undone.`"
    @confirm="handleDeleteConfirm"
  />
</template>
