<script setup lang="ts">
import type { QuestionBankData } from '~/types/admin';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import QuestionBankForm from './QuestionBankForm.vue';
import { toast } from 'vue-sonner';

const props = defineProps<{ departmentId: number }>();

// Add debugging logs

const {
  questionBankList,
  questionBankPagination,
  isLoadingQuestionBank,
  courseNames,
  fetchCourseNames,
  fetchQuestionBankList,
  createQuestionBank,
  updateQuestionBank,
  deleteQuestionBank,
} = useDepartment();

// Add watch to debug questionBankList changes
watch(
  questionBankList,
  (newValue) => {
    console.log('questionBankList updated:', newValue);
  },
  { immediate: true }
);

// Add watch to debug loading state
watch(
  isLoadingQuestionBank,
  (newValue) => {
    console.log('isLoadingQuestionBank updated:', newValue);
  },
  { immediate: true }
);

const columns = [
  { key: 'title', label: 'Title' },
  { key: 'year', label: 'Year' },
  { key: 'semester', label: 'Semester' },
  { key: 'actions', label: 'Actions' },
];

const isDialogOpen = ref(false);
const selectedQuestionBank = ref<QuestionBankData | null>(null);
const isSubmitting = ref(false);
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Add delete dialog state
const showDeleteDialog = ref(false);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

// Update onMounted to include error handling and debugging
onMounted(async () => {
  try {
    await fetchCourseNames(props.departmentId);
    await fetchQuestionBankList(
      props.departmentId,
      currentPage.value,
      itemsPerPage.value
    );
  } catch (error) {
    console.error('Error in onMounted:', error);
    toast.error('Failed to load question banks');
  }
});

const handleEdit = (questionBank: QuestionBankData) => {
  selectedQuestionBank.value = questionBank;
  isDialogOpen.value = true;
};

const handleDelete = async (questionBank: QuestionBankData) => {
  selectedQuestionBank.value = questionBank;
  showDeleteDialog.value = true;
};

const confirmDelete = async () => {
  if (!selectedQuestionBank.value) return;

  deleteState.value = 'loading';
  try {
    await deleteQuestionBank(props.departmentId, selectedQuestionBank.value.id);
    deleteState.value = 'success';

    // Close dialog and refresh after a short delay
    setTimeout(async () => {
      showDeleteDialog.value = false;
      deleteState.value = 'idle';
      selectedQuestionBank.value = null;
      await fetchQuestionBankList(
        props.departmentId,
        currentPage.value,
        itemsPerPage.value
      );
    }, 1000);
  } catch (error) {
    console.error('Error deleting question bank:', error);
    deleteState.value = 'error';
    toast.error('Failed to delete question bank');
  }
};

const handleAdd = () => {
  selectedQuestionBank.value = null;
  isDialogOpen.value = true;
};

const handleFormSubmit = async (values: any) => {
  try {
    isSubmitting.value = true;
    if (selectedQuestionBank.value) {
      await updateQuestionBank(
        props.departmentId,
        selectedQuestionBank.value.id,
        values
      );
      toast.success('Question bank updated successfully');
    } else {
      await createQuestionBank(props.departmentId, values);
      toast.success('Question bank created successfully');
    }
    isDialogOpen.value = false;
    await fetchQuestionBankList(
      props.departmentId,
      currentPage.value,
      itemsPerPage.value
    );
  } catch (error) {
    console.error('Error saving question bank:', error);
    toast.error('Failed to save question bank');
  } finally {
    isSubmitting.value = false;
  }
};

const handleFormCancel = () => {
  isDialogOpen.value = false;
};

const handleDownload = (questionBank: QuestionBankData) => {
  if (questionBank.file?.pathname) {
    window.open(`/api/files/${questionBank.file.pathname}`, '_blank');
  }
};

const handlePageChange = async (page: number) => {
  currentPage.value = page;
  await fetchQuestionBankList(props.departmentId, page, itemsPerPage.value);
};
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader
      class="px-2 pt-0 pb-4 flex flex-row items-center justify-between"
    >
      <CardTitle>Question Banks</CardTitle>
      <Button
        type="button"
        variant="default"
        size="sm"
        :disabled="isSubmitting"
        @click="handleAdd"
      >
        Add Question Bank
      </Button>
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <div v-if="isLoadingQuestionBank" class="flex justify-center py-8">
        Loading...
      </div>
      <Table v-else>
        <TableHeader>
          <TableRow>
            <TableHead v-for="column in columns" :key="column.key">
              {{ column.label }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-if="isLoadingQuestionBank">
            <TableCell colspan="4" class="text-center py-4">
              Loading question banks data...
            </TableCell>
          </TableRow>
          <TableRow v-else-if="!questionBankList.length">
            <TableCell colspan="4" class="text-center py-4">
              No question banks found.
            </TableCell>
          </TableRow>
          <TableRow
            v-for="questionBank in questionBankList"
            :key="questionBank.id"
          >
            <TableCell>{{ questionBank.title }}</TableCell>
            <TableCell>{{ questionBank.year }}</TableCell>
            <TableCell>{{ questionBank.semester }}</TableCell>
            <TableCell>
              <div class="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Download question bank"
                  @click="handleDownload(questionBank)"
                >
                  Download
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Edit question bank"
                  @click="handleEdit(questionBank)"
                >
                  {{ isSubmitting ? 'Editing...' : 'Edit' }}
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Delete question bank"
                  @click="handleDelete(questionBank)"
                >
                  {{ isSubmitting ? 'Deleting...' : 'Delete' }}
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <!-- Pagination -->
      <div
        v-if="questionBankPagination.totalPages > 1"
        class="mt-4 flex justify-center"
      >
        <div class="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            :disabled="!questionBankPagination.hasPreviousPage"
            @click="handlePageChange(currentPage - 1)"
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            :disabled="!questionBankPagination.hasNextPage"
            @click="handlePageChange(currentPage + 1)"
          >
            Next
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>

  <Dialog :open="isDialogOpen" @update:open="isDialogOpen = $event">
    <DialogContent class="sm:max-w-3xl">
      <DialogHeader>
        <DialogTitle>
          {{
            selectedQuestionBank
              ? 'Edit Question Bank'
              : 'Add New Question Bank'
          }}
        </DialogTitle>
      </DialogHeader>
      <QuestionBankForm
        :department-id="departmentId"
        :initial-values="selectedQuestionBank"
        :is-submitting="isSubmitting"
        :course-names="courseNames"
        @submit="handleFormSubmit"
        @cancel="handleFormCancel"
      />
    </DialogContent>
  </Dialog>

  <!-- Delete Confirmation Dialog -->
  <AdminMenuDeleteDialog
    v-if="selectedQuestionBank"
    v-model:is-open="showDeleteDialog"
    :is-submitting="deleteState === 'loading'"
    :delete-state="deleteState"
    menu-title="question bank"
    type="menu"
    :description="`Are you sure you want to delete question bank '${selectedQuestionBank.title}'? This action cannot be undone.`"
    @confirm="confirmDelete"
  />
</template>
