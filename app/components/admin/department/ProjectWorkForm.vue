<script setup lang="ts">
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import type { ProjectWorkData } from '~/types/admin';
import type { CreateProject } from '~~/shared/schema/department/files/create';
import type { UpdateProject } from '~~/shared/schema/department/files/update';
import { createProjectSchema } from '~~/shared/schema/department/files/create';
import { updateProjectSchema } from '~~/shared/schema/department/files/update';
import { toast } from 'vue-sonner';

const props = defineProps<{
  departmentId: number;
  initialValues?: ProjectWorkData | null;
  isSubmitting?: boolean;
}>();

const emit = defineEmits<{
  submit: [values: CreateProject | UpdateProject];
  cancel: [];
}>();

// Use appropriate schema based on whether we're editing or creating
const validationSchema = toTypedSchema(
  props.initialValues ? updateProjectSchema : createProjectSchema
);

const initialValues = computed(() => {
  return {
    type: 'project' as const,
    title: props.initialValues?.title ?? '',
    year: props.initialValues?.year ?? new Date().getFullYear(),
    courseId: props.initialValues?.course?.id ?? 1,
    semester: props.initialValues?.semester ?? 1,
    file: props.initialValues?.file
      ? {
          pathname: props.initialValues.file.pathname,
          title: props.initialValues.file.title,
          type: props.initialValues.file.type,
        }
      : undefined,
  };
});

const { values, errors, setFieldValue, resetForm } = useForm<
  CreateProject | UpdateProject
>({
  validationSchema,
  initialValues: initialValues.value,
});

const handleSubmit = () => {
  emit('submit', values);
};

const handleCancel = () => {
  resetForm();
  emit('cancel');
};

// Add type for file upload
interface FileUpload {
  file: File;
  title?: string;
}

const primaryFile = ref<
  | {
      pathname: string;
      title: string;
      type: string;
    }
  | undefined
>(
  props.initialValues?.file
    ? {
        pathname: props.initialValues.file.pathname,
        title: props.initialValues.file.title,
        type: props.initialValues.file.type,
      }
    : undefined
);

const handleFileUpload = async (files: FileUpload[]) => {
  const file = files[0]?.file;
  if (!file) {
    primaryFile.value = undefined;
    setFieldValue('file', undefined);
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'project');
    form.append('title', values.title || 'Project Work');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', {
      method: 'POST',
      body: form,
    });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
    };
    primaryFile.value = fileData;
    setFieldValue('file', fileData);
  } catch (err) {
    console.error('Failed to upload file:', err);
    toast.error('Failed to upload file');
  }
};

const handleFileRemove = () => {
  primaryFile.value = undefined;
  setFieldValue('file', undefined);
};

const handleNumericInput = (
  value: string | number | null,
  field: 'year' | 'semester'
) => {
  const numValue = value === null || value === '' ? 0 : Number(value);
  setFieldValue(field, numValue);
};
</script>

<template>
  <Form
    :validation-schema="validationSchema"
    :initial-values="initialValues"
    class="space-y-6"
  >
    <div class="grid gap-6">
      <FormField v-slot="{ field, errorMessage }" name="title">
        <FormItem>
          <FormLabel>Title</FormLabel>
          <!-- {{ values }} -->
          <FormControl>
            <Input
              v-bind="field"
              :model-value="field.value"
              placeholder="Enter project work title"
              :disabled="props.isSubmitting"
              @update:model-value="
                (value) => setFieldValue('title', value as string)
              "
            />
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <div class="grid grid-cols-2 gap-4">
        <FormField v-slot="{ field, errorMessage }" name="year">
          <FormItem>
            <FormLabel>Year</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="String(field.value || '')"
                type="number"
                min="2000"
                :max="new Date().getFullYear() + 1"
                placeholder="Enter year"
                :disabled="props.isSubmitting"
                @update:model-value="
                  (value) => handleNumericInput(value, 'year')
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <FormField v-slot="{ field, errorMessage }" name="semester">
          <FormItem>
            <FormLabel>Semester</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="String(field.value || '')"
                type="number"
                min="1"
                max="8"
                placeholder="Enter semester"
                :disabled="props.isSubmitting"
                @update:model-value="
                  (value) => handleNumericInput(value, 'semester')
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
      </div>

      <!-- File Upload -->
      <AdminBaseDropzone
        :is-multiple-allowed="false"
        :file-types="['pdf']"
        title="Project Work File"
        :existing-files="primaryFile ? [primaryFile] : []"
        description="Upload the project work file in PDF format"
        :is-submitting="props.isSubmitting"
        @files-selected="(files) => handleFileUpload(files)"
        @file-removed="handleFileRemove"
      />
      <!-- Show error of file -->
      <div v-if="errors.file" class="text-red-500 text-xs -mt-4">
        {{ errors.file }}
      </div>

      <div class="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          :disabled="props.isSubmitting"
          @click="handleCancel"
        >
          Cancel
        </Button>
        <Button
          :disabled="props.isSubmitting || Object.keys(errors).length > 0"
          @click="handleSubmit"
        >
          {{
            props.isSubmitting
              ? 'Saving...'
              : props.initialValues
                ? 'Update'
                : 'Create'
          }}
        </Button>
      </div>
    </div>
  </Form>
</template>
