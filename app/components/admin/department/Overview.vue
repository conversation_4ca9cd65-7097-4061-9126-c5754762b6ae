<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Save, SquarePen } from 'lucide-vue-next';

const props = defineProps<{ departmentId: number }>();
const emit = defineEmits<{ (e: 'update'): void }>();

const isEditing = ref(false);
const originalOverview = ref('');

const { overview, isLoadingOverview, fetchOverview, updateOverview } =
  useDepartment();

// Fetch overview when component mounts
onMounted(() => {
  fetchOverview(props.departmentId);
});

const handleEdit = () => {
  originalOverview.value = overview.value;
  isEditing.value = true;
};

const handleCancel = () => {
  overview.value = originalOverview.value;
  isEditing.value = false;
};

const handleSave = async () => {
  if (!props.departmentId) return;

  try {
    await updateOverview(props.departmentId, overview.value);
    isEditing.value = false;
    emit('update');
  } catch (error) {
    console.error('Error updating overview:', error);
  }
};
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader class="px-2 pt-0 pb-4">
      <CardTitle class="flex justify-between items-center gap-2"
        >Department Overview
        <template v-if="!isEditing">
          <Button
            variant="outline"
            :disabled="isLoadingOverview"
            aria-label="Edit overview"
            @click="handleEdit"
          >
            <SquarePen class="w-4 h-4" />
            Edit
          </Button>
        </template></CardTitle
      >
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <div class="space-y-4">
        <AdminBaseTextEditor
          v-model="overview"
          class="w-full min-h-[200px] p-4 rounded-md border border-gray-200 focus:border-primary focus:ring-1 focus:ring-primary"
          placeholder="Enter department overview..."
          :disabled="!isEditing"
          :aria-label="
            isEditing ? 'Edit department overview' : 'Department overview'
          "
        />
        <div class="flex gap-3">
          <template v-if="isEditing">
            <Button
              :disabled="isLoadingOverview || overview === originalOverview"
              :aria-label="
                isLoadingOverview ? 'Saving overview...' : 'Save overview'
              "
              @click="handleSave"
            >
              <Save class="w-4 h-4" />
              {{ isLoadingOverview ? 'Saving...' : 'Save' }}
            </Button>
            <Button
              variant="outline"
              :disabled="isLoadingOverview"
              aria-label="Cancel editing"
              @click="handleCancel"
            >
              Cancel
            </Button>
          </template>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
