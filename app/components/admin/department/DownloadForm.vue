<script setup lang="ts">
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import type { DownloadData } from '~/types/admin';
import type { CreateDownload } from '~~/shared/schema/department/files/create';
import type { UpdateDownload } from '~~/shared/schema/department/files/update';
import { createDownloadSchema } from '~~/shared/schema/department/files/create';
import { updateDownloadSchema } from '~~/shared/schema/department/files/update';
import { toast } from 'vue-sonner';

const props = defineProps<{
  departmentId: number;
  initialValues?: DownloadData | null;
  isSubmitting?: boolean;
}>();

const emit = defineEmits<{
  submit: [values: CreateDownload | UpdateDownload];
  cancel: [];
}>();

// Use appropriate schema based on whether we're editing or creating
const validationSchema = toTypedSchema(
  props.initialValues ? updateDownloadSchema : createDownloadSchema
);

const initialValues = computed(() => {
  return {
    type: 'download' as const,
    title: props.initialValues?.title ?? '',
    file: props.initialValues?.file
      ? {
          pathname: props.initialValues.file.pathname,
          title: props.initialValues.file.title,
          type: props.initialValues.file.type,
        }
      : undefined,
  };
});

const { values, errors, setFieldValue, resetForm } = useForm<
  CreateDownload | UpdateDownload
>({
  validationSchema,
  initialValues: initialValues.value,
});

const handleSubmit = () => {
  emit('submit', values);
};

const handleCancel = () => {
  resetForm();
  emit('cancel');
};

// Add type for file upload
interface FileUpload {
  file: File;
  title?: string;
}

const primaryFile = ref<
  | {
      pathname: string;
      title: string;
      type: string;
    }
  | undefined
>(
  props.initialValues?.file
    ? {
        pathname: props.initialValues.file.pathname,
        title: props.initialValues.file.title,
        type: props.initialValues.file.type,
      }
    : undefined
);

const handleFileUpload = async (files: FileUpload[]) => {
  const file = files[0]?.file;
  if (!file) {
    primaryFile.value = undefined;
    setFieldValue('file', undefined);
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'download');
    form.append('title', values.title || 'Download');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', {
      method: 'POST',
      body: form,
    });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
    };
    primaryFile.value = fileData;
    setFieldValue('file', fileData);
  } catch (err) {
    console.error('Failed to upload file:', err);
    toast.error('Failed to upload file');
  }
};

const handleFileRemove = () => {
  primaryFile.value = undefined;
  setFieldValue('file', undefined);
};
</script>

<template>
  <Form
    :validation-schema="validationSchema"
    :initial-values="initialValues"
    class="space-y-6"
  >
    <div class="grid gap-6">
      <FormField v-slot="{ field, errorMessage }" name="title">
        <FormItem>
          <FormLabel>Title</FormLabel>
          <FormControl>
            <Input
              v-bind="field"
              :model-value="field.value"
              placeholder="Enter download title"
              :disabled="props.isSubmitting"
              @update:model-value="
                (value) => setFieldValue('title', value as string)
              "
            />
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- File Upload -->
      <AdminBaseDropzone
        :is-multiple-allowed="false"
        :file-types="['pdf']"
        title="Question Bank File"
        :existing-files="primaryFile ? [primaryFile] : []"
        description="Upload the question bank file in PDF format"
        :is-submitting="props.isSubmitting"
        @files-selected="(files) => handleFileUpload(files)"
        @file-removed="handleFileRemove"
      />
      <!-- Show error of file -->
      <div v-if="errors.file" class="text-red-500 text-xs -mt-4">
        {{ errors.file }}
      </div>

      <div class="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          :disabled="props.isSubmitting"
          @click="handleCancel"
        >
          Cancel
        </Button>
        <Button
          :disabled="props.isSubmitting || Object.keys(errors).length > 0"
          @click="handleSubmit"
        >
          {{
            props.isSubmitting
              ? 'Saving...'
              : props.initialValues
                ? 'Update'
                : 'Create'
          }}
        </Button>
      </div>
    </div>
  </Form>
</template>
