<script setup lang="ts">
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import CourseForm from './CourseForm.vue';
import type { CourseListData } from '~/types/admin';
import { toast } from 'vue-sonner';

const props = defineProps<{ departmentId: number }>();

const {
  courseList,
  isLoadingCourse,
  fetchCourseList,
  createCourse,
  updateCourse,
  deleteCourse,
} = useDepartment();

const columns = [
  { key: 'name', label: 'Name' },
  { key: 'title', label: 'Title' },
  { key: 'specialization', label: 'Specialization' },
  { key: 'durationInMonths', label: 'Duration (Months)' },
  { key: 'semesterCount', label: 'Semesters' },
  { key: 'seatsCount', label: 'Total Seats' },
  { key: 'actions', label: 'Actions' },
];

const isDialogOpen = ref(false);
const selectedCourse = ref<CourseListData | null>(null);
const isSubmitting = ref(false);

// Add delete dialog state
const showDeleteDialog = ref(false);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

// Load courses when component mounts
onMounted(async () => {
  await fetchCourseList(props.departmentId);
});

const handleEdit = (course: CourseListData) => {
  selectedCourse.value = course;
  isDialogOpen.value = true;
};

const handleDelete = async (course: CourseListData) => {
  selectedCourse.value = course;
  showDeleteDialog.value = true;
};

const confirmDelete = async () => {
  if (!selectedCourse.value) return;

  deleteState.value = 'loading';
  try {
    await deleteCourse(props.departmentId, selectedCourse.value.id);
    deleteState.value = 'success';

    // Close dialog and refresh after a short delay
    setTimeout(async () => {
      showDeleteDialog.value = false;
      deleteState.value = 'idle';
      selectedCourse.value = null;
      await fetchCourseList(props.departmentId);
      toast.success('Course deleted successfully');
    }, 1000);
  } catch (error) {
    console.error('Error deleting course:', error);
    deleteState.value = 'error';
    toast.error('Failed to delete course');
  }
};

const handleAdd = () => {
  selectedCourse.value = null;
  isDialogOpen.value = true;
};

const handleFormSubmit = async (values: any) => {
  try {
    isSubmitting.value = true;
    if (selectedCourse.value) {
      await updateCourse(props.departmentId, selectedCourse.value.id, values);
      toast.success('Course updated successfully');
    } else {
      await createCourse(props.departmentId, values);
      toast.success('Course created successfully');
    }
    isDialogOpen.value = false;
    await fetchCourseList(props.departmentId);
  } catch (error) {
    console.error('Error saving course:', error);
    toast.error('Failed to save course');
  } finally {
    isSubmitting.value = false;
  }
};

const handleFormCancel = () => {
  isDialogOpen.value = false;
};
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader
      class="px-2 pt-0 pb-4 flex flex-row items-center justify-between"
    >
      <CardTitle>Courses</CardTitle>
      <Button
        type="button"
        variant="default"
        size="sm"
        :disabled="isSubmitting"
        @click="handleAdd"
      >
        Add Course
      </Button>
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <div v-if="isLoadingCourse" class="flex justify-center py-8">
        Loading...
      </div>
      <Table v-else>
        <TableHeader>
          <TableRow>
            <TableHead v-for="column in columns" :key="column.key">
              {{ column.label }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-if="isLoadingCourse">
            <TableCell colspan="6" class="text-center py-4">
              Loading courses data...
            </TableCell>
          </TableRow>
          <TableRow v-else-if="!courseList.length">
            <TableCell colspan="6" class="text-center py-4">
              No courses found.
            </TableCell>
          </TableRow>
          <TableRow v-for="course in courseList" :key="course.id">
            <TableCell>{{ course.name }}</TableCell>
            <TableCell>{{ course.title }}</TableCell>
            <TableCell>{{ course.specialization || '-' }}</TableCell>
            <TableCell>{{ course.durationInMonths }}</TableCell>
            <TableCell>{{ course.semesterCount }}</TableCell>
            <TableCell>{{ course.seatsCount }}</TableCell>
            <TableCell>
              <div class="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Edit course"
                  @click="handleEdit(course)"
                >
                  {{ isSubmitting ? 'Editing...' : 'Edit' }}
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Delete course"
                  @click="handleDelete(course)"
                >
                  {{ isSubmitting ? 'Deleting...' : 'Delete' }}
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </CardContent>
  </Card>

  <Dialog :open="isDialogOpen" @update:open="isDialogOpen = $event">
    <DialogContent class="sm:max-w-3xl">
      <DialogHeader>
        <DialogTitle>
          {{ selectedCourse ? 'Edit Course' : 'Add New Course' }}
        </DialogTitle>
      </DialogHeader>
      <CourseForm
        :department-id="departmentId"
        :initial-values="selectedCourse"
        :is-submitting="isSubmitting"
        @submit="handleFormSubmit"
        @cancel="handleFormCancel"
      />
    </DialogContent>
  </Dialog>

  <!-- Delete Confirmation Dialog -->
  <AdminMenuDeleteDialog
    v-if="selectedCourse"
    v-model:is-open="showDeleteDialog"
    :is-submitting="deleteState === 'loading'"
    :delete-state="deleteState"
    menu-title="course"
    type="menu"
    :description="`Are you sure you want to delete course '${selectedCourse.name}'? This action cannot be undone.  All related records will be deleted as well.`"
    @confirm="confirmDelete"
  />
</template>
