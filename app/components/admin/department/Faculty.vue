<script setup lang="ts">
import type { FacultyListData } from '~/types/admin';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { toast } from 'vue-sonner';

const props = defineProps<{
  departmentId: number;
}>();

const {
  facultyList,
  isLoadingFaculty,
  fetchFacultyList,
  deleteFaculty,
  updateFaculty,
  createFaculty,
} = useDepartment();

// Fetch faculty list on mount
onMounted(async () => {
  await fetchFacultyList(props.departmentId);
});

const columns = [
  { key: 'priority', label: 'Priority' },
  { key: 'name', label: 'Name' },
  { key: 'designation', label: 'Designation' },
  { key: 'education', label: 'Education' },
  { key: 'experience', label: 'Experience' },
  { key: 'areaOfInterest', label: 'Area of Interest' },
  { key: 'actions', label: 'Actions' },
];

// Dialog states
const showAddDialog = ref(false);
const showEditDialog = ref(false);
const showDeleteDialog = ref(false);
const selectedFaculty = ref<FacultyListData | null>(null);
const isSubmitting = ref(false);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

const handleEdit = (faculty: FacultyListData) => {
  selectedFaculty.value = faculty;
  showEditDialog.value = true;
};

const handleDelete = (faculty: FacultyListData) => {
  selectedFaculty.value = faculty;
  showDeleteDialog.value = true;
};

const handleAdd = () => {
  selectedFaculty.value = null;
  showAddDialog.value = true;
};

const confirmDelete = async () => {
  if (!selectedFaculty.value) return;

  deleteState.value = 'loading';
  try {
    await deleteFaculty(props.departmentId, selectedFaculty.value.id);
    deleteState.value = 'success';

    // Close dialog and refresh after a short delay
    setTimeout(async () => {
      showDeleteDialog.value = false;
      deleteState.value = 'idle';
      selectedFaculty.value = null;
      await fetchFacultyList(props.departmentId);
    }, 1000);
  } catch (error) {
    console.error('Error deleting faculty:', error);
    deleteState.value = 'error';
  }
};

const handleSubmit = async (values: any) => {
  // Check if name is empty
  if (!values.name) {
    toast.error('Name is required');
    return;
  }

  // Check if designation is empty
  if (!values.designation) {
    toast.error('Designation is required');
    return;
  }

  // Check if education has degree, university, year
  for (const education of values.education) {
    if (!education.degree || !education.university || !education.passOutYear) {
      toast.error('Check if education has degree, university, year');
      return;
    }
  }

  // Check if experience is empty
  for (const experience of values.experience) {
    if (
      !experience.organization ||
      !experience.startYear ||
      !experience.designation
    ) {
      toast.error('Check if experience has organization, year, designation');
      return;
    }
  }

  // Check if area of interest is empty
  for (const area of values.areaOfInterest) {
    if (!area?.length) {
      toast.error('Check if area of interest has at least one item');
      return;
    }
  }

  // Check if image is empty
  if (!values.image?.pathname) {
    toast.error('Image is required');
    return;
  }

  isSubmitting.value = true;
  try {
    if (selectedFaculty.value) {
      // Edit mode
      await updateFaculty(props.departmentId, selectedFaculty.value.id, values);
    } else {
      // Add mode
      await createFaculty(props.departmentId, values);
    }

    showAddDialog.value = false;
    showEditDialog.value = false;
    await fetchFacultyList(props.departmentId);
  } catch (error) {
    console.error('Error saving faculty:', error);
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader
      class="px-2 pt-0 pb-4 flex flex-row items-center justify-between"
    >
      <CardTitle>Faculty</CardTitle>
      <Button @click="handleAdd">Add Faculty</Button>
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead v-for="column in columns" :key="column.key">
              {{ column.label }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-if="isLoadingFaculty">
            <TableCell colspan="7" class="text-center py-4">
              Loading faculty data...
            </TableCell>
          </TableRow>
          <TableRow v-else-if="!facultyList.length">
            <TableCell colspan="7" class="text-center py-4">
              No faculty members found.
            </TableCell>
          </TableRow>
          <TableRow v-for="member in facultyList" :key="member.id">
            <TableCell>{{ member.name }}</TableCell>
            <TableCell>{{ member.designation }}</TableCell>
            <TableCell>{{
              member.education.map((e) => e.degree).join(', ')
            }}</TableCell>
            <TableCell>{{
              member.experience.map((e) => e.organization).join(', ')
            }}</TableCell>
            <TableCell>{{ member.areaOfInterest.join(', ') }}</TableCell>
            <TableCell>{{ member.priority || 0 }}</TableCell>
            <TableCell>
              <div class="flex space-x-2">
                <Button variant="outline" size="sm" @click="handleEdit(member)">
                  Edit
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  @click="handleDelete(member)"
                >
                  Delete
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </CardContent>
  </Card>

  <!-- Add Faculty Dialog -->
  <Dialog :open="showAddDialog" @update:open="showAddDialog = false">
    <DialogContent class="sm:max-w-3xl">
      <DialogHeader>
        <DialogTitle>Add Faculty</DialogTitle>
        <DialogDescription>
          Add a new faculty member to the department.
        </DialogDescription>
      </DialogHeader>
      <AdminDepartmentFacultyForm
        :is-submitting="isSubmitting"
        @submit="handleSubmit"
        @cancel="showAddDialog = false"
      />
    </DialogContent>
  </Dialog>

  <!-- Edit Faculty Dialog -->
  <Dialog :open="showEditDialog" @update:open="showEditDialog = false">
    <DialogContent class="sm:max-w-3xl">
      <DialogHeader>
        <DialogTitle>Edit Faculty</DialogTitle>
        <DialogDescription>
          Update faculty member information.
        </DialogDescription>
      </DialogHeader>
      <AdminDepartmentFacultyForm
        v-if="selectedFaculty"
        :is-submitting="isSubmitting"
        :initial-values="selectedFaculty"
        @submit="handleSubmit"
        @cancel="showEditDialog = false"
      />
    </DialogContent>
  </Dialog>

  <!-- Delete Confirmation Dialog -->
  <AdminMenuDeleteDialog
    v-if="selectedFaculty"
    v-model:is-open="showDeleteDialog"
    :is-submitting="deleteState === 'loading'"
    :delete-state="deleteState"
    :menu-title="selectedFaculty.name"
    type="menu"
    :description="`Are you sure you want to delete faculty member '${selectedFaculty.name}'? This action cannot be undone.`"
    @confirm="confirmDelete"
  />
</template>
