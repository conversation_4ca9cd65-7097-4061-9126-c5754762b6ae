<script setup lang="ts">
import {
  Camera,
  Trash2,
  PlusCircle,
  ImageIcon,
  Check,
  Search,
} from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { toast } from 'vue-sonner';
import type { Album } from '~~/shared/schema/album/get';

const props = defineProps<{
  departmentId: number;
}>();

const emit = defineEmits<{
  update: [];
}>();

const { galleryList, isLoadingGallery, fetchGalleryList, updateGallery } =
  useDepartment();

const showAttachDialog = ref(false);
const availableGalleries = ref<Album[]>([]);
const isLoadingAvailableGalleries = ref(false);
const searchQuery = ref('');
const selectedGallery = ref<Album | null>(null);

const filteredGalleries = computed(() => {
  if (!searchQuery.value) return availableGalleries.value;
  const query = searchQuery.value.toLowerCase();
  return availableGalleries.value.filter((gallery) =>
    gallery.title.toLowerCase().includes(query)
  );
});

const handleAttach = async () => {
  if (!selectedGallery.value) return;

  try {
    const updatedIds = [
      ...(galleryList.value?.map((g) => g.id) || []),
      Number(selectedGallery.value.id),
    ];
    await updateGallery(props.departmentId, updatedIds);
    await fetchGalleryList(props.departmentId);
    showAttachDialog.value = false;
    selectedGallery.value = null;
    searchQuery.value = '';
    emit('update');
  } catch (error) {
    console.error('Error attaching gallery:', error);
    toast.error('Failed to attach gallery');
  }
};

const handleDetach = async (galleryId: number) => {
  try {
    const updatedIds =
      galleryList.value?.filter((g) => g.id !== galleryId).map((g) => g.id) ||
      [];
    await updateGallery(props.departmentId, updatedIds);
    await fetchGalleryList(props.departmentId);
    emit('update');
  } catch (error) {
    console.error('Error detaching gallery:', error);
    toast.error('Failed to detach gallery');
  }
};

const openAttachDialog = async () => {
  try {
    isLoadingAvailableGalleries.value = true;
    const response = await $fetch<{ success: boolean; data: Album[] }>(
      '/api/admin/album'
    );

    if (response.success) {
      const currentIds = new Set(galleryList.value?.map((g) => g.id));
      availableGalleries.value = response.data.filter(
        (gallery) => !currentIds.has(Number(gallery.id))
      );
    }
    showAttachDialog.value = true;
  } catch (error) {
    console.error('Error fetching available galleries:', error);
    toast.error('Failed to fetch available galleries');
  } finally {
    isLoadingAvailableGalleries.value = false;
  }
};

onMounted(async () => {
  await fetchGalleryList(props.departmentId);
});
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader class="px-2 pt-0 pb-4">
      <div class="flex items-center justify-between">
        <div>
          <CardTitle>Gallery</CardTitle>
          <CardDescription>
            Manage photo galleries for this department
          </CardDescription>
        </div>
        <Button @click="openAttachDialog">
          <PlusCircle class="h-4 w-4 mr-2" />
          Attach Gallery
        </Button>
      </div>
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <div class="space-y-6">
        <!-- Loading State -->
        <div v-if="isLoadingGallery" class="flex justify-center py-8">
          <div
            class="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"
          ></div>
        </div>

        <!-- Empty State -->
        <div
          v-else-if="!galleryList?.length"
          class="flex flex-col items-center justify-center py-16 border-2 border-dashed rounded-lg"
        >
          <div class="rounded-full bg-primary/10 p-4 mb-4">
            <Camera class="h-8 w-8 text-primary" />
          </div>
          <h3 class="text-lg font-semibold mb-2">No Galleries Attached</h3>
          <p class="text-sm text-muted-foreground max-w-sm text-center">
            Attach galleries to display them in the department page. Click the
            "Attach Gallery" button above.
          </p>
        </div>

        <!-- Gallery Grid -->
        <div
          v-else
          class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
        >
          <Card
            v-for="gallery in galleryList"
            :key="gallery.id"
            class="group relative overflow-hidden border-none bg-background shadow-sm hover:shadow-md transition-all duration-300"
          >
            <!-- Gallery Cover -->
            <div class="aspect-square bg-muted relative">
              <div v-if="gallery.primaryImage" class="w-full h-full">
                <img
                  :src="getPreviewUrl(gallery.primaryImage.pathname)"
                  :alt="gallery.title"
                  class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
              </div>
              <div
                v-else
                class="w-full h-full flex flex-col items-center justify-center bg-muted/50"
              >
                <ImageIcon class="h-10 w-10 text-muted-foreground/50" />
                <span class="text-xs text-muted-foreground mt-2"
                  >No images</span
                >
              </div>

              <!-- Overlay Content -->
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              />
              <div
                class="absolute inset-x-0 bottom-0 p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20"
              >
                <div class="flex items-center justify-between">
                  <div class="max-w-[85%]">
                    <h3 class="font-semibold text-white truncate">
                      {{ gallery.title }}
                    </h3>
                    <p class="text-sm text-white/80">
                      {{ gallery.count || 0 }} photos
                    </p>
                  </div>
                  <Button
                    variant="secondary"
                    size="icon"
                    class="p-2 bg-white/20 hover:bg-white/30 backdrop-blur-sm"
                    @click="handleDetach(gallery.id)"
                  >
                    <Trash2 class="h-4 w-4 text-white" />
                  </Button>
                </div>
              </div>
            </div>

            <!-- Mobile/SEO Friendly Content -->
            <CardContent class="p-4 sm:hidden">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="font-semibold truncate">{{ gallery.title }}</h3>
                  <p class="text-sm text-muted-foreground">
                    {{ gallery.count || 0 }} photos
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  class="h-8 w-8 text-destructive"
                  @click="handleDetach(gallery.id)"
                >
                  <Trash2 class="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </CardContent>
  </Card>

  <!-- Attach Gallery Dialog -->
  <Dialog :open="showAttachDialog" @update:open="showAttachDialog = false">
    <DialogContent class="sm:max-w-[900px]">
      <DialogHeader>
        <DialogTitle>Attach Gallery</DialogTitle>
        <DialogDescription>
          Select a gallery to attach to this department. You can search
          galleries by title.
        </DialogDescription>
      </DialogHeader>

      <!-- Search Input -->
      <div class="relative">
        <Search
          class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"
        />
        <Input
          v-model="searchQuery"
          class="pl-9"
          placeholder="Search galleries by title..."
        />
      </div>
      <div class="grid gap-4 py-4 max-h-[500px] overflow-y-auto">
        <!-- Loading State -->
        <div
          v-if="isLoadingAvailableGalleries"
          class="flex justify-center py-8"
        >
          <div
            class="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"
          ></div>
        </div>

        <!-- Empty State -->
        <div
          v-else-if="!filteredGalleries.length"
          class="flex flex-col items-center justify-center py-8 text-center"
        >
          <div class="rounded-full bg-muted p-3 mb-4">
            <ImageIcon class="h-6 w-6 text-muted-foreground" />
          </div>
          <h3 class="font-semibold mb-1">No Galleries Available</h3>
          <p class="text-sm text-muted-foreground">
            {{
              searchQuery
                ? 'No galleries match your search.'
                : 'Create new galleries in the gallery management section.'
            }}
          </p>
        </div>

        <!-- Gallery Grid -->
        <div
          v-else
          class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 px-4"
        >
          <Card
            v-for="gallery in filteredGalleries"
            :key="gallery.id"
            class="group relative overflow-hidden border-none bg-background shadow-sm hover:shadow-md transition-all duration-300"
            :class="{
              'ring-2 ring-primary ring-offset-2':
                selectedGallery?.id === gallery.id,
              'hover:ring-2 hover:ring-primary/50 hover:ring-offset-2':
                selectedGallery?.id !== gallery.id,
            }"
            @click="selectedGallery = gallery"
          >
            <!-- Gallery Cover -->
            <div class="aspect-square bg-muted relative">
              <div v-if="gallery.primaryImage" class="w-full h-full">
                <img
                  :src="getPreviewUrl(gallery.primaryImage.pathname)"
                  :alt="gallery.title"
                  class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
              </div>
              <div
                v-else
                class="w-full h-full flex flex-col items-center justify-center bg-muted/50"
              >
                <ImageIcon class="h-10 w-10 text-muted-foreground/50" />
                <span class="text-xs text-muted-foreground mt-2"
                  >No images</span
                >
              </div>

              <!-- Selection Indicator -->
              <div
                v-if="selectedGallery?.id === gallery.id"
                class="absolute top-2 right-2 h-6 w-6 bg-primary rounded-full flex items-center justify-center"
              >
                <Check class="h-4 w-4 text-primary-foreground" />
              </div>
            </div>

            <CardContent class="p-4">
              <h3 class="font-semibold truncate mb-1">{{ gallery.title }}</h3>
              <p class="text-sm text-muted-foreground">
                {{ gallery.count || 0 }} photos
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="showAttachDialog = false">
          Cancel
        </Button>
        <Button :disabled="!selectedGallery" @click="handleAttach">
          Attach Selected Gallery
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<style scoped>
/* Optional: Add a subtle scaling effect on hover */
.group:hover .card {
  transform: translateY(-2px);
}
</style>
