<script setup lang="ts">
import type { FacultyListData } from '~/types/admin';
import type { FileData } from '~/types/home';
import { PlusCircle, MinusCircle } from 'lucide-vue-next';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import type * as z from 'zod';
import { facultyUpdateTemplateSchema } from '~~/shared/schema/department/faculty/update';
import { facultySchema } from '~~/shared/schema/department/faculty/create';
import { toast } from 'vue-sonner';

const props = defineProps<{
  isSubmitting?: boolean;
  initialValues?: Partial<FacultyListData>;
  mode?: 'create' | 'update';
}>();

const emit = defineEmits<{
  (e: 'submit', values: any): void;
  (e: 'cancel'): void;
}>();

// Form Schema based on mode
const formSchema = toTypedSchema(
  props.mode === 'create' ? facultySchema : facultyUpdateTemplateSchema
);

// Add proper type for form values
type FormValues = z.infer<typeof facultyUpdateTemplateSchema>;

const resumeFile = ref<FileData | undefined>(
  props.initialValues?.resume && props.initialValues.resume.pathname
    ? {
        type: props.initialValues.resume.type,
        pathname: props.initialValues.resume.pathname,
        title: props.initialValues.resume.title,
        prefix: props.initialValues.resume?.prefix ?? 'resume',
      }
    : undefined
);

const imageFile = ref<FileData | undefined>(
  props.initialValues?.image
    ? {
        type: props.initialValues.image.type,
        pathname: props.initialValues.image.pathname,
        title: props.initialValues.image.title,
        prefix: props.initialValues.image?.prefix ?? 'image',
      }
    : undefined
);

const { values, setFieldValue, resetForm } = useForm<FormValues>({
  validationSchema: formSchema,
  initialValues: {
    name: props.initialValues?.name ?? '',
    designation: props.initialValues?.designation ?? '',
    priority: props.initialValues?.priority ?? 0,
    startDate: props.initialValues?.startDate
      ? new Date(props.initialValues.startDate)
      : new Date(),
    endDate: props.initialValues?.endDate
      ? new Date(props.initialValues.endDate)
      : null,
    areaOfInterest: props.initialValues?.areaOfInterest ?? [''],
    education: props.initialValues?.education ?? [
      { degree: '', university: '', passOutYear: new Date().getFullYear() },
    ],
    experience: props.initialValues?.experience ?? [
      {
        startYear: new Date().getFullYear(),
        organization: '',
        designation: '',
      },
    ],
  },
});

// Add type safety for form values
const formValues = computed(() => ({
  ...values,
  education: values.education ?? [],
  experience: values.experience ?? [],
  areaOfInterest: values.areaOfInterest ?? [],
}));

const onSubmit = () => {
  // Only include resume and image if they have changed from initial value
  const hasResumeChanged =
    JSON.stringify(resumeFile.value) !==
    JSON.stringify(props.initialValues?.resume);
  const hasImageChanged =
    JSON.stringify(imageFile.value) !==
    JSON.stringify(props.initialValues?.image);

  const submissionValues = {
    ...values,
    resume: hasResumeChanged ? resumeFile.value : undefined,
    image: hasImageChanged ? imageFile.value : undefined,
  };
  emit('submit', submissionValues);
};

const onCancel = () => {
  resetForm();
  emit('cancel');
};

// Helper functions for dynamic fields
const addEducation = () => {
  const currentEducation = [...(formValues.value.education ?? [])];
  currentEducation.push({
    degree: '',
    university: '',
    passOutYear: new Date().getFullYear(),
  });
  setFieldValue('education', currentEducation);
};

const removeEducation = (index: number) => {
  const education = formValues.value.education;
  if (education && education.length > 1) {
    const currentEducation = [...education];
    currentEducation.splice(index, 1);
    setFieldValue('education', currentEducation);
  }
};

const addExperience = () => {
  const currentExperience = [...(formValues.value.experience ?? [])];
  currentExperience.push({
    startYear: new Date().getFullYear(),
    organization: '',
    designation: '',
  });
  setFieldValue('experience', currentExperience);
};

const removeExperience = (index: number) => {
  const experience = formValues.value.experience;
  if (experience) {
    const currentExperience = [...experience];
    currentExperience.splice(index, 1);
    setFieldValue('experience', currentExperience);
  }
};

const addAreaOfInterest = () => {
  const currentAreas = [...(formValues.value.areaOfInterest ?? [])];
  currentAreas.push('');
  setFieldValue('areaOfInterest', currentAreas);
};

const removeAreaOfInterest = (index: number) => {
  const areaOfInterest = formValues.value.areaOfInterest;
  if (areaOfInterest && areaOfInterest.length > 1) {
    const currentAreas = [...areaOfInterest];
    currentAreas.splice(index, 1);
    setFieldValue('areaOfInterest', currentAreas);
  }
};

// Handle resume upload
const handleResumeUpload = async (file: File | undefined) => {
  if (!file) {
    resumeFile.value = undefined;
    setFieldValue('resume', undefined);
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'faculty');
    form.append('title', 'Faculty Resume');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', { method: 'POST', body: form });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'faculty',
    };

    resumeFile.value = fileData;
    setFieldValue('resume', fileData);
  } catch (err) {
    console.error('Failed to upload resume:', err);
    toast.error('Failed to upload resume');
  }
};

// Handle image upload
const handleImageUpload = async (file: File | undefined) => {
  if (!file) {
    imageFile.value = undefined;
    setFieldValue('image', undefined);
    return;
  }

  try {
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'faculty');
    form.append('title', 'Faculty Image');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', { method: 'POST', body: form });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'faculty',
    };

    imageFile.value = fileData;
    setFieldValue('image', fileData);
  } catch (err) {
    console.error('Failed to upload image:', err);
    toast.error('Failed to upload image');
  }
};
</script>

<template>
  <Form
    :validation-schema="formSchema"
    :initial-values="initialValues"
    class="space-y-6"
    @submit="onSubmit"
  >
    <!-- Basic Information -->
    <div class="space-y-4">
      <div class="grid grid-cols-2 gap-4">
        <FormField v-slot="{ field, errorMessage }" name="name">
          <FormItem>
            <FormLabel>Name</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="field.value"
                placeholder="Enter name"
                :disabled="isSubmitting"
                @update:model-value="
                  (newValue: any) => setFieldValue('name', newValue)
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <FormField v-slot="{ field, errorMessage }" name="designation">
          <FormItem>
            <FormLabel>Designation</FormLabel>
            <FormControl>
              <Input
                v-bind="field"
                :model-value="field.value"
                placeholder="Enter designation"
                :disabled="isSubmitting"
                @update:model-value="
                  (newValue: any) => setFieldValue('designation', newValue)
                "
              />
            </FormControl>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>
      </div>

      <FormField v-slot="{ field, errorMessage }" name="priority">
        <FormItem>
          <FormLabel>Priority (higher number = higher priority)</FormLabel>
          <FormControl>
            <Input
              v-bind="field"
              :model-value="field.value"
              type="number"
              min="0"
              placeholder="Enter priority (0 = highest)"
              :disabled="isSubmitting"
              @update:model-value="
                (newValue: any) => setFieldValue('priority', newValue)
              "
            />
          </FormControl>
          <FormDescription>
            Faculty with higher priority numbers will be displayed first
          </FormDescription>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <div class="grid grid-cols-2 gap-4">
        <div class="space-y-2">
          <Label>Start Date</Label>
          <AdminBaseDatePicker
            :date-value="values.startDate"
            @update:date-value="
              (newValue: any) =>
                setFieldValue('startDate', newValue || new Date())
            "
          />
          <p class="text-xs text-muted-foreground">
            Start date of the faculty member
          </p>
        </div>
        <div class="space-y-2">
          <Label>End Date</Label>
          <AdminBaseDatePicker
            :date-value="values.endDate || undefined"
            @update:date-value="
              (newValue: any) => setFieldValue('endDate', newValue || null)
            "
          />
          <p class="text-xs text-muted-foreground">
            End date of the faculty member
          </p>
        </div>
      </div>

      <!-- Image Upload -->
      <AdminBaseDropzone
        :is-multiple-allowed="false"
        :file-types="['image']"
        title="Faculty Image"
        :existing-files="imageFile ? [imageFile] : []"
        description="Upload faculty profile image. Only one image is allowed."
        @files-selected="(files: any) => handleImageUpload(files[0]?.file)"
        @file-removed="() => (imageFile = undefined)"
      />

      <!-- Resume Upload -->
      <AdminBaseDropzone
        :is-multiple-allowed="false"
        :file-types="['pdf']"
        title="Faculty Resume"
        :existing-files="resumeFile ? [resumeFile] : []"
        description="Upload faculty resume. Only one file is allowed."
        @files-selected="(files: any) => handleResumeUpload(files[0]?.file)"
        @file-removed="() => (resumeFile = undefined)"
      />

      <!-- Areas of Interest -->
      <FormField v-slot="{ errorMessage }" name="areaOfInterest">
        <FormItem>
          <div class="flex items-center justify-between">
            <FormLabel>Areas of Interest</FormLabel>
            <Button
              type="button"
              variant="outline"
              size="sm"
              :disabled="isSubmitting"
              @click="addAreaOfInterest"
            >
              <PlusCircle class="h-4 w-4 mr-2" />
              Add Area
            </Button>
          </div>
          <div
            v-for="(area, index) in formValues.areaOfInterest"
            :key="index"
            class="flex gap-2 mt-2"
          >
            <FormControl>
              <Input
                :model-value="area"
                placeholder="Enter area of interest"
                :disabled="isSubmitting"
                @update:model-value="
                  (newValue: any) => {
                    const newAreas = [...formValues.areaOfInterest];
                    newAreas[index] = newValue;
                    setFieldValue('areaOfInterest', newAreas);
                  }
                "
              />
            </FormControl>
            <Button
              type="button"
              variant="outline"
              size="icon"
              :disabled="isSubmitting || formValues.areaOfInterest.length === 1"
              @click="removeAreaOfInterest(index)"
            >
              <MinusCircle class="h-4 w-4" />
            </Button>
          </div>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- Education -->
      <FormField v-slot="{ errorMessage }" name="education">
        <FormItem>
          <div class="flex items-center justify-between">
            <FormLabel>Education</FormLabel>
            <Button
              type="button"
              variant="outline"
              size="sm"
              :disabled="isSubmitting"
              @click="addEducation"
            >
              <PlusCircle class="h-4 w-4 mr-2" />
              Add Education
            </Button>
          </div>
          <div
            v-for="(edu, index) in formValues.education"
            :key="index"
            class="space-y-4 p-4 border rounded-lg mt-2"
          >
            <div class="flex justify-between items-center">
              <h4 class="font-medium">Education {{ index + 1 }}</h4>
              <Button
                type="button"
                variant="outline"
                size="icon"
                :disabled="isSubmitting || formValues.education.length === 1"
                @click="removeEducation(index)"
              >
                <MinusCircle class="h-4 w-4" />
              </Button>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <FormItem>
                <FormLabel>Degree</FormLabel>
                <FormControl>
                  <Input
                    :model-value="edu.degree"
                    placeholder="Enter degree"
                    :disabled="isSubmitting"
                    @update:model-value="
                      (newValue: any) => {
                        const newEducation = [...formValues.education];
                        newEducation[index] = { ...edu, degree: newValue };
                        setFieldValue('education', newEducation);
                      }
                    "
                  />
                </FormControl>
              </FormItem>
              <FormItem>
                <FormLabel>University</FormLabel>
                <FormControl>
                  <Input
                    :model-value="edu.university"
                    placeholder="Enter university"
                    :disabled="isSubmitting"
                    @update:model-value="
                      (newValue: any) => {
                        const newEducation = [...formValues.education];
                        newEducation[index] = { ...edu, university: newValue };
                        setFieldValue('education', newEducation);
                      }
                    "
                  />
                </FormControl>
              </FormItem>
            </div>
            <FormItem>
              <FormLabel>Pass Out Year</FormLabel>
              <FormControl>
                <Input
                  :model-value="edu.passOutYear"
                  type="number"
                  :min="1900"
                  :max="new Date().getFullYear()"
                  :disabled="isSubmitting"
                  @update:model-value="
                    (newValue: any) => {
                      const newEducation = [...formValues.education];
                      newEducation[index] = {
                        ...edu,
                        passOutYear: Number(newValue),
                      };
                      setFieldValue('education', newEducation);
                    }
                  "
                />
              </FormControl>
            </FormItem>
          </div>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- Experience -->
      <FormField v-slot="{ errorMessage }" name="experience">
        <FormItem>
          <div class="flex items-center justify-between">
            <FormLabel>Experience</FormLabel>
            <Button
              type="button"
              variant="outline"
              size="sm"
              :disabled="isSubmitting"
              @click="addExperience"
            >
              <PlusCircle class="h-4 w-4 mr-2" />
              Add Experience
            </Button>
          </div>
          <div
            v-for="(exp, index) in formValues.experience"
            :key="index"
            class="space-y-4 p-4 border rounded-lg mt-2"
          >
            <div class="flex justify-between items-center">
              <h4 class="font-medium">Experience {{ index + 1 }}</h4>
              <Button
                type="button"
                variant="outline"
                size="icon"
                :disabled="isSubmitting"
                @click="removeExperience(index)"
              >
                <MinusCircle class="h-4 w-4" />
              </Button>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <FormItem>
                <FormLabel>Start Year</FormLabel>
                <FormControl>
                  <Input
                    :model-value="exp.startYear"
                    type="number"
                    :min="1900"
                    :max="new Date().getFullYear()"
                    :disabled="isSubmitting"
                    @update:model-value="
                      (newValue: any) => {
                        const newExperience = [...formValues.experience];
                        newExperience[index] = {
                          ...exp,
                          startYear: Number(newValue),
                        };
                        setFieldValue('experience', newExperience);
                      }
                    "
                  />
                </FormControl>
              </FormItem>
              <FormItem>
                <FormLabel>Organization</FormLabel>
                <FormControl>
                  <Input
                    :model-value="exp.organization"
                    placeholder="Enter organization"
                    :disabled="isSubmitting"
                    @update:model-value="
                      (newValue: any) => {
                        const newExperience = [...formValues.experience];
                        newExperience[index] = {
                          ...exp,
                          organization: newValue,
                        };
                        setFieldValue('experience', newExperience);
                      }
                    "
                  />
                </FormControl>
              </FormItem>
            </div>
            <FormItem>
              <FormLabel>Designation</FormLabel>
              <FormControl>
                <Input
                  :model-value="exp.designation"
                  placeholder="Enter designation"
                  :disabled="isSubmitting"
                  @update:model-value="
                    (newValue: any) => {
                      const newExperience = [...formValues.experience];
                      newExperience[index] = { ...exp, designation: newValue };
                      setFieldValue('experience', newExperience);
                    }
                  "
                />
              </FormControl>
            </FormItem>
          </div>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          :disabled="isSubmitting"
          @click="onCancel"
        >
          Cancel
        </Button>
        <Button :disabled="isSubmitting" @click="onSubmit">
          {{ isSubmitting ? 'Saving...' : 'Save' }}
        </Button>
      </div>
    </div></Form
  >
</template>
