<script setup lang="ts">
import type { DownloadData } from '~/types/admin';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import DownloadForm from './DownloadForm.vue';
import { toast } from 'vue-sonner';

const props = defineProps<{ departmentId: number }>();

const {
  downloadList,
  downloadPagination,
  isLoadingDownload,
  fetchDownloadList,
  createDownload,
  updateDownload,
  deleteDownload,
} = useDepartment();

const columns = [
  { key: 'title', label: 'Title' },
  { key: 'year', label: 'Year' },
  { key: 'semester', label: 'Semester' },
  { key: 'actions', label: 'Actions' },
];

const isDialogOpen = ref(false);
const selectedDownload = ref<DownloadData | null>(null);
const isSubmitting = ref(false);
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Add delete dialog state
const showDeleteDialog = ref(false);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

// Update onMounted to include error handling and debugging
onMounted(async () => {
  try {
    await fetchDownloadList(
      props.departmentId,
      currentPage.value,
      itemsPerPage.value
    );
  } catch (error) {
    console.error('Error in onMounted:', error);
    toast.error('Failed to load downloads');
  }
});

const handleEdit = (download: DownloadData) => {
  selectedDownload.value = download;
  isDialogOpen.value = true;
};

const handleDelete = async (download: DownloadData) => {
  selectedDownload.value = download;
  showDeleteDialog.value = true;
};

const confirmDelete = async () => {
  if (!selectedDownload.value) return;

  deleteState.value = 'loading';
  try {
    await deleteDownload(props.departmentId, selectedDownload.value.id);
    deleteState.value = 'success';

    // Close dialog and refresh after a short delay
    setTimeout(async () => {
      showDeleteDialog.value = false;
      deleteState.value = 'idle';
      selectedDownload.value = null;
      await fetchDownloadList(
        props.departmentId,
        currentPage.value,
        itemsPerPage.value
      );
    }, 1000);
  } catch (error) {
    console.error('Error deleting download:', error);
    deleteState.value = 'error';
    toast.error('Failed to delete download');
  }
};

const handleAdd = () => {
  selectedDownload.value = null;
  isDialogOpen.value = true;
};

const handleFormSubmit = async (values: any) => {
  try {
    isSubmitting.value = true;
    if (selectedDownload.value) {
      await updateDownload(
        props.departmentId,
        selectedDownload.value.id,
        values
      );
      toast.success('Download updated successfully');
    } else {
      await createDownload(props.departmentId, values);
      toast.success('Download created successfully');
    }
    isDialogOpen.value = false;
    await fetchDownloadList(
      props.departmentId,
      currentPage.value,
      itemsPerPage.value
    );
  } catch (error) {
    console.error('Error saving download:', error);
    toast.error('Failed to save download');
  } finally {
    isSubmitting.value = false;
  }
};

const handleFormCancel = () => {
  isDialogOpen.value = false;
};

const handleDownload = (download: DownloadData) => {
  if (download.file?.pathname) {
    window.open(`/api/files/${download.file.pathname}`, '_blank');
  }
};

const handlePageChange = async (page: number) => {
  currentPage.value = page;
  await fetchDownloadList(props.departmentId, page, itemsPerPage.value);
};
</script>

<template>
  <Card class="w-full p-0 border-none shadow-none">
    <CardHeader
      class="px-2 pt-0 pb-4 flex flex-row items-center justify-between"
    >
      <CardTitle>Downloads</CardTitle>
      <Button
        type="button"
        variant="default"
        size="sm"
        :disabled="isSubmitting"
        @click="handleAdd"
      >
        Add Download
      </Button>
    </CardHeader>
    <CardContent class="px-2 pt-0 pb-4">
      <div v-if="isLoadingDownload" class="flex justify-center py-8">
        Loading...
      </div>
      <Table v-else>
        <TableHeader>
          <TableRow>
            <TableHead v-for="column in columns" :key="column.key">
              {{ column.label }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-if="isLoadingDownload">
            <TableCell colspan="4" class="text-center py-4">
              Loading downloads data...
            </TableCell>
          </TableRow>
          <TableRow v-else-if="!downloadList.length">
            <TableCell colspan="4" class="text-center py-4">
              No downloads found.
            </TableCell>
          </TableRow>
          <TableRow v-for="download in downloadList" :key="download.id">
            <TableCell>{{ download.title }}</TableCell>
            <TableCell>
              <div class="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Download"
                  @click="handleDownload(download)"
                >
                  Download
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Edit download"
                  @click="handleEdit(download)"
                >
                  {{ isSubmitting ? 'Editing...' : 'Edit' }}
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  :disabled="isSubmitting"
                  aria-label="Delete download"
                  @click="handleDelete(download)"
                >
                  {{ isSubmitting ? 'Deleting...' : 'Delete' }}
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <!-- Pagination -->
      <div
        v-if="downloadPagination.totalPages > 1"
        class="mt-4 flex justify-center"
      >
        <div class="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            :disabled="!downloadPagination.hasPreviousPage"
            @click="handlePageChange(currentPage - 1)"
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            :disabled="!downloadPagination.hasNextPage"
            @click="handlePageChange(currentPage + 1)"
          >
            Next
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>

  <Dialog :open="isDialogOpen" @update:open="isDialogOpen = $event">
    <DialogContent class="sm:max-w-3xl">
      <DialogHeader>
        <DialogTitle>
          {{ selectedDownload ? 'Edit Download' : 'Add New Download' }}
        </DialogTitle>
      </DialogHeader>
      <DownloadForm
        :department-id="departmentId"
        :initial-values="selectedDownload"
        :is-submitting="isSubmitting"
        @submit="handleFormSubmit"
        @cancel="handleFormCancel"
      />
    </DialogContent>
  </Dialog>

  <!-- Delete Confirmation Dialog -->
  <AdminMenuDeleteDialog
    v-if="selectedDownload"
    v-model:is-open="showDeleteDialog"
    :is-submitting="deleteState === 'loading'"
    :delete-state="deleteState"
    menu-title="download"
    type="menu"
    :description="`Are you sure you want to delete download '${selectedDownload.title}'? This action cannot be undone.`"
    @confirm="confirmDelete"
  />
</template>
