export interface PageSeoOptions {
  title?: string;
  description?: string;
  image?: string;
  keywords?: string[];
  noIndex?: boolean;
  structuredData?: Record<string, any>;
}

export const usePageSeo = (options: PageSeoOptions = {}) => {
  const route = useRoute();
  const config = useRuntimeConfig();
  const appUrl = config.public.appUrl || 'https://dbcollegemannuthy.edu.in/';

  const defaults = {
    title: 'Don Bosco College Mannuthy, Thrissur',
    description:
      'Don Bosco College Mannuthy, Thrissur - A leading arts and science college in Thrissur, Kerala, offering undergraduate and postgraduate programs. Known for its quality education, vibrant campus life, and strong focus on student development.',
    image: `${appUrl}/og-image.jpg`,
    keywords: [
      'Don Bosco College',
      'Mannuthy',
      'Thrissur',
      'Kerala',
      'Education',
    ],
    noIndex: false,
  };

  // Merge defaults with provided options
  const seoOptions = {
    ...defaults,
    ...options,
    // Ensure keywords are merged, not replaced
    keywords: [...(defaults.keywords || []), ...(options.keywords || [])],
  };

  // Set meta tags
  useSeoMeta({
    title: seoOptions.title,
    ogTitle: seoOptions.title,
    twitterTitle: seoOptions.title,

    description: seoOptions.description,
    ogDescription: seoOptions.description,
    twitterDescription: seoOptions.description,

    ogImage: seoOptions.image,
    twitterImage: seoOptions.image,

    keywords: seoOptions.keywords?.join(', '),

    robots: seoOptions.noIndex ? 'noindex, nofollow' : 'index, follow',
  });

  // Add canonical URL
  useHead({
    link: [{ rel: 'canonical', href: `${appUrl}${route.path}` }],
    meta: [{ property: 'og:url', content: `${appUrl}${route.path}` }],
  });

  // Add structured data if provided
  if (seoOptions.structuredData) {
    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify(seoOptions.structuredData),
        },
      ],
    });
  }
};
