import { toast } from 'vue-sonner';
import type {
  IQAC_TEAM_MEMBER_RESPONSE,
  IQACTeamTitleResponse,
} from '~~/app/types/admin/iqac';
import type { TeamMember } from '~~/shared/schema/iqac/team-members/create';
import type { UpdateTeamMember } from '~~/shared/schema/iqac/team-members/update';
import type { TeamTitle } from '~~/shared/schema/iqac/team-title/create';
import type { UpdateTeamTitle } from '~~/shared/schema/iqac/team-title/update';

export function useIqac() {
  // Team Members state
  const teamMembers = ref<IQAC_TEAM_MEMBER_RESPONSE[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const showCreateDialog = ref(false);
  const showUpdateDialog = ref(false);
  const showDeleteDialog = ref(false);
  const currentTeamMember = ref<IQAC_TEAM_MEMBER_RESPONSE | null>(null);
  const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

  // Team Titles state
  const teamTitles = ref<IQACTeamTitleResponse[]>([]);
  const titlesLoading = ref(false);
  const titlesError = ref<string | null>(null);
  const showTitleCreateDialog = ref(false);
  const showTitleUpdateDialog = ref(false);
  const showTitleDeleteDialog = ref(false);
  const currentTeamTitle = ref<IQACTeamTitleResponse | null>(null);
  const titleDeleteState = ref<'idle' | 'loading' | 'success' | 'error'>(
    'idle'
  );

  // Team Members methods
  async function fetchTeamMembers() {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await $fetch<IQAC_TEAM_MEMBER_RESPONSE[]>(
        '/api/admin/iqac/team-members'
      );
      teamMembers.value = response;
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to fetch team members';
      error.value = errorMessage;
      toast.error(errorMessage);
    } finally {
      isLoading.value = false;
    }
  }

  async function createTeamMember(data: TeamMember) {
    isLoading.value = true;
    const toastId = toast.loading('Creating team member...');

    try {
      const response = await $fetch<{
        message: string;
        data: any;
      }>('/api/admin/iqac/team-members', {
        method: 'POST',
        body: data,
      });

      toast.success(response.message || 'Team member created successfully', {
        id: toastId,
      });
      showCreateDialog.value = false;
      await fetchTeamMembers();
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to create team member';
      toast.error(errorMessage, { id: toastId });
    } finally {
      isLoading.value = false;
    }
  }

  async function updateTeamMember(
    id: number,
    data: Omit<UpdateTeamMember, 'id'>
  ) {
    if (!id) return;

    isLoading.value = true;
    const toastId = toast.loading('Updating team member...');

    try {
      const response = await $fetch<{
        message: string;
        data: any;
      }>(`/api/admin/iqac/team-members/${id}`, {
        method: 'PUT',
        body: {
          id,
          ...data,
        },
      });

      toast.success(response.message || 'Team member updated successfully', {
        id: toastId,
      });
      showUpdateDialog.value = false;
      currentTeamMember.value = null;
      await fetchTeamMembers();
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to update team member';
      toast.error(errorMessage, { id: toastId });
    } finally {
      isLoading.value = false;
    }
  }

  async function deleteTeamMember(id: number) {
    if (!id) return;

    deleteState.value = 'loading';
    const toastId = toast.loading('Deleting team member...');

    try {
      const response = await $fetch<{
        message: string;
        id: number;
      }>(`/api/admin/iqac/team-members/${id}`, {
        method: 'DELETE',
      });

      deleteState.value = 'success';
      toast.success(response.message || 'Team member deleted successfully', {
        id: toastId,
      });

      // Wait for animation to complete before closing
      setTimeout(() => {
        showDeleteDialog.value = false;
        deleteState.value = 'idle';
        currentTeamMember.value = null;
        fetchTeamMembers();
      }, 1000);
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to delete team member';
      deleteState.value = 'error';
      toast.error(errorMessage, { id: toastId });
    }
  }

  // Team Titles methods
  async function fetchTeamTitles() {
    titlesLoading.value = true;
    titlesError.value = null;

    try {
      const response = await $fetch<IQACTeamTitleResponse[]>(
        '/api/admin/iqac/team-titles'
      );
      teamTitles.value = response;
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to fetch team titles';
      titlesError.value = errorMessage;
      toast.error(errorMessage);
    } finally {
      titlesLoading.value = false;
    }
  }

  async function createTeamTitle(data: TeamTitle) {
    titlesLoading.value = true;
    const toastId = toast.loading('Creating team title...');

    try {
      const response = await $fetch<{
        message: string;
        data: any;
      }>('/api/admin/iqac/team-titles', {
        method: 'POST',
        body: data,
      });

      toast.success(response.message || 'Team title created successfully', {
        id: toastId,
      });
      showTitleCreateDialog.value = false;
      await fetchTeamTitles();
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to create team title';
      toast.error(errorMessage, { id: toastId });
    } finally {
      titlesLoading.value = false;
    }
  }

  async function updateTeamTitle(
    id: number,
    data: Omit<UpdateTeamTitle, 'id'>
  ) {
    if (!id) return;

    titlesLoading.value = true;
    const toastId = toast.loading('Updating team title...');

    try {
      const response = await $fetch<{
        message: string;
        data: any;
      }>(`/api/admin/iqac/team-titles/${id}`, {
        method: 'PUT',
        body: {
          id,
          ...data,
        },
      });

      toast.success(response.message || 'Team title updated successfully', {
        id: toastId,
      });
      showTitleUpdateDialog.value = false;
      currentTeamTitle.value = null;
      await fetchTeamTitles();
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to update team title';
      toast.error(errorMessage, { id: toastId });
    } finally {
      titlesLoading.value = false;
    }
  }

  async function deleteTeamTitle(id: number) {
    if (!id) return;

    titleDeleteState.value = 'loading';
    const toastId = toast.loading('Deleting team title...');

    try {
      const response = await $fetch<{
        message: string;
        id: number;
      }>(`/api/admin/iqac/team-titles/${id}`, {
        method: 'DELETE',
      });

      titleDeleteState.value = 'success';
      toast.success(response.message || 'Team title deleted successfully', {
        id: toastId,
      });

      // Wait for animation to complete before closing
      setTimeout(() => {
        showTitleDeleteDialog.value = false;
        titleDeleteState.value = 'idle';
        currentTeamTitle.value = null;
        fetchTeamTitles();
      }, 1000);
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to delete team title';
      titleDeleteState.value = 'error';
      toast.error(errorMessage, { id: toastId });
    }
  }

  return {
    // Team Members
    teamMembers,
    isLoading,
    error,
    showCreateDialog,
    showUpdateDialog,
    showDeleteDialog,
    currentTeamMember,
    deleteState,
    fetchTeamMembers,
    createTeamMember,
    updateTeamMember,
    deleteTeamMember,

    // Team Titles
    teamTitles,
    titlesLoading,
    titlesError,
    showTitleCreateDialog,
    showTitleUpdateDialog,
    showTitleDeleteDialog,
    currentTeamTitle,
    titleDeleteState,
    fetchTeamTitles,
    createTeamTitle,
    updateTeamTitle,
    deleteTeamTitle,
  };
}
