import type { DashboardItem } from '~/types/admin';

export const useDashboard = () => {
  const {
    data: dashboardData,
    status: dashboardStatus,
    refresh: refreshDashboard,
  } = useFetch<{ success: boolean; data: DashboardItem[] }>(
    '/api/admin/dashboard',
    {
      immediate: true,
      watch: false,
    }
  );

  return {
    dashboardData,
    dashboardStatus,
    refreshDashboard,
  };
};
