import type { QuickLink } from '~~/server/database/tables/quick-link';
import { computed, ref } from 'vue';
import { toast } from 'vue-sonner';

// E-Services implementation
export function useEservices() {
  const isSubmitting = ref(false);
  const fetchError = ref<string | null>(null);

  // Fetch e-services with pagination
  const {
    data: eServiceData,
    status: eServiceStatus,
    refresh: refreshEservices,
  } = useFetch<{
    items: QuickLink[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }>('/api/admin/e-service', {
    immediate: true,
    watch: false,
  });

  const eServices = computed(() => eServiceData.value?.items || []);
  const isLoading = computed(() => eServiceStatus.value === 'pending');
  const total = computed(() => eServiceData.value?.total || 0);
  const currentPage = computed(() => eServiceData.value?.page || 1);
  const totalPages = computed(() => eServiceData.value?.totalPages || 1);

  // Create e-service
  async function createEservice(values: any) {
    if (isSubmitting.value) return;

    isSubmitting.value = true;
    const toastId = toast.loading('Creating e-service...');

    try {
      const response = await $fetch<{ success: boolean; data: QuickLink }>(
        '/api/admin/e-service',
        {
          method: 'POST',
          body: values,
        }
      );

      if (response) {
        toast.success('E-service created successfully', { id: toastId });
        await refreshEservices();
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to create e-service';
      toast.error(errorMessage, { id: toastId });
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Update e-service
  async function updateEservice(id: number, values: any) {
    if (isSubmitting.value) return;

    isSubmitting.value = true;
    const toastId = toast.loading('Updating e-service...');

    try {
      const response = await $fetch<{ success: boolean; data: QuickLink }>(
        `/api/admin/e-service/${id}`,
        {
          method: 'PUT',
          body: values,
        }
      );

      if (response) {
        toast.success('E-service updated successfully', { id: toastId });
        await refreshEservices();
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to update e-service';
      toast.error(errorMessage, { id: toastId });
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Delete e-service
  async function deleteEservice(id: number) {
    if (isSubmitting.value) return;

    isSubmitting.value = true;
    const toastId = toast.loading('Deleting e-service...');

    try {
      const response = await $fetch<{ success: boolean }>(
        `/api/admin/e-service/${id}`,
        {
          method: 'DELETE',
        }
      );

      if (response) {
        toast.success('E-service deleted successfully', { id: toastId });
        await refreshEservices();
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to delete e-service';
      toast.error(errorMessage, { id: toastId });
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  return {
    eServices,
    isLoading,
    fetchError,
    isSubmitting,
    total,
    currentPage,
    totalPages,
    createEservice,
    updateEservice,
    deleteEservice,
    refreshEservices,
  };
}

// Quick Links implementation
export function useQuickLinks() {
  const isSubmitting = ref(false);
  const fetchError = ref<string | null>(null);

  // Fetch quick links with pagination
  const {
    data: quickLinkData,
    status: quickLinkStatus,
    refresh: refreshQuickLinks,
  } = useFetch<{
    items: QuickLink[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }>('/api/admin/quick-link', {
    immediate: true,
    watch: false,
  });

  const quickLinks = computed(() => quickLinkData.value?.items || []);
  const isLoading = computed(() => quickLinkStatus.value === 'pending');
  const total = computed(() => quickLinkData.value?.total || 0);
  const currentPage = computed(() => quickLinkData.value?.page || 1);
  const totalPages = computed(() => quickLinkData.value?.totalPages || 1);

  // Create quick link
  async function createQuickLink(values: any) {
    if (isSubmitting.value) return;

    isSubmitting.value = true;
    const toastId = toast.loading('Creating quick link...');

    try {
      const response = await $fetch<{ success: boolean; data: QuickLink }>(
        '/api/admin/quick-link',
        {
          method: 'POST',
          body: values,
        }
      );

      if (response) {
        toast.success('Quick link created successfully', { id: toastId });
        await refreshQuickLinks();
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to create quick link';
      toast.error(errorMessage, { id: toastId });
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Update quick link
  async function updateQuickLink(id: number, values: any) {
    if (isSubmitting.value) return;

    isSubmitting.value = true;
    const toastId = toast.loading('Updating quick link...');

    try {
      const response = await $fetch<{ success: boolean; data: QuickLink }>(
        `/api/admin/quick-link/${id}`,
        {
          method: 'PUT',
          body: values,
        }
      );

      if (response) {
        toast.success('Quick link updated successfully', { id: toastId });
        await refreshQuickLinks();
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to update quick link';
      toast.error(errorMessage, { id: toastId });
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Delete quick link
  async function deleteQuickLink(id: number) {
    if (isSubmitting.value) return;

    isSubmitting.value = true;
    const toastId = toast.loading('Deleting quick link...');

    try {
      const response = await $fetch<{ success: boolean }>(
        `/api/admin/quick-link/${id}`,
        {
          method: 'DELETE',
        }
      );

      if (response) {
        toast.success('Quick link deleted successfully', { id: toastId });
        await refreshQuickLinks();
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to delete quick link';
      toast.error(errorMessage, { id: toastId });
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  return {
    quickLinks,
    isLoading,
    fetchError,
    isSubmitting,
    total,
    currentPage,
    totalPages,
    createQuickLink,
    updateQuickLink,
    deleteQuickLink,
    refreshQuickLinks,
  };
}
