export interface SeoOptions {
  title?: string;
  description?: string;
  image?: string;
  ogType?: 'website' | 'article' | 'profile';
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  keywords?: string[];
  noIndex?: boolean;
  canonical?: string;
  structuredData?: Record<string, any>;
}

export const useSeo = (options: SeoOptions = {}) => {
  const route = useRoute();
  const config = useRuntimeConfig();
  const appUrl = config.public.appUrl || 'https://dbcollegemannuthy.edu.in/';

  const defaults = {
    title: 'Don Bosco College Mannuthy, Thrissur',
    description:
      'Don Bosco College Mannuthy, Thrissur - A leading arts and science college in Thrissur, Kerala, offering undergraduate and postgraduate programs. Known for its quality education, vibrant campus life, and strong focus on student development.',
    image: `${appUrl}/images/og-image.jpg`,
    ogType: 'website',
    twitterCard: 'summary_large_image',
    keywords: [
      'Don Bosco College',
      'Mannuthy',
      'Thrissur',
      'Kerala',
      'Education',
      'Higher Education',
      'College',
      'Academic Programs',
      'Admissions',
    ],
    noIndex: false,
    canonical: `${appUrl}${route.path}`,
  } as SeoOptions;

  // Merge defaults with provided options
  const seoOptions = {
    ...defaults,
    ...options,
    // Ensure keywords are merged, not replaced
    keywords: [...(defaults.keywords || []), ...(options.keywords || [])],
  };

  // Set meta tags using Nuxt's useSeoMeta composable
  useSeoMeta({
    title: seoOptions.title,
    ogTitle: seoOptions.title,
    twitterTitle: seoOptions.title,

    description: seoOptions.description,
    ogDescription: seoOptions.description,
    twitterDescription: seoOptions.description,

    ogImage: seoOptions.image,
    twitterImage: seoOptions.image,

    ogType: seoOptions.ogType,
    twitterCard: seoOptions.twitterCard,

    keywords: seoOptions.keywords?.join(', '),

    robots: seoOptions.noIndex ? 'noindex, nofollow' : 'index, follow',
  });

  // Add canonical URL and og:url separately
  useHead({
    link: [{ rel: 'canonical', href: seoOptions.canonical }],
    meta: [{ property: 'og:url', content: seoOptions.canonical }],
  });

  // Add structured data if provided
  if (seoOptions.structuredData) {
    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify(seoOptions.structuredData),
        },
      ],
    });
  }

  return {
    ...seoOptions,
    updateSeo: (newOptions: SeoOptions) =>
      useSeo({
        ...seoOptions,
        ...newOptions,
      }),
  };
};
