// composables/useAlbums.ts
import { toast } from 'vue-sonner';
import type { Album } from '~~/shared/schema/album/get';

export function useAlbums() {
  const albums = ref<Album[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const showCreateDialog = ref(false);
  const showDeleteDialog = ref(false);
  const currentAlbum = ref<Album | null>(null);
  const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

  async function fetchAlbums() {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await $fetch<{
        success: boolean;
        data: Album[];
      }>('/api/admin/album');

      if (response.success) {
        albums.value = response.data;
      }
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to fetch albums';
      error.value = errorMessage;
      toast.error(errorMessage);
    } finally {
      isLoading.value = false;
    }
  }

  async function createAlbum(data: { name: string }) {
    isLoading.value = true;
    const toastId = toast.loading('Creating album...');

    try {
      const response = await $fetch<{ success: boolean }>('/api/admin/album', {
        method: 'POST',
        body: {
          title: data.name,
        },
      });

      if (response.success) {
        toast.success('Album created successfully', { id: toastId });
        showCreateDialog.value = false;
        await fetchAlbums();
      }
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to create album';
      toast.error(errorMessage, { id: toastId });
    } finally {
      isLoading.value = false;
    }
  }

  async function deleteAlbum(albumId: string) {
    if (!albumId) return;

    deleteState.value = 'loading';
    const toastId = toast.loading('Deleting album...');

    try {
      const response = await $fetch<{ success: boolean }>(
        `/api/admin/album/${albumId}`,
        {
          method: 'DELETE',
        }
      );

      if (response.success) {
        deleteState.value = 'success';
        toast.success('Album deleted successfully', { id: toastId });

        // Wait for animation to complete before closing
        setTimeout(() => {
          showDeleteDialog.value = false;
          deleteState.value = 'idle';
          currentAlbum.value = null;
          fetchAlbums();
        }, 1000);
      }
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to delete album';
      deleteState.value = 'error';
      toast.error(errorMessage, { id: toastId });
    }
  }

  return {
    albums,
    isLoading,
    error,
    showCreateDialog,
    showDeleteDialog,
    currentAlbum,
    deleteState,
    fetchAlbums,
    createAlbum,
    deleteAlbum,
  };
}
