import type { ModalWithRelations } from '~~/server/database/tables';
// composables/useModal.ts
import { ref } from 'vue';

export function useModal() {
  const isModalOpen = ref(false);
  const modalContent = ref<ModalWithRelations | null>(null);

  const openModal = (content: ModalWithRelations) => {
    modalContent.value = content;
    isModalOpen.value = true;
  };

  const closeModal = () => {
    isModalOpen.value = false;
    modalContent.value = null;
  };

  return {
    isModalOpen,
    modalContent,
    openModal,
    closeModal,
  };
}
