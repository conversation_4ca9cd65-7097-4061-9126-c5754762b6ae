// composables/useAlbumPhotos.ts
import { toast } from 'vue-sonner';
import type { AlbumData, AlbumPhoto } from '~~/shared/schema/album/get';
import type { fileSchema } from '~~/shared/schema';

export function useAlbumPhotos(albumId: string) {
  const album = ref<AlbumData | null>(null);
  const isLoading = ref(false);
  const isUploading = ref(false);
  const error = ref<string | null>(null);
  const currentPhoto = ref<AlbumPhoto | null>(null);
  const showDeleteDialog = ref(false);

  async function fetchAlbumPhotos() {
    isLoading.value = true;
    error.value = null;

    try {
      const albumResponse = await $fetch<{ success: boolean; data: AlbumData }>(
        `/api/admin/album/${albumId}`
      );

      if (albumResponse.success) {
        album.value = albumResponse.data;
      }
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to load album photos';
      error.value = errorMessage;
      toast.error(errorMessage);
    } finally {
      isLoading.value = false;
    }
  }

  async function uploadPhotos(files: File[]) {
    if (files.length === 0) return;

    isUploading.value = true;
    const toastId = toast.loading(
      `Uploading ${files.length} photo${files.length > 1 ? 's' : ''}...`
    );

    try {
      const formData = new FormData();

      // Add each file to FormData
      files.forEach((file) => {
        formData.append('files', file);
        formData.append('prefix', 'album');
        formData.append('title', file.name);
        formData.append('type', file.type);
      });

      const fileResponse = await $fetch<(typeof fileSchema._type)[]>(
        '/api/blob/upload',
        {
          method: 'POST',
          body: formData,
        }
      );

      if (fileResponse && Array.isArray(fileResponse)) {
        const albumFiles = fileResponse.map((file) => ({
          pathname: file.pathname,
          title: file.title,
          type: file.type,
          prefix: 'announcement',
        }));

        const response = await $fetch<{ success: boolean }>(
          `/api/admin/album/${albumId}`,
          {
            method: 'PUT',
            body: {
              files: albumFiles,
            },
          }
        );

        if (response.success) {
          toast.success(
            `Successfully uploaded ${files.length} photo${files.length > 1 ? 's' : ''}`,
            { id: toastId }
          );
          await fetchAlbumPhotos();
        }
      }
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to upload photos';
      toast.error(errorMessage, { id: toastId });
    } finally {
      isUploading.value = false;
    }
  }

  async function deletePhotos(fileIds: number[]) {
    if (!fileIds) return;

    const toastId = toast.loading('Deleting photo...');

    try {
      const response = await $fetch<{ success: boolean }>(
        `/api/admin/album/${albumId}/files`,
        {
          method: 'DELETE',
          body: {
            fileIds,
          },
        }
      );

      if (response.success) {
        toast.success('Photo deleted successfully', { id: toastId });
        showDeleteDialog.value = false;
        currentPhoto.value = null;
        await fetchAlbumPhotos();
      }
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to delete photo';
      toast.error(errorMessage, { id: toastId });
    }
  }

  return {
    album,
    isLoading,
    isUploading,
    error,
    currentPhoto,
    showDeleteDialog,
    fetchAlbumPhotos,
    uploadPhotos,
    deletePhotos,
  };
}
