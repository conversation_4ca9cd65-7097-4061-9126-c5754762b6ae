// composables/useTemplateManager.ts
import type { CardType, CardLayout } from '~/types/admin';
import type { SectionWithConfigData } from '~/types/api/section';
import type { CreateSectionInput } from '~~/shared/schema/section/create';

interface Section {
  id: number;
  step: 'type' | 'template' | 'form';
  cardType: CardType;
  layout: CardLayout;
  configuration: any;
  priority: number;
  type?: 'accordion' | 'card' | 'cardList';
  templateId?: number;
}

interface APIResponse<T> {
  success?: boolean;
  data?: T;
}

export const useTemplateManager = () => {
  const currentSection = ref<Section>();
  const currentSectionId = ref<number | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  const fetchSection = async (templateId: number) => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await $fetch<APIResponse<SectionWithConfigData>>(
        `/api/admin/section?templateId=${templateId}`,
        {
          method: 'GET',
        }
      );

      if (response.success && response.data) {
        // Safely handle configuration
        const config = response.data.configuration || {};
        const layout =
          typeof config === 'object' && config !== null && 'layout' in config
            ? (config.layout as CardLayout)
            : 'standard';

        currentSection.value = {
          id: response.data.id,
          step: 'form' as const,
          cardType: response.data.type === 'card' ? 'single' : 'list',
          layout,
          configuration: config,
          priority: response.data.priority || 0,
          type: response.data.type as 'accordion' | 'card' | 'cardList',
          templateId: response.data.templateId || 0,
        };
      }

      if (currentSection.value) {
        currentSectionId.value = currentSection.value?.id || null;
      }

      return response.data;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch sections';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const addSection = () => {
    const newSection: Section = {
      id: Date.now(), // Temporary ID until saved
      step: 'type',
      cardType: 'single',
      layout: 'standard',
      configuration: {},
      priority: 0, // Always use priority 0 for single section
    };

    currentSection.value = newSection; // Replace any existing sections with the new one
    currentSectionId.value = newSection.id;
  };

  const selectSection = (id: number) => {
    currentSectionId.value = id;
  };

  const selectCardType = (type: CardType) => {
    if (!currentSection.value) return;
    currentSection.value.cardType = type;
    currentSection.value.type = type === 'single' ? 'card' : 'cardList';
    currentSection.value.step = 'template';
  };

  const selectTemplate = (layout: string) => {
    if (!currentSection.value) return;
    currentSection.value.layout = layout as CardLayout;
    currentSection.value.step = 'form';

    // Initialize configuration based on layout and card type
    if (currentSection.value.cardType === 'single') {
      // Initialize configuration for single card
      currentSection.value.configuration = {
        layout,
        key: `${layout}_${Date.now()}`,
        title: '',
      };
    } else if (currentSection.value.cardType === 'list') {
      // Initialize configuration for card list
      // For card lists, the layout determines the type of all cards in the list
      currentSection.value.configuration = {
        title: '',
        description: '',
        maxCards: 10,
        minCards: 1,
        layout, // Store the layout to use for all cards in the list
        cards: [],
      };
    }
  };

  const goBack = () => {
    if (!currentSection.value) return;

    if (currentSection.value.step === 'template') {
      currentSection.value.step = 'type';
    } else if (currentSection.value.step === 'form') {
      currentSection.value.step = 'template';
    }
  };

  const createSection = async (
    templateId: number,
    sectionData: Partial<CreateSectionInput>
  ) => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await $fetch<APIResponse<SectionWithConfigData>>(
        '/api/admin/section/create',
        {
          method: 'POST',
          body: {
            templateId,
            type: sectionData.type,
            priority: 0, // Always use priority 0 for single section
            configuration: sectionData.configuration,
          },
        }
      );

      if (response.success && response.data) {
        // const newSection: Section = {
        //   id: response.data.id,
        //   step: 'form',
        //   cardType: response.data.type === 'card' ? 'single' : 'list',
        //   layout: response.data.configuration?.layout || 'standard',
        //   configuration: response.data.configuration,
        //   priority: response.data.priority,
        //   type: response.data.type as 'accordion' | 'card' | 'cardList',
        //   // templateId: response.data.templateId,
        // };

        // currentSection.value = newSection;
        // currentSectionId.value = newSection.id;
        fetchSection(templateId);
      }

      return response.data;
    } catch (err: any) {
      error.value = err.message || 'Failed to create section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateSection = async (
    id: number,
    sectionData: Partial<CreateSectionInput>
  ) => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await $fetch<APIResponse<SectionWithConfigData>>(
        '/api/admin/section/update',
        {
          method: 'PUT',
          body: {
            id,
            type: sectionData.type,
            priority: 0, // Always use priority 0 for single section
            configuration: sectionData.configuration,
          },
        }
      );

      if (response.success && response.data) {
        // const existingSection = currentSection.value;
        // if (existingSection) {
        //   currentSection.value = {
        //     ...existingSection,
        //     configuration: response.data.configuration,
        //     priority: response.data.priority,
        //     type: response.data.type as 'accordion' | 'card' | 'cardList',
        //   };
        // }
        fetchSection(currentSection.value?.templateId || 0);
      }

      return response.data;
    } catch (err: any) {
      error.value = err.message || 'Failed to update section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const removeSection = async (id: number) => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await $fetch<APIResponse<void>>(
        '/api/admin/section/remove',
        {
          method: 'DELETE',
          body: { id },
        }
      );

      if (response.success) {
        if (currentSectionId.value === id) {
          currentSectionId.value = currentSection.value?.id ?? null;
        }
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to remove section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  return {
    currentSection,
    currentSectionId,
    isLoading,
    error,
    fetchSection,
    addSection,
    selectSection,
    selectCardType,
    selectTemplate,
    goBack,
    createSection,
    updateSection,
    removeSection,
  };
};
