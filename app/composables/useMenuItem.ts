// composables/useMenuItem.ts
import type { MenuWithRelations } from '~~/server/database/tables/menu';
import { useForm } from 'vee-validate';
import { toast } from 'vue-sonner';
import * as z from 'zod';

export function useMenuItem() {
  const route = useRoute();
  const router = useRouter();

  // State management
  const isLoading = ref(true);
  const error = ref<string | null>(null);
  const isSubmitting = ref(false);
  const isEditing = ref(false);
  const showDeleteDialog = ref(false);
  const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

  // Menu data
  const menuData = ref<MenuWithRelations | null>(null);
  const originalData = ref<MenuWithRelations | null>(null);
  // Form Schema
  const formSchema = z.object({
    title: z
      .string()
      .min(1, 'Title is required')
      .max(30, 'Title must not exceed 30 characters'),
    menuName: z.string().max(15, 'Menu name must not exceed 15 characters'),
    isActive: z.boolean(),
    routeEnabled: z.boolean(),
    slug: z
      .string()
      .optional()
      .refine(
        (val) => !val || /^[a-z0-9-/]+$/.test(val),
        'Route path can only contain lowercase letters, numbers, and hyphens'
      ),
  });

  // Form setup
  const { resetForm, values: formValues } = useForm({
    validationSchema: formSchema,
  });

  // Fetch menu data
  async function fetchMenuData() {
    try {
      isLoading.value = true;
      const menuId = route.query.id as string;

      if (!menuId) {
        router.push('/admin/menus');
        return;
      }

      const response = await $fetch<{
        success: boolean;
        data: MenuWithRelations;
      }>('/api/admin/menu/menu-by-id', {
        params: { id: menuId },
      });

      if (response.success && response.data) {
        menuData.value = response.data;
        originalData.value = JSON.parse(JSON.stringify(response.data));
        updateForm(response.data);
      }
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to fetch menu';
      error.value = errorMessage;
      toast.error(errorMessage);
    } finally {
      isLoading.value = false;
    }
  }

  // Update form with menu data
  function updateForm(data: MenuWithRelations) {
    resetForm({
      values: {
        title: data.title,
        menuName: data.menuName || '',
        isActive: data.isActive,
        routeEnabled: data.routeEnabled,
        slug: data.slug || '',
      },
    });
  }

  // Toggle edit mode
  function toggleEdit() {
    if (isEditing.value && originalData.value) updateForm(originalData.value);

    isEditing.value = !isEditing.value;
  }

  // Save basic info
  async function saveBasicInfo(formValues: any) {
    if (formValues.routeEnabled && !formValues.slug) {
      toast.error('Route path is required when route is enabled');
      return;
    }

    if (!menuData.value?.id || isSubmitting.value) return;

    isSubmitting.value = true;
    const toastId = toast.loading('Saving changes...');

    try {
      const response = await $fetch<{ success: boolean }>(
        '/api/admin/menu/update',
        {
          method: 'PUT',
          body: {
            id: menuData.value.id,
            ...formValues,
          },
        }
      );

      if (response.success) {
        toast.success('Changes saved successfully', { id: toastId });
        await fetchMenuData();
        isEditing.value = false;
      }
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to save changes';
      toast.error(errorMessage, { id: toastId });
    } finally {
      isSubmitting.value = false;
    }
  }

  // Delete menu
  async function deleteMenu() {
    if (!menuData.value?.id || deleteState.value === 'loading') return;

    deleteState.value = 'loading';
    const toastId = toast.loading(`Deleting "${menuData.value.title}"...`);

    try {
      const response = await $fetch<{ success: boolean }>(
        `/api/admin/menu/remove?id=${menuData.value.id}`,
        {
          method: 'DELETE',
        }
      );

      if (response.success) {
        deleteState.value = 'success';
        toast.success('Menu deleted successfully', { id: toastId });
        // Wait for animation to complete before redirecting
        setTimeout(() => {
          showDeleteDialog.value = false;
          router.push('/admin/menus');
        }, 1500);
      }
    } catch (err: any) {
      deleteState.value = 'error';
      const errorMessage =
        err.data?.message || err.message || 'Failed to delete menu';
      toast.error(errorMessage, { id: toastId });
    }
  }

  return {
    isLoading,
    error,
    isSubmitting,
    isEditing,
    showDeleteDialog,
    menuData,
    formValues,
    formSchema,
    toggleEdit,
    saveBasicInfo,
    deleteMenu,
    fetchMenuData,
    deleteState,
  };
}
