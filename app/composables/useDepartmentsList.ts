import type {
  DepartmentListData,
  CreateDepartmentResponse,
  SlugValidateResponse,
} from '../types/admin/department';
import { computed, ref } from 'vue';
import { toast } from 'vue-sonner';

export function useDepartmentsList() {
  const isSubmitting = ref(false);
  const fetchError = ref<string | null>(null);
  const isValidatingSlug = ref(false);
  const departmentsData = ref<DepartmentListData[]>([]);
  const departmentsStatus = ref<'idle' | 'pending' | 'success' | 'error'>(
    'idle'
  );

  const departments = computed(() => departmentsData.value);
  const isLoading = computed(() => departmentsStatus.value === 'pending');

  async function fetchDepartments() {
    if (departmentsStatus.value === 'pending') return;

    departmentsStatus.value = 'pending';
    try {
      const response = await $fetch<DepartmentListData[]>(
        '/api/admin/department'
      );
      departmentsData.value = response;
      departmentsStatus.value = 'success';
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to fetch departments';
      fetchError.value = errorMessage;
      departmentsStatus.value = 'error';
      toast.error(errorMessage);
    }
  }

  async function validateSlug(slug: string): Promise<boolean> {
    if (!slug) return false;
    isValidatingSlug.value = true;

    try {
      const response = await $fetch<SlugValidateResponse>(
        '/api/admin/department/slug-validate',
        {
          query: { slug },
        }
      );
      return response.isAvailable;
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to validate slug';
      toast.error(errorMessage);
      return false;
    } finally {
      isValidatingSlug.value = false;
    }
  }

  async function createDepartment(values: { name: string; slug: string }) {
    if (isSubmitting.value) return;

    isSubmitting.value = true;
    const toastId = toast.loading('Creating department...');

    try {
      const response = await $fetch<CreateDepartmentResponse>(
        '/api/admin/department',
        {
          method: 'POST',
          body: values,
        }
      );

      if (response.success) {
        toast.success('Department created successfully', { id: toastId });
        await fetchDepartments();
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to create department';
      toast.error(errorMessage, { id: toastId });
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  async function updateDepartment(
    id: number,
    values: { name: string; slug: string }
  ) {
    if (isSubmitting.value) return;

    isSubmitting.value = true;
    const toastId = toast.loading('Updating department...');

    try {
      const response = await $fetch<{ success: boolean }>(
        `/api/admin/department/${id}`,
        {
          method: 'PUT',
          body: {
            id,
            ...values,
          },
        }
      );

      if (response.success) {
        toast.success('Department updated successfully', { id: toastId });
        await fetchDepartments();
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to update department';
      toast.error(errorMessage, { id: toastId });
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  async function deleteDepartment(id: number) {
    if (isSubmitting.value) return;

    isSubmitting.value = true;
    const toastId = toast.loading('Deleting department...');

    try {
      const response = await $fetch<{ success: boolean }>(
        `/api/admin/department/${id}`,
        {
          method: 'DELETE',
          query: { id },
        }
      );

      if (response.success) {
        toast.success('Department deleted successfully', { id: toastId });
        await fetchDepartments();
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to delete department';
      toast.error(errorMessage, { id: toastId });
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  return {
    departments,
    isLoading,
    fetchError,
    isSubmitting,
    isValidatingSlug,
    createDepartment,
    updateDepartment,
    deleteDepartment,
    fetchDepartments,
    validateSlug,
  };
}
