import type { AnnouncementWithRelations } from '~~/server/database/tables';
import type { AnnouncementType } from '~~/server/database/tables/enums';
import { toast } from 'vue-sonner';

export function useAnnouncement() {
  const announcements = ref<AnnouncementWithRelations[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const isSubmitting = ref(false);
  const showCreateEdit = ref(false);
  const actionState = ref<'create' | 'edit' | 'delete' | null>(null);
  const showDeleteDialog = ref(false);
  const currentAnnouncement = ref<AnnouncementWithRelations | null>(null);
  const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

  async function fetchAnnouncements(
    type?: AnnouncementType,
    filterStatus?: 'all' | 'active' | 'inactive' | 'expired'
  ) {
    isLoading.value = true;
    error.value = null;

    try {
      const queryParams: Record<string, string> = {};

      if (type) {
        queryParams.type = type;
      }

      if (filterStatus && filterStatus !== 'all') {
        queryParams.filterStatus = filterStatus;
      }

      const response = await $fetch<{
        success: boolean;
        data: AnnouncementWithRelations[];
      }>('/api/admin/announcement', {
        query: queryParams,
      });

      if (response.success) {
        announcements.value = response.data;
      }
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to fetch announcements';
      error.value = errorMessage;
      toast.error(errorMessage);
    } finally {
      isLoading.value = false;
    }
  }

  async function createAnnouncement(data: any, type: AnnouncementType) {
    isSubmitting.value = true;
    const toastId = toast.loading('Creating announcement...');
    const transformedData = {
      ...data,
      button: data.button ? transformButton(data.button) : null,
      scheduledAt: data.scheduledAt,
      expiresAt: data.expiresAt,
    };

    try {
      const response = await $fetch<{ success: boolean }>(
        '/api/admin/announcement',
        {
          method: 'POST',
          body: transformedData,
        }
      );

      if (response.success) {
        toast.success('Announcement created successfully', { id: toastId });
        showCreateEdit.value = false;
        actionState.value = null;
        await fetchAnnouncements(type);
      }
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to create announcement';
      toast.error(errorMessage, { id: toastId });
    } finally {
      isSubmitting.value = false;
    }
  }

  async function updateAnnouncement(data: any, type: AnnouncementType) {
    isSubmitting.value = true;
    const toastId = toast.loading('Updating announcement...');

    try {
      const response = await $fetch<{ success: boolean }>(
        `/api/admin/announcement/${data.id}`,
        {
          method: 'PUT',
          body: {
            ...data,
            button: data.button ? transformButton(data.button) : null,
            scheduledAt:
              data.scheduledAt instanceof Date
                ? data.scheduledAt.toISOString()
                : data.scheduledAt,
            expiresAt:
              data.expiresAt instanceof Date
                ? data.expiresAt.toISOString()
                : data.expiresAt,
            eventDate:
              data.eventDate instanceof Date
                ? data.eventDate.toISOString()
                : data.eventDate,
          },
        }
      );

      if (response.success) {
        toast.success('Announcement updated successfully', { id: toastId });
        showCreateEdit.value = false;
        actionState.value = null;
        currentAnnouncement.value = null;
        await fetchAnnouncements(type);
      }
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to update announcement';
      toast.error(errorMessage, { id: toastId });
    } finally {
      isSubmitting.value = false;
    }
  }
  // Update in app/composables/useAnnouncement.ts
  async function deleteAnnouncement(type: AnnouncementType) {
    if (!currentAnnouncement.value?.id) return;

    deleteState.value = 'loading';
    const toastId = toast.loading('Deleting announcement...');

    try {
      const response = await $fetch<{ success: boolean }>(
        `/api/admin/announcement/${currentAnnouncement.value.id}`,
        {
          method: 'DELETE',
        }
      );

      if (response.success) {
        deleteState.value = 'success';
        toast.success('Announcement deleted successfully', { id: toastId });

        // Wait for animation to complete before closing
        setTimeout(() => {
          showDeleteDialog.value = false;
          deleteState.value = 'idle';
          currentAnnouncement.value = null;
          fetchAnnouncements(type);
        }, 1000);
      }
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to delete announcement';
      deleteState.value = 'error';
      toast.error(errorMessage, { id: toastId });
    }
  }

  return {
    announcements,
    isLoading,
    error,
    isSubmitting,
    showCreateEdit,
    actionState,
    showDeleteDialog,
    currentAnnouncement,
    deleteState,
    fetchAnnouncements,
    createAnnouncement,
    updateAnnouncement,
    deleteAnnouncement,
  };
}
