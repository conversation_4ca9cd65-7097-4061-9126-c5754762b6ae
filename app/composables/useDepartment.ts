import type {
  FacultyListData,
  CourseListData,
  QuestionBankData,
  DownloadData,
  ProjectWorkData,
  StudentData,
} from '~/types/admin';
import type { FacultyUpdate } from '~~/shared/schema/department/faculty/update';
import type { Course } from '~~/shared/schema/department/course/create';
import type { CourseUpdate } from '~~/shared/schema/department/course/update';
import type {
  CreateQuestionBank,
  CreateProject,
} from '~~/shared/schema/department/files/create';
import type {
  UpdateQuestionBank,
  UpdateProject,
} from '~~/shared/schema/department/files/update';
import type {
  Pagination,
  DepartmentGallery,
  DepartmentEvent,
  DepartmentGalleryResponse,
  DepartmentEventResponse,
} from '~/types/home';
import type { CreateStudentInput } from '~~/shared/schema/department/student/create';
import type { UpdateStudentInput } from '~~/shared/schema/department/student/update';
import { toast } from 'vue-sonner';

interface UseDepartmentReturn {
  // Faculty Management
  facultyList: Ref<FacultyListData[]>;
  isLoadingFaculty: Ref<boolean>;
  fetchFacultyList: (departmentId: number) => Promise<void>;
  createFaculty: (departmentId: number, data: FacultyUpdate) => Promise<void>;
  deleteFaculty: (departmentId: number, facultyId: number) => Promise<void>;
  updateFaculty: (
    departmentId: number,
    facultyId: number,
    data: FacultyUpdate
  ) => Promise<void>;
  // Overview Management
  overview: Ref<string>;
  isLoadingOverview: Ref<boolean>;
  fetchOverview: (departmentId: number) => Promise<void>;
  updateOverview: (departmentId: number, overview: string) => Promise<void>;
  // Course Management
  courseList: Ref<CourseListData[]>;
  isLoadingCourse: Ref<boolean>;
  courseNames: Ref<{ name: string; id: number }[]>;
  fetchCourseNames: (departmentId: number) => Promise<void>;
  fetchCourseList: (departmentId: number) => Promise<void>;
  createCourse: (departmentId: number, data: Course) => Promise<void>;
  updateCourse: (
    departmentId: number,
    courseId: number,
    data: CourseUpdate
  ) => Promise<void>;
  deleteCourse: (departmentId: number, courseId: number) => Promise<void>;
  // Question Bank Management
  questionBankList: Ref<QuestionBankData[]>;
  questionBankPagination: Ref<Pagination>;
  isLoadingQuestionBank: Ref<boolean>;
  fetchQuestionBankList: (
    departmentId: number,
    page?: number,
    limit?: number
  ) => Promise<void>;
  createQuestionBank: (
    departmentId: number,
    data: CreateQuestionBank
  ) => Promise<void>;
  updateQuestionBank: (
    departmentId: number,
    questionBankId: number,
    data: UpdateQuestionBank
  ) => Promise<void>;
  deleteQuestionBank: (
    departmentId: number,
    questionBankId: number
  ) => Promise<void>;
  // Download Management
  downloadList: Ref<DownloadData[]>;
  downloadPagination: Ref<Pagination>;
  isLoadingDownload: Ref<boolean>;
  fetchDownloadList: (
    departmentId: number,
    page?: number,
    limit?: number
  ) => Promise<void>;
  createDownload: (
    departmentId: number,
    data: {
      title: string;
      file: { pathname: string; title: string; type: string };
    }
  ) => Promise<void>;
  updateDownload: (
    departmentId: number,
    downloadId: number,
    data: {
      title?: string;
      file?: { pathname: string; title: string; type: string };
    }
  ) => Promise<void>;
  deleteDownload: (departmentId: number, downloadId: number) => Promise<void>;
  // Project Work Management
  projectWorkList: Ref<ProjectWorkData[]>;
  projectWorkPagination: Ref<Pagination>;
  isLoadingProjectWork: Ref<boolean>;
  fetchProjectWorkList: (
    departmentId: number,
    page?: number,
    limit?: number
  ) => Promise<void>;
  createProjectWork: (
    departmentId: number,
    data: CreateProject
  ) => Promise<void>;
  updateProjectWork: (
    departmentId: number,
    projectWorkId: number,
    data: UpdateProject
  ) => Promise<void>;
  deleteProjectWork: (
    departmentId: number,
    projectWorkId: number
  ) => Promise<void>;
  // Gallery Management
  galleryList: Ref<DepartmentGallery[]>;
  isLoadingGallery: Ref<boolean>;
  fetchGalleryList: (departmentId: number) => Promise<void>;
  updateGallery: (departmentId: number, albumIds: number[]) => Promise<void>;

  // Event Management
  eventList: Ref<DepartmentEvent[]>;
  isLoadingEvent: Ref<boolean>;
  fetchEventList: (departmentId: number) => Promise<void>;
  updateEvent: (departmentId: number, eventIds: number[]) => Promise<void>;

  // Toppers Management
  topperList: Ref<StudentData[]>;
  isLoadingTopper: Ref<boolean>;
  fetchTopperList: (
    departmentId: number,
    page?: number,
    limit?: number
  ) => Promise<void>;
  createTopper: (
    departmentId: number,
    data: CreateStudentInput
  ) => Promise<void>;
  updateTopper: (
    departmentId: number,
    topperId: number,
    data: UpdateStudentInput
  ) => Promise<void>;
  deleteTopper: (departmentId: number, topperId: number) => Promise<void>;

  // Placement Management
  placementList: Ref<StudentData[]>;
  isLoadingPlacement: Ref<boolean>;
  fetchPlacementList: (
    departmentId: number,
    page?: number,
    limit?: number
  ) => Promise<void>;
  createPlacement: (
    departmentId: number,
    data: CreateStudentInput
  ) => Promise<void>;
  updatePlacement: (
    departmentId: number,
    placementId: number,
    data: UpdateStudentInput
  ) => Promise<void>;
  deletePlacement: (departmentId: number, placementId: number) => Promise<void>;
}

export const useDepartment = (): UseDepartmentReturn => {
  const facultyList = ref<FacultyListData[]>([]);
  const isLoadingFaculty = ref(false);
  const overview = ref('');
  const isLoadingOverview = ref(false);
  const courseList = ref<CourseListData[]>([]);
  const isLoadingCourse = ref(false);
  const courseNames = ref<{ name: string; id: number }[]>([]);
  const questionBankList = ref<QuestionBankData[]>([]);
  const questionBankPagination = ref<Pagination>({
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });
  const isLoadingQuestionBank = ref(false);

  const downloadList = ref<DownloadData[]>([]);
  const downloadPagination = ref<Pagination>({
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });
  const isLoadingDownload = ref(false);

  const projectWorkList = ref<ProjectWorkData[]>([]);
  const projectWorkPagination = ref<Pagination>({
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });
  const isLoadingProjectWork = ref(false);

  const galleryList = ref<DepartmentGallery[]>([]);
  const isLoadingGallery = ref(false);
  const eventList = ref<DepartmentEvent[]>([]);
  const isLoadingEvent = ref(false);

  // Toppers Management
  const topperList = ref<StudentData[]>([]);
  const isLoadingTopper = ref(false);

  // Placement Management
  const placementList = ref<StudentData[]>([]);
  const isLoadingPlacement = ref(false);

  const fetchCourseNames = async (departmentId: number) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
        data: { name: string; id: number }[];
      }>(`/api/admin/department/${departmentId}/course/list`);

      if (response.value?.success) {
        courseNames.value = response.value.data;
      }
    } catch (error) {
      console.error('Error fetching course names:', error);
      throw error;
    }
  };

  const fetchFacultyList = async (departmentId: number) => {
    try {
      isLoadingFaculty.value = true;
      const { data: response } = await useFetch<{
        success: boolean;
        data: FacultyListData[];
      }>(`/api/admin/department/${departmentId}/faculty`, { method: 'GET' });

      if (response.value?.success) {
        facultyList.value = response.value.data;
      }
    } catch (error) {
      console.error('Error fetching faculty list:', error);
      throw error;
    } finally {
      isLoadingFaculty.value = false;
    }
  };

  const createFaculty = async (departmentId: number, data: FacultyUpdate) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
        data: FacultyListData;
      }>(`/api/admin/department/${departmentId}/faculty`, {
        method: 'POST',
        body: data,
      });

      if (!response.value?.success) {
        throw new Error('Failed to create faculty');
      }
    } catch (error) {
      console.error('Error creating faculty:', error);
      throw error;
    }
  };

  const deleteFaculty = async (departmentId: number, facultyId: number) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
        message: string;
      }>(`/api/admin/department/${departmentId}/faculty/${facultyId}`, {
        method: 'DELETE',
      });

      if (!response.value?.success) {
        throw new Error('Failed to delete faculty');
      }
    } catch (error) {
      console.error('Error deleting faculty:', error);
      throw error;
    }
  };

  const updateFaculty = async (
    departmentId: number,
    facultyId: number,
    data: FacultyUpdate
  ) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
        data: FacultyListData;
      }>(`/api/admin/department/${departmentId}/faculty/${facultyId}`, {
        method: 'PUT',
        body: data,
      });

      if (!response.value?.success) {
        throw new Error('Failed to update faculty');
      }
    } catch (error) {
      console.error('Error updating faculty:', error);
      throw error;
    }
  };

  const fetchOverview = async (departmentId: number) => {
    if (!departmentId) return;

    try {
      isLoadingOverview.value = true;
      const { data: response } = await useFetch<{
        success: boolean;
        data: { overview: string | null };
      }>(`/api/admin/department/${departmentId}/overview`);

      if (response.value?.success && response.value.data) {
        overview.value = response.value.data.overview || '';
      }
    } catch (error) {
      console.error('Error fetching overview:', error);
      throw error;
    } finally {
      isLoadingOverview.value = false;
    }
  };

  const updateOverview = async (departmentId: number, newOverview: string) => {
    if (!departmentId) return;

    try {
      isLoadingOverview.value = true;
      const { data: response } = await useFetch<{
        success: boolean;
        message: string;
      }>(`/api/admin/department/${departmentId}/overview`, {
        method: 'POST',
        body: { overview: newOverview },
      });

      if (!response.value?.success) {
        throw new Error('Failed to update overview');
      }

      overview.value = newOverview;
    } catch (error) {
      console.error('Error updating overview:', error);
      throw error;
    } finally {
      isLoadingOverview.value = false;
    }
  };

  const fetchCourseList = async (departmentId: number) => {
    try {
      isLoadingCourse.value = true;
      const { data: response } = await useFetch<{
        success: boolean;
        data: CourseListData[];
      }>(`/api/admin/department/${departmentId}/course`);

      if (response.value?.success) {
        courseList.value = response.value.data;
      }
    } catch (error) {
      console.error('Error fetching course list:', error);
      throw error;
    } finally {
      isLoadingCourse.value = false;
    }
  };

  const createCourse = async (departmentId: number, data: Course) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
        data: CourseListData;
      }>(`/api/admin/department/${departmentId}/course`, {
        method: 'POST',
        body: data,
      });

      if (!response.value?.success) {
        throw new Error('Failed to create course');
      }
    } catch (error) {
      console.error('Error creating course:', error);
      throw error;
    }
  };

  const updateCourse = async (
    departmentId: number,
    courseId: number,
    data: CourseUpdate
  ) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
        data: CourseListData;
      }>(`/api/admin/department/${departmentId}/course/${courseId}`, {
        method: 'PUT',
        body: data,
      });

      if (!response.value?.success) {
        throw new Error('Failed to update course');
      }
    } catch (error) {
      console.error('Error updating course:', error);
      throw error;
    }
  };

  const deleteCourse = async (departmentId: number, courseId: number) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
      }>(`/api/admin/department/${departmentId}/course/${courseId}`, {
        method: 'DELETE',
      });

      if (!response.value?.success) {
        throw new Error('Failed to delete course');
      }
    } catch (error) {
      console.error('Error deleting course:', error);
      throw error;
    }
  };

  const fetchQuestionBankList = async (
    departmentId: number,
    page = 1,
    limit = 10
  ) => {
    try {
      isLoadingQuestionBank.value = true;

      const { data: response } = await useFetch<{
        success: boolean;
        data: {
          files: QuestionBankData[];
          pagination: Pagination;
        };
      }>(`/api/admin/department/${departmentId}/question-bank`, {
        method: 'GET',
        params: { page, limit },
      });

      if (response.value?.success) {
        questionBankList.value = response.value.data.files;
        questionBankPagination.value = response.value.data.pagination;
      } else {
        console.error('API request was not successful:', response.value);
      }
    } catch (error) {
      console.error('Error fetching question bank list:', error);
      throw error;
    } finally {
      isLoadingQuestionBank.value = false;
    }
  };

  const createQuestionBank = async (
    departmentId: number,
    data: CreateQuestionBank
  ) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
        data: QuestionBankData;
      }>(`/api/admin/department/${departmentId}/question-bank`, {
        method: 'POST',
        body: data,
      });

      if (!response.value?.success) {
        throw new Error('Failed to create question bank');
      }
    } catch (error) {
      console.error('Error creating question bank:', error);
      throw error;
    }
  };

  const updateQuestionBank = async (
    departmentId: number,
    questionBankId: number,
    data: UpdateQuestionBank
  ) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
        data: QuestionBankData;
      }>(
        `/api/admin/department/${departmentId}/question-bank/${questionBankId}`,
        {
          method: 'PUT',
          body: data,
        }
      );

      if (!response.value?.success) {
        throw new Error('Failed to update question bank');
      }
    } catch (error) {
      console.error('Error updating question bank:', error);
      throw error;
    }
  };

  const deleteQuestionBank = async (
    departmentId: number,
    questionBankId: number
  ) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
        data: QuestionBankData;
      }>(
        `/api/admin/department/${departmentId}/question-bank/${questionBankId}`,
        {
          method: 'DELETE',
        }
      );

      if (!response.value?.success) {
        throw new Error('Failed to delete question bank');
      }
    } catch (error) {
      console.error('Error deleting question bank:', error);
      throw error;
    }
  };

  const fetchDownloadList = async (
    departmentId: number,
    page = 1,
    limit = 10
  ) => {
    try {
      isLoadingDownload.value = true;
      const { data: response } = await useFetch<{
        success: boolean;
        data: {
          files: DownloadData[];
          pagination: Pagination;
        };
      }>(`/api/admin/department/${departmentId}/download`, {
        method: 'GET',
        params: { page, limit },
      });

      if (response.value?.success) {
        downloadList.value = response.value.data.files;
        downloadPagination.value = response.value.data.pagination;
      }
    } catch (error) {
      console.error('Error fetching download list:', error);
      throw error;
    } finally {
      isLoadingDownload.value = false;
    }
  };

  const createDownload = async (
    departmentId: number,
    data: {
      title: string;
      file: { pathname: string; title: string; type: string };
    }
  ) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
        data: DownloadData;
      }>(`/api/admin/department/${departmentId}/download`, {
        method: 'POST',
        body: { ...data, type: 'download' },
      });

      if (!response.value?.success) {
        throw new Error('Failed to create download');
      }
    } catch (error) {
      console.error('Error creating download:', error);
      throw error;
    }
  };

  const updateDownload = async (
    departmentId: number,
    downloadId: number,
    data: {
      title?: string;
      file?: { pathname: string; title: string; type: string };
    }
  ) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
        data: DownloadData;
      }>(`/api/admin/department/${departmentId}/download/${downloadId}`, {
        method: 'PUT',
        body: { ...data, type: 'download' },
      });

      if (!response.value?.success) {
        throw new Error('Failed to update download');
      }
    } catch (error) {
      console.error('Error updating download:', error);
      throw error;
    }
  };

  const deleteDownload = async (departmentId: number, downloadId: number) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
      }>(`/api/admin/department/${departmentId}/download/${downloadId}`, {
        method: 'DELETE',
      });

      if (!response.value?.success) {
        throw new Error('Failed to delete download');
      }
    } catch (error) {
      console.error('Error deleting download:', error);
      throw error;
    }
  };

  const fetchProjectWorkList = async (
    departmentId: number,
    page = 1,
    limit = 10
  ) => {
    try {
      isLoadingProjectWork.value = true;
      const { data: response } = await useFetch<{
        success: boolean;
        data: {
          files: ProjectWorkData[];
          pagination: Pagination;
        };
      }>(`/api/admin/department/${departmentId}/project-work`, {
        method: 'GET',
        params: { page, limit },
      });

      if (response.value?.success) {
        projectWorkList.value = response.value.data.files;
        projectWorkPagination.value = response.value.data.pagination;
      }
    } catch (error) {
      console.error('Error fetching project work list:', error);
      throw error;
    } finally {
      isLoadingProjectWork.value = false;
    }
  };

  const createProjectWork = async (
    departmentId: number,
    data: CreateProject
  ) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
        data: ProjectWorkData;
      }>(`/api/admin/department/${departmentId}/project-work`, {
        method: 'POST',
        body: data,
      });

      if (!response.value?.success) {
        throw new Error('Failed to create project work');
      }
    } catch (error) {
      console.error('Error creating project work:', error);
      throw error;
    }
  };

  const updateProjectWork = async (
    departmentId: number,
    projectWorkId: number,
    data: UpdateProject
  ) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
        data: ProjectWorkData;
      }>(
        `/api/admin/department/${departmentId}/project-work/${projectWorkId}`,
        {
          method: 'PUT',
          body: data,
        }
      );

      if (!response.value?.success) {
        throw new Error('Failed to update project work');
      }
    } catch (error) {
      console.error('Error updating project work:', error);
      throw error;
    }
  };

  const deleteProjectWork = async (
    departmentId: number,
    projectWorkId: number
  ) => {
    try {
      const { data: response } = await useFetch<{
        success: boolean;
      }>(
        `/api/admin/department/${departmentId}/project-work/${projectWorkId}`,
        {
          method: 'DELETE',
        }
      );

      if (!response.value?.success) {
        throw new Error('Failed to delete project work');
      }
    } catch (error) {
      console.error('Error deleting project work:', error);
      throw error;
    }
  };

  const fetchGalleryList = async (departmentId: number) => {
    try {
      isLoadingGallery.value = true;
      const { data: response } = await useFetch<DepartmentGalleryResponse>(
        `/api/admin/department/${departmentId}/gallery`,
        {
          method: 'GET',
        }
      );

      if (response.value?.success) {
        galleryList.value = response.value.data;
      }
    } catch (error) {
      console.error('Error fetching gallery list:', error);
      throw error;
    } finally {
      isLoadingGallery.value = false;
    }
  };

  const updateGallery = async (departmentId: number, albumIds: number[]) => {
    try {
      const { data: response } = await useFetch<{ success: boolean }>(
        `/api/admin/department/${departmentId}/gallery`,
        {
          method: 'POST',
          body: { albumIds },
        }
      );

      if (!response.value?.success) {
        throw new Error('Failed to update gallery');
      }
    } catch (error) {
      console.error('Error updating gallery:', error);
      throw error;
    }
  };

  const fetchEventList = async (departmentId: number) => {
    try {
      isLoadingEvent.value = true;
      const { data: response } = await useFetch<DepartmentEventResponse>(
        `/api/admin/department/${departmentId}/event`,
        {
          method: 'GET',
        }
      );

      if (response.value?.success) {
        eventList.value = response.value.data;
      }
    } catch (error) {
      console.error('Error fetching event list:', error);
      throw error;
    } finally {
      isLoadingEvent.value = false;
    }
  };

  const updateEvent = async (departmentId: number, eventIds: number[]) => {
    try {
      const { data: response } = await useFetch<{ success: boolean }>(
        `/api/admin/department/${departmentId}/event`,
        {
          method: 'POST',
          body: { eventIds },
        }
      );

      if (!response.value?.success) {
        throw new Error('Failed to update events');
      }
    } catch (error) {
      console.error('Error updating events:', error);
      throw error;
    }
  };

  const fetchTopperList = async (
    departmentId: number,
    page = 1,
    limit = 10
  ) => {
    try {
      isLoadingTopper.value = true;
      const { data, error } = await useFetch<{
        data: StudentData[];
        pagination: Pagination;
      }>(`/api/admin/department/${departmentId}/toppers`, {
        query: {
          page,
          limit,
        },
      });

      if (error.value) throw error.value;

      if (data.value) {
        topperList.value = data.value.data;
      }
    } catch (error) {
      console.error('Error fetching topper list:', error);
      toast.error('Failed to fetch topper list');
    } finally {
      isLoadingTopper.value = false;
    }
  };

  const createTopper = async (
    departmentId: number,
    data: CreateStudentInput
  ) => {
    try {
      isLoadingTopper.value = true;
      const { error } = await useFetch(
        `/api/admin/department/${departmentId}/toppers`,
        {
          method: 'POST',
          body: data,
        }
      );

      if (error.value) throw error.value;

      await fetchTopperList(departmentId);
      toast.success('Topper created successfully');
    } catch (error) {
      console.error('Error creating topper:', error);
      toast.error('Failed to create topper');
    } finally {
      isLoadingTopper.value = false;
    }
  };

  const updateTopper = async (
    departmentId: number,
    topperId: number,
    data: UpdateStudentInput
  ) => {
    try {
      isLoadingTopper.value = true;
      const { error } = await useFetch(
        `/api/admin/department/${departmentId}/toppers/${topperId}`,
        {
          method: 'PUT',
          body: data,
        }
      );

      if (error.value) throw error.value;

      await fetchTopperList(departmentId);
      toast.success('Topper updated successfully');
    } catch (error) {
      console.error('Error updating topper:', error);
      toast.error('Failed to update topper');
    } finally {
      isLoadingTopper.value = false;
    }
  };

  const deleteTopper = async (departmentId: number, topperId: number) => {
    try {
      isLoadingTopper.value = true;
      const { error } = await useFetch(
        `/api/admin/department/${departmentId}/toppers/${topperId}`,
        {
          method: 'DELETE',
        }
      );

      if (error.value) throw error.value;

      await fetchTopperList(departmentId);
      toast.success('Topper deleted successfully');
    } catch (error) {
      console.error('Error deleting topper:', error);
      toast.error('Failed to delete topper');
    } finally {
      isLoadingTopper.value = false;
    }
  };

  const fetchPlacementList = async (
    departmentId: number,
    page = 1,
    limit = 10
  ) => {
    try {
      isLoadingPlacement.value = true;
      const { data, error } = await useFetch<{
        data: StudentData[];
        pagination: Pagination;
      }>(`/api/admin/department/${departmentId}/placement`, {
        query: {
          page,
          limit,
        },
      });

      if (error.value) throw error.value;

      if (data.value) {
        placementList.value = data.value.data;
      }
    } catch (error) {
      console.error('Error fetching placement list:', error);
      toast.error('Failed to fetch placement list');
    } finally {
      isLoadingPlacement.value = false;
    }
  };

  const createPlacement = async (
    departmentId: number,
    data: CreateStudentInput
  ) => {
    try {
      isLoadingPlacement.value = true;
      const { error } = await useFetch(
        `/api/admin/department/${departmentId}/placement`,
        {
          method: 'POST',
          body: data,
        }
      );

      if (error.value) throw error.value;

      await fetchPlacementList(departmentId);
      toast.success('Placement created successfully');
    } catch (error) {
      console.error('Error creating placement:', error);
      toast.error('Failed to create placement');
    } finally {
      isLoadingPlacement.value = false;
    }
  };

  const updatePlacement = async (
    departmentId: number,
    placementId: number,
    data: UpdateStudentInput
  ) => {
    try {
      isLoadingPlacement.value = true;
      const { error } = await useFetch(
        `/api/admin/department/${departmentId}/placement/${placementId}`,
        {
          method: 'PUT',
          body: data,
        }
      );

      if (error.value) throw error.value;

      await fetchPlacementList(departmentId);
      toast.success('Placement updated successfully');
    } catch (error) {
      console.error('Error updating placement:', error);
      toast.error('Failed to update placement');
    } finally {
      isLoadingPlacement.value = false;
    }
  };

  const deletePlacement = async (departmentId: number, placementId: number) => {
    try {
      isLoadingPlacement.value = true;
      const { error } = await useFetch(
        `/api/admin/department/${departmentId}/placement/${placementId}`,
        {
          method: 'DELETE',
        }
      );

      if (error.value) throw error.value;

      await fetchPlacementList(departmentId);
      toast.success('Placement deleted successfully');
    } catch (error) {
      console.error('Error deleting placement:', error);
      toast.error('Failed to delete placement');
    } finally {
      isLoadingPlacement.value = false;
    }
  };

  return {
    facultyList,
    isLoadingFaculty,
    fetchFacultyList,
    createFaculty,
    deleteFaculty,
    updateFaculty,
    overview,
    isLoadingOverview,
    fetchOverview,
    updateOverview,
    courseList,
    isLoadingCourse,
    courseNames,
    fetchCourseNames,
    fetchCourseList,
    createCourse,
    updateCourse,
    deleteCourse,
    questionBankList,
    questionBankPagination,
    isLoadingQuestionBank,
    fetchQuestionBankList,
    createQuestionBank,
    updateQuestionBank,
    deleteQuestionBank,
    downloadList,
    downloadPagination,
    isLoadingDownload,
    fetchDownloadList,
    createDownload,
    updateDownload,
    deleteDownload,
    projectWorkList,
    projectWorkPagination,
    isLoadingProjectWork,
    fetchProjectWorkList,
    createProjectWork,
    updateProjectWork,
    deleteProjectWork,
    galleryList,
    isLoadingGallery,
    fetchGalleryList,
    updateGallery,
    eventList,
    isLoadingEvent,
    fetchEventList,
    updateEvent,
    topperList,
    isLoadingTopper,
    fetchTopperList,
    createTopper,
    updateTopper,
    deleteTopper,
    placementList,
    isLoadingPlacement,
    fetchPlacementList,
    createPlacement,
    updatePlacement,
    deletePlacement,
  } satisfies UseDepartmentReturn;
};
