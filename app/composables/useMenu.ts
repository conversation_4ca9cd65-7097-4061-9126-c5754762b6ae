// composables/useMenu.ts
import type { Menu } from '~~/server/database/tables/menu';
import { computed, ref } from 'vue';
import { toast } from 'vue-sonner';

export function useMenu() {
  const isSubmitting = ref(false);
  const fetchError = ref<string | null>(null);

  const {
    data: menuData,
    status: menuStatus,
    refresh: refreshMenus,
  } = useFetch<{ success: boolean; data: Menu[] }>('/api/admin/menu', {
    immediate: true,
    watch: false,
  });

  const menus = computed(() => menuData.value?.data || []);
  const isLoading = computed(() => menuStatus.value === 'pending');

  async function createMenu(values: any) {
    if (isSubmitting.value) return;

    isSubmitting.value = true;
    const toastId = toast.loading('Creating menu...');

    try {
      const response = await $fetch<{ success: boolean; data: Menu }>(
        '/api/admin/menu/create',
        {
          method: 'POST',
          body: values,
        }
      );

      if (response.success) {
        toast.success('Menu created successfully', { id: toastId });
        await refreshMenus();
        return true;
      }
      return false;
    } catch (err: any) {
      const errorMessage =
        err.data?.message || err.message || 'Failed to create menu';
      toast.error(errorMessage, { id: toastId });
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  return {
    menus,
    isLoading,
    fetchError,
    isSubmitting,
    createMenu,
    refreshMenus,
  };
}
