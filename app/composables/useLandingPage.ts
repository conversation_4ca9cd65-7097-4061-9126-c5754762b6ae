import { toast } from 'vue-sonner';
import type { LandingPageConfiguration } from '~~/shared/schema/section/landing-page/update';

export const useLandingPage = () => {
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // State for landing page data
  const landingPageData = ref<LandingPageConfiguration>({
    principalsMessage: {
      title: '',
      name: '',
      designation: '',
      message: '',
      callToAction: {
        enabled: false,
        buttonText: 'Learn More',
        newTab: false,
        buttonStyle: 'primary',
        linkType: 'internal',
        internalLink: '/',
        externalLink: '',
        icon: '',
      },
    },
    campusDetails: {
      title: '',
      description: '',
      callToAction: {
        enabled: false,
        buttonText: 'Learn More',
        newTab: false,
        buttonStyle: 'primary',
        linkType: 'internal',
        internalLink: '/',
        externalLink: '',
        icon: '',
      },
    },
    research: {
      title: '',
      description: '',
      callToAction: {
        enabled: false,
        buttonText: 'Learn More',
        newTab: false,
        buttonStyle: 'primary',
        linkType: 'internal',
        internalLink: '/',
        externalLink: '',
        icon: '',
      },
    },
    stats: {
      items: [],
    },
    sectionVisibility: {
      programsWeOffer: false,
      events: false,
      gallery: false,
    },
  });

  // Fetch landing page data
  const fetchLandingPage = async () => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await $fetch<LandingPageConfiguration>(
        '/api/admin/landing-page'
      );

      if (response) {
        // Update state with fetched data and ensure valid callToAction values
        // Fix any potential callToAction validation issues
        if (response.principalsMessage?.callToAction) {
          const cta = response.principalsMessage.callToAction;
          if (cta.enabled) {
            // Ensure buttonText is not empty
            if (!cta.buttonText) cta.buttonText = 'Learn More';

            // Ensure correct link based on linkType
            if (cta.linkType === 'internal') {
              if (!cta.internalLink) cta.internalLink = '/';
              // Don't set externalLink when type is internal
              cta.externalLink = undefined;
            } else {
              if (!cta.externalLink || !isValidUrl(cta.externalLink)) {
                cta.externalLink = 'https://example.com';
              }
              cta.internalLink = ''; // Empty when external
            }
          }
        }

        if (response.campusDetails?.callToAction) {
          const cta = response.campusDetails.callToAction;
          if (cta.enabled) {
            // Ensure buttonText is not empty
            if (!cta.buttonText) cta.buttonText = 'Learn More';

            // Ensure correct link based on linkType
            if (cta.linkType === 'internal') {
              if (!cta.internalLink) cta.internalLink = '/';
              // Don't set externalLink when type is internal
              cta.externalLink = undefined;
            } else {
              if (!cta.externalLink || !isValidUrl(cta.externalLink)) {
                cta.externalLink = 'https://example.com';
              }
              cta.internalLink = ''; // Empty when external
            }
          }
        }

        if (response.research?.callToAction) {
          const cta = response.research.callToAction;
          if (cta.enabled) {
            // Ensure buttonText is not empty
            if (!cta.buttonText) cta.buttonText = 'Learn More';

            // Ensure correct link based on linkType
            if (cta.linkType === 'internal') {
              if (!cta.internalLink) cta.internalLink = '/';
              // Don't set externalLink when type is internal
              cta.externalLink = undefined;
            } else {
              if (!cta.externalLink || !isValidUrl(cta.externalLink)) {
                cta.externalLink = 'https://example.com';
              }
              cta.internalLink = ''; // Empty when external
            }
          }
        }

        landingPageData.value = response;
      }
    } catch (err) {
      error.value = 'Failed to fetch landing page data';
      toast.error('Failed to fetch landing page data');
      console.error('Error fetching landing page data:', err);
    } finally {
      isLoading.value = false;
    }
  };

  // Helper to validate URLs
  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  // Update landing page data
  const updateLandingPage = async (updateData: LandingPageConfiguration) => {
    const toastId = toast.loading('Updating landing page data...');
    try {
      isLoading.value = true;
      error.value = null;

      // Validate and fix callToAction objects before submission
      if (updateData.principalsMessage?.callToAction) {
        const cta = updateData.principalsMessage.callToAction;
        if (cta.enabled) {
          // Ensure buttonText is not empty
          if (!cta.buttonText) cta.buttonText = 'Learn More';

          // Ensure correct link based on linkType
          if (cta.linkType === 'internal') {
            if (!cta.internalLink) cta.internalLink = '/';
            // Don't set externalLink when type is internal
            cta.externalLink = undefined;
          } else {
            if (!cta.externalLink || !isValidUrl(cta.externalLink)) {
              cta.externalLink = 'https://example.com';
            }
            cta.internalLink = ''; // Empty when external
          }
        }
      }

      if (updateData.campusDetails?.callToAction) {
        const cta = updateData.campusDetails.callToAction;
        if (cta.enabled) {
          // Ensure buttonText is not empty
          if (!cta.buttonText) cta.buttonText = 'Learn More';

          // Ensure correct link based on linkType
          if (cta.linkType === 'internal') {
            if (!cta.internalLink) cta.internalLink = '/';
            // Don't set externalLink when type is internal
            cta.externalLink = undefined;
          } else {
            if (!cta.externalLink || !isValidUrl(cta.externalLink)) {
              cta.externalLink = 'https://example.com';
            }
            cta.internalLink = ''; // Empty when external
          }
        }
      }

      if (updateData.research?.callToAction) {
        const cta = updateData.research.callToAction;
        if (cta.enabled) {
          // Ensure buttonText is not empty
          if (!cta.buttonText) cta.buttonText = 'Learn More';

          // Ensure correct link based on linkType
          if (cta.linkType === 'internal') {
            if (!cta.internalLink) cta.internalLink = '/';
            // Don't set externalLink when type is internal
            cta.externalLink = undefined;
          } else {
            if (!cta.externalLink || !isValidUrl(cta.externalLink)) {
              cta.externalLink = 'https://example.com';
            }
            cta.internalLink = ''; // Empty when external
          }
        }
      }

      const response = await $fetch<{ success: boolean }>(
        '/api/admin/landing-page/update',
        {
          method: 'PUT',
          body: updateData,
        }
      );

      if (response.success) {
        toast.success('Landing page updated successfully', { id: toastId });
        // Refresh data after update
        await fetchLandingPage();
      }
    } catch (err) {
      error.value = 'Failed to update landing page';
      console.error('Error updating landing page:', err);
      toast.error('Failed to update landing page', { id: toastId });
      throw err; // Re-throw to handle in component if needed
    } finally {
      isLoading.value = false;
    }
  };

  // Reset error state
  const clearError = () => {
    error.value = null;
  };

  return {
    // State
    landingPageData,
    isLoading,
    error,

    // Methods
    fetchLandingPage,
    updateLandingPage,
    clearError,
  };
};
