import type { linkButtonSchema } from '~~/shared/schema';
export const APP_URL = import.meta.env.NUXT_HUB_APP_URL;
export const BLOB_URL = `${APP_URL}/api/_hub/blob`;

export function getPreviewUrl(pathname: string): string {
  if (!pathname) {
    return '/temp/default-sample.jpg';
  }

  return `/images/${pathname}`;
  // return `${BLOB_URL}/${pathname}`;
}

export function formatDate(date: number): {
  month: string;
  day: string;
  year: number;
} {
  // Create date object and adjust for timezone
  const dateObj = new Date(date);
  const localDate = new Date(
    dateObj.getTime() + dateObj.getTimezoneOffset() * 60000
  );

  return {
    month: localDate.toLocaleString('en-US', { month: 'short' }),
    day: localDate.getDate().toString().padStart(2, '0'),
    year: localDate.getFullYear(),
  };
}

export function transformButton(button: typeof linkButtonSchema._type) {
  const formattedButton = {
    title: button.title,
    style: button.style || 'primary',
    icon: button.icon,
    internalLink: button.internalLink,
    externalLink: button.externalLink,
    newTab: button.newTab,
    type: button.type || 'link',
  };
  //if icon is none, remove it from formattedButton
  if (button.icon === 'none' || !button.icon) {
    delete formattedButton.icon;
  }
  if (button.internalLink) {
    //remove externalLink from formattedButton
    delete formattedButton.externalLink;
  } else if (button.externalLink) {
    //remove internalLink from formattedButton
    delete formattedButton.internalLink;
  }
  return formattedButton;
}
