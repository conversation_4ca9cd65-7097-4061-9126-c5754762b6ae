import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const getFileType = (fileType: string) => {
  if (fileType.includes('image')) return 'image';
  if (fileType.includes('pdf')) return 'pdf';
  return 'other';
};

export const downloadFile = ({
  fileName = 'sample',
  fileUrl,
}: {
  fileUrl: string;
  fileName: string;
}) => {
  const link = document.createElement('a');
  link.href = getPreviewUrl(fileUrl);
  link.setAttribute('download', fileName); // Name of the downloaded file
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
