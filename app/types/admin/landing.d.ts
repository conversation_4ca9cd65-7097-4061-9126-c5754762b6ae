export type ButtonConfig = {
  text: string;
  link: string;
};

export type PrincipalMessage = {
  title: string;
  subtitle1?: string;
  subtitle2?: string;
  content1?: string;
  button: ButtonConfig;
};

export type CampusDetails = {
  title: string;
  description: string;
  button: ButtonConfig;
};

export type Stat = {
  icon: string;
  value: string;
  title: string;
};

export type Research = {
  title: string;
  description: string;
  button: ButtonConfig;
};

export type Toggles = {
  showPrograms: boolean;
  showEvents: boolean;
  showGallery: boolean;
};

export type HomePageFormData = {
  principalMessage: PrincipalMessage;
  campus: CampusDetails;
  stats: Stat[];
  research: Research;
  toggles: Toggles;
};
