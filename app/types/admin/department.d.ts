import type { CourseType } from '~~/server/database/tables';
import type { FileData } from '../home';
import type { LinkButtonConfig } from './card';

export type DownloadData = {
  id: number;
  title: string;
  file: FileData;
};

export type QuestionBankData = DownloadData & {
  year: number | null;
  semester: number | null;
  course: {
    id: number;
    name: string;
  } | null;
};

export type ProjectWorkData = QuestionBankData;

export type DepartmentFileResponse = {
  success: boolean;
  data: {
    files: QuestionBankData[] | ProjectWorkData[] | DownloadData[];
    pagination: Pagination;
  };
};

export type Department = {
  id: number;
  name: string;
  slug: string;
  overview: string;
  galleryIds: number[];
  eventIds: number[];
  achievementId: number | null;
  downloads: DepartmentDownload[];
  files: DepartmentFile[];
};

export type CreateDepartmentPayload = {
  overview: string;
  galleryIds: number[];
  eventIds: number[];
  achievementId: number | null;
  downloads: DepartmentDownload[];
  files: DepartmentFile[];
};

export type DepartmentOverviewData = {
  id: number;
  name: string;
  slug: string;
};

export type Course = {
  id: number;
  name: string;
  code: string;
  departmentId: number;
  semester: number;
  credits: number;
  description?: string;
  syllabus?: string;
};

export type Faculty = {
  id: number;
  name: string;
  departmentId: number;
  designation: string;
  startDate: string;
  endDate?: string;
  resumeId?: number;
  education: string;
  experience: string;
  areaOfInterest: string;
};

export type Student = {
  id: number;
  name: string;
  startDate: string;
  endDate?: string;
  departmentId: number;
  courseId: number;
  startYear: number;
  passOutYear?: number;
  mark?: string;
  placedAt?: string;
};

export type DepartmentListData = {
  id: number;
  name: string;
  slug: string;
};

export type CreateDepartmentData = {
  id: number;
  name: string;
  slug: string;
};

export type CreateDepartmentResponse = {
  success: boolean;
  data: CreateDepartmentData;
};

export type SlugValidateResponse = {
  isAvailable: boolean;
};
export type CourseListData = {
  id: number;
  name: string;
  specialization: string;
  durationInMonths: number;
  semesterCount: number;
  title: string;
  type: CourseType;
  seatsCount: number;
  content: string;
  departmentId: number;
  image: FileData;
  syllabus: FileData;
  pos: FileData;
  linkButton: LinkButtonConfig;
};

export type FacultyEducation = {
  degree: string;
  university: string;
  passOutYear: number;
};

export type FacultyExperience = {
  startYear: number;
  organization: string;
  designation: string;
};

export type FacultyListData = {
  id: number;
  name: string;
  designation: string;
  departmentId: number;
  startDate: string;
  endDate: string | null;
  areaOfInterest: string[];
  education: FacultyEducation[];
  experience: FacultyExperience[];
  resume: FileData;
  image: FileData;
  priority: number;
};

export type StudentData = {
  course: { name: string };
  courseId: number;
  createdAt: number;
  departmentId: number;
  id: number;
  image: FileData;
  imageId: number;
  mark: number;
  name: string;
  passOutYear: number;
  placedAt: string;
  startYear: number;
  type: StudentType;
  updatedAt: number;
};

export type DepartmentDetails = {
  id: number;
  name: string;
  slug: string;
};
