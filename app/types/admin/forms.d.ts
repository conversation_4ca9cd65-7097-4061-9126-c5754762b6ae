import type { ButtonConfig, FileConfig } from '~~/server/helper/section';

export type BaseCardFormData = {
  title: string;
  image: FileConfig;
  key: string;
  priority: number;
  description?: string; // Added for UI consistency
};

export type ProfileCardFormData = BaseCardFormData & {
  subtitle1: string;
  linkButton?: ButtonConfig;
  downloadButton?: ButtonConfig;
};

export type StandardCardFormData = BaseCardFormData & {
  subtitle1: string;
  content1: string;
};

export type VisionCardFormData = BaseCardFormData & {
  title: string;
  content1: string;
};

export type CardFormData =
  | ProfileCardFormData
  | StandardCardFormData
  | VisionCardFormData;

export type CardListFormData = {
  title: string;
  description?: string;
  maxCards: number;
  minCards: number;
  cards: CardFormData[];
};
