import type { buttonBaseTable } from '~~/server/database/tables/buttons/buttonBase';
import type { contentSchema } from '~~/server/database/tables/content';
import type { FileData } from '../home';
// types.ts
export type CardType = 'single' | 'list';
export type CardLayout = 'profile' | 'standard' | 'vision';

// Define the profile card configuration type based on the schema
// type ContentSchema = {
//   title: string;
//   content?: string;
// };
export type ContentSchema = typeof contentSchema._type;

export type ButtonBase = typeof buttonBaseTable._type;

type LinkButtonConfig = ButtonBase & {
  type: 'link';
  newTab: boolean;
  internalLink?: string | null;
  externalLink?: string | null;
};

type DownloadButtonConfig = ButtonBase & {
  type: 'download';
  newTab: boolean;
  file: {
    type: string;
    pathname: string;
    title?: string;
    prefix?: string;
  };
};

type StandardCardConfig = {
  layout: 'standard';
  title: string;
  content1: ContentSchema;
  image: FileData;
  linkButton?: LinkButtonConfig;
  downloadButton?: DownloadButtonConfig;
  files?: FileData[];
  key: string;
};

type ProfileCardConfig = {
  layout: 'profile';
  content1?: ContentSchema;
  content2?: ContentSchema;
  image: FileData;
  linkButton?: LinkButtonConfig;
  downloadButton?: DownloadButtonConfig;
  key: string;
};

export type VisionCardConfig = {
  layout: 'vision';
  content1: ContentSchema;
  image: FileData;
  key: string;
};

export type CardListConfig = {
  title: string;
  description: string;
  maxCards: number;
  minCards: number;
  layout: CardLayout;
  cards: (ProfileCardConfig | StandardCardConfig | VisionCardConfig)[];
};
type CardSectionInput = {
  type: 'card';
  templateId: number;
  priority: number;
  configuration:
    | ProfileCardConfig
    | StandardCardConfig
    | VisionCardConfig
    | CardListConfig;
};
