export type LandingPageData = {
  readonly principalsMessage: PrincipalsMessage;
  readonly campusDetails: CampusDetails;
  readonly research: CampusDetails;
  readonly stats: Stats;
  // readonly sectionVisibility: SectionVisibility;
};

export type CampusDetails = {
  readonly title: string;
  readonly description: string;
  readonly image?:
    | { pathname: string; title: string; type: string }
    | undefined;
  readonly callToAction: CallToAction | undefined;
};

export type CallToAction = {
  readonly announcementId: number | null;
  readonly cardTemplateId: number | undefined;
  readonly title: string | undefined;
  readonly style: string | undefined;
  readonly icon: string | undefined;
  readonly internalLink: string | undefined;
  readonly externalLink: string | undefined;
  readonly newTab: boolean | undefined;
  readonly enabled: boolean | undefined;
};

export type PrincipalsMessage = {
  readonly title: string;
  readonly name: string;
  readonly designation: string;
  readonly message: string;
  readonly image?:
    | { pathname: string; title: string; type: string }
    | undefined;
  readonly callToAction: CallToAction | undefined;
};

export type SectionVisibility = {
  readonly programsWeOffer: boolean;
  readonly events: boolean;
  readonly gallery: boolean;
};

export type Stats = {
  readonly items: Item[];
};

export type Item = {
  readonly icon: string;
  readonly value: number;
  readonly title: string;
};
