import type { IQAC_TEAM_MEMBERS } from '~~/server/database/tables/dynamic-types';
import type { PaginationResponse } from '~~/shared/schema/pagination';
import type { FileData } from '../home';
import type { LinkButtonConfig } from './card';

export type IQAC_TEAM_MEMBER_RESPONSE = {
  id: number;
} & IQAC_TEAM_MEMBERS;

export type IQACDownloadData = {
  id: number;
  title: string;
  file: FileData;
};

export type IQACAttachmentData = {
  id: number;
  title: string;
  linkButton: LinkButtonConfig;
  files: FileData[];
};

export type IQACDownloadsResponse = {
  downloads: IQACDownloadData[];
  pagination: PaginationResponse;
};

export type IQACOverviewResponse = {
  title: string;
  image: FileData | null;
  content: string;
  files: FileData[];
  linkButton: LinkButtonConfig;
};

export type IQACAttachmentsResponse = {
  attachments: IQACAttachmentData[];
  pagination: PaginationResponse;
};

export type IQACTeamTitleResponse = {
  id: number;
  title: string;
  priority: number;
};
