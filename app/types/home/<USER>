//NOTE: Helloo Dennyyy   This is the type for the cards for the landing page

export type VisionCard = {
  title: string;
  description: string;
  image: FileData;
  layout: 'vision';
};

export type VisionCardListResponse = {
  success: boolean;
  data: VisionCard[];
};

export type StandardCard = {
  title: string;
  subtitle: string;
  content: string;
  image: FileData;
  linkButton?: LinkButton;
  files: FileData[];
  downloadButton?: DownloadButton;
  layout: 'standard';
};

export type CardsList = {
  title: string;
  description: string;
  layout: 'standard' | 'profile' | 'vision';
  cards: StandardCard[] | ProfileCard[] | VisionCard[];
};

export type ProfileCard = {
  title: string;
  subtitle1: string;
  content1: string;
  subtitle2: string;
  content2: string;
  image: FileData;
  linkButton?: LinkButton;
  downloadButton?: DownloadButton;
  layout: 'profile';
};

export type ProfileCardListResponse = {
  success: boolean;
  data: ProfileCard[];
};

export type Configuration = StandardCard | ProfileCard | VisionCard | CardsList;

export type MenuBySlugData = {
  menuData: {
    name: string;
    slug: string;
    relatedLinks: {
      label: string;
      link: string;
    }[];
  };
  configuration: Configuration;
};
