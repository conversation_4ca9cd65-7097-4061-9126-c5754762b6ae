import type { FileData } from './common';

export type Education = {
  degree: string;
  university: string;
  passOutYear: number;
};

export type Experience = {
  startYear: number;
  endYear: number;
  organization: string;
  designation: string;
};

export type Faculty = {
  name: string;
  id: number;
  designation: string;
  image: FileData;
};

export type FacultyFullData = {
  name: string;
  designation: string;
  startDate: string;
  education: Education[];
  experience: Experience[];
  areaOfInterest: string[];
  image: FileData | null;
  resume: FileData | null;
};

export interface FacultyListResponse {
  success: boolean;
  data: Faculty[];
}
