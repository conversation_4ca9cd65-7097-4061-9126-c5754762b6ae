import type { FileData, LinkButton } from './common';
import type { PaginationResponse } from './pagination';

export type Download = {
  title: string;
  file: FileData;
};

export type Composition = {
  title: string;
  members: {
    name: string;
    designation: string;
    designationOrder: number;
  }[];
};

export type CompositionResponse = {
  compositions: Composition[];
  attachments: Download[];
};

export type Attachment = {
  title: string;
  button: LinkButton | null;
  files: FileData[];
};

export type AttachmentResponse = {
  attachments: Attachment[];
  pagination: PaginationResponse;
};

export type OverviewResponse = {
  id: number;
  title: string;
  content: string;
  image: FileData | null;
  files: FileData[];
  linkButton: LinkButton | null;
};

export type TeamMember = {
  id: number;
  name: string;
  designation: string;
  image: FileData;
  bio?: string;
};

export type TeamMembersResponse = {
  members: TeamMember[];
  pagination: PaginationResponse;
};

export type DownloadResponse = {
  downloads: Download[];
  pagination: PaginationResponse;
};

export type IQACData = Download[] | Composition[] | Attachment[] | Overview;
