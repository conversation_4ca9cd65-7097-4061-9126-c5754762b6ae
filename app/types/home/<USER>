import type { FileData } from './common';

export type DownloadFiles = {
  id: number;
  title: string;
  department: {
    id: number;
    name: string;
  };
  file: FileData;
};

export type ProjectFiles = {
  id: number;
  title: string;
  year: number;
  courseName: string;
  semester: number;
  department: {
    id: number;
    name: string;
  };
  file: FileData;
};

export type QuestionBankFiles = ProjectFiles;

export type DepartmentFilesResponse = {
  success: boolean;
  data: QuestionBankFiles[] | DownloadFiles[] | ProjectFiles[];
  pagination: Pagination;
};
