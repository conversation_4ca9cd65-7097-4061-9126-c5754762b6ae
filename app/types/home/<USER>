export type Pagination = {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

export type LinkButton = {
  enabled?: boolean | undefined;
  title: string;
  style: string;
  newTab: boolean;
  icon?: string;
  link: string;
} | null;

export type FileData = {
  pathname: string;
  title: string;
  type: string;
  prefix: string;
};
