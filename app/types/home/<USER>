import type { <PERSON><PERSON><PERSON>, Button, Pagination } from '.';

export type AnnouncementListData = {
  id: number;
  title: string;
  date: number | null;
  description: string;
  primaryImage: FileData;
};

export type AnnouncementData = {
  id: number;
  title: string;
  date: number | null;
  description: string;
  button: Button | null;
  primaryImage: FileData;
  attachments: FileData[];
};

export type AnnouncementListQuery = {
  type: 'event' | 'update' | 'notice';
  page: number;
  limit: number;
};

export type AnnouncementListResponse = {
  success: boolean;
  data: AnnouncementListData[];
  pagination: Pagination;
};

export type GalleryListData = {
  id: number;
  title: string;
  photosCount: number;
  photos: FileData[];
};

export type GalleryListQuery = {
  page: number;
  limit: number;
};

export type GalleryListResponse = {
  success: boolean;
  data: GalleryListData[];
  pagination: Pagination;
};

export type GalleryData = {
  id: number;
  title: string;
  photosCount: number;
  photos: FileData[];
};

export type GalleryResponse = {
  success: boolean;
  data: GalleryData;
};
