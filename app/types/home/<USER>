import type { FileData, LinkButton } from './common';

export type CourseFilter = {
  id: number;
  name: string;
};

export type QuestionBankFilterResponse = {
  courses: CourseFilter[];
  semesters: number[];
  years: number[];
};

export type Course = {
  id: number;
  departmentId: number;
  departmentSlug?: string;
  name: string;
  image: FileData;
  specialization: string;
  durationInMonths: number;
  semesterCount: number;
  seatsCount: number;
  content: {
    title: string;
    description: string;
  };
  syllabus: FileData | null;
  pos: FileData | null;
  primaryButton: LinkButton | null;
};

export interface CourseListResponse {
  success: boolean;
  data: Course[];
}
