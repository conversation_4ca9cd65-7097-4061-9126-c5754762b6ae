import type { <PERSON><PERSON><PERSON>, <PERSON>Button, Pagination } from '.';

export type UpdateListData = {
  id: number;
  title: string;
  link: string | null;
};

export type AnnouncementListData = {
  id: number;
  title: string;
  date: number | null;
  description: string;
  primaryImage: FileData;
};

export type AnnouncementData = {
  id: number;
  title: string;
  date: number | null;
  description: string;
  button: LinkButton | null;
  primaryImage: FileData | null;
  attachments: FileData[];
};

export type AnnouncementListQuery = {
  type: 'event' | 'update' | 'notice';
  page: number;
  limit: number;
};

export type AnnouncementListResponse = {
  success: boolean;
  data: AnnouncementListData[];
  pagination: Pagination;
};

export type UpdateListResponse = {
  success: boolean;
  data: UpdateListData[];
};
