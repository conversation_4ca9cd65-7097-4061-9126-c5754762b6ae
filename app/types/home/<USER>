export type DepartmentGallery = {
  id: number;
  title: string;
  primaryImage: {
    id: number;
    title: string;
    pathname: string;
    type: string;
  };
  count: number;
};

export type DepartmentGalleryResponse = {
  success: boolean;
  data: DepartmentGallery[];
};

export type DepartmentEvent = {
  id: number;
  displayOrder: number;
  departmentId: number;
  createdAt: number | null;
  updatedAt: number | null;
  title: string;
  link: string | null;
  isActive: boolean;
  priority: number;
  content: string;
  eventDate: number | null;
  scheduledAt: number | null;
  expiresAt: number | null;
  announcementType: 'event';
  files: {
    id: number;
    createdAt: number | null;
    updatedAt: number | null;
    announcementId: number;
    fileId: number;
    file: {
      id: number;
      title: string | null;
      type: string;
      pathname: string;
    };
  }[];
  button: {
    id: number;
    createdAt: number | null;
    updatedAt: number | null;
    title: string;
    announcementId: number | null;
    icon: string | null;
    style: 'primary' | 'secondary' | 'outline';
    externalLink: string | null;
    internalLink: string | null;
    newTab: boolean | null;
    cardTemplateId: number | null;
  } | null;
};

export type DepartmentEventResponse = {
  success: boolean;
  data: DepartmentEvent[];
};

export type FacultyCard = {
  imgSrc: string;
  name: string;
  caption: string;
  percentage?: number;
  companyName?: string;
};

export type ProjectYears = {
  success: boolean;
  data: number[];
};

export type QuestionBankFilters = {
  success: boolean;
  data: {
    courses: Array<{ id: number; name: string }>;
    semesters: Array<number>;
    years: Array<number>;
  };
};

export type Topper = {
  name: string;
  id: number;
  course: string;
  department: string;
  startYear: number;
  endYear: number;
  percentage: number;
  image: FileData;
};

export type ToppersListResponse = {
  success: boolean;
  data: Topper[];
  pagination: Pagination;
};

export type Placement = Omit<Topper, 'percentage'> & { company: string };
export type PlacementListResponse = Omit<ToppersListResponse, 'data'> & {
  data: Placement[];
};
