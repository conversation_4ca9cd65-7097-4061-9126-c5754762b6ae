import type { AnnouncementListData, FileData, GalleryListData } from '../home';
import type { LinkButtonConfig } from './card';

export interface ClubCommitteeData {
  id: number;
  title: string;
  overview: string;
  slug: string;
  image: FileData | null;
  type: 'club' | 'committee';
  attachments: FileData[] | null;
  linkButton: LinkButtonConfig | null;
  galleries: GalleryListData[];
  events: AnnouncementListData[];
}

export type HomeClubCommitteeResponse = {
  menuData: {
    name: string;
    slug: string;
    relatedLinks: {
      label: string;
      link: string;
    }[];
  };
  data: ClubCommitteeData;
};
