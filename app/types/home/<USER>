import type { AnnouncementListData } from './announcement';
import type { Course } from './course';

type QuickLink = {
  id: number;
  title: string;
  link: string;
  icon: string;
};

type PrincipalMessage = {
  title: string;
  name: string;
  designation: string;
  message: string;
  primaryImage: FileData;
  button: <PERSON>Button;
};

type CampusDetails = {
  title: string;
  description: string;
  button: LinkButton;
  primaryImage: FileData;
};

type SectionVisibility = {
  programsWeOffer: boolean;
  events: boolean;
  gallery: boolean;
};

type Stat = {
  icon: string;
  value: string;
  title: string;
};

type Research = {
  title: string;
  description: string;
  button: LinkButton;
};

type NotificationData = {
  id: number;
  title: string;
  date: number | null;
};

type UpdateData = {
  id: number;
  title: string;
  buttonTitle: string | null;
  link: string | null;
};

type Programs = {
  ug: Course[];
  pg: Course[];
  addOn: Course[];
};

type LandingPage = {
  principalMessage: PrincipalMessage;
  campusDetails: CampusDetails;
  stats: Stat[];
  research: Research;
  // sectionVisibility: SectionVisibility;
  quickLinks: QuickLink[];
  programs: Programs;
  events: AnnouncementListData[];
  notifications: NotificationData[];
  updates: UpdateData[];
  gallery: FileData[];
};
