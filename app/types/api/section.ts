import type { CreateSectionInput } from '~~/shared/schema/section/create';

// Base content type
export interface ContentEntry {
  title: string;
  content?: string;
  type: string;
  priority: number;
}

// File type
export interface FileData {
  pathname: string;
  title: string;
  type: string;
}

// Button types
export interface BaseButton {
  title: string;
  style: string;
  icon?: string;
  newTab?: boolean;
}

export interface LinkButton extends BaseButton {
  type: 'link';
  externalLink?: string;
  internalLink?: string;
}

export interface DownloadButton extends BaseButton {
  type: 'download';
  file: FileData;
}

export type ButtonConfig = LinkButton | DownloadButton;

// Card Configuration Types
interface BaseCardConfig {
  layout: string;
  key: string;
  image?: FileData;
}

export interface StandardCardConfig extends BaseCardConfig {
  layout: 'standard';
  title: string;
  content1: { title: string; content: string };
}

export interface VisionCardConfig extends BaseCardConfig {
  layout: 'vision';
  content: { title: string; content: string };
}

export interface ProfileCardConfig extends BaseCardConfig {
  layout: 'profile';
  content: { title: string; content: string };
}

export type CardConfig =
  | StandardCardConfig
  | VisionCardConfig
  | ProfileCardConfig;

// Card List Configuration
export interface CardListConfig {
  title: string;
  description?: string;
  maxCards: number;
  minCards: number;
  cards: CardConfig[];
  layout: string;
}

// Text Editor Configuration
export interface TextEditorConfig {
  title: string;
  description?: string;
}

// Accordion Configuration
export interface AccordionConfig {
  title: string;
  description?: string;
  content?: string;
}

// Section Configuration Union Type
export type SectionConfiguration =
  | CardConfig
  | CardListConfig
  | TextEditorConfig
  | AccordionConfig;

// API Request Type
export type CreateSectionRequest = CreateSectionInput;

// API Response Types
export interface CreateSectionResponse {
  success: boolean;
  data: {
    id: number;
    templateId: number;
    type: string;
    priority: number;
    configuration: SectionConfiguration;
  };
}

// API Error Response
export interface ApiError {
  statusCode: number;
  message: string;
  stack?: string;
}
export type SectionWithConfigData = {
  id: number;
  type: string;
  priority: number;
  templateId: number | null;
  createdAt: number | null;
  updatedAt: number | null;
  configuration: any;
};
// Standardized Section Response Type
export interface SectionResponse {
  success: boolean;
  data: SectionData | null;
}

export interface SectionData {
  id: number;
  type: string;
  priority: number;
  templateId: number | null;
  createdAt: number | null;
  updatedAt: number | null;
  configuration: SectionConfiguration;
}
