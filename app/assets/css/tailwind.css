@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 245 21% 100%;
    --foreground: 245 5% 10%;
    --card: 245 21% 100%;
    --card-foreground: 245 5% 15%;
    --popover: 245 21% 100%;
    --popover-foreground: 245 95% 10%;
    --primary: 212 100% 27%;
    --primary-foreground: 0 0% 100%;
    --secondary: 245 21% 90%;
    --secondary-foreground: 0 0% 0%;
    --muted: 220 43% 97%;
    --muted-foreground: 245 5% 40%;
    --accent: 207 21% 90%;
    --accent-foreground: 245 5% 15%;
    --destructive: 0 50% 50%;
    --destructive-foreground: 245 5% 100%;
    --border: 245 21% 82%;
    --input: 0 0% 75%;
    --ring: 245 66.2% 59.4%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 245 21% 10%;
    --foreground: 245 5% 100%;
    --card: 245 21% 10%;
    --card-foreground: 245 5% 100%;
    --popover: 245 21% 5%;
    --popover-foreground: 245 5% 100%;
    --primary: 245 66.2% 59.4%;
    --primary-foreground: 0 0% 100%;
    --secondary: 245 21% 20%;
    --secondary-foreground: 0 0% 100%;
    --muted: 207 21% 25%;
    --muted-foreground: 245 5% 65%;
    --accent: 207 21% 25%;
    --accent-foreground: 245 5% 95%;
    --destructive: 0 50% 50%;
    --destructive-foreground: 245 5% 100%;
    --border: 245 21% 50%;
    --input: 0 0% 75%;
    --ring: 245 66.2% 59.4%;
    --radius: 0.75rem;
  }
}
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@layer utilities {
  .bg-primary-gradient {
    background: linear-gradient(101deg, #2369aa -5%, #267ccb 90%);
  }
  .scrollbar-hidden::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hidden {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .flex-center {
    @apply flex items-center justify-center;
  }
  .bg-border-blue {
    background-color: #3e86ca;
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}
@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground text-justify;
    font-family: 'Inter', sans-serif;
  }

  .admin-body {
    @apply fixed w-full;
    font-family: 'Inter', sans-serif;
  }

  .font-raleway {
    font-family: 'Raleway', serif;
  }
}

@keyframes zoom {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.1);
  }
}

.animate-zoom {
  animation: zoom 5s ease-in-out forwards;
}

@layer components {
  .flex-center {
    @apply flex items-center justify-center;
  }
}

.marquee {
  animation: marquee 5s linear infinite;
}

.notices:hover {
  .marquee {
    animation-play-state: paused;
  }
}

@keyframes marquee {
  to {
    transform: translateY(calc(-100% - 24px));
  }
}
