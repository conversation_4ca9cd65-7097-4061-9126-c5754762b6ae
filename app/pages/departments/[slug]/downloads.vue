<script setup lang="ts">
import { Search } from 'lucide-vue-next';
import type { DepartmentFilesResponse } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

// State management
const page = ref(1);
const limit = ref(15);
const isLoading = ref(false);

const route = useRoute('departments-slug');
const slug = route.params.slug;

// Initial data fetch using useAsyncData
const { data: downloadList, refresh: refreshEvents } =
  await useAsyncData<DepartmentFilesResponse>(
    'downloads-list',
    async () => {
      const response = await useFetch<DepartmentFilesResponse>(
        `/api/home/<USER>/${slug}/downloads`,
        {
          query: {
            filterStatus: 'active',
            page: page.value,
            limit: limit.value,
          },
        }
      );

      // Ensure we return a valid downloadsResponse or throw an error
      if (!response.data.value) {
        throw new Error('Failed to fetch downloads');
      }

      return response.data.value;
    },
    {
      immediate: true,
      default: () => ({
        success: false,
        data: [],
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: limit.value,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      }),
    }
  );

// Page change handler
const handlePageChange = async (newPage: number) => {
  isLoading.value = true;
  try {
    page.value = newPage;
    await refreshEvents();
  } catch (error) {
    console.error('Failed to fetch downloads:', error);
  } finally {
    isLoading.value = false;
  }
};

// Computed properties for better readability and performance
const hasDownloadsList = computed(() =>
  Boolean(downloadList.value?.data && downloadList.value.data.length > 0)
);

const paginationVisible = computed(() =>
  Boolean(
    downloadList.value?.pagination &&
      downloadList.value.pagination.totalPages > 1
  )
);

const searchTerm = ref('');

const filteredDownloads = computed(() => {
  if (!searchTerm.value) return downloadList.value.data;
  const lowerCaseSearchTerm = searchTerm.value.toLowerCase();
  return downloadList.value.data.filter((item) =>
    item.title.toLowerCase().includes(lowerCaseSearchTerm)
  );
});
</script>

<template>
  <HomeDepartmentContainer
    title="Downloads"
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'Departments' },
        { label: slug, link: `/departments/${slug}` },
        { label: 'Downloads' },
      ],
    }"
  >
    <!-- Loading state -->
    <div v-if="isLoading" class="py-8">
      <p class="text-muted-foreground">Loading downloads...</p>
    </div>

    <div v-else-if="hasDownloadsList">
      <div
        class="h-[48px] flex items-center px-3 lg:px-5 bg-white w-full rounded-[27px] overflow-hidden border border-[#E1E1E1]"
      >
        <Search class="text-[#A7A7A7]" />
        <input
          v-model="searchTerm"
          type="text"
          class="h-full w-full border-none text-lg font-semibold outline-none px-2 lg:px-3 placeholder:text-[#A7A7A7]"
          placeholder="Search Albums"
        />
      </div>
      <HomeTemplateDownloadItem
        v-for="item of filteredDownloads"
        :key="item.id"
        :data="item"
      />
    </div>

    <!-- Empty state -->
    <div v-else class="text-center py-8">
      <HomeNoDataFound message="No downloads found" />
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="paginationVisible"
      v-slot="{ page: currentPageData }"
      class="my-16"
      :total="downloadList?.pagination?.totalItems ?? 0"
      :sibling-count="1"
      :items-per-page="downloadList?.pagination?.itemsPerPage ?? limit"
      show-edges
      :default-page="downloadList?.pagination?.currentPage ?? 1"
      @update:page="handlePageChange"
    >
      <PaginationList
        v-slot="{ items }"
        class="flex items-center justify-center gap-1"
      >
        <PaginationFirst />
        <PaginationPrev />

        <template v-for="(item, index) in items" :key="index">
          <PaginationListItem
            v-if="item.type === 'page'"
            :value="item.value"
            as-child
          >
            <Button
              class="w-10 h-10 p-0"
              :variant="item.value === currentPageData ? 'default' : 'outline'"
            >
              {{ item.value }}
            </Button>
          </PaginationListItem>
          <PaginationEllipsis v-else :index="index" />
        </template>

        <PaginationNext />
        <PaginationLast />
      </PaginationList>
    </Pagination>
  </HomeDepartmentContainer>
</template>
