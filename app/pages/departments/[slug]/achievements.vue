<script setup lang="ts">
definePageMeta({
  layout: 'landing',
});
const route = useRoute('departments-slug');
const slug = route.params.slug;
</script>

<template>
  <HomeDepartmentContainer
    title="Achievements"
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'Departments' },
        { label: slug, link: `/departments/${slug}` },
        { label: 'Achievements' },
      ],
    }"
  >
    <Accordion type="single" collapsible>
      <AccordionItem value="item-1">
        <AccordionTrigger
          class="bg-[#E4E4E4] px-5 rounded-[5px]"
          chevron-class="text-primary w-5 h-auto"
        >
          <span>University Rank/Position</span>
        </AccordionTrigger>
        <AccordionContent class="py-4 border-none">
          <Table>
            <TableHeader class="bg-primary">
              <TableRow>
                <TableHead class="w-[120px] text-white">Year</TableHead>
                <TableHead class="text-white">Name</TableHead>
                <TableHead class="text-white">Course</TableHead>
                <TableHead class="w-[120px] text-white">Rank</TableHead>
                <TableHead class="text-white">University</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow
                v-for="(_, index) of Array.from({ length: 10 })"
                :key="index"
              >
                <TableCell class-name="font-medium">2001</TableCell>
                <TableCell>Ashish Sharma</TableCell>
                <TableCell>Technology</TableCell>
                <TableCell class-name="text-right">1</TableCell>
                <TableCell class-name="text-right">Google</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value="item-1">
        <AccordionTrigger
          class="bg-[#E4E4E4] px-5 rounded-[5px]"
          chevron-class="text-primary w-5 h-auto"
        >
          <span>Arts/Cultural</span>
        </AccordionTrigger>
        <AccordionContent class="py-4 border-none"></AccordionContent>
      </AccordionItem>
      <AccordionItem value="item-1">
        <AccordionTrigger
          class="bg-[#E4E4E4] px-5 rounded-[5px]"
          chevron-class="text-primary w-5 h-auto"
        >
          <span>Alumini</span>
        </AccordionTrigger>
        <AccordionContent class="py-4 border-none"></AccordionContent>
      </AccordionItem>
    </Accordion>
  </HomeDepartmentContainer>
</template>
