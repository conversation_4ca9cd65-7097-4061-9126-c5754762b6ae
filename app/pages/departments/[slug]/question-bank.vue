<script setup lang="ts">
import { ChevronDown, SlidersHorizontal } from 'lucide-vue-next';
import type {
  QuestionBankFilters,
  DepartmentFilesResponse,
  QuestionBankFiles,
} from '~/types/home';

definePageMeta({
  layout: 'landing',
});

// State management
const page = ref(1);
const limit = ref(15);
const isLoading = ref(false);
const selectedYear = ref<number | ''>(''); // Default value
const selectedCourseId = ref<number | ''>(''); // Default value
const selectedSemester = ref<number | ''>(''); // Default value
const route = useRoute('departments-slug');
const slug = route.params.slug;

// Initial data fetch using useAsyncData
const { data: questionBankList, refresh: refreshQuestionBank } =
  await useAsyncData<DepartmentFilesResponse>(
    'question-bank-list',
    async () => {
      const response = await useFetch<DepartmentFilesResponse>(
        `/api/home/<USER>/${slug}/question-bank`,
        {
          query: {
            year: selectedYear.value,
            courseId: selectedCourseId.value,
            semester: selectedSemester.value,
            filterStatus: 'active',
            page: page.value,
            limit: limit.value,
          },
        }
      );

      // Ensure we return a valid response or throw an error
      if (!response.data.value) {
        throw new Error('Failed to fetch question-bank');
      }

      return response.data.value;
    },
    {
      immediate: true,
      default: () => ({
        success: false,
        data: [],
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: limit.value,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      }),
    }
  );

const { data: questionBankFilters } = await useAsyncData<QuestionBankFilters>(
  'question-bank-filters',
  async () => {
    const response = await useFetch<QuestionBankFilters>(
      `/api/home/<USER>
      {
        query: {
          year: selectedYear.value,
          courseId: selectedCourseId.value,
          semester: selectedSemester.value,
        },
      }
    );

    // Ensure we return a valid response or throw an error
    if (!response.data.value) {
      throw new Error('Failed to fetch question-bank');
    }

    return response.data.value;
  },
  {
    immediate: true,
    default: () => ({
      success: false,
      data: {
        courses: [],
        semesters: [],
        years: [],
      },
    }),
  }
);

const selectYear = async (year: number | '') => {
  isLoading.value = true;
  try {
    selectedYear.value = year;
    await refreshQuestionBank();
  } catch (error) {
    console.error('Failed to fetch question bank list:', error);
  } finally {
    isLoading.value = false;
  }
};
const selectSemester = async (semester: number | '') => {
  isLoading.value = true;
  try {
    selectedSemester.value = semester;
    await refreshQuestionBank();
  } catch (error) {
    console.error('Failed to fetch question bank list:', error);
  } finally {
    isLoading.value = false;
  }
};
const selectCourse = async (course: number | '') => {
  isLoading.value = true;
  try {
    selectedCourseId.value = course;
    await refreshQuestionBank();
  } catch (error) {
    console.error('Failed to fetch question bank list:', error);
  } finally {
    isLoading.value = false;
  }
};

const resetFilters = () => {
  selectedCourseId.value = '';
  selectedSemester.value = '';
  selectedYear.value = '';
  refreshQuestionBank();
};

// Page change handler
const handlePageChange = async (newPage: number) => {
  isLoading.value = true;
  try {
    page.value = newPage;
    await refreshQuestionBank();
  } catch (error) {
    console.error('Failed to fetch question bank list:', error);
  } finally {
    isLoading.value = false;
  }
};

// Computed properties for better readability and performance
const hasQuestionBankList = computed(() =>
  Boolean(
    questionBankList.value?.data && questionBankList.value.data.length > 0
  )
);

const paginationVisible = computed(() =>
  Boolean(
    questionBankList.value?.pagination &&
      questionBankList.value.pagination.totalPages > 1
  )
);
</script>

<template>
  <HomeDepartmentContainer
    title="Question Bank"
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'Departments' },
        { label: slug, link: `/departments/${slug}` },
        { label: 'Question Bank' },
      ],
    }"
  >
    <div
      class="flex flex-col lg:flex-row lg:items-center text-lg font-semibold px-3 lg:px-5 py-3 bg-[#E4E4E4] rounded-[5px] w-full"
    >
      <div class="flex gap-x-2 text-base text-primary">
        <SlidersHorizontal class="w-4 text-primary" />Filters
        <Button
          variant="outline"
          class="lg:hidden rounded-[5px] bg-transparent ml-auto"
          @click="resetFilters"
          >Reset</Button
        >
      </div>
      <div
        class="flex flex-col md:flex-row items-center gap-3 mt-3 lg:mt-0 lg:ml-3"
      >
        <DropdownMenu :modal="false">
          <DropdownMenuTrigger class="w-full">
            <Button
              variant="outline"
              class="w-full justify-between text-sm h-[50px] lg:justify-center bg-white rounded-[4px] border-none"
              >Select Course <ChevronDown />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem :text-value="''" @click="selectCourse('')"
              >Select Course</DropdownMenuItem
            >
            <DropdownMenuItem
              v-for="item of questionBankFilters?.data.courses || []"
              :key="item.id"
              @click="selectCourse(item.id)"
              >{{ item.name }}</DropdownMenuItem
            >
          </DropdownMenuContent>
        </DropdownMenu>
        <DropdownMenu :modal="false">
          <DropdownMenuTrigger class="w-full">
            <Button
              variant="outline"
              class="w-full justify-between text-sm h-[50px] lg:justify-center bg-white rounded-[4px] border-none"
              >{{ selectedSemester || 'Select Semester' }} <ChevronDown />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem :text-value="''" @click="selectSemester('')"
              >Select Year</DropdownMenuItem
            >
            <DropdownMenuItem
              v-for="item of questionBankFilters?.data.semesters || []"
              :key="item"
              @click="selectSemester(item)"
              >{{ item }}</DropdownMenuItem
            >
          </DropdownMenuContent>
        </DropdownMenu>
        <DropdownMenu :modal="false">
          <DropdownMenuTrigger class="w-full">
            <Button
              variant="outline"
              class="w-full justify-between text-sm h-[50px] lg:justify-center bg-white rounded-[4px] border-none"
              >{{ selectedYear || 'Select Year' }}<ChevronDown />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem :text-value="''" @click="selectYear('')"
              >Select Year</DropdownMenuItem
            >
            <DropdownMenuItem
              v-for="item of questionBankFilters?.data.years || []"
              :key="item"
              @click="selectYear(item)"
              >{{ item }}</DropdownMenuItem
            >
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <Button
        variant="outline"
        class="hidden lg:inline-block rounded-[5px] bg-transparent ml-auto"
        @click="resetFilters"
        >Reset</Button
      >
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="py-8">
      <p class="text-muted-foreground">Loading Question Bank List...</p>
    </div>

    <HomeQuestionBankItem
      v-for="item of questionBankList.data"
      v-else-if="hasQuestionBankList"
      :key="item.id"
      :data="{ ...(item as QuestionBankFiles) }"
    />

    <!-- Empty state -->
    <div v-else class="text-center py-8">
      <HomeNoDataFound message="No Question Bank Found" />
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="paginationVisible"
      v-slot="{ page: currentPageData }"
      class="my-16"
      :total="questionBankList?.pagination?.totalItems ?? 0"
      :sibling-count="1"
      :items-per-page="questionBankList?.pagination?.itemsPerPage ?? limit"
      show-edges
      :default-page="questionBankList?.pagination?.currentPage ?? 1"
      @update:page="handlePageChange"
    >
      <PaginationList
        v-slot="{ items }"
        class="flex items-center justify-center gap-1"
      >
        <PaginationFirst />
        <PaginationPrev />

        <template v-for="(item, index) in items" :key="index">
          <PaginationListItem
            v-if="item.type === 'page'"
            :value="item.value"
            as-child
          >
            <Button
              class="w-10 h-10 p-0"
              :variant="item.value === currentPageData ? 'default' : 'outline'"
            >
              {{ item.value }}
            </Button>
          </PaginationListItem>
          <PaginationEllipsis v-else :index="index" />
        </template>

        <PaginationNext />
        <PaginationLast />
      </PaginationList>
    </Pagination>
  </HomeDepartmentContainer>
</template>
