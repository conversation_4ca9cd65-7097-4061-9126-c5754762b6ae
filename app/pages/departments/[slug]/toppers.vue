<script setup lang="ts">
import type { ToppersListResponse } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

// State management
const page = ref(1);
const limit = ref(15);
const isLoading = ref(false);
const route = useRoute('departments-slug');
const slug = route.params.slug;

const { data: topperResponse, refresh: refreshToppers } =
  await useAsyncData<ToppersListResponse>(
    'department-toppers',
    async () => {
      const response = await useFetch<ToppersListResponse>(
        `/api/home/<USER>/${slug}/topper`,
        {
          query: {
            filterStatus: 'active',
            page: page.value,
            limit: limit.value,
          },
        }
      );

      // Ensure we return a valid response or throw an error
      if (!response.data.value) {
        throw new Error('Failed to fetch department toppers');
      }

      return response.data.value;
    },
    {
      immediate: true,
      default: () => ({
        success: false,
        data: [],
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: limit.value,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      }),
    }
  );
const topperList = computed(() => topperResponse.value?.data || []);

// Page change handler
const handlePageChange = async (newPage: number) => {
  isLoading.value = true;
  page.value = newPage;
  await refreshToppers();
  isLoading.value = false;
};

// Computed properties for better readability and performance
const hasTopperData = computed(() => Boolean(topperList.value.length > 0));

const paginationVisible = computed(() =>
  Boolean(topperResponse.value?.pagination?.totalItems > limit.value)
);
</script>

<template>
  <HomeDepartmentContainer
    title="Toppers"
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'Departments' },
        { label: slug, link: `/departments/${slug}` },
        { label: 'Toppers' },
      ],
    }"
  >
    <!-- Loading state -->
    <div v-if="isLoading" class="py-8">
      <p class="text-muted-foreground">Loading Toppers List...</p>
    </div>

    <div
      v-else-if="hasTopperData"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5"
    >
      <HomeTemplateFacultyCard
        v-for="item in topperList"
        :key="item.id"
        :data="{
          imgSrc: item.image.pathname || '/temp/faculty-profile.png',
          caption: item.course,
          name: item.name,
          percentage: item.percentage,
        }"
      />
    </div>

    <!-- Empty state -->
    <div v-else class="text-center py-8">
      <HomeNoDataFound message="No Toppers Data Found" />
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="paginationVisible"
      v-slot="{ page: currentPageData }"
      class="my-16"
      :total="topperResponse?.pagination?.totalItems ?? 0"
      :sibling-count="1"
      :items-per-page="topperResponse?.pagination?.itemsPerPage ?? limit"
      show-edges
      :default-page="topperResponse?.pagination?.currentPage ?? 1"
      @update:page="handlePageChange"
    >
      <PaginationList
        v-slot="{ items }"
        class="flex items-center justify-center gap-1"
      >
        <PaginationFirst />
        <PaginationPrev />

        <template v-for="(item, index) in items" :key="index">
          <PaginationListItem
            v-if="item.type === 'page'"
            :value="item.value"
            as-child
          >
            <Button
              class="w-10 h-10 p-0"
              :variant="item.value === currentPageData ? 'default' : 'outline'"
            >
              {{ item.value }}
            </Button>
          </PaginationListItem>
          <PaginationEllipsis v-else :index="index" />
        </template>

        <PaginationNext />
        <PaginationLast />
      </PaginationList>
    </Pagination>
  </HomeDepartmentContainer>
</template>
