<script setup lang="ts">
import { ChevronDown } from 'lucide-vue-next';
import type { ProjectYears, DepartmentFilesResponse } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

// State management
const page = ref(1);
const limit = ref(15);
const isLoading = ref(false);
const selectedYear = ref<number | ''>(''); // Default value

const route = useRoute('departments-slug');
const slug = route.params.slug;

// Initial data fetch using useAsyncData
const { data: projectList, refresh: refreshEvents } =
  await useAsyncData<DepartmentFilesResponse>(
    'project-work-list',
    async () => {
      const response = await useFetch<DepartmentFilesResponse>(
        `/api/home/<USER>/${slug}/project-work`,
        {
          query: {
            year: selectedYear.value,
            filterStatus: 'active',
            page: page.value,
            limit: limit.value,
          },
        }
      );

      // Ensure we return a valid response or throw an error
      if (!response.data.value) {
        throw new Error('Failed to fetch project-works');
      }

      return response.data.value;
    },
    {
      immediate: true,
      default: () => ({
        success: false,
        data: [],
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: limit.value,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      }),
    }
  );

const { data: projectYears } = await useFetch<ProjectYears>(
  `/api/home/<USER>
);

const selectYear = async (year: number | '') => {
  isLoading.value = true;
  try {
    selectedYear.value = year;
    await refreshEvents();
  } catch (error) {
    console.error('Failed to fetch project work:', error);
  } finally {
    isLoading.value = false;
  }
};

// Page change handler
const handlePageChange = async (newPage: number) => {
  isLoading.value = true;
  try {
    page.value = newPage;
    await refreshEvents();
  } catch (error) {
    console.error('Failed to fetch downloads:', error);
  } finally {
    isLoading.value = false;
  }
};

// Computed properties for better readability and performance
const hasProjectList = computed(() =>
  Boolean(projectList.value?.data && projectList.value.data.length > 0)
);

const paginationVisible = computed(() =>
  Boolean(
    projectList.value?.pagination && projectList.value.pagination.totalPages > 1
  )
);
</script>

<template>
  <HomeDepartmentContainer
    title="Project Work"
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'Departments' },
        { label: slug, link: `/departments/${slug}` },
        { label: 'Project Work' },
      ],
    }"
  >
    <div
      class="flex items-center justify-between text-lg font-semibold p-3 bg-[#E4E4E4] w-full"
    >
      Academic Year
      <DropdownMenu :modal="false">
        <DropdownMenuTrigger>
          <Button variant="outline" class="bg-white rounded-[4px] border-none"
            >{{ selectedYear || 'Select Year' }} <ChevronDown />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem :text-value="''" @click="selectYear('')"
            >Select Year</DropdownMenuItem
          >
          <DropdownMenuItem
            v-for="item of projectYears?.data || []"
            :key="item"
            @click="selectYear(item)"
            >{{ item }}</DropdownMenuItem
          >
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="py-8">
      <p class="text-muted-foreground">Loading project work...</p>
    </div>

    <HomeTemplateDownloadItem
      v-for="item of projectList.data"
      v-else-if="hasProjectList"
      :key="item.id"
      :data="item"
    />

    <!-- Empty state -->
    <div v-else class="text-center py-8">
      <HomeNoDataFound message="No Downloads Found" />
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="paginationVisible"
      v-slot="{ page: currentPageData }"
      class="my-16"
      :total="projectList?.pagination?.totalItems ?? 0"
      :sibling-count="1"
      :items-per-page="projectList?.pagination?.itemsPerPage ?? limit"
      show-edges
      :default-page="projectList?.pagination?.currentPage ?? 1"
      @update:page="handlePageChange"
    >
      <PaginationList
        v-slot="{ items }"
        class="flex items-center justify-center gap-1"
      >
        <PaginationFirst />
        <PaginationPrev />

        <template v-for="(item, index) in items" :key="index">
          <PaginationListItem
            v-if="item.type === 'page'"
            :value="item.value"
            as-child
          >
            <Button
              class="w-10 h-10 p-0"
              :variant="item.value === currentPageData ? 'default' : 'outline'"
            >
              {{ item.value }}
            </Button>
          </PaginationListItem>
          <PaginationEllipsis v-else :index="index" />
        </template>

        <PaginationNext />
        <PaginationLast />
      </PaginationList>
    </Pagination>
  </HomeDepartmentContainer>
</template>
