<script setup lang="ts">
definePageMeta({
  layout: 'landing',
});
const route = useRoute('departments-slug');
const slug = route.params.slug;

const { data } = await useFetch<{ content: string }>(
  `/api/home/<USER>/${slug}/overview`
);

const overviewData = data.value;

if (!overviewData) {
  throw createError({
    status: 404,
    statusText: 'Overview not found',
  });
}
</script>

<template>
  <HomeDepartmentContainer
    title="Overview"
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'Departments' },
        { label: slug, link: `/departments/${slug}` },
        { label: 'Overview' },
      ],
    }"
  >
    <p class="text-sm !leading-8">
      {{ overviewData.content }}
    </p>
  </HomeDepartmentContainer>
</template>
