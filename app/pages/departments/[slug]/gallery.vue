<script setup lang="ts">
import DepartmentContainer from '~/components/home/<USER>';
import type { FileData, GalleryListResponse } from '~/types/home';
definePageMeta({
  layout: 'landing',
});

// State management
const page = ref(1);
const limit = ref(15);
const isLoading = ref(false);
const route = useRoute('departments-slug');
const slug = route.params.slug;

const { data: galleryResponse, refresh: refreshGallery } =
  await useAsyncData<GalleryListResponse>(
    'question-bank-list',
    async () => {
      const response = await useFetch<GalleryListResponse>(
        `/api/home/<USER>/${slug}/gallery`,
        {
          query: {
            filterStatus: 'active',
            page: page.value,
            limit: limit.value,
          },
        }
      );

      // Ensure we return a valid response or throw an error
      if (!response.data.value) {
        throw new Error('Failed to fetch question-bank');
      }

      return response.data.value;
    },
    {
      immediate: true,
      default: () => ({
        success: false,
        data: [],
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: limit.value,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      }),
    }
  );

const galleryData = galleryResponse.value?.data;

if (!galleryData) {
  throw createError({
    status: 404,
    statusText: 'Gallery Data not found',
  });
}

const selectedPhoto = ref<FileData>();
const isModalOpen = ref(false);

const openModal = (photo: FileData) => {
  selectedPhoto.value = photo;
  isModalOpen.value = true;
};

// Page change handler
const handlePageChange = async (newPage: number) => {
  isLoading.value = true;
  try {
    page.value = newPage;
    await refreshGallery();
  } catch (error) {
    console.error('Failed to fetch question bank list:', error);
  } finally {
    isLoading.value = false;
  }
};

// Computed properties for better readability and performance
const hasGalleryData = computed(() =>
  Boolean(galleryData && galleryData.length > 0)
);

const paginationVisible = computed(() =>
  Boolean(
    galleryResponse.value.pagination &&
      galleryResponse.value.pagination.totalPages > 1
  )
);
</script>

<template>
  <DepartmentContainer
    title="Gallery"
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'Departments' },
        { label: slug, link: `/departments/${slug}` },
        { label: 'Gallery' },
      ],
    }"
  >
    <!-- Loading state -->
    <div v-if="isLoading" class="py-8">
      <p class="text-muted-foreground">Loading Question Bank List...</p>
    </div>

    <div
      v-for="album in galleryData"
      v-else-if="hasGalleryData"
      :key="album.id"
      class="mt-7 first:mt-0"
    >
      <div
        class="w-full px-5 bg-[#E4E4E4] lg:px-7 flex flex-col items-start lg:flex-row lg:justify-between lg:gap-2 py-1.5 lg:items-center min-h-[54px] rounded-[6px]"
      >
        <h4 class="text-lg font-semibold">{{ album.title }}</h4>
        <span class="text-sm">{{ album.photosCount ?? 0 }} Photos</span>
      </div>
      <div
        v-if="album?.photos?.length > 0"
        class="mt-7 grid grid-cols-2 lg:grid-cols-4 gap-4"
      >
        <HomeGalleryCard
          v-for="(photo, index) in album.photos"
          :key="index"
          :data="{
            groupId: album?.id,
            photo,
            isSeeMore: index === 3,
          }"
          :open-modal="openModal"
        />
      </div>
      <div v-else class="py-4 text-center">
        No Photos Found in {{ album.title }}
      </div>
    </div>

    <!-- Empty state -->
    <div v-else class="text-center py-8">
      <HomeNoDataFound message="No Gallery Data found" />
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="paginationVisible"
      v-slot="{ page: currentPageData }"
      class="my-16"
      :total="galleryResponse?.pagination?.totalItems ?? 0"
      :sibling-count="1"
      :items-per-page="galleryResponse?.pagination?.itemsPerPage ?? limit"
      show-edges
      :default-page="galleryResponse?.pagination?.currentPage ?? 1"
      @update:page="handlePageChange"
    >
      <PaginationList
        v-slot="{ items }"
        class="flex items-center justify-center gap-1"
      >
        <PaginationFirst />
        <PaginationPrev />

        <template v-for="(item, index) in items" :key="index">
          <PaginationListItem
            v-if="item.type === 'page'"
            :value="item.value"
            as-child
          >
            <Button
              class="w-10 h-10 p-0"
              :variant="item.value === currentPageData ? 'default' : 'outline'"
            >
              {{ item.value }}
            </Button>
          </PaginationListItem>
          <PaginationEllipsis v-else :index="index" />
        </template>

        <PaginationNext />
        <PaginationLast />
      </PaginationList>
    </Pagination>

    <HomeGalleryImageViewer
      :multiple="false"
      :is-open="isModalOpen"
      :photo="selectedPhoto"
      @update:is-open="isModalOpen = $event"
    />
  </DepartmentContainer>
</template>
