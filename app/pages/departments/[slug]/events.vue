<script setup lang="ts">
import DepartmentContainer from '~/components/home/<USER>';
import type { AnnouncementListResponse } from '~/types/home';
definePageMeta({
  layout: 'landing',
});

// State management
const page = ref(1);
const limit = ref(15);
const isLoading = ref(false);
const route = useRoute('departments-slug');
const slug = route.params.slug;

const { data: eventResponse, refresh: refreshGallery } =
  await useAsyncData<AnnouncementListResponse>(
    'department-events',
    async () => {
      const response = await useFetch<AnnouncementListResponse>(
        `/api/home/<USER>/${slug}/event`,
        {
          query: {
            filterStatus: 'active',
            page: page.value,
            limit: limit.value,
          },
        }
      );

      // Ensure we return a valid response or throw an error
      if (!response.data.value) {
        throw new Error('Failed to fetch department events');
      }

      return response.data.value;
    },
    {
      immediate: true,
      default: () => ({
        success: false,
        data: [],
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: limit.value,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      }),
    }
  );

const eventList = eventResponse.value?.data;

if (!eventList) {
  throw createError({
    status: 404,
    statusText: 'Faculties not found',
  });
}

// Page change handler
const handlePageChange = async (newPage: number) => {
  isLoading.value = true;
  try {
    page.value = newPage;
    await refreshGallery();
  } catch (error) {
    console.error('Failed to fetch question bank list:', error);
  } finally {
    isLoading.value = false;
  }
};

// Computed properties for better readability and performance
const hasEventData = computed(() => Boolean(eventList && eventList.length > 0));

const paginationVisible = computed(() =>
  Boolean(
    eventResponse.value.pagination &&
      eventResponse.value.pagination.totalPages > 1
  )
);
</script>

<template>
  <DepartmentContainer
    title="Events"
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'Departments' },
        { label: slug, link: `/departments/${slug}` },
        { label: 'Events' },
      ],
    }"
  >
    <!-- Loading state -->
    <div v-if="isLoading" class="py-8">
      <p class="text-muted-foreground">Loading Event List...</p>
    </div>

    <div
      v-else-if="hasEventData"
      class="mt-5 space-y-7 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-3 gap-x-7 gap-y-14"
    >
      <HomeEventsCard2
        v-for="item in eventList"
        :key="item.id"
        :event-data="item"
      />
    </div>

    <!-- Empty state -->
    <div v-else class="text-center py-8">
      <HomeNoDataFound message="No Event List found" />
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="paginationVisible"
      v-slot="{ page: currentPageData }"
      class="my-16"
      :total="eventResponse?.pagination?.totalItems ?? 0"
      :sibling-count="1"
      :items-per-page="eventResponse?.pagination?.itemsPerPage ?? limit"
      show-edges
      :default-page="eventResponse?.pagination?.currentPage ?? 1"
      @update:page="handlePageChange"
    >
      <PaginationList
        v-slot="{ items }"
        class="flex items-center justify-center gap-1"
      >
        <PaginationFirst />
        <PaginationPrev />

        <template v-for="(item, index) in items" :key="index">
          <PaginationListItem
            v-if="item.type === 'page'"
            :value="item.value"
            as-child
          >
            <Button
              class="w-10 h-10 p-0"
              :variant="item.value === currentPageData ? 'default' : 'outline'"
            >
              {{ item.value }}
            </Button>
          </PaginationListItem>
          <PaginationEllipsis v-else :index="index" />
        </template>

        <PaginationNext />
        <PaginationLast />
      </PaginationList>
    </Pagination>
  </DepartmentContainer>
</template>
