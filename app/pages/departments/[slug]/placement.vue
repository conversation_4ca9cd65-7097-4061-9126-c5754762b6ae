<script setup lang="ts">
import type { PlacementListResponse } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

// State management
const page = ref(1);
const limit = ref(15);
const isLoading = ref(false);
const route = useRoute('departments-slug');
const slug = route.params.slug;

const { data: placementResponse, refresh: refreshPlacement } =
  await useAsyncData<PlacementListResponse>(
    'department-events',
    async () => {
      const response = await useFetch<PlacementListResponse>(
        `/api/home/<USER>/${slug}/placement`,
        {
          query: {
            filterStatus: 'active',
            page: page.value,
            limit: limit.value,
          },
        }
      );

      // Ensure we return a valid response or throw an error
      if (!response.data.value) {
        throw new Error('Failed to fetch department events');
      }

      return response.data.value;
    },
    {
      immediate: true,
      default: () => ({
        success: false,
        data: [],
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: limit.value,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      }),
    }
  );
const placementList = computed(() => placementResponse.value?.data || []);

// Page change handler
const handlePageChange = async (newPage: number) => {
  isLoading.value = true;
  page.value = newPage;
  await refreshPlacement();
  isLoading.value = false;
};

// Computed properties for better readability and performance
const hasPlacementData = computed(() =>
  Boolean(placementList.value.length > 0)
);

const paginationVisible = computed(() =>
  Boolean(placementResponse.value?.pagination?.totalItems > limit.value)
);
</script>

<template>
  <HomeDepartmentContainer
    title="Placement"
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'Departments' },
        { label: slug, link: `/departments/${slug}` },
        { label: 'Placement' },
      ],
    }"
  >
    <!-- Loading state -->
    <div v-if="isLoading" class="py-8">
      <p class="text-muted-foreground">Loading Placement List...</p>
    </div>

    <div
      v-else-if="hasPlacementData"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5"
    >
      <HomeTemplateFacultyCard
        v-for="item in placementList"
        :key="item.id"
        :data="{
          imgSrc: item.image.pathname || './temp/faculty-profile.png',
          caption: `${item.course} ${item.department} ${item.startYear}-${item.endYear}`,
          name: item.name,
          companyName: item.company,
        }"
      />
    </div>

    <!-- Empty state -->
    <div v-else class="text-center py-8">
      <HomeNoDataFound message="No Placement data found" />
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="paginationVisible"
      v-slot="{ page: currentPageData }"
      class="my-16"
      :total="placementResponse?.pagination?.totalItems ?? 0"
      :sibling-count="1"
      :items-per-page="placementResponse?.pagination?.itemsPerPage ?? limit"
      show-edges
      :default-page="placementResponse?.pagination?.currentPage ?? 1"
      @update:page="handlePageChange"
    >
      <PaginationList
        v-slot="{ items }"
        class="flex items-center justify-center gap-1"
      >
        <PaginationFirst />
        <PaginationPrev />

        <template v-for="(item, index) in items" :key="index">
          <PaginationListItem
            v-if="item.type === 'page'"
            :value="item.value"
            as-child
          >
            <Button
              class="w-10 h-10 p-0"
              :variant="item.value === currentPageData ? 'default' : 'outline'"
            >
              {{ item.value }}
            </Button>
          </PaginationListItem>
          <PaginationEllipsis v-else :index="index" />
        </template>

        <PaginationNext />
        <PaginationLast />
      </PaginationList>
    </Pagination>
  </HomeDepartmentContainer>
</template>
