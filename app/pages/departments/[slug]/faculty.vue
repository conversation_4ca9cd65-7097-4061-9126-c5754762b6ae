<script setup lang="ts">
import type { FacultyListResponse } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

const route = useRoute('departments-slug');
const slug = route.params.slug;

const { data } = await useFetch<FacultyListResponse>(
  `/api/home/<USER>/${slug}/faculty`
);

const overviewData = data.value?.data;
if (!overviewData) {
  throw createError({
    status: 404,
    statusText: 'Faculties not found',
  });
}
const isFacultyModalOpen = ref(false);

const selectedFacultyId = ref(0);
</script>

<template>
  <HomeDepartmentContainer
    title="Faculty"
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'Departments' },
        { label: slug, link: `/departments/${slug}` },
        { label: 'Faculty' },
      ],
    }"
  >
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-5">
      <HomeTemplateFacultyCard
        v-for="item of overviewData"
        :key="item.id"
        :data="{
          imgSrc: item.image.pathname,
          caption: item.designation,
          name: item.name,
        }"
        :view-profile="true"
        @view-profile="
          {
            selectedFacultyId = item.id;
            isFacultyModalOpen = true;
          }
        "
      />
    </div>
    <HomeFacultyInfoModal
      :faculty-id="selectedFacultyId"
      :slug="slug"
      :is-open="isFacultyModalOpen"
      @update:open="
        {
          (isFacultyModalOpen = $event), (selectedFacultyId = 0);
        }
      "
    />
  </HomeDepartmentContainer>
</template>
