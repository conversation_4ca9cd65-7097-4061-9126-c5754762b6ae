<script setup lang="ts">
import type { CourseListResponse } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

const route = useRoute('departments-slug');
const slug = route.params.slug;

const { data } = await useFetch<CourseListResponse>(
  `/api/home/<USER>/${slug}/course`
);

const overviewData = data.value?.data;

if (!overviewData) {
  throw createError({
    status: 404,
    statusText: 'Overview not found',
  });
}

const courseId = route.query.courseId;

// Add a function to check if a card should be highlighted
const shouldHighlight = (itemId: number) => {
  return courseId && String(itemId) === courseId;
};

// Create a ref for the container
const containerRef = ref<HTMLDivElement>();

// Add scroll into view effect when courseId is present
onMounted(() => {
  if (courseId) {
    // Wait for the DOM to be fully updated and images to load
    nextTick(async () => {
      // Wait for a short time to ensure all content is rendered
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Find the highlighted card within the container
      const highlightedCard = document.querySelector('.highlighted-card');

      if (highlightedCard) {
        highlightedCard.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
    });
  }
});

// Watch for route changes to handle navigation between courses
watch(
  () => route.query.courseId,
  (newCourseId) => {
    if (newCourseId) {
      nextTick(async () => {
        await new Promise((resolve) => setTimeout(resolve, 300));
        const highlightedCard = document.querySelector('.highlighted-card');
        if (highlightedCard) {
          highlightedCard.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        }
      });
    }
  }
);
</script>

<template>
  <HomeDepartmentContainer
    title="Courses"
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'Departments' },
        { label: slug, link: `/departments/${slug}` },
        { label: 'Courses' },
      ],
    }"
  >
    <div ref="containerRef" class="space-y-6">
      <HomeTemplateProgramOfferCard
        v-for="item of overviewData"
        :key="item.id"
        :data="item"
        :class="{
          'transition-all duration-700': true,
          'highlighted-card shadow-lg shadow-primary/10 animate-[glow_3s_ease-in-out_infinite] bg-gradient-to-r from-transparent via-primary/5 to-transparent bg-[length:200%_100%] ring-1 ring-primary/40 rounded-[8px] transform scale-[1.02]':
            shouldHighlight(item.id),
        }"
      />
    </div>
  </HomeDepartmentContainer>
</template>

<style scoped>
@keyframes glow {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
