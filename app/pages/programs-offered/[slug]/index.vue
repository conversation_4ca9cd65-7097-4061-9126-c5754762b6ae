<script setup lang="ts">
import type { RelatedLinks } from '~/components/home/<USER>/related-links.vue';
import type { CourseListResponse } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

const route = useRoute('programs-offered-slug');
const slug = route.params.slug;

// const fetchData = async () => {
//   const data = await $fetch<CourseListResponse>(
//     `/api/home/<USER>/${slug}`
//   );
//   overviewData.value = data;
// };
const { data: overviewData, status } =  await useFetch<CourseListResponse>(`/api/home/<USER>/${slug}`)

const links: RelatedLinks = [
  { link: '/ug', label: 'UG Courses' },
  { link: '/pg', label: 'PG Courses' },
  { link: '/add-on', label: 'Add On Courses' },
];

const label = computed(() => {
  return links.find((link) => link.link === `/${slug}`)?.label ?? '';
});
</script>

<template>
  <HomeSubHeader
    title="Programs Offered"
    :breadcrumbs="[
      { label: 'Home', link: '/' },
      { label: 'Academics' },
      { label },
    ]"
    :links="links"
  />
  <div v-if="status === 'pending'">
    <Loader2 class="h-4 w-4 animate-spin" />
  </div>
  <div v-else class="container py-10 flex gap-x-7 bg-[#F4F4F4]">
    <section class="grow">
      <h2
        class="text-[25px] pl-4 lg:pl-0 font-semibold text-primary before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
      >
        {{ label }}
      </h2>
      <div class="flex items-start gap-x-4 mt-6">
        <div v-if="overviewData?.data" class="grow">
          <HomeTemplateProgramOfferCard
            v-for="item of overviewData?.data"
            :key="item.id"
            :data="item"
          />
        </div>
        <div v-else class="grow">
          <HomeNoDataFound />
        </div>
        <div class="hidden lg:block">
          <HomeAboutUsRelatedLinks
            :links="links"
            link-prefix="/programs-offered"
          />
        </div>
      </div>
    </section>
  </div>
</template>
