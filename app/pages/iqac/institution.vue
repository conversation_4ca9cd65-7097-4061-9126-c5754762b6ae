<script setup lang="ts">
import type { OverviewResponse } from '~~/app/types/home/<USER>';

definePageMeta({
  layout: 'landing',
});
const { data: overviewData } = useFetch<OverviewResponse>(
  '/api/home/<USER>/institution'
);
</script>

<template>
  <HomeIqacContainer
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'IQAC' },
        { label: 'Institution Distinctiveness' },
      ],
    }"
    title="Institution Distinctiveness"
  >
    <HomeIqacOverview v-if="overviewData" :overview-data="overviewData" />
  </HomeIqacContainer>
</template>
