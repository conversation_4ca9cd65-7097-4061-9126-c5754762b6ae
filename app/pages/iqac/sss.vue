<script setup lang="ts">
import type { DownloadResponse } from '~/types/home';

definePageMeta({
  layout: 'landing',
});
const { data: downloadData } = useFetch<DownloadResponse>('/api/home/<USER>/sss');
</script>

<template>
  <HomeIqacContainer
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'IQAC' },
        { label: 'SSS' },
      ],
    }"
    title="SSS"
  >
    <div
      v-if="downloadData && downloadData?.downloads?.length > 0"
      class="gap-y-1"
    >
      <HomeIqacDownload
        v-for="item of downloadData.downloads"
        :key="item.title"
        :data="item"
      />
    </div>
  </HomeIqacContainer>
</template>
