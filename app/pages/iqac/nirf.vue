<script setup lang="ts">
import type { AttachmentResponse } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

const { data: attachmentData } = useFetch<AttachmentResponse>(
  '/api/home/<USER>/nirf'
);
</script>

<template>
  <HomeIqacContainer
    title="NIRF"
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'IQAC' },
        { label: 'NIRF' },
      ],
    }"
  >
    <section class="grow">
      <div class="flex items-start gap-x-4">
        <div class="grow">
          <Accordion type="single" collapsible default-value="item-1">
            <HomeIqacAttachment
              v-for="item in attachmentData?.attachments"
              :key="item.title"
              :attachment-data="item"
              :attachment-key="item.title"
              :attachment-value="item.title"
            />
          </Accordion>
        </div>
      </div>
    </section>
  </HomeIqacContainer>
</template>
