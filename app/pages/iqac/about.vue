<script setup lang="ts">
import type { OverviewResponse } from '~~/app/types/home/<USER>';

definePageMeta({
  layout: 'landing',
});
const { data: overviewData } = useFetch<OverviewResponse>(
  '/api/home/<USER>/about'
);
</script>

<template>
  <HomeIqacContainer
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'IQAC' },
        { label: 'About' },
      ],
    }"
    title="IQAC"
  >
    <HomeIqacOverview v-if="overviewData" :overview-data="overviewData" />
  </HomeIqacContainer>
</template>
