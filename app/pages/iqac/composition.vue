<script setup lang="ts">
import { FileBox } from 'lucide-vue-next';
import type { CompositionResponse } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

const { data: compositionData } = useFetch<CompositionResponse>(
  '/api/home/<USER>/composition'
);
</script>

<template>
  <HomeIqacContainer
    :sub-header="{
      breadcrumbs: [
        { label: 'Home', link: '/' },
        { label: 'IQAC' },
        { label: 'Composition of IQAC' },
      ],
    }"
    title="Composition of IQAC"
  >
    <HomeIqacTable
      v-if="compositionData"
      :composition-data="compositionData.compositions"
    />
    <div
      v-if="compositionData && compositionData?.attachments?.length > 0"
      class="mt-12 border-t-4 border-primary pt-8 mb-8"
    >
      <div class="gap-y-1">
        <h4
          class="text-lg font-semibold leading-normal mb-6 flex items-center gap-2"
        >
          <FileBox class="w-5 h-5 text-primary-600" />
          Attachments
        </h4>
        <HomeIqacDownload
          v-for="item of compositionData.attachments"
          :key="item.title"
          :data="item"
        />
      </div>
    </div>
  </HomeIqacContainer>
</template>
