<script setup lang="ts">
import type { AnnouncementData } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

const route = useRoute('notice-board-slug');

const { data } = await useFetch(`/api/home/<USER>/${route.params.slug}`, {
  transform: (response: any) => response.data as AnnouncementData,
});

if (!data) {
  throw createError({
    status: 404,
    statusText: 'Notice not found',
  });
}
</script>

<template>
  <HomeSubHeader
    :title="data?.title || 'Notice'"
    :breadcrumbs="[
      { label: 'Home', link: '/' },
      { label: 'Notice Board', link: '/notice-board' },
      { label: data?.title || 'Notice' },
    ]"
  />
  <section class="container py-10">
    <NuxtImg
      v-if="data?.primaryImage"
      loading="lazy"
      class="w-full h-[180px] md:h-[260px] lg:h-[300px] object-contain"
      fit="cover"
      height="260"
      format="webp"
      :src="getPreviewUrl(data?.primaryImage.pathname)"
      :alt="data?.primaryImage.title"
    />
    <div class="flex items-center gap-x-4 rounded-[5px] mt-7">
      <div
        class="px-4 py-2 flex flex-col gap-y-1 justify-center items-center bg-primary-gradient text-white rounded-[5px]"
      >
        <span class="text-sm font-light leading-none">{{
          formatDate(data?.date!).month
        }}</span>
        <span class="text-xl font-bold leading-none">{{
          formatDate(data?.date!).day
        }}</span>
        <span class="text-sm font-light leading-none">{{
          formatDate(data?.date!).year
        }}</span>
      </div>
      <h4 class="text-lg font-medium leading-normal">
        {{ data?.title }}
      </h4>
    </div>
    <div
      class="mt-3 text-sm font-medium !leading-8"
      v-html="data?.description"
    />
    <HomeTemplateButton
      v-if="data?.button"
      :button="data?.button"
      class="mt-6"
    />
  </section>
</template>
