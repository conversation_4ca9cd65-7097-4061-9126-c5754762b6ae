<script setup lang="ts">
import type { AnnouncementListResponse } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

// State management
const page = ref(1);
const limit = ref(15);
const isLoading = ref(false);

// Initial data fetch using useAsyncData
const { data: noticeList, refresh: refreshEvents } =
  await useAsyncData<AnnouncementListResponse>(
    'notice-list',
    async () => {
      const response = await useFetch<AnnouncementListResponse>(
        '/api/home/<USER>/notice',
        {
          query: {
            filterStatus: 'active',
            page: page.value,
            limit: limit.value,
          },
        }
      );

      // Ensure we return a valid AnnouncementListResponse or throw an error
      if (!response.data.value) {
        throw new Error('Failed to fetch notices');
      }

      return response.data.value;
    },
    {
      immediate: true,
      default: () => ({
        success: false,
        data: [],
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: limit.value,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      }),
    }
  );

// Page change handler
const handlePageChange = async (newPage: number) => {
  isLoading.value = true;
  try {
    page.value = newPage;
    await refreshEvents();
  } catch (error) {
    console.error('Failed to fetch notices:', error);
  } finally {
    isLoading.value = false;
  }
};

// Computed properties for better readability and performance
const hasNoticeList = computed(() =>
  Boolean(noticeList.value?.data && noticeList.value.data.length > 0)
);

const paginationVisible = computed(() =>
  Boolean(
    noticeList.value?.pagination && noticeList.value.pagination.totalPages > 1
  )
);
</script>

<template>
  <HomeSubHeader
    title="Notice Board"
    :breadcrumbs="[{ label: 'Home', link: '/' }, { label: 'Notice Board' }]"
  />
  <section class="container py-10">
    <!-- Loading state -->
    <div v-if="isLoading" class="py-8">
      <p class="text-muted-foreground">Loading notices...</p>
    </div>

    <!-- Notices Grid -->
    <div v-else-if="hasNoticeList">
      <HomeNoticeBoardCard
        v-for="item in noticeList?.data ?? []"
        :key="item.id"
        :data="{
          id: item.id,
          timestamp: item.date,
          title: item.title,
          description: item.description,
        }"
      />
    </div>

    <!-- Empty state -->
    <div v-else class="text-center py-8">
      <p class="text-muted-foreground">No Notices found.</p>
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="paginationVisible"
      v-slot="{ page: currentPageData }"
      class="my-16"
      :total="noticeList?.pagination?.totalItems ?? 0"
      :sibling-count="1"
      :items-per-page="noticeList?.pagination?.itemsPerPage ?? limit"
      show-edges
      :default-page="noticeList?.pagination?.currentPage ?? 1"
      @update:page="handlePageChange"
    >
      <PaginationList
        v-slot="{ items }"
        class="flex items-center justify-center gap-1"
      >
        <PaginationFirst />
        <PaginationPrev />

        <template v-for="(item, index) in items" :key="index">
          <PaginationListItem
            v-if="item.type === 'page'"
            :value="item.value"
            as-child
          >
            <Button
              class="w-10 h-10 p-0"
              :variant="item.value === currentPageData ? 'default' : 'outline'"
            >
              {{ item.value }}
            </Button>
          </PaginationListItem>
          <PaginationEllipsis v-else :index="index" />
        </template>

        <PaginationNext />
        <PaginationLast />
      </PaginationList>
    </Pagination>
  </section>
</template>
