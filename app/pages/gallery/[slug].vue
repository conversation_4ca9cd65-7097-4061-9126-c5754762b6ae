<script setup lang="ts">
import type { GalleryResponse } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

const route = useRoute('gallery-slug');
const slug = route.params.slug;

const { data: galleryResponse } = await useFetch<GalleryResponse>(
  `/api/home/<USER>/${slug}`
);

const galleryData = galleryResponse.value?.data;

if (!galleryData) {
  throw createError({
    status: 404,
    statusText: 'Gallery not found',
  });
}
const isModalOpen = ref(false);

const openModal = () => {
  isModalOpen.value = true;
};
</script>

<template>
  <HomeSubHeader
    :title="galleryData.title"
    :breadcrumbs="[
      { label: 'Home', link: '/' },
      { label: 'Gallery', link: '/gallery' },
      { label: galleryData.title },
    ]"
  />
  <div class="container py-10">
    <section>
      <div
        class="w-full px-5 bg-[#E4E4E4] rounded-[6px] lg:px-7 flex flex-col items-start lg:flex-row lg:justify-between lg:gap-2 py-1.5 lg:items-center min-h-[54px]"
      >
        <h4 class="text-lg font-semibold">{{ galleryData.title }}</h4>
        <span class="text-sm">{{ galleryData?.photos.length }} Photos</span>
      </div>
      <div class="mt-7 grid grid-cols-2 lg:grid-cols-4 gap-4">
        <HomeGalleryCard
          v-for="(item, index) of galleryData?.photos ?? []"
          :key="index"
          :data="{
            photo: item,
            isSeeMore: false,
          }"
          :open-modal="openModal"
        />
      </div>
    </section>
  </div>
  <HomeGalleryImageViewer
    :multiple="true"
    :photos="galleryData?.photos ?? []"
    :is-open="isModalOpen"
    @update:is-open="isModalOpen = $event"
  />
</template>
