<script setup lang="ts">
import { Search, ChevronRight } from 'lucide-vue-next';
import type { FileData, GalleryListData } from '~/types/home';
definePageMeta({
  layout: 'landing',
});

const { data: galleryResponse } = await useFetch<{
  success: boolean;
  data: GalleryListData[];
}>('/api/home/<USER>');

const galleryData = galleryResponse.value?.data;

if (!galleryData) {
  throw createError({
    status: 404,
    statusText: 'Event not found',
  });
}

const isModalOpen = ref(false);
const selectedPhoto = ref<FileData>();

const openModal = (photo: FileData) => {
  selectedPhoto.value = photo;
  isModalOpen.value = true;
};
</script>

<template>
  <HomeSubHeader
    title="Gallery"
    :breadcrumbs="[{ label: 'Home', link: '/' }, { label: 'Gallery' }]"
  />
  <div class="container py-10">
    <div
      class="h-[54px] flex items-center px-3 lg:px-5 w-full rounded-[27px] overflow-hidden border border-[#E1E1E1] bg-white"
    >
      <Search class="text-[#A7A7A7]" />
      <input
        type="text"
        class="h-full w-full border-none text-lg font-semibold outline-none px-2 lg:px-3 placeholder:text-[#A7A7A7]"
        placeholder="Search Albums"
      />
    </div>
    <section
      v-for="album in galleryData"
      :key="album.id"
      class="mt-7 first-of-type:mt-10"
    >
      <div
        v-if="album.photos.length > 0"
        class="w-full px-5 bg-[#E4E4E4] lg:px-7 flex flex-col items-start lg:flex-row lg:justify-between lg:gap-2 py-1.5 lg:items-center min-h-[54px] rounded-[6px]"
      >
        <NuxtLink :to="`/gallery/${album?.id}`">
          <h4 class="text-lg font-semibold hover:text-primary">
            {{ album.title }} <ChevronRight class="w-5 inline-block" />
          </h4>
        </NuxtLink>
        <span class="text-sm">{{ album.photosCount }} Photos</span>
      </div>
      <div
        v-if="album.photos.length > 0"
        class="mt-7 grid grid-cols-2 lg:grid-cols-4 gap-4"
      >
        <HomeGalleryCard
          v-for="(photo, index) in album.photos"
          :key="index"
          :data="{
            groupId: album?.id,
            photo,
            isSeeMore: index === 3 && album.photosCount > 4,
          }"
          :open-modal="openModal"
        />
      </div>
    </section>
  </div>
  <HomeGalleryImageViewer
    :multiple="false"
    :is-open="isModalOpen"
    :photo="selectedPhoto"
    @update:is-open="isModalOpen = $event"
  />
</template>
