<script setup lang="ts">
import type { AnnouncementData } from '~/types/home';
import { FileBox } from 'lucide-vue-next';

definePageMeta({
  layout: 'landing',
});

const route = useRoute('events-slug');
const { data: eventResponse } = await useFetch(
  `/api/home/<USER>/${route.params.slug}`,
  {
    transform: (response: any) => response.data as AnnouncementData,
  }
);

const eventData = eventResponse.value;

if (!eventData) {
  throw createError({
    status: 404,
    statusText: 'Event not found',
  });
}
</script>

<template>
  <HomeSubHeader
    :title="eventData.title"
    :breadcrumbs="[
      { label: 'Home', link: '/' },
      { label: 'Events', link: '/events' },
      { label: eventData.title },
    ]"
  />
  <section class="container py-10">
    <NuxtImg
      v-if="eventData.primaryImage"
      loading="lazy"
      class="w-full h-[180px] md:h-[260px] lg:h-[300px] object-contain"
      fit="cover"
      width="100%"
      height="260"
      format="webp"
      :src="getPreviewUrl(eventData.primaryImage.pathname)"
      alt="logo"
    />
    <div class="flex items-center gap-x-4 rounded-[5px] mt-7">
      <div
        class="px-4 py-2 flex flex-col gap-y-1 justify-center items-center bg-primary-gradient text-white rounded-[5px]"
      >
        <span class="text-sm font-light leading-none">{{
          formatDate(eventData.date!).month
        }}</span>
        <span class="text-xl font-bold leading-none">{{
          formatDate(eventData.date!).day
        }}</span>
        <span class="text-sm font-light leading-none">{{
          formatDate(eventData.date!).year
        }}</span>
      </div>
      <h4 class="text-lg font-medium leading-normal">
        {{ eventData.title }}
      </h4>
    </div>
    <div
      class="mt-3 text-sm font-medium !leading-8"
      v-html="eventData.description"
    />
    <HomeTemplateButton
      v-if="eventData.button"
      :button="eventData.button"
      class="mt-6"
    />

    <!-- Updated Attachments Section -->
    <div
      v-if="eventData.attachments && eventData.attachments.length > 0"
      class="mt-12 border-t-4 border-primary pt-8 mb-8"
    >
      <h4
        class="text-lg font-medium leading-normal mb-6 flex items-center gap-2"
      >
        <FileBox class="w-5 h-5 text-primary-600" />
        Attachments
      </h4>
      <HomeAttachmentsList :files-list="eventData.attachments" />
    </div>
  </section>
</template>
