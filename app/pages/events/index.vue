<script setup lang="ts">
import type { AnnouncementListResponse } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

// State management
const page = ref(1);
const limit = ref(15);
const isLoading = ref(false);

// Initial data fetch using useAsyncData
const { data: eventsList, refresh: refreshEvents } =
  await useAsyncData<AnnouncementListResponse>(
    'events-list',
    async () => {
      const response = await useFetch<AnnouncementListResponse>(
        '/api/home/<USER>/event',
        {
          query: {
            page: page.value,
            limit: limit.value,
            filterStatus: 'active',
          },
        }
      );

      // Ensure we return a valid AnnouncementListResponse or throw an error
      if (!response.data.value) {
        throw new Error('Failed to fetch events');
      }

      return response.data.value;
    },
    {
      immediate: true,
      default: () => ({
        success: false,
        data: [],
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: limit.value,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      }),
    }
  );

// Page change handler
const handlePageChange = async (newPage: number) => {
  isLoading.value = true;
  try {
    page.value = newPage;
    await refreshEvents();
  } catch (error) {
    console.error('Failed to fetch events:', error);
  } finally {
    isLoading.value = false;
  }
};

// Computed properties for better readability and performance
const hasEvents = computed(() =>
  Boolean(eventsList.value?.data && eventsList.value.data.length > 0)
);

const paginationVisible = computed(() =>
  Boolean(
    eventsList.value?.pagination && eventsList.value.pagination.totalItems > 0
  )
);
</script>

<template>
  <HomeSubHeader
    title="College Events"
    :breadcrumbs="[{ label: 'Home', link: '/' }, { label: 'College Events' }]"
  />

  <section class="latest-events container mt-10 pb-24">
    <!-- Loading state -->
    <div v-if="isLoading" class="text-center py-8">
      <p class="text-muted-foreground">Loading events...</p>
    </div>

    <!-- Events grid -->
    <div
      v-else-if="hasEvents"
      class="mt-5 space-y-7 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-3 gap-x-7 gap-y-14"
    >
      <HomeEventsCard
        v-for="item in eventsList?.data ?? []"
        :key="item.id"
        :event-data="item"
      />
    </div>

    <!-- Empty state -->
    <div v-else class="text-center py-8">
      <p class="text-muted-foreground">No events found.</p>
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="paginationVisible"
      v-slot="{ page: currentPageData }"
      class="my-16"
      :total="eventsList?.pagination?.totalItems ?? 0"
      :sibling-count="1"
      :items-per-page="eventsList?.pagination?.itemsPerPage ?? limit"
      show-edges
      :default-page="eventsList?.pagination?.currentPage ?? 1"
      @update:page="handlePageChange"
    >
      <PaginationList
        v-slot="{ items }"
        class="flex items-center justify-center gap-1"
      >
        <PaginationFirst />
        <PaginationPrev />

        <template v-for="(item, index) in items" :key="index">
          <PaginationListItem
            v-if="item.type === 'page'"
            :value="item.value"
            as-child
          >
            <Button
              class="w-10 h-10 p-0"
              :variant="item.value === currentPageData ? 'default' : 'outline'"
            >
              {{ item.value }}
            </Button>
          </PaginationListItem>
          <PaginationEllipsis v-else :index="index" />
        </template>

        <PaginationNext />
        <PaginationLast />
      </PaginationList>
    </Pagination>
  </section>
</template>
