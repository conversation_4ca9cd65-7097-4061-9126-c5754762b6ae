<script setup lang="ts">
import { Download, Loader2 } from 'lucide-vue-next';
import type { AdmissionResponse } from '~~/app/types/admin/admissions';

const { data: admissionData, status: admissionStatus } =
  useFetch<AdmissionResponse>('/api/home/<USER>');

// Page title and meta
useHead({
  title: 'Admissions - Don Bosco College',
  meta: [
    {
      name: 'description',
      content:
        'Admissions at Don Bosco College Mannuthy for UG, PG, and Add On Courses',
    },
  ],
});
definePageMeta({
  layout: 'landing',
});
</script>
<template>
  <HomeSubHeader
    title="Admissions"
    :breadcrumbs="[{ label: 'Home', link: '/' }, { label: 'Admissions' }]"
  />
  <div v-if="!admissionData" class="container py-10">
    <HomeNoDataFound />
  </div>
  <div v-else-if="admissionStatus === 'pending'" class="container py-10">
    <Loader2 class="h-4 w-4 animate-spin" />
  </div>
  <section v-else class="container py-10">
    <h2
      class="text-[25px] text-primary font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
    >
      {{ admissionData.title }}
    </h2>
    <NuxtImg
      v-if="admissionData?.image?.pathname"
      loading="lazy"
      class="w-full h-[180px] md:h-[260px] lg:h-[300px] object-cover"
      fit="cover"
      width="100%"
      height="260"
      format="webp"
      :src="getPreviewUrl(admissionData?.image?.pathname || '')"
      alt="logo"
    />
    <div v-for="(section, index) in admissionData.content" :key="index" class="py-6">
      <h4 class="text-lg font-semibold text-primary leading-normal">
        {{ section.title }}
      </h4>
      <div
        class="mt-3 text-sm font-medium !leading-8"
        v-html="section.content"
      />
      <div class="mt-4 flex justify-start items-center flex-wrap gap-2">
        <HomeTemplateButton
          v-if="section.linkButton"
          :button="{
            title: section.linkButton.title,
            link:
              section.linkButton.internalLink ||
              section.linkButton.externalLink,
            newTab: section.linkButton.newTab,
            icon: section.linkButton.icon,
            style: section.linkButton.style,
          }"
        />
        <NuxtLink
          v-if="section.file"
          :to="getPreviewUrl(section.file.pathname)"
          target="_blank"
          class="inline-flex items-center justify-center gap-2 border border-primary bg-transparent hover:bg-secondary font-medium transition-colors text-sm w-fit px-4 py-2 min-w-[120px] rounded-[6px]"
        >
          <Download class="w-4 h-4" />
          <!-- {{ section.file.title }} -->
          Brochure
        </NuxtLink>
      </div>
    </div>
  </section>
</template>
