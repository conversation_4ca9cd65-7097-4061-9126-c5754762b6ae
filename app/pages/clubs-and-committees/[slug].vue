<script setup lang="ts">
import { Image, Calendar, FileBox, Loader2 } from 'lucide-vue-next';
import type { HomeClubCommitteeResponse, FileData } from '~/types/home';

definePageMeta({
  layout: 'landing',
});

const route = useRoute('clubs-and-committees-slug');
const slug = route.params.slug;

const { data: pageData, status } = await useFetch<HomeClubCommitteeResponse>(
  `/api/home/<USER>/${slug}`
);

const isModalOpen = ref(false);
const selectedPhoto = ref<FileData>();

const firstLink = computed(() => {
  return `${pageData.value?.menuData.slug}/${pageData.value?.menuData?.relatedLinks?.[0]?.link}`;
});
const openModal = (photo: FileData) => {
  selectedPhoto.value = photo;
  isModalOpen.value = true;
};
</script>

<template>
  <HomeSubHeader
    :links="pageData?.menuData.relatedLinks || []"
    title="About Us"
    :breadcrumbs="[
      { label: 'Home', link: '/' },
      { label: pageData?.menuData?.name || '', link: firstLink },
      { label: pageData?.data.title || '' },
    ]"
  />
  <div v-if="status === 'pending'">
    <Loader2 class="h-4 w-4 animate-spin" />
  </div>
  <div
    v-else-if="pageData?.data"
    class="container py-10 flex gap-x-7 bg-[#F4F4F4]"
  >
    <section class="grow">
      <h2
        class="text-[25px] text-primary font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
      >
        {{ pageData?.data.title }}
      </h2>
      <NuxtImg
        v-if="pageData?.data.image"
        loading="lazy"
        class="w-full h-auto max-h-[370px] object-cover mt-3"
        fit="cover"
        format="webp"
        :src="getPreviewUrl(pageData?.data.image?.pathname ?? '')"
        :alt="pageData?.data.image?.title ?? ''"
      />
      <h4
        v-if="pageData?.data?.overview"
        class="text-lg font-semibold mt-6 text-primary"
      >
        Overview
      </h4>
      <p v-if="pageData?.data?.overview" class="text-sm mt-2 !leading-8">
        {{ pageData?.data?.overview }}
      </p>
      <div class="mt-4 flex gap-x-4">
        <HomeTemplateButton
          v-if="pageData?.data?.linkButton"
          :button="{
            title: pageData?.data?.linkButton?.title ?? '',
            link:
              pageData?.data?.linkButton?.externalLink ||
              pageData?.data?.linkButton?.internalLink,
            style: pageData?.data?.linkButton?.style ?? '',
            newTab: pageData?.data?.linkButton?.newTab ?? false,
            icon: pageData?.data?.linkButton?.icon ?? '',
          }"
          class="mt-4 xl:mt-5"
        />
      </div>
      <!-- Gallery -->
      <div
        v-if="pageData?.data?.galleries?.length > 0"
        class="mt-12 border-t-4 border-primary pt-8 mb-8"
      >
        <div class="gap-y-1">
          <h4
            class="text-lg text-primary font-semibold leading-normal mb-6 flex items-center gap-2"
          >
            <Image class="w-5 h-5 text-primary-600" />
            Gallery
          </h4>
          <div
            v-for="gallery in pageData?.data?.galleries"
            :key="gallery.id"
            class="mt-7 first-of-type:mt-10"
          >
            <div
              v-if="gallery.photos.length > 0"
              class="w-full px-5 bg-[#E4E4E4] lg:px-7 flex flex-col items-start lg:flex-row lg:justify-between lg:gap-2 py-1.5 lg:items-center min-h-[54px] rounded-[6px]"
            >
              <NuxtLink :to="`/gallery/${gallery?.id}`">
                <h4 class="text-lg font-semibold hover:text-primary">
                  {{ gallery.title }} <ChevronRight class="w-5 inline-block" />
                </h4>
              </NuxtLink>
              <span class="text-sm">{{ gallery.photosCount }} Photos</span>
            </div>
            <div
              v-if="gallery.photos.length > 0"
              class="mt-7 grid grid-cols-2 lg:grid-cols-4 gap-4"
            >
              <HomeGalleryCard
                v-for="(photo, index) in gallery.photos"
                :key="index"
                :data="{
                  groupId: gallery?.id,
                  photo,
                  isSeeMore: index === 3 && gallery.photosCount > 4,
                }"
                :open-modal="openModal"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Events -->
      <div
        v-if="pageData?.data?.events?.length > 0"
        class="mt-12 border-t-4 border-primary pt-8 mb-8"
      >
        <div class="gap-y-1">
          <h4
            class="text-lg text-primary font-semibold leading-normal mb-6 flex items-center gap-2"
          >
            <Calendar class="w-5 h-5 text-primary-600" />
            Events
          </h4>
          <div
            class="mt-7 space-y-7 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-3 gap-x-7 gap-y-14"
          >
            <HomeEventsCard
              v-for="item in pageData?.data?.events ?? []"
              :key="item.id"
              :event-data="item"
            />
          </div>
        </div>
      </div>

      <!-- Attachments -->
      <div
        v-if="
          pageData?.data?.attachments && pageData?.data?.attachments?.length > 0
        "
        class="mt-12 border-t-4 border-primary pt-8 mb-8"
      >
        <div class="gap-y-1">
          <h4
            class="text-lg text-primary font-semibold leading-normal mb-6 flex items-center gap-2"
          >
            <FileBox class="w-5 h-5 text-primary-600" />
            Attachments
          </h4>
          <HomeAttachmentsList
            :files-list="pageData?.data?.attachments"
            :link-prefix="''"
          />
        </div>
      </div>
    </section>
    <div class="hidden lg:block">
      <HomeAboutUsRelatedLinks
        :links="pageData?.menuData.relatedLinks || []"
        :link-prefix="''"
      />
    </div>
  </div>
  <!-- Empty state -->
  <div v-else class="text-center py-8">
    <HomeNoDataFound />
  </div>
  <HomeGalleryImageViewer
    :multiple="false"
    :is-open="isModalOpen"
    :photo="selectedPhoto"
    @update:is-open="isModalOpen = $event"
  />
</template>
