<script setup lang="ts">
import type {
  MenuBySlugData,
  StandardCard,
  ProfileCard,
  VisionCard,
  CardsList,
} from '~/types/home';

const route = useRoute();
const { menu: menuSlug, submenu: submenuSlug } = route.params as {
  menu: string;
  submenu?: string;
};
definePageMeta({
  layout: 'landing',
});
const { data: pageData } = await useFetch<MenuBySlugData>(
  `/api/home/<USER>/${submenuSlug}`
);

const firstLink = computed(() => {
  return `${pageData.value?.menuData.slug}/${pageData.value?.menuData?.relatedLinks?.[0]?.link}`;
});

const isCardsList = computed(() => {
  const configuration:
    | ProfileCard
    | StandardCard
    | VisionCard
    | CardsList
    | undefined = pageData.value?.configuration;
  return (
    configuration &&
    'cards' in configuration &&
    configuration.cards &&
    configuration.cards.length > 0
  );
});
</script>

<template>
  <HomeSubHeader
    :links="pageData?.menuData.relatedLinks || []"
    :title="pageData?.menuData?.name || ''"
    :breadcrumbs="[
      { label: 'Home', link: '/' },
      { label: pageData?.menuData?.name || '', link: firstLink },
      { label: pageData?.configuration.title || '' },
    ]"
  />
  <div
    v-if="pageData?.configuration"
    class="container py-10 flex gap-x-7 bg-[#F4F4F4]"
  >
    <HomeTemplateCardStandard
      v-if="pageData?.configuration.layout === 'standard' && !isCardsList"
      :data="pageData?.configuration as StandardCard"
    />
    <HomeTemplateCardProfile
      v-if="pageData?.configuration.layout === 'profile' && !isCardsList"
      :data="pageData?.configuration as ProfileCard"
    />
    <HomeTemplateCardVision
      v-if="pageData?.configuration.layout === 'vision' && !isCardsList"
      :data="pageData?.configuration as VisionCard"
    />
    <div
      v-if="isCardsList && pageData?.configuration.layout === 'standard'"
      class="flex flex-col w-full gap-y-5"
    >
      <HomeTemplateCardStandard
        v-for="card in (pageData?.configuration as CardsList).cards"
        :key="card.title"
        :data="card as StandardCard"
      />
    </div>
    <div
      v-if="isCardsList && pageData?.configuration.layout === 'profile'"
      class="flex flex-col w-full gap-y-5"
    >
      <h2
        class="text-[25px] text-primary font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
      >
        {{ pageData?.configuration.title }}
      </h2>
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 w-full gap-x-5 gap-y-5"
      >
        <HomeTemplateCardProfile
          v-for="card in (pageData?.configuration as CardsList).cards"
          :key="card.title"
          :data="card as ProfileCard"
        />
      </div>
    </div>
    <div
      v-if="isCardsList && pageData?.configuration.layout === 'vision'"
      class="flex flex-col w-full gap-y-5"
    >
      <h2
        class="text-[25px] text-primary font-semibold before:contents-[''] before:mr-1.5 before:inline-block before:w-[6px] before:h-[30px] before:-mb-1.5 before:bg-border-blue before:rounded-[6px]"
      >
        {{ pageData?.configuration.title }}
      </h2>
      <div class="flex flex-col w-full gap-y-5">
        <HomeTemplateCardVision
          v-for="card in (pageData?.configuration as CardsList).cards"
          :key="card.title"
          :data="card as VisionCard"
        />
      </div>
    </div>
    <div class="hidden lg:block">
      <HomeAboutUsRelatedLinks
        :links="pageData?.menuData.relatedLinks || []"
        :link-prefix="''"
      />
    </div>
  </div>
  <!-- Empty state -->
  <div v-else class="text-center py-8">
    <HomeNoDataFound />
  </div>
</template>
