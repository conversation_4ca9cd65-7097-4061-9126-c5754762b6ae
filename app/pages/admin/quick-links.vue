<script setup lang="ts">
definePageMeta({
  middleware: ['admin'],
});

function saveNavigationSettings() {}

const breadcrumbs = ref([
  { label: 'Home', href: '/admin' },
  { label: 'Quick Links' },
]);
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <div class="flex flex-wrap items-center justify-between gap-2">
      <h2 class="text-2xl font-bold tracking-tight">Navigation Management</h2>
      <Button @click="saveNavigationSettings"> Save Changes </Button>
    </div>

    <main class="flex flex-1 flex-col gap-4 md:gap-8">
      <Card>
        <CardHeader>
          <CardTitle>Quick Links Configuration</CardTitle>
          <CardDescription>
            Manage the visibility of quick links throughout the site
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <AdminQuickLink />
          </div>
        </CardContent>
      </Card>
    </main>
  </div>
</template>

<style scoped>
.card {
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
}

/* Prevent text selection when dragging */
.cursor-grab {
  user-select: none;
}
</style>
