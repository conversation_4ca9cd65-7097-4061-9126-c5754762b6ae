<script setup lang="ts">
const breadcrumbs = [
  { label: 'Home', href: '/admin' },
  { label: 'Admissions' },
];
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <div class="flex flex-col items-center justify-start gap-2 w-full">
      <h2 class="text-2xl font-bold tracking-tight w-full">Admissions</h2>

      <div class="space-y-4 w-full bg-white rounded-md p-4 shadow-sm">
        <AdminAdmission />
      </div>
    </div>
  </div>
</template>
