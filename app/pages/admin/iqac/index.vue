<script setup lang="ts">
definePageMeta({
  middleware: ['admin'],
});

// Active tab management
const activeTab = ref('about');
const tabs = [
  { id: 'about', label: 'About' },
  { id: 'objectives', label: 'Objectives' },
  { id: 'institution', label: 'Institution Distinctiveness' },
  { id: 'mou', label: 'MOU' },
  { id: 'self_study_report', label: 'SSR' },
  { id: 'aqar', label: 'AQAR' },
  { id: 'quality_audits', label: 'Quality Audits' },
  { id: 'minutes', label: 'Minutes' },
  { id: 'reports', label: 'Reports' },
  { id: 'best_practice', label: 'Best Practices' },
  { id: 'feedback', label: 'Feedback' },
  { id: 'composition', label: 'Composition' },
  { id: 'policy_documents', label: 'Policy Documents' },
  { id: 'saac', label: 'SAAC' },
  { id: 'sss', label: 'SSS' },
  { id: 'nirf', label: 'NIRF' },
];

const breadcrumbs = ref([{ label: 'Home', href: '/admin' }, { label: 'IQAC' }]);
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <div class="flex flex-wrap items-center justify-between gap-2">
      <h2 class="text-2xl font-bold tracking-tight">IQAC Management</h2>
    </div>

    <!-- Tab Navigation -->
    <nav class="flex border-b border-gray-200 flex-wrap gap-2">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        :class="[
          'px-4 py-1 text-sm font-medium rounded-t-lg',
          activeTab === tab.id
            ? 'bg-white text-primary border-b-2 border-primary'
            : 'text-gray-500 hover:text-gray-700',
        ]"
        @click="activeTab = tab.id"
      >
        {{ tab.label }}
      </button>
    </nav>
    <!-- Tab Content -->
    <!-- Check if department is loaded -->
    <main class="bg-white rounded-lg shadow p-6">
      <!-- Text Content Section -->
      <section v-if="activeTab === 'about'">
        <AdminIqacOverview param="about" />
      </section>
      <section v-if="activeTab === 'objectives'">
        <AdminIqacOverview param="objectives" />
      </section>
      <section v-if="activeTab === 'institution'">
        <AdminIqacOverview param="institution" />
      </section>

      <!-- Download Section -->
      <section v-if="activeTab === 'self_study_report'">
        <AdminIqacDownload param="self_study_report" />
      </section>
      <section v-if="activeTab === 'aqar'">
        <AdminIqacDownload param="aqar" />
      </section>
      <section v-if="activeTab === 'quality_audits'">
        <AdminIqacDownload param="quality_audits" />
      </section>
      <section v-if="activeTab === 'mou'">
        <AdminIqacDownload param="mou" />
      </section>
      <section v-if="activeTab === 'saac'">
        <AdminIqacDownload param="saac" />
      </section>
      <section v-if="activeTab === 'policy_documents'">
        <AdminIqacDownload param="policy_documents" />
      </section>
      <section v-if="activeTab === 'sss'">
        <AdminIqacDownload param="sss" />
      </section>

      <section v-if="activeTab === 'best_practice'">
        <AdminIqacAttachment param="best_practice" />
      </section>
      <section v-if="activeTab === 'feedback'">
        <AdminIqacAttachment param="feedback" />
      </section>
      <section v-if="activeTab === 'reports'">
        <AdminIqacAttachment param="reports" />
      </section>
      <section v-if="activeTab === 'minutes'">
        <AdminIqacAttachment param="minutes" />
      </section>
      <section v-if="activeTab === 'nirf'">
        <AdminIqacAttachment param="nirf" />
      </section>

      <!-- Composition Section -->
      <section v-if="activeTab === 'composition'">
        <AdminIqacComposition />
      </section>
    </main>
  </div>
</template>
