<!-- pages/admin/menus/index.vue -->
<script setup lang="ts">
import type { Menu } from '~~/server/database/tables/menu';
import { AlertCircle, PlusCircle } from 'lucide-vue-next';

const router = useRouter();
const isDialogOpen = ref(false);

const { menus, isLoading, fetchError, isSubmitting, createMenu } = useMenu();

async function onSubmit(values: any) {
  const success = await createMenu(values);
  if (success) isDialogOpen.value = false;
}

function navigateToMenu(item: Menu) {
  const routeVal = `/admin/menus/menu-item${`?id=${encodeURI(item?.id?.toString() ?? '')}`}`;
  router.push(routeVal);
}

const breadcrumbs = ref([
  { label: 'Home', href: '/admin' },
  { label: 'Menus' },
]);

definePageMeta({
  middleware: ['admin'],
});
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <div class="flex flex-wrap items-center justify-between gap-2">
      <h2 class="text-2xl font-bold tracking-tight">Manage Menus</h2>
      <div class="flex items-center space-x-2">
        <Dialog :open="isDialogOpen" @update:open="isDialogOpen = false">
          <DialogTrigger as-child>
            <Button variant="outline" @click="isDialogOpen = true">
              <PlusCircle class="h-4 w-4" />
              Add Menu
            </Button>
          </DialogTrigger>
          <DialogContent class="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Create New Menu</DialogTitle>
              <DialogDescription>
                Add a new menu to your list. Fill in the details below.
              </DialogDescription>
            </DialogHeader>

            <AdminMenuForm
              :is-submitting="isSubmitting"
              @submit="onSubmit"
              @cancel="isDialogOpen = false"
            />
          </DialogContent>
        </Dialog>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <!-- <Loader2 class="h-8 w-8 animate-spin" /> -->
      <AdminLayoutLoading />
    </div>

    <!-- Fetch Error State -->
    <Alert v-else-if="fetchError" variant="destructive" class="my-4">
      <AlertCircle class="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{{ fetchError }}</AlertDescription>
    </Alert>

    <!-- Content -->
    <main v-else class="flex flex-1 flex-col gap-4 md:gap-8">
      <AdminMenuList
        :menus="menus"
        @add-menu="isDialogOpen = true"
        @menu-click="navigateToMenu"
      />
    </main>
  </div>
</template>

<style scoped>
.card {
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
}
</style>
