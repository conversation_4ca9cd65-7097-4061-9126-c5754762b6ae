<!-- pages/admin/menus/menu-item.vue -->
<script setup lang="ts">
import { AlertCircle } from 'lucide-vue-next';
import { useMenuItem } from '~/composables/useMenuItem';

const {
  isLoading,
  error,
  isSubmitting,
  isEditing,
  showDeleteDialog,
  menuData,
  formValues,
  formSchema,
  toggleEdit,
  saveBasicInfo,
  deleteMenu,
  fetchMenuData,
  deleteState,
} = useMenuItem();

const isMenu = computed(() => {
  return menuData.value?.parentId === null;
});

onMounted(() => {
  fetchMenuData();
});

definePageMeta({
  middleware: ['admin'],
});

const breadcrumbs = computed(() => {
  const breadcrumbsList: { label: string; href?: string }[] = [
    { label: 'Home', href: '/admin' },
    { label: 'Menus', href: '/admin/menus' },
  ];

  if (!isMenu.value) {
    breadcrumbsList.push({
      label: 'Menu Item',
      href: '/admin/menus/menu-item?id=' + menuData.value?.parentId,
    });
    breadcrumbsList.push({
      label: 'Submenu',
    });
  } else {
    breadcrumbsList.push({
      label: 'Menu',
    });
  }
  return breadcrumbsList;
});
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs v-if="menuData?.id" :breadcrumbs="breadcrumbs" />

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center h-full">
      <!-- <Loader2 class="h-8 w-8 animate-spin" /> -->
      <AdminLayoutLoading />
    </div>

    <!-- Error State -->
    <Alert v-else-if="error" variant="destructive" class="my-4">
      <AlertTitle> <AlertCircle class="h-4 w-4" />Error </AlertTitle>
      <AlertDescription>{{ error }}</AlertDescription>
    </Alert>

    <!-- Content -->
    <main v-else-if="menuData" class="flex flex-1 flex-col gap-4 md:gap-8">
      <Tabs default-value="basic-info" class="w-full">
        <TabsList class="grid grid-cols-2">
          <TabsTrigger value="basic-info"> Basic Info </TabsTrigger>
          <TabsTrigger v-if="isMenu" value="submenu"> Submenu </TabsTrigger>
          <TabsTrigger
            v-if="!isMenu"
            value="template"
            :disabled="!formValues.routeEnabled"
            :class="
              !formValues.routeEnabled ? 'opacity-50 cursor-not-allowed' : ''
            "
          >
            Template
            <span
              v-if="!menuData?.routeEnabled"
              class="ml-1 text-[10px] sm:text-xs"
            >
              (Route Disabled)
            </span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic-info">
          <AdminMenuBasicInfo
            :menu-data="menuData"
            :is-editing="isEditing"
            :is-submitting="isSubmitting"
            :form-schema="formSchema"
            :form-values="formValues"
            :is-menu="isMenu"
            @save="saveBasicInfo"
            @toggle-edit="toggleEdit"
            @delete="showDeleteDialog = true"
          />
        </TabsContent>

        <TabsContent value="template">
          <AdminMenuTemplate
            :menu-data="menuData"
            @fetch-menu-data="fetchMenuData"
          />
        </TabsContent>

        <TabsContent value="submenu">
          <AdminMenuSubmenu :menu-data="menuData" @refresh="fetchMenuData" />
        </TabsContent>
      </Tabs>
    </main>

    <AdminMenuDeleteDialog
      v-if="menuData"
      v-model:is-open="showDeleteDialog"
      :is-submitting="isSubmitting"
      :menu-title="menuData.title"
      :delete-state="deleteState"
      @confirm="deleteMenu"
    />
  </div>
</template>
