<script setup lang="ts">
import { Loader2 } from 'lucide-vue-next';
import type { DepartmentDetails } from '~/types/admin/department';

const route = useRoute();
const departmentId = computed(() => {
  const id = (route.params as any).id;
  return typeof id === 'string' ? Number(id) : 0;
});

definePageMeta({
  middleware: ['admin'],
});

const { data: department } = useFetch<DepartmentDetails>(
  `/api/admin/department/${departmentId.value}`
);

// Active tab management
const activeTab = ref('overview');
const tabs = [
  { id: 'overview', label: 'Overview' },
  { id: 'courses', label: 'Courses' },
  { id: 'faculty', label: 'Faculty' },
  { id: 'placements', label: 'Placements' },
  { id: 'downloads', label: 'Downloads' },
  { id: 'questionBank', label: 'Question Bank' },
  { id: 'projectWork', label: 'Project Work' },
  { id: 'gallery', label: 'Gallery' },
  { id: 'events', label: 'Events' },
  { id: 'toppers', label: 'Toppers' },
];

const breadcrumbs = computed(() => [
  { label: 'Departments', href: '/admin/departments' },
  { label: department.value?.name || 'Department' },
]);
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs
      v-if="department && department.name"
      :breadcrumbs="breadcrumbs"
    />
    <div class="flex flex-wrap items-center justify-between gap-2">
      <h2 class="text-2xl font-bold tracking-tight">
        {{ department?.name || 'Department Management' }}
      </h2>
    </div>

    <!-- Tab Navigation -->
    <nav class="flex border-b border-gray-200 flex-wrap gap-2">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        :class="[
          'px-4 py-1 text-sm font-medium rounded-t-lg',
          activeTab === tab.id
            ? 'bg-white text-primary border-b-2 border-primary'
            : 'text-gray-500 hover:text-gray-700',
        ]"
        @click="activeTab = tab.id"
      >
        {{ tab.label }}
      </button>
    </nav>
    <!-- Tab Content -->
    <!-- Check if department is loaded -->
    <main v-if="departmentId" class="bg-white rounded-lg shadow p-6">
      <!-- Overview Section -->
      <section v-if="activeTab === 'overview'">
        <AdminDepartmentOverview :department-id="departmentId" />
      </section>

      <!-- Courses Section -->
      <section v-if="activeTab === 'courses'">
        <AdminDepartmentCourses :department-id="departmentId" />
      </section>

      <!-- Faculty Section -->
      <section v-if="activeTab === 'faculty'">
        <AdminDepartmentFaculty :department-id="departmentId" />
      </section>

      <!-- Placements Section -->
      <section v-if="activeTab === 'placements'">
        <AdminDepartmentPlacements :department-id="departmentId" />
      </section>

      <!-- Downloads Section -->
      <section v-if="activeTab === 'downloads'">
        <AdminDepartmentDownloads :department-id="departmentId" />
      </section>

      <!-- Question Bank Section -->
      <section v-if="activeTab === 'questionBank'">
        <AdminDepartmentQuestionBanks
          title="Question Banks"
          :department-id="departmentId"
        />
      </section>

      <!-- Project Work Section -->
      <section v-if="activeTab === 'projectWork'">
        <AdminDepartmentProjectWorks :department-id="departmentId" />
      </section>

      <!-- Achievements Section -->
      <section v-if="activeTab === 'achievements'">
        <AdminDepartmentAchievements :department-id="departmentId" />
      </section>

      <!-- Gallery Section -->
      <section v-if="activeTab === 'gallery'">
        <AdminDepartmentGallery :department-id="departmentId" />
      </section>

      <!-- Events Section -->
      <section v-if="activeTab === 'events'">
        <AdminDepartmentEvents :department-id="departmentId" />
      </section>

      <!-- Toppers Section -->
      <section v-if="activeTab === 'toppers'">
        <AdminDepartmentToppers :department-id="departmentId" />
      </section>
    </main>
    <div v-else>
      <div class="w-full h-full flex items-center justify-center">
        <Loader2 class="w-4 h-4 animate-spin" />
      </div>
    </div>
  </div>
</template>
