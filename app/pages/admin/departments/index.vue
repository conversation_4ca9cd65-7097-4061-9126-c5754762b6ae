<script setup lang="ts">
import { AlertCircle, PlusCircle, Pencil, Trash2 } from 'lucide-vue-next';
import type { DepartmentListData } from '~/types/admin';

const router = useRouter();
const isDialogOpen = ref(false);
const isEditDialogOpen = ref(false);
const showDeleteDialog = ref(false);
const currentDepartment = ref<DepartmentListData | null>(null);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

const {
  departments,
  isLoading,
  fetchError,
  isSubmitting,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  fetchDepartments,
} = useDepartmentsList();

definePageMeta({
  middleware: ['admin'],
});

// Fetch departments on mount
onMounted(() => {
  fetchDepartments();
});

const breadcrumbs = ref([
  { label: 'Home', href: '/admin' },
  { label: 'Departments' },
]);

const onSubmit = async (values: { name: string; slug: string }) => {
  const success = await createDepartment(values);
  if (success) {
    isDialogOpen.value = false;
  }
};

const handleEdit = (department: DepartmentListData) => {
  currentDepartment.value = { ...department }; // Create a copy of the department data
  isEditDialogOpen.value = true;
};

const handleDelete = (department: DepartmentListData) => {
  currentDepartment.value = { ...department };
  showDeleteDialog.value = true;
};

const onUpdate = async (values: { name: string; slug: string }) => {
  if (!currentDepartment.value) return;

  const success = await updateDepartment(currentDepartment.value.id, values);

  if (success) {
    isEditDialogOpen.value = false;
    currentDepartment.value = null;
  }
};

const onDelete = async () => {
  if (!currentDepartment.value) return;
  deleteState.value = 'loading';

  const success = await deleteDepartment(currentDepartment.value.id);

  if (success) {
    deleteState.value = 'success';
    setTimeout(() => {
      showDeleteDialog.value = false;
      deleteState.value = 'idle';
      currentDepartment.value = null;
    }, 1000);
  } else {
    deleteState.value = 'error';
  }
};

const navigateToDepartment = (department: { id: number }) => {
  router.push(`/admin/departments/${department.id}`);
};
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <div class="flex flex-wrap items-center justify-between gap-2">
      <h2 class="text-2xl font-bold tracking-tight">Manage Departments</h2>
      <div class="flex items-center space-x-2">
        <Dialog :open="isDialogOpen" @update:open="isDialogOpen = false">
          <DialogTrigger as-child>
            <Button variant="outline" @click="isDialogOpen = true">
              <PlusCircle class="h-4 w-4 mr-2" />
              Add Department
            </Button>
          </DialogTrigger>
          <DialogContent class="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Create New Department</DialogTitle>
              <DialogDescription>
                Add a new department to your institution. Fill in the details
                below.
              </DialogDescription>
            </DialogHeader>

            <AdminDepartmentForm
              :is-submitting="isSubmitting"
              type="create"
              @submit="onSubmit"
              @cancel="isDialogOpen = false"
            >
              <template #submit-text>
                {{ isSubmitting ? 'Creating...' : 'Create Department' }}
              </template>
            </AdminDepartmentForm>
          </DialogContent>
        </Dialog>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <AdminLayoutLoading />
    </div>

    <!-- Fetch Error State -->
    <Alert v-else-if="fetchError" variant="destructive" class="my-4">
      <AlertCircle class="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{{ fetchError }}</AlertDescription>
    </Alert>

    <!-- Departments List -->
    <main v-else class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <Card
        v-for="department in departments"
        :key="department.id"
        class="hover:shadow-md transition-shadow"
      >
        <CardHeader>
          <div class="flex items-center justify-between">
            <div
              class="flex-1 cursor-pointer"
              tabindex="0"
              role="button"
              :aria-label="'View ' + department.name + ' department'"
              @click="navigateToDepartment(department)"
              @keydown.enter="navigateToDepartment(department)"
            >
              <CardTitle>{{ department.name }}</CardTitle>
              <CardDescription>{{ department.slug }}</CardDescription>
            </div>
            <div class="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                class="text-muted-foreground hover:text-foreground"
                @click="handleEdit(department)"
              >
                <Pencil class="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                class="text-muted-foreground hover:text-destructive"
                @click="handleDelete(department)"
              >
                <Trash2 class="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>
    </main>

    <!-- Edit Dialog -->
    <Dialog :open="isEditDialogOpen" @update:open="isEditDialogOpen = false">
      <DialogContent class="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Department</DialogTitle>
          <DialogDescription>
            Update department details below.
          </DialogDescription>
        </DialogHeader>

        <AdminDepartmentForm
          v-if="currentDepartment"
          :is-submitting="isSubmitting"
          type="edit"
          :initial-values="{
            name: currentDepartment.name,
            slug: currentDepartment.slug,
          }"
          @submit="onUpdate"
          @cancel="isEditDialogOpen = false"
        >
          <template #submit-text>
            {{ isSubmitting ? 'Updating...' : 'Update Department' }}
          </template>
        </AdminDepartmentForm>
      </DialogContent>
    </Dialog>

    <!-- Delete Dialog -->
    <AdminMenuDeleteDialog
      v-if="currentDepartment"
      v-model:is-open="showDeleteDialog"
      :is-submitting="isSubmitting"
      :menu-title="currentDepartment.name"
      :delete-state="deleteState"
      :description="`Are you sure you want to delete department '${currentDepartment.name}'? This action cannot be undone.`"
      @confirm="onDelete"
    />
  </div>
</template>
