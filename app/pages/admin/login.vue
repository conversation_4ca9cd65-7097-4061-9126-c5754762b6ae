<script setup lang="ts">
import { z } from 'zod';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import { Loader2, Lock } from 'lucide-vue-next';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

definePageMeta({
  layout: 'blank',
  auth: false,
});

const { loggedIn, user: _user, fetch: fetchSession } = useUserSession();

// Fetch the user session on page load
await fetchSession();

// Redirect if already logged in
whenever(
  loggedIn,
  () => {
    navigateTo('/admin/');
  },
  { immediate: true }
);

// Login form schema
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
});

// Error message
const errorMessage = ref('');

// Form validation - properly initialize with initial values
const { handleSubmit, errors, isSubmitting, values, setFieldValue } = useForm({
  validationSchema: toTypedSchema(loginSchema),
  initialValues: {
    username: 'admin',
    password: 'donbosco@abcd',
  },
});

// Handle form submission
const onSubmit = handleSubmit(async (formValues) => {
  try {
    errorMessage.value = '';

    // Call login API with proper error handling
    const { data, error } = await useFetch<{ success: boolean }>(
      '/api/admin/login',
      {
        method: 'POST',
        body: formValues, // Use values from form submission
        credentials: 'include', // Ensure cookies are sent/received
      }
    );

    if (error.value) {
      errorMessage.value =
        error.value?.data?.message || 'Login failed. Please try again.';
      console.error('Login API error:', error.value);
      return;
    }

    if (data.value?.success) {
      // Refresh session data before redirect
      await fetchSession();
      // Redirect to dashboard on success
      navigateTo('/admin/', { replace: true });
    } else {
      errorMessage.value = 'Login failed. Please check your credentials.';
    }
  } catch (err) {
    console.error('Login error:', err);
    errorMessage.value = 'An unexpected error occurred. Please try again.';
  }
});

// Page metadata for SEO
useHead({
  title: 'College CMS - Admin Login',
  meta: [
    {
      name: 'description',
      content:
        'Secure login portal for college website content management system administrators',
    },
  ],
});
</script>

<template>
  <div
    class="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 via-blue-100 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-800 py-6 px-4 sm:py-12 sm:px-6 lg:px-8 transition-all duration-300 relative overflow-hidden"
  >
    <!-- Decorative elements -->
    <div
      class="absolute top-0 left-0 w-full h-24 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 dark:from-blue-500/5 dark:to-indigo-500/5 transform -skew-y-3"
    ></div>
    <div
      class="absolute bottom-0 right-0 w-full h-24 bg-gradient-to-l from-blue-500/10 to-indigo-500/10 dark:from-blue-500/5 dark:to-indigo-500/5 transform skew-y-3"
    ></div>

    <div
      class="w-full max-w-md mx-auto space-y-8 bg-white dark:bg-gray-800 shadow-2xl rounded-2xl p-6 sm:p-8 md:p-10 border border-gray-200 dark:border-gray-700 transition-all duration-300 backdrop-blur-sm bg-opacity-95 dark:bg-opacity-90 z-10"
    >
      <div class="text-center">
        <!-- College Logo Placeholder -->
        <div
          class="mx-auto h-16 w-16 sm:h-20 sm:w-20 bg-gradient-to-tr from-blue-100 to-blue-200 border border-blue-200 rounded-full flex items-center justify-center mb-5 shadow-xl shadow-blue-500/20 dark:shadow-blue-500/10 transform transition-transform duration-300 hover:scale-105"
        >
          <NuxtImg
            width="60"
            height="auto"
            class="w-[40px] md:w-[54px] h-auto object-scale-down"
            src="/img/db-college-logo.png"
            alt="Don Bosco College Mannuthy"
          />
        </div>
        <h2
          class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 dark:text-white tracking-tight mb-1"
        >
          Admin Login
        </h2>
        <p class="text-sm sm:text-base text-gray-600 dark:text-gray-400">
          Enter your credentials to access the admin panel
        </p>
      </div>

      <Form
        class="mt-8 space-y-6"
        :validation-schema="loginSchema"
        :initial-values="{
          username: values.username,
          password: values.password,
        }"
      >
        <div class="space-y-5">
          <div class="group">
            <FormField v-slot="{ field, meta }" name="username">
              <FormItem>
                <FormLabel>Username</FormLabel>
                <div
                  class="relative transition-all duration-200 focus-within:ring-1 focus-within:ring-offset-1 focus-within:ring-blue-500 rounded-lg"
                >
                  <div
                    class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  </div>
                  <FormControl>
                    <Input
                      id="username"
                      v-bind="field"
                      :model-value="field.value"
                      type="text"
                      class="appearance-none block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 text-gray-900 dark:text-white dark:bg-gray-700 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-all duration-200"
                      placeholder="Enter your username"
                      autocomplete="username"
                      @update:model-value="
                        (value: any) =>
                          setFieldValue('username', value as string)
                      "
                    />
                  </FormControl>
                </div>
                <p
                  v-if="meta.touched && errors.username"
                  class="mt-2 text-sm text-red-600 dark:text-red-400"
                >
                  {{ errors.username }}
                </p>
              </FormItem>
            </FormField>
          </div>

          <div class="group">
            <FormField v-slot="{ field, meta }" name="password">
              <FormItem>
                <FormLabel>Password</FormLabel>
                <div
                  class="relative transition-all duration-200 focus-within:ring-1 focus-within:ring-offset-1 focus-within:ring-blue-500 rounded-lg"
                >
                  <div
                    class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                  </div>
                  <FormControl>
                    <Input
                      id="password"
                      v-bind="field"
                      :model-value="field.value"
                      type="password"
                      placeholder="Enter your password"
                      autocomplete="current-password"
                      class="appearance-none block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 text-gray-900 dark:text-white dark:bg-gray-700 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-all duration-200"
                      @update:model-value="
                        (value: any) =>
                          setFieldValue('password', value as string)
                      "
                    />
                  </FormControl>
                </div>
                <p
                  v-if="meta.touched && errors.password"
                  class="mt-2 text-sm text-red-600 dark:text-red-400"
                >
                  {{ errors.password }}
                </p>
              </FormItem>
            </FormField>
          </div>
        </div>

        <div
          v-if="errorMessage"
          class="rounded-lg bg-red-50 dark:bg-red-900/20 my-5 border border-red-200 dark:border-red-800 animate-fadeIn"
        >
          <div class="flex">
            <div class="flex-shrink-0">
              <svg
                class="h-5 w-5 text-red-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-300">
                {{ errorMessage }}
              </h3>
            </div>
          </div>
        </div>

        <div class="flex flex-col gap-2 w-full">
          <Button
            type="submit"
            :disabled="isSubmitting"
            class="group relative w-full flex justify-center !p-5 border border-transparent text-sm sm:text-base font-medium rounded-lg text-white"
            aria-label="Sign in"
            tabindex="0"
            @click="onSubmit"
          >
            <span
              v-if="isSubmitting"
              class="absolute left-0 inset-y-0 flex items-center pl-3"
            >
              <!-- Loading spinner -->
              <Loader2 class="animate-spin h-5 w-5 text-white" />
            </span>
            <span
              v-else
              class="absolute left-0 inset-y-0 flex items-center pl-3"
            >
              <!-- Lock icon -->
              <Lock
                class="h-5 w-5 text-blue-300 group-hover:text-blue-200 transition-colors duration-200"
              />
            </span>
            {{ isSubmitting ? 'Signing in...' : 'Sign in' }}
          </Button>
          <!-- Add a go back button -->
          <Button
            type="button"
            variant="ghost"
            class="w-full p-5"
            @click="navigateTo('/')"
          >
            Go back
          </Button>
        </div>
      </Form>

      <div class="text-center mt-6">
        <p class="text-xs text-gray-500 dark:text-gray-400">
          © {{ new Date().getFullYear() }} College CMS. All rights reserved.
        </p>
      </div>
    </div>
  </div>
</template>
