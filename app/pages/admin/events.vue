<script setup lang="ts">
definePageMeta({
  middleware: ['admin'],
});

const breadcrumbs = ref([
  { label: 'Home', href: '/admin' },
  { label: 'Events' },
]);
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <main class="flex flex-1 flex-col gap-4 md:gap-8">
      <AdminAnnouncementTemplate type="event" />
    </main>
  </div>
</template>
