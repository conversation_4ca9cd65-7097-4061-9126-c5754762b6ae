<script setup lang="ts">
import { AlertCircle } from 'lucide-vue-next';

definePageMeta({
  middleware: ['admin'],
});

const isLoading = ref(false);
const fetchError = ref(null);

const breadcrumbs = ref([
  { label: 'Home', href: '/admin' },
  { label: 'Landing Page' },
]);
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <div class="flex flex-wrap items-center justify-between gap-2">
      <h2 class="text-2xl font-bold tracking-tight">
        Landing Page Configuration
      </h2>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <AdminLayoutLoading />
    </div>

    <!-- Fetch Error State -->
    <Alert v-else-if="fetchError" variant="destructive" class="my-4">
      <AlertCircle class="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{{ fetchError }}</AlertDescription>
    </Alert>

    <!-- Content -->
    <main v-else class="flex flex-1 flex-col gap-4">
      <AdminLandingForm />
    </main>
  </div>
</template>
