<script setup lang="ts">
definePageMeta({
  middleware: ['admin'],
});

const breadcrumbs = [
  { label: 'Admin', href: '/admin' },
  { label: 'Clubs and Committees', href: '/admin/clubs-and-committees' },
];
</script>
<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <AdminClubAndCommittee />
  </div>
</template>
