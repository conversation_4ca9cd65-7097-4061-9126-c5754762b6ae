<script setup lang="ts">
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { UploadList } from '~/components/admin/file';

definePageMeta({
  middleware: ['admin'],
});

const breadcrumbs = ref([
  { label: 'Home', href: '/admin' },
  { label: 'File Management' },
]);
const activeTab = ref('all');

const fileTypes = [
  { id: 'all', label: 'All Files' },
  { id: 'document', label: 'Documents' },
  { id: 'image', label: 'Images' },
  { id: 'video', label: 'Videos' },
  { id: 'audio', label: 'Audio' },
  { id: 'other', label: 'Other' },
];
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <div class="flex flex-wrap items-center justify-between gap-2">
      <h2 class="text-2xl font-bold tracking-tight">File Management</h2>
    </div>

    <!-- Tabs -->
    <Tabs v-model="activeTab" class="w-full">
      <TabsList class="grid grid-cols-6 w-full">
        <TabsTrigger v-for="type in fileTypes" :key="type.id" :value="type.id">
          {{ type.label }}
        </TabsTrigger>
      </TabsList>

      <TabsContent
        v-for="type in fileTypes"
        :key="type.id"
        :value="type.id"
        class="mt-4"
      >
        <UploadList :file-type="type.id === 'all' ? undefined : type.id" />
      </TabsContent>
    </Tabs>
  </div>
</template>
