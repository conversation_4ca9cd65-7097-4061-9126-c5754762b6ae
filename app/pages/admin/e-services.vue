<script setup lang="ts">
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { AlertCircle, PlusCircle } from 'lucide-vue-next';
import { useEservices } from '~/composables/useQuickLinks';
import type { QuickLink } from '~~/server/database/tables/quick-link';
import type { CreateEServiceSchema } from '~~/shared/schema/e-service/create';

definePageMeta({
  middleware: ['admin'],
});

const isDialogOpen = ref(false);
const selectedService = ref<QuickLink | null>(null);
const showDeleteDialog = ref(false);
const deleteState = ref<'idle' | 'loading' | 'success' | 'error'>('idle');

const {
  eServices,
  isLoading,
  fetchError,
  isSubmitting,
  createEservice,
  updateEservice,
  deleteEservice,
} = useEservices();

const columns = [
  { key: 'title', label: 'Title' },
  { key: 'link', label: 'Link' },
  { key: 'actions', label: 'Actions' },
];

const handleAdd = () => {
  selectedService.value = null;
  isDialogOpen.value = true;
};

const handleEdit = (service: QuickLink) => {
  selectedService.value = service;
  isDialogOpen.value = true;
};

const handleDelete = (service: QuickLink) => {
  selectedService.value = service;
  showDeleteDialog.value = true;
};

const onDelete = async () => {
  if (!selectedService.value?.id) return;
  deleteState.value = 'loading';

  const success = await deleteEservice(selectedService.value.id);

  if (success) {
    deleteState.value = 'success';
    setTimeout(() => {
      showDeleteDialog.value = false;
      deleteState.value = 'idle';
      selectedService.value = null;
    }, 1000);
  } else {
    deleteState.value = 'error';
  }
};

const handleFormSubmit = async (values: CreateEServiceSchema) => {
  let success;

  if (selectedService.value?.id) {
    success = await updateEservice(selectedService.value.id, values);
  } else {
    success = await createEservice(values);
  }

  if (success) {
    isDialogOpen.value = false;
    selectedService.value = null;
  }
};

const handleFormCancel = () => {
  isDialogOpen.value = false;
  selectedService.value = null;
};

const breadcrumbs = ref([
  { label: 'Home', href: '/admin' },
  { label: 'E-Services' },
]);
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <div class="flex flex-wrap items-center justify-between gap-2">
      <h2 class="text-2xl font-bold tracking-tight">Manage E-Services</h2>
      <div class="flex items-center space-x-2">
        <Dialog :open="isDialogOpen" @update:open="isDialogOpen = $event">
          <DialogTrigger as-child>
            <Button variant="outline" @click="handleAdd">
              <PlusCircle class="h-4 w-4 mr-2" />
              Add Service
            </Button>
          </DialogTrigger>
          <DialogContent class="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {{ selectedService ? 'Edit Service' : 'Create New Service' }}
              </DialogTitle>
              <DialogDescription>
                {{
                  selectedService
                    ? 'Edit existing service details.'
                    : 'Add a new service to your list. Fill in the details below.'
                }}
              </DialogDescription>
            </DialogHeader>
            <AdminEServiceForm
              :initial-values="selectedService"
              :is-submitting="isSubmitting"
              @submit="handleFormSubmit"
              @cancel="handleFormCancel"
            />
          </DialogContent>
        </Dialog>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <AdminLayoutLoading />
    </div>

    <!-- Fetch Error State -->
    <Alert v-else-if="fetchError" variant="destructive" class="my-4">
      <AlertCircle class="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{{ fetchError }}</AlertDescription>
    </Alert>

    <!-- Content -->
    <Card v-else class="w-full p-6 border-none shadow-none">
      <CardHeader class="px-2 pt-0 pb-4">
        <CardTitle>E-Services List</CardTitle>
      </CardHeader>

      <CardContent class="px-2 pt-0 pb-4">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead v-for="column in columns" :key="column.key">
                {{ column.label }}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-if="!eServices.length">
              <TableCell colspan="3" class="text-center py-4">
                No services found.
              </TableCell>
            </TableRow>
            <TableRow v-for="service in eServices" :key="service.id">
              <TableCell>{{ service.title }}</TableCell>
              <TableCell>
                <a
                  :href="service.link ?? ''"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="text-blue-600 hover:underline"
                  tabindex="0"
                >
                  {{ service.link }}
                </a>
              </TableCell>
              <TableCell>
                <div class="flex space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    :disabled="isSubmitting"
                    aria-label="Edit service"
                    @click="handleEdit(service)"
                  >
                    Edit
                  </Button>
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    :disabled="isSubmitting"
                    aria-label="Delete service"
                    @click="handleDelete(service)"
                  >
                    Delete
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>

    <!-- Delete Dialog -->
    <AdminMenuDeleteDialog
      v-if="selectedService"
      v-model:is-open="showDeleteDialog"
      :is-submitting="isSubmitting"
      :menu-title="selectedService.title"
      :delete-state="deleteState"
      :description="`Are you sure you want to delete e-service '${selectedService.title}'? This action cannot be undone.`"
      @confirm="onDelete"
    />
  </div>
</template>
