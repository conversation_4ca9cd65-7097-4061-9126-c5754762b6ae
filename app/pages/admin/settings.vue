<script setup lang="ts">
import { z } from 'zod';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import { Loader2 } from 'lucide-vue-next';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

definePageMeta({
  middleware: ['admin'],
});

// Password update form schema
const passwordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: z
      .string()
      .min(8, 'New password must be at least 8 characters'),
    confirmPassword: z
      .string()
      .min(8, 'Confirm password must be at least 8 characters'),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

// Error message
const errorMessage = ref('');
const successMessage = ref('');

// Form validation
const { handleSubmit, errors, isSubmitting, setFieldValue } = useForm({
  validationSchema: toTypedSchema(passwordSchema),
  initialValues: {
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  },
});

// Handle form submission
const onSubmit = handleSubmit(async (formValues) => {
  try {
    errorMessage.value = '';
    successMessage.value = '';

    // Extract only the required fields for the API call
    const { currentPassword, newPassword } = formValues;

    // Call update password API
    const { data, error } = await useFetch<{ success: boolean }>(
      '/api/admin/update-password',
      {
        method: 'POST',
        body: { currentPassword, newPassword },
        credentials: 'include', // Ensure cookies are sent/received
      }
    );

    if (error.value) {
      errorMessage.value =
        error.value.data?.message ||
        'Failed to update password. Please try again.';
      return;
    }

    if (data.value?.success) {
      successMessage.value = 'Password updated successfully!';
    } else {
      errorMessage.value = 'Failed to update password. Please try again.';
    }
  } catch (err) {
    console.error('Update password error:', err);
    errorMessage.value = 'An unexpected error occurred. Please try again.';
  }
});
</script>

<template>
  <div class="w-full flex flex-col gap-4 p-6">
    <div class="flex flex-wrap items-center justify-between gap-2">
      <h2 class="text-3xl font-bold text-gray-900 mb-2">Settings</h2>
    </div>
    <main class="flex flex-1 flex-col gap-4 md:gap-8">
      <div class="grid gap-4 md:gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Update Password</CardTitle>
            <p class="text-sm text-muted-foreground">
              Ensure your account is using a strong and secure password.
            </p>
          </CardHeader>
          <CardContent>
            <Form :validation-schema="passwordSchema" class="grid gap-4">
              <FormField v-slot="{ field, meta }" name="currentPassword">
                <FormItem>
                  <FormLabel>Current Password</FormLabel>
                  <div
                    class="relative transition-all duration-200 focus-within:ring-1 focus-within:ring-offset-1 focus-within:ring-blue-500 rounded-lg"
                  >
                    <div
                      class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5 text-gray-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                        />
                      </svg>
                    </div>
                    <FormControl>
                      <Input
                        id="current-password"
                        v-bind="field"
                        :model-value="field.value"
                        type="password"
                        class="appearance-none block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 text-gray-900 dark:text-white dark:bg-gray-700 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-all duration-200"
                        placeholder="Enter your current password"
                        autocomplete="current-password"
                        @update:model-value="
                          (value) => {
                            setFieldValue('currentPassword', value as string);
                            errorMessage = ''; // Reset error message
                            successMessage = ''; // Reset success message
                          }
                        "
                      />
                    </FormControl>
                  </div>
                  <p
                    v-if="meta.touched && errors.currentPassword"
                    class="mt-2 text-sm text-red-600 dark:text-red-400"
                  >
                    {{ errors.currentPassword }}
                  </p>
                </FormItem>
              </FormField>

              <FormField v-slot="{ field, meta }" name="newPassword">
                <FormItem>
                  <FormLabel>New Password</FormLabel>
                  <div
                    class="relative transition-all duration-200 focus-within:ring-1 focus-within:ring-offset-1 focus-within:ring-blue-500 rounded-lg"
                  >
                    <div
                      class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5 text-gray-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                        />
                      </svg>
                    </div>
                    <FormControl>
                      <Input
                        id="new-password"
                        v-bind="field"
                        :model-value="field.value"
                        type="password"
                        class="appearance-none block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 text-gray-900 dark:text-white dark:bg-gray-700 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-all duration-200"
                        placeholder="Enter your new password"
                        autocomplete="new-password"
                        @update:model-value="
                          (value) => {
                            setFieldValue('newPassword', value as string);
                            errorMessage = ''; // Reset error message
                            successMessage = ''; // Reset success message
                          }
                        "
                      />
                    </FormControl>
                  </div>
                  <p
                    v-if="meta.touched && errors.newPassword"
                    class="mt-2 text-sm text-red-600 dark:text-red-400"
                  >
                    {{ errors.newPassword }}
                  </p>
                </FormItem>
              </FormField>

              <FormField v-slot="{ field, meta }" name="confirmPassword">
                <FormItem>
                  <FormLabel>Confirm New Password</FormLabel>
                  <div
                    class="relative transition-all duration-200 focus-within:ring-1 focus-within:ring-offset-1 focus-within:ring-blue-500 rounded-lg"
                  >
                    <div
                      class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5 text-gray-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                        />
                      </svg>
                    </div>
                    <FormControl>
                      <Input
                        id="confirm-password"
                        v-bind="field"
                        :model-value="field.value"
                        type="password"
                        class="appearance-none block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 text-gray-900 dark:text-white dark:bg-gray-700 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-all duration-200"
                        placeholder="Confirm your new password"
                        autocomplete="new-password"
                        @update:model-value="
                          (value) => {
                            setFieldValue('confirmPassword', value as string);
                            errorMessage = ''; // Reset error message
                            successMessage = ''; // Reset success message
                          }
                        "
                      />
                    </FormControl>
                  </div>
                  <p
                    v-if="meta.touched && errors.confirmPassword"
                    class="mt-2 text-sm text-red-600 dark:text-red-400"
                  >
                    {{ errors.confirmPassword }}
                  </p>
                </FormItem>
              </FormField>

              <div v-if="errorMessage" class="text-red-600 text-sm">
                {{ errorMessage }}
              </div>
              <div v-if="successMessage" class="text-green-600 text-sm">
                {{ successMessage }}
              </div>

              <Button type="submit" :disabled="isSubmitting" @click="onSubmit">
                <Loader2
                  v-if="isSubmitting"
                  class="mr-2 h-4 w-4 animate-spin"
                />
                {{ isSubmitting ? 'Updating...' : 'Update Password' }}
              </Button>
            </Form>
          </CardContent>
        </Card>
      </div>
    </main>
  </div>
</template>
