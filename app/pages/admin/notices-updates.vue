<script setup lang="ts">
definePageMeta({
  middleware: ['admin'],
});

const breadcrumbs = ref([
  { label: 'Home', href: '/admin' },
  { label: 'Notices & Updates' },
]);
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <main class="flex flex-1 flex-col gap-4 md:gap-8">
      <Tabs default-value="notice" class="w-full">
        <TabsList class="grid grid-cols-2 w-full">
          <TabsTrigger value="notice"> Notice </TabsTrigger>
          <TabsTrigger value="update"> Update </TabsTrigger>
        </TabsList>

        <TabsContent value="notice">
          <AdminAnnouncementTemplate type="notice" />
        </TabsContent>
        <TabsContent value="update">
          <AdminAnnouncementTemplate type="update" />
        </TabsContent>
      </Tabs>
    </main>
  </div>
</template>
