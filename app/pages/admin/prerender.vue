<script setup lang="ts">
definePageMeta({
  middleware: ['admin'],
});

const breadcrumbs = ref([
  { label: 'Home', href: '/admin' },
  { label: 'Pre-render Manager' },
]);
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />

    <!-- Pre-render Manager Component -->
    <AdminPrerenderManager />
  </div>
</template>
