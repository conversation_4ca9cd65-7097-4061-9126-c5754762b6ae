// pages/admin/gallery/[id].vue
<script setup lang="ts">
import { Image } from 'lucide-vue-next';

const route = useRoute();
const albumId = (route.params as any).id as string;

const {
  album,
  isLoading,
  isUploading,
  fetchAlbumPhotos,
  uploadPhotos,
  deletePhotos,
} = useAlbumPhotos(albumId);

// Fetch album data when component mounts
onMounted(() => {
  fetchAlbumPhotos();
});

definePageMeta({
  middleware: ['admin'],
});

// const isUploading = ref(false);

// async function uploadFiles(files: File[]) {
//   isUploading.value = true;
//   await uploadPhotos(files);

//   isUploading.value = false;
// }

const breadcrumbs = computed(() => [
  { label: 'Home', href: '/admin' },
  { label: 'Gallery', href: '/admin/gallery' },
  { label: album?.value?.title ?? 'Album' },
]);
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <main class="flex flex-1 flex-col gap-4 md:gap-8">
      <AdminGalleryAlbumView
        v-if="album"
        :album-id="albumId"
        :initial-data="album"
        :is-loading="isLoading"
        :is-uploading="isUploading"
        @update:photos="uploadPhotos"
        @delete-photos="deletePhotos"
      />

      <Card
        v-else
        class="flex flex-col items-center justify-center py-8 text-center border-none"
      >
        <div class="rounded-full bg-muted p-3 mb-4">
          <Image class="h-6 w-6 text-muted-foreground" />
        </div>
        <h3 class="font-semibold mb-1">Album Not Found</h3>
        <p class="text-sm text-muted-foreground">
          Go back to the gallery page to create an album.
        </p>
      </Card>
    </main>
  </div>
</template>
