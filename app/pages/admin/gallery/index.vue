<script setup lang="ts">
const breadcrumbs = ref([
  { label: 'Home', href: '/admin' },
  { label: 'Gallery' },
]);
definePageMeta({
  middleware: ['admin'],
});
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <main class="flex flex-1 flex-col gap-4 md:gap-8">
      <AdminGalleryAlbums />
    </main>
  </div>
</template>
