<script setup lang="ts">
import { toast } from 'vue-sonner';
import type { FileData } from '~/types/home';

const popUpFile = ref<FileData | null>(null);
const status = ref<'idle' | 'pending'>('idle');
const showConfirmDialog = ref(false);

const fetchPopUp = async () => {
  status.value = 'pending';
  try {
    const popUpData = await $fetch('/api/admin/pop-up');
    popUpFile.value = popUpData
      ? {
          pathname: popUpData.pathname || '',
          title: popUpData.title || '',
          type: popUpData.type || '',
          prefix: popUpData.prefix || 'Pop-Up',
        }
      : null;
  } catch (err) {
    console.error('Failed to fetch pop-up:', err);
    toast.error('Failed to fetch pop-up');
  } finally {
    status.value = 'idle';
  }
};

const handleSubmit = async () => {
  if (popUpFile.value) {
    try {
      status.value = 'pending';
      await $fetch('/api/admin/pop-up', {
        method: 'POST',
        body: popUpFile.value,
      });
      fetchPopUp();
      toast.success('Pop-Up created successfully');
    } catch (err) {
      console.error('Failed to create pop-up:', err);
      toast.error('Failed to create pop-up');
    } finally {
      status.value = 'idle';
    }
  } else {
    toast.error('Please upload a file before submitting');
  }
};

const handleRemoveConfirm = async () => {
  try {
    status.value = 'pending';
    await $fetch('/api/admin/pop-up', {
      method: 'DELETE',
    });
    popUpFile.value = null;
    toast.success('Pop-Up removed successfully');
  } catch (err) {
    console.error('Failed to remove pop-up:', err);
    toast.error('Failed to remove pop-up');
  } finally {
    status.value = 'idle';
    showConfirmDialog.value = false;
  }
};

const handleRemove = () => {
  showConfirmDialog.value = true;
};

onMounted(() => {
  fetchPopUp();
});

const handleCancel = () => {
  fetchPopUp();
};

// Add type for file upload
interface FileUpload {
  file: File;
  title?: string;
}

const handleFileUpload = async (files: FileUpload[]) => {
  const file = files[0]?.file;
  if (!file) {
    popUpFile.value = null;
    return;
  }

  try {
    status.value = 'pending';
    const form = new FormData();
    form.append('files', file);
    form.append('prefix', 'pop-up');
    form.append('title', 'Pop-Up Notification');

    const response = await $fetch<
      { pathname: string; title: string; type: string }[]
    >('/api/blob/upload', {
      method: 'POST',
      body: form,
    });

    const fileData = {
      pathname: response[0]?.pathname || '',
      title: response[0]?.title || '',
      type: response[0]?.type || '',
      prefix: 'pop-up',
    };
    popUpFile.value = fileData;
  } catch (err) {
    console.error('Failed to upload file:', err);
    toast.error('Failed to upload file');
  } finally {
    status.value = 'idle';
  }
};

const handleFileRemove = () => {
  popUpFile.value = null;
};

definePageMeta({
  middleware: ['admin'],
});

const breadcrumbs = ref([
  { label: 'Home', href: '/admin' },
  { label: 'Pop-Up' },
]);
</script>

<template>
  <div class="w-full flex flex-col gap-4">
    <!-- Header -->
    <AdminLayoutNavBreadCrumbs :breadcrumbs="breadcrumbs" />
    <main class="flex flex-1 flex-col gap-4 md:gap-8">
      <!-- File Upload Section -->
      <div class="bg-white p-6 rounded-lg shadow-sm border">
        <h2 class="text-xl font-semibold mb-4">Pop-Up Notification Image</h2>

        <!-- Status message -->
        <div v-if="!popUpFile" class="mb-4">
          <div
            class="bg-amber-50 border border-amber-200 text-amber-800 rounded-md p-4 mb-4"
          >
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <Icon
                  name="heroicons:exclamation-triangle"
                  class="h-5 w-5 text-amber-400"
                  aria-hidden="true"
                />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium">No pop-up image uploaded</h3>
                <div class="mt-2 text-sm">
                  <p>
                    Upload an image to display as a pop-up notification on the
                    website's landing page. Only one pop-up can be active at a
                    time.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="mb-4">
          <div
            class="bg-green-50 border border-green-200 text-green-800 rounded-md p-4"
          >
            <div class="flex">
              <div class="flex-shrink-0">
                <Icon
                  name="heroicons:check-circle"
                  class="h-5 w-5 text-green-400"
                  aria-hidden="true"
                />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium">Pop-up image ready</h3>
                <div class="mt-2 text-sm">
                  <p>
                    Your pop-up image is ready to display. You can replace it by
                    uploading a new image.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Dropzone -->
        <AdminBaseDropzone
          v-if="popUpFile"
          :is-multiple-allowed="false"
          :file-types="['image']"
          title="Pop-Up File"
          :existing-files="
            popUpFile
              ? [
                  {
                    pathname: popUpFile.pathname,
                    type: popUpFile.type,
                    title: popUpFile.title,
                    prefix: popUpFile.prefix,
                  },
                ]
              : []
          "
          description="Upload the pop-up file in image format (JPG, PNG, WebP)"
          :is-submitting="status === 'pending'"
          @files-selected="handleFileUpload"
          @file-removed="handleFileRemove"
        />
        <AdminBaseDropzone
          v-else
          :is-multiple-allowed="false"
          :file-types="['image']"
          title="Pop-Up File"
          :existing-files="[]"
          description="Upload the pop-up file in image format (JPG, PNG, WebP)"
          :is-submitting="status === 'pending'"
          @files-selected="handleFileUpload"
          @file-removed="handleFileRemove"
        />

        <div class="flex justify-end space-x-4 mt-6">
          <Button
            v-if="popUpFile"
            type="button"
            variant="destructive"
            :disabled="status === 'pending'"
            aria-label="Remove pop-up notification"
            @click="handleRemove"
          >
            <Icon name="heroicons:trash" class="h-4 w-4 mr-2" />
            Remove
          </Button>
          <Button
            type="button"
            variant="outline"
            :disabled="status === 'pending'"
            @click="handleCancel"
          >
            Cancel
          </Button>
          <Button
            :disabled="status === 'pending' || !popUpFile"
            @click="handleSubmit"
          >
            {{
              status === 'pending'
                ? 'Saving...'
                : popUpFile
                  ? 'Update'
                  : 'Create'
            }}
          </Button>
        </div>
      </div>
    </main>

    <!-- Confirmation Dialog -->
    <Teleport to="body">
      <Transition
        enter-active-class="ease-out duration-300"
        enter-from-class="opacity-0"
        enter-to-class="opacity-100"
        leave-active-class="ease-in duration-200"
        leave-from-class="opacity-100"
        leave-to-class="opacity-0"
      >
        <div
          v-if="showConfirmDialog"
          class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        />
      </Transition>

      <Transition
        enter-active-class="ease-out duration-300"
        enter-from-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        enter-to-class="opacity-100 translate-y-0 sm:scale-100"
        leave-active-class="ease-in duration-200"
        leave-from-class="opacity-100 translate-y-0 sm:scale-100"
        leave-to-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
      >
        <div
          v-if="showConfirmDialog"
          class="fixed inset-0 z-10 overflow-y-auto"
        >
          <div
            class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0"
          >
            <div
              class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6"
            >
              <div class="sm:flex sm:items-start">
                <div
                  class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10"
                >
                  <Icon
                    name="heroicons:exclamation-triangle"
                    class="h-6 w-6 text-red-600"
                    aria-hidden="true"
                  />
                </div>
                <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                  <h3 class="text-base font-semibold leading-6 text-gray-900">
                    Remove Pop-up Notification
                  </h3>
                  <div class="mt-2">
                    <p class="text-sm text-gray-500">
                      Are you sure you want to remove this pop-up notification?
                      This action cannot be undone.
                    </p>
                  </div>
                </div>
              </div>
              <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <Button
                  type="button"
                  variant="destructive"
                  class="w-full sm:ml-3 sm:w-auto"
                  :disabled="status === 'pending'"
                  @click="handleRemoveConfirm"
                >
                  {{ status === 'pending' ? 'Removing...' : 'Remove' }}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  class="mt-3 w-full sm:mt-0 sm:w-auto"
                  :disabled="status === 'pending'"
                  @click="showConfirmDialog = false"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>
