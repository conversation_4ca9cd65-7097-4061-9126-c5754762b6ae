<script setup lang="ts">
const { loggedIn, user, fetch } = useUserSession();
const { dashboardData } = useDashboard();

// Check authentication on component mount
onMounted(async () => {
  // Fetch the latest session data
  await fetch();

  // Redirect if not logged in
  if (!loggedIn.value) {
    navigateTo('/admin/login');
  }
});

// Page metadata for SEO
useHead({
  title: 'Admin Dashboard - College CMS',
  meta: [
    {
      name: 'description',
      content: 'Admin dashboard for college website content management system',
    },
  ],
});

definePageMeta({
  middleware: ['admin'],
});

// Define the color scheme type
interface ColorScheme {
  bg: string;
  text: string;
  icon: string;
  border: string;
}

// Define valid titles as a type
type ContentType =
  | 'Albums'
  | 'E-Services'
  | 'Quick Links'
  | 'Other Menus'
  | 'Notices'
  | 'Updates'
  | 'Events';

const getColorClass = (title: string) => {
  const colors: Record<ContentType, ColorScheme> = {
    Albums: {
      bg: 'bg-blue-50',
      text: 'text-blue-600',
      icon: 'i-lucide-image',
      border: 'border-blue-200',
    },
    'E-Services': {
      bg: 'bg-green-50',
      text: 'text-green-600',
      icon: 'i-heroicons-server-20-solid',
      border: 'border-green-200',
    },
    'Quick Links': {
      bg: 'bg-purple-50',
      text: 'text-purple-600',
      icon: 'i-heroicons-link-20-solid',
      border: 'border-purple-200',
    },
    'Other Menus': {
      bg: 'bg-yellow-50',
      text: 'text-yellow-600',
      icon: 'i-heroicons-bars-3-20-solid',
      border: 'border-yellow-200',
    },
    Notices: {
      bg: 'bg-red-50',
      text: 'text-red-600',
      icon: 'i-heroicons-megaphone-20-solid',
      border: 'border-red-200',
    },
    Updates: {
      bg: 'bg-indigo-50',
      text: 'text-indigo-600',
      icon: 'i-heroicons-bell-20-solid',
      border: 'border-indigo-200',
    },
    Events: {
      bg: 'bg-pink-50',
      text: 'text-pink-600',
      icon: 'i-heroicons-calendar-20-solid',
      border: 'border-pink-200',
    },
  };

  const defaultColor: ColorScheme = {
    bg: 'bg-gray-50',
    text: 'text-gray-600',
    icon: 'i-heroicons-document-20-solid',
    border: 'border-gray-200',
  };

  return (colors as Record<string, ColorScheme>)[title] || defaultColor;
};
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <div class="p-6 max-w-7xl mx-auto">
      <!-- Welcome Banner -->
      <div
        class="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl shadow-xl p-6 mb-8"
      >
        <div class="flex items-center justify-between">
          <div class="text-white">
            <h1 class="text-3xl font-bold mb-2">
              Welcome back, {{ user?.fullName || 'Admin' }}!
            </h1>
            <p class="text-blue-100">
              {{
                new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })
              }}
            </p>
          </div>
          <div class="hidden md:block">
            <div class="bg-white/20 backdrop-blur-sm rounded-lg p-3 text-white">
              <div class="flex space-x-2 items-center">
                <div
                  class="w-3 h-3 rounded-full bg-green-400 animate-pulse"
                ></div>
                <span>System Status: Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Dashboard Content -->
        <div class="lg:col-span-2 space-y-8">
          <!-- Stats Card Grid -->
          <div>
            <h2
              class="text-xl font-semibold text-gray-800 mb-4 flex items-center"
            >
              <div class="mr-2 w-5 h-5 i-heroicons-chart-bar-20-solid"></div>
              Content Statistics
            </h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
              <!-- Loop through dashboardData and render cards -->
              <template v-for="item in dashboardData?.data" :key="item.title">
                <div
                  class="rounded-xl shadow-sm p-5 border transition-all duration-200 hover:shadow-md"
                  :class="[
                    getColorClass(item.title).bg,
                    getColorClass(item.title).border,
                  ]"
                >
                  <div class="flex justify-between items-start mb-4">
                    <h3
                      class="text-lg font-semibold"
                      :class="getColorClass(item.title).text"
                    >
                      {{ item.title }}
                    </h3>
                    <div
                      class="w-8 h-8 flex items-center justify-center rounded-lg bg-white shadow-sm"
                      :class="getColorClass(item.title).border"
                    >
                      <!-- <div
                        class="w-5 h-5"
                        :class="getColorClass(item.title).icon"
                      ></div> -->
                      <Icon :name="getColorClass(item.title).icon" />
                    </div>
                  </div>

                  <div class="flex justify-between items-end">
                    <!-- Total Count -->
                    <div>
                      <p
                        class="text-3xl font-bold"
                        :class="getColorClass(item.title).text"
                      >
                        {{ item.count }}
                      </p>
                      <p class="text-sm text-gray-500">Total</p>
                    </div>
                    <!-- Active Count -->
                    <div v-if="item.showCount" class="text-right">
                      <p
                        class="text-2xl font-bold"
                        :class="getColorClass(item.title).text"
                      >
                        {{ item.activeCount }}
                      </p>
                      <p class="text-sm text-gray-500">Active</p>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>

        <!-- Side Panel -->
        <div class="space-y-8">
          <!-- User Profile Card -->
          <div
            v-if="loggedIn"
            class="bg-white rounded-xl shadow-sm border border-gray-100 p-6"
          >
            <div class="flex items-center space-x-4 mb-6">
              <div
                class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 font-bold text-xl"
              >
                {{ user?.fullName?.charAt(0) || 'U' }}
              </div>
              <div>
                <h2 class="text-xl font-semibold">{{ user?.fullName }}</h2>
                <div
                  class="px-2 py-1 text-xs font-medium rounded-full inline-flex items-center mt-1"
                  :class="{
                    'bg-blue-100 text-blue-800': user?.role === 'admin',
                    'bg-gray-100 text-gray-800': user?.role === 'viewer',
                  }"
                >
                  <div
                    class="w-3 h-3 mr-1"
                    :class="
                      user?.role === 'admin'
                        ? 'i-heroicons-shield-check-20-solid'
                        : 'i-heroicons-eye-20-solid'
                    "
                  ></div>
                  {{ user?.role }}
                </div>
              </div>
            </div>

            <div class="space-y-3 text-sm">
              <div class="flex items-center border-b border-gray-100 pb-3">
                <div
                  class="w-4 h-4 i-heroicons-user-20-solid text-gray-400 mr-2"
                ></div>
                <span class="text-gray-500 w-24">Username:</span>
                <span class="font-medium text-gray-900">{{
                  user?.username
                }}</span>
              </div>
              <div class="flex items-center border-b border-gray-100 pb-3">
                <div
                  class="w-4 h-4 i-heroicons-envelope-20-solid text-gray-400 mr-2"
                ></div>
                <span class="text-gray-500 w-24">Email:</span>
                <span class="font-medium text-gray-900">{{ user?.email }}</span>
              </div>
            </div>

            <div class="mt-6">
              <button
                class="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
                @click="navigateTo('/admin/settings')"
              >
                <div
                  class="w-4 h-4 i-heroicons-pencil-square-20-solid mr-2"
                ></div>
                Edit Profile
              </button>
            </div>
          </div>

          <!-- Content Management Actions Card -->
          <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h2
              class="text-lg font-semibold text-gray-800 mb-4 flex items-center"
            >
              <div class="mr-2 w-5 h-5 i-heroicons-bolt-20-solid"></div>
              Quick Actions
            </h2>

            <div class="grid grid-cols-2 gap-3">
              <NuxtLink
                to="/admin/notices-updates"
                class="bg-blue-50 hover:bg-blue-100 text-blue-700 p-4 rounded-lg transition-colors flex flex-col items-center"
              >
                <!-- <div
                  class="w-6 h-6 i-heroicons-document-plus-20-solid mb-1"
                ></div> -->
                <Icon
                  class="w-6 h-6 mb-1"
                  name="i-heroicons-document-plus-20-solid"
                />
                <span class="text-sm font-medium">New Notice</span>
              </NuxtLink>

              <NuxtLink
                to="/admin/gallery"
                class="bg-green-50 hover:bg-green-100 text-green-700 p-4 rounded-lg transition-colors flex flex-col items-center"
              >
                <!-- <div class="w-6 h-6 i-heroicons-photo-20-solid mb-1"></div> -->
                <Icon class="w-6 h-6 mb-1" name="i-heroicons-photo-20-solid" />
                <span class="text-sm font-medium">Add Album</span>
              </NuxtLink>

              <NuxtLink
                to="/admin/events"
                class="bg-purple-50 hover:bg-purple-100 text-purple-700 p-4 rounded-lg transition-colors flex flex-col items-center"
              >
                <!-- <div
                  class="w-6 h-6 i-heroicons-calendar-days-20-solid mb-1"
                ></div> -->
                <Icon
                  class="w-6 h-6 mb-1"
                  name="i-heroicons-calendar-days-20-solid"
                />
                <span class="text-sm font-medium">New Event</span>
              </NuxtLink>

              <NuxtLink
                to="/admin/quick-links"
                class="bg-yellow-50 hover:bg-yellow-100 text-yellow-700 p-4 rounded-lg transition-colors flex flex-col items-center"
              >
                <!-- <div class="w-6 h-6 i-heroicons-link-20-solid mb-1"></div> -->
                <Icon class="w-6 h-6 mb-1" name="i-heroicons-link-20-solid" />
                <span class="text-sm font-medium">Quick Link</span>
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
