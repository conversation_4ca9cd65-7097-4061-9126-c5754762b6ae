<script setup lang="ts">
import type { LandingPage } from '~/types/home';
import { usePageSeo } from '../composables/usePageSeo';

definePageMeta({
  layout: 'landing',
});

const { data: homeData } = await useFetch<LandingPage>('/api/home');

// Add SEO for homepage
usePageSeo({
  title: 'Don Bosco College Mannuthy, Thrissur - Home',
  description:
    'Welcome to Don Bosco College Mannuthy - A leading arts and science college in Thrissur, Kerala, offering undergraduate and postgraduate programs. Known for its quality education, vibrant campus life, and strong focus on student development.',
  keywords: [
    'College Home',
    'Higher Education',
    'Undergraduate Programs',
    'Admission',
    'Campus Life',
  ],
  structuredData: {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: 'Don Bosco College Mannuthy - Home',
    description:
      'Welcome to Don Bosco College Mannuthy - A leading arts and science college in Thrissur, Kerala, offering undergraduate and postgraduate programs. Known for its quality education, vibrant campus life, and strong focus on student development.',
    publisher: {
      '@type': 'Organization',
      name: 'Don Bosco College Mannuthy',
      logo: {
        '@type': 'ImageObject',
        url: 'https://dbcollegemannuthy.edu.in/logo.png',
      },
    },
  },
});
</script>

<template>
  <HomeHeroVideoHero
    v-if="homeData"
    :quick-links="homeData?.quickLinks ?? []"
  />

  <HomeNoticeBoard
    v-if="homeData"
    :notifications="homeData?.notifications ?? []"
    :updates="homeData?.updates ?? []"
    :principal-message="homeData?.principalMessage"
  />
  <HomeOurSection
    v-if="homeData"
    :stats="homeData?.stats ?? []"
    :campus-details="homeData?.campusDetails"
  />
  <HomeProgramsOffer v-if="homeData" :programs="homeData?.programs" />
  <HomeEventsHome v-if="homeData" :events="homeData?.events ?? []" />
  <HomeGallery v-if="homeData" :gallery="homeData?.gallery ?? []" />
  <HomeYoutube v-if="homeData" />
  <HomeResearch v-if="homeData" :research="homeData?.research" />
</template>
