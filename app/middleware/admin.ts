export default defineNuxtRouteMiddleware(async () => {
  const { loggedIn, user, fetch: fetchSession } = useUserSession();

  // Fetch the user session if it hasn't been fetched yet
  if (!loggedIn.value) {
    await fetchSession();
  }

  // If still not logged in after fetching, redirect to login
  if (!loggedIn.value) {
    return navigateTo('/admin/login');
  }

  // Optional: Check admin role if you have multiple roles
  if (user.value?.role !== 'admin') {
    // Create access denied page if needed
    return navigateTo('/');
  }
});
