<script setup lang="ts">
import type { FileData } from '~/types/home';

// Ensure admin-body class is removed when using landing layout
onMounted(() => {
  document.body.classList.remove('admin-body');
});

// Add loading state for page transitions
const isLoading = ref(true);

// Fetch popup data
const { data: popUpData, status: popUpStatus } =
  await useFetch<FileData>('/api/home/<USER>');

const route = useRoute();
const isLandingPage = computed(
  () => route.path === '/' || route.path === '/home'
);
const showPopup = ref(!!popUpData.value && isLandingPage.value);

// Watch for route changes to hide popup when navigating away from landing page
watch(
  () => route.path,
  (_newPath) => {
    if (!isLandingPage.value) {
      showPopup.value = false;
    }
  }
);

// Advanced loading state management
watch(
  () => popUpStatus.value,
  () => {
    if (popUpStatus.value !== 'pending') {
      // Remove loader after all data is fetched with a slight delay
      setTimeout(() => {
        isLoading.value = false;
      }, 500);
    }
  }
);

// Reset loading state on route change
const nuxtApp = useNuxtApp();
nuxtApp.hook('page:start', () => {
  isLoading.value = true;
});
nuxtApp.hook('page:finish', () => {
  if (popUpStatus.value !== 'pending') {
    setTimeout(() => {
      isLoading.value = false;
    }, 300);
  }
});

// Handle initial load
onMounted(() => {
  // If first load is complete, remove loader
  if (popUpStatus.value !== 'pending') {
    setTimeout(() => {
      isLoading.value = false;
    }, 800);
  }
});
</script>

<template>
  <div class="min-h-[100vh]">
    <!-- Full page loader overlay -->
    <Transition name="fade" mode="out-in">
      <div
        v-if="isLoading"
        class="fixed inset-0 z-[9999] flex items-center justify-center bg-white/95 backdrop-blur-sm"
        aria-live="polite"
        role="status"
      >
        <HomeLoader />
      </div>
    </Transition>

    <HomeNavigationHeader />
    <main
      class="bg-[#F4F4F4] min-h-[calc(100vh-595px)] lg:min-h-[calc(100vh-470px)]"
    >
      <slot />
    </main>
    <HomeNavigationFooterMain />

    <!-- Popup Dialog - Only shown on landing page -->
    <Dialog
      v-if="popUpData && isLandingPage && popUpData.pathname"
      :open="showPopup"
      @update:open="showPopup = $event"
    >
      <DialogContent
        class="p-0 max-w-none w-auto border-none bg-transparent shadow-none overflow-hidden flex items-center justify-center h-max max-h-[90vh] w-max max-w-[90vw] object-scale-down"
      >
        <NuxtImg
          loading="lazy"
          format="webp"
          :src="getPreviewUrl(popUpData?.pathname)"
          :alt="popUpData?.title"
          class="h-auto w-auto object-scale-down"
        />
      </DialogContent>
    </Dialog>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
