<script setup lang="ts">
const store = useNavbar();
const { isOpen } = storeToRefs(store);
const router = useRouter();

defineShortcuts({
  Meta_B: () => store.toggle(),
  'G-H': () => router.push('/'),
});

// Add admin-body class to body when using default layout
onMounted(() => {
  document.body.classList.add('admin-body');
});

// Remove admin-body class when component is unmounted
onBeforeUnmount(() => {
  document.body.classList.remove('admin-body');
});
</script>

<template>
  <div
    class="grid w-full transition-width duration-300 min-h-dvh"
    :class="cn('pl-0', isOpen ? 'lg:pl-56 xl:pl-64 md:pl-20' : 'lg:pl-20')"
  >
    <AdminLayoutSidebar />
    <div flex="~ col" of-hidden>
      <AdminLayoutHeader />
      <main
        class="flex-1 bg-background h-[92vh] dark:bg-muted/20 pr-3 overflow-hidden"
      >
        <div
          class="p-4 xl:p-6 !pt-0 bg-muted h-full dark:bg-muted/20 rounded-xl overflow-y-auto"
        >
          <slot />
        </div>
      </main>
    </div>
    <!-- Toast Container -->
    <Toaster position="bottom-right" />
  </div>
</template>

<style scoped></style>
