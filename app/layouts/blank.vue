<script setup lang="ts">
import { cn } from '~/lib/utils';

import { onMounted } from 'vue';

// Ensure admin-body class is removed when using blank layout
onMounted(() => {
  document.body.classList.remove('admin-body');
});
</script>

<template>
  <div
    class="grid w-full transition-width duration-300 min-h-dvh bg-primary"
    :class="cn('pl-0 lg:pl-56 xl:pl-64 md:pl-20')"
  >
    <div class="flex flex-col overflow-hidden">
      <main class="flex-1 bg-muted dark:bg-muted/20">
        <slot />
      </main>
    </div>
  </div>
</template>

<style scoped></style>
