{"name": "clg-app-builder", "type": "module", "private": true, "packageManager": "bun@1.2.8", "scripts": {"dev": "nuxt dev", "dev:remote": "nuxt dev --remote", "reset": "rm -rf .data && rm -rf server/database/migrations && bun run db:generate", "build": "nuxt build", "preview": "npx nuxthub preview", "deploy": "npx nuxthub deploy", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix", "format:check": "prettier . --ignore-path .gitignore --check", "format:fix": "prettier . --ignore-path .gitignore --write", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "check-branch": "bun run scripts/check-branch.ts", "prepare": "lefthook install", "typecheck": "nuxt typecheck"}, "dependencies": {"@iconify-json/material-symbols": "1.2.19", "@nuxt/eslint": "1.3.0", "@nuxt/image": "1.10.0", "@nuxt/scripts": "0.11.5", "@nuxthub/core": "0.8.24", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/robots": "5.2.9", "@nuxtjs/tailwindcss": "6.13.2", "@pinia/nuxt": "0.11.0", "@radix-icons/vue": "^1.0.0", "@unhead/vue": "2.0.5", "@vee-validate/zod": "^4.15.0", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "13.1.0", "@vueuse/math": "13.1.0", "@vueuse/nuxt": "13.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "0.41.0", "drizzle-seed": "0.3.1", "eslint": "9.24.0", "isomorphic-dompurify": "2.23.0", "nuxt": "3.16.2", "nuxt-auth-utils": "0.5.19", "nuxt-mcp": "0.1.2", "nuxt-simple-sitemap": "^4.4.1", "radix-vue": "1.9.17", "shadcn-nuxt": "2.0.1", "tailwind-merge": "3.2.0", "tailwindcss-animate": "^1.0.7", "vaul-vue": "0.4.1", "vee-validate": "^4.15.0", "vite-plugin-eslint2": "^5.0.3", "vue-sonner": "^1.3.0", "zod": "3.24.2"}, "devDependencies": {"@iconify-json/lucide": "1.2.36", "@iconify-json/radix-icons": "^1.2.2", "@iconify/vue": "4.3.0", "@nuxt/icon": "1.12.0", "@nuxt/kit": "3.16.2", "@nuxtjs/color-mode": "^3.5.2", "@types/bun": "1.2.9", "@types/youtube": "^0.1.0", "consola": "3.4.2", "drizzle-kit": "0.30.6", "lefthook": "1.11.10", "lucide-vue-next": "0.488.0", "nuxt-aos": "^1.2.5", "nuxt-svgo": "4.0.17", "nuxt-swiper": "^2.0.0", "prettier": "3.5.3", "sass-embedded": "1.86.3", "typescript": "5.8.3", "unenv": "^1.10.0", "vue-tsc": "2.2.8", "wrangler": "4.10.0"}}