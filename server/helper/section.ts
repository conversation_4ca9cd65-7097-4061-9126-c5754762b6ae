import type { Configuration } from '~/types/home/<USER>';
import type { CardLayout } from '../database/tables/enums';
export type { CardLayout };

// File Configuration Types
export interface FileConfig {
  pathname: string;
  title: string;
  type: string;
}

export interface FileInsert {
  pathname: string;
  title: string;
  type: string;
}

// Button Configuration Types
interface BaseButtonFields {
  title: string;
  style: string;
  icon?: string | null;
  newTab: boolean;
}

export interface LinkButtonConfig extends BaseButtonFields {
  type: 'link';
  internalLink: string | null;
  externalLink: string | null;
}

export interface DownloadButtonConfig extends BaseButtonFields {
  type: 'download';
  file: FileConfig;
}

export type ButtonConfig = LinkButtonConfig | DownloadButtonConfig;

export interface LinkButtonInsert extends BaseButtonFields {
  cardTemplateId: number;
  internalLink: string | null;
  externalLink: string | null;
}

export interface DownloadButtonInsert extends BaseButtonFields {
  cardTemplateId: number;
}

// Content Configuration Types
export interface ContentConfig {
  title: string;
  content: string;
}

export interface ContentEntry {
  type: 'content1' | 'content2' | 'content3';
  title: string;
  content: string;
}

export interface CardContent {
  cardId: number;
  priority: number;
  title: string;
  content: string;
  type?: string;
}

// Card Configuration Types
export interface BaseCard {
  layout: CardLayout;
  title?: string | null;
  key: string;
  image?: FileConfig;
}

export interface ProfileCard extends BaseCard {
  layout: 'profile';
  content1?: ContentConfig | null;
  content2?: ContentConfig | null;
  button?: ButtonConfig;
}

export interface StandardCard extends BaseCard {
  layout: 'standard';
  content1?: ContentConfig | null;
  linkButton?: ButtonConfig;
  downloadButton?: ButtonConfig;
  files?: FileConfig[];
}

export interface VisionCard extends BaseCard {
  layout: 'vision';
  content1?: ContentConfig | null;
}

export type CardConfig = ProfileCard | StandardCard | VisionCard;
// Section Configuration Types
export interface StatsConfig {
  title: string;
  value: number;
  icon: string;
}

export interface ToggleConfig {
  title: string;
  isActive: boolean;
  type: 'menu' | 'event' | 'gallery';
  menuId?: number | null;
}

export interface CardListConfig {
  title: string;
  description: string | null;
  maxCards: number;
  minCards: number;
  layout: CardLayout;
  sectionId: number | null;
}

export interface SectionConfig {
  title?: string;
  description?: string | null;
  content?: string | null;
  maxCards?: number;
  minCards?: number;
  cards?: CardConfig[];
}

export interface CardFiles {
  cardId: number;
  fileId: number;
}

// Type Guard Functions
export function isButtonIncludedCard(layout: CardLayout): boolean {
  return layout === 'standard' || layout === 'profile';
}

export function isStandardCard(config: any): config is StandardCard {
  return config.layout === 'standard';
}

export function isProfileCard(config: any): config is ProfileCard {
  return config.layout === 'profile';
}

export function isVisionCard(config: any): config is VisionCard {
  return config.layout === 'vision' || config.layout === 'profile';
}

// Helper Functions
export function createContentEntry(
  type: 'content1' | 'content2' | 'content3',
  title: string,
  content: string
): ContentEntry {
  return {
    type,
    title,
    content,
  };
}

/**
 * Process card config to match the expected response format
 * This function transforms raw database records into the expected frontend format
 */
export function processCardConfig(cardConfig: any): CardConfig {
  // If the card is already processed, return it as is
  if (cardConfig.layout && (cardConfig.content1 || cardConfig.content)) {
    return cardConfig as CardConfig;
  }

  // Create the response object
  const contentMap: Record<string, ContentConfig> = {};

  // Process card contents if available
  if (cardConfig.contents) {
    for (const content of cardConfig.contents) {
      contentMap[`content${content.priority}`] = {
        title: content.title,
        content: content.content || '',
      };
    }
  }

  // Get the main image if it exists
  // Get the main image as the first file, and expose all files as an array
  const mainImage = cardConfig.cardFiles?.[0]?.file || null;

  // Ensure cardFiles is an array before mapping
  let files: any[] = [];
  if (cardConfig?.cardFiles && Array.isArray(cardConfig.cardFiles)) {
    // For standard cards, we need to separate the primary image from additional files
    if (cardConfig.layout === 'standard') {
      // Skip the first file (which is the main image) for standard cards
      if (cardConfig.cardFiles.length > 1) {
        files = cardConfig.cardFiles.slice(1).map((cf: any) => cf.file);
      }
    } else {
      // For other card types, include all files
      files = cardConfig.cardFiles.map((cf: any) => cf.file);
    }
  }

  // Process link button
  const linkButtonConfig = cardConfig.linkButton || null;

  // Determine layout type based on available content
  // let layout = cardConfig.layout || 'standard';
  // if (Object.keys(contentMap).includes('content2')) {
  //   layout = 'profile';
  // } else if (Object.keys(contentMap).length === 1 && !cardConfig.title) {
  //   layout = 'vision';
  // }

  // Create the card configuration with types that match CardConfig
  const cardResponse: any = {
    layout: cardConfig?.layout ?? '',
    ...contentMap,
    title: cardConfig.title || undefined,
  };

  // Add image if it exists
  if (mainImage) {
    cardResponse.image = mainImage;
  }

  // Add link button configuration if it exists
  if (linkButtonConfig) {
    cardResponse.linkButton = {
      title: linkButtonConfig.title,
      style: linkButtonConfig.style,
      icon: linkButtonConfig.icon || undefined,
      newTab: !!linkButtonConfig.newTab,
      type: 'link',
      internalLink: linkButtonConfig.internalLink || null,
      externalLink: linkButtonConfig.externalLink || null,
    };
  }
  // Add all files array if present
  if (files.length > 0) {
    cardResponse.files = files;
  } else {
    // Always initialize files array for standard cards
    if (cardConfig.layout === 'standard') {
      cardResponse.files = [];
    }
  }
  return cardResponse as CardConfig;
}

export function processCardConfigHome(cardConfig: any): Configuration {
  const cardResponse = processCardConfig(cardConfig);
  let formattedCardResponse: Configuration;

  // Check if it's a CardsList by checking for cards array
  if (cardConfig.cards && Array.isArray(cardConfig.cards)) {
    formattedCardResponse = {
      title: cardConfig.title as string,
      description: (cardConfig.description as string) || '',
      layout: cardConfig.layout as 'standard' | 'profile' | 'vision',
      cards: cardConfig.cards.map((card: any) => processCardConfigHome(card)),
    };
  } else if (cardResponse.layout === 'profile') {
    formattedCardResponse = {
      title: cardResponse.title as string,
      subtitle1: cardResponse.content1?.title ?? '',
      content1: cardResponse.content1?.content ?? '',
      subtitle2: cardResponse.content2?.title ?? '',
      content2: cardResponse.content2?.content ?? '',
      image: cardResponse.image || { pathname: '', title: '', type: '' },
      layout: 'profile',
    };
  } else if (cardResponse.layout === 'standard') {
    // For standard cards, separate the main image from additional files
    // The main image is the first file, and the rest are additional files
    const mainImage = cardResponse.image || {
      pathname: '',
      title: '',
      type: '',
    };
    const additionalFiles = cardResponse.files || [];

    // If we have files, ensure they're properly separated
    if (additionalFiles.length > 0) {
      console.log('Standard card has files:', additionalFiles);
    }

    formattedCardResponse = {
      title: cardResponse.title as string,
      subtitle: cardResponse.content1?.title ?? '',
      content: cardResponse.content1?.content ?? '',
      image: mainImage,
      files: additionalFiles,
      layout: 'standard',
      linkButton: cardResponse.linkButton as LinkButtonConfig,
      downloadButton: cardResponse.downloadButton as DownloadButtonConfig,
    };
  } else if (cardResponse.layout === 'vision') {
    formattedCardResponse = {
      title: cardResponse.content1?.title ?? '',
      description: cardResponse.content1?.content ?? '',
      image: cardResponse.image || { pathname: '', title: '', type: '' },
      layout: 'vision',
    };
  } else {
    // Default to standard with empty values
    formattedCardResponse = {
      title: '',
      subtitle: '',
      content: '',
      image: {
        pathname: '',
        title: '',
        type: '',
      },
      files: [],
      layout: 'standard',
    };
  }

  return formattedCardResponse;
}

/**
 * Process section configuration based on its type
 * This function handles different section types (card, cardList, accordion)
 */
export function processSectionConfig(
  section: any,
  config: any,
  cards?: any[]
): any {
  if (!config) return null;

  if (section.type === 'card') {
    return processCardConfig(config);
  } else if (section.type === 'cardList') {
    return {
      title: config.title,
      description: (config as any).description as string | null,
      maxCards: (config as any).maxCards || 1,
      minCards: (config as any).minCards || 0,
      layout: (config as any).layout || 'standard',
      cards: cards ? cards.map((card) => processCardConfig(card)) : [],
    };
  } else if (section.type === 'accordion') {
    return config;
  } else if (section.type === 'stats') {
    return config;
  } else if (section.type === 'toggle') {
    return config;
  }

  return config;
}
