import consola from 'consola';
import {
  department,
  departmentFiles,
  departmentGallery,
  departmentEvent,
  course,
  faculty,
  student,
  files,
  linkButton,
} from '~~/server/database/tables';

// Sample departments data
const DEPARTMENTS = [
  {
    name: 'Computer Science',
    slug: 'computer-science',
    overview:
      'Department of Computer Science focuses on cutting-edge technology and innovation.',
  },
  {
    name: 'Commerce',
    slug: 'commerce',
    overview:
      'Department of Commerce provides comprehensive business education.',
  },
  {
    name: 'Physics',
    slug: 'physics',
    overview: 'Department of Physics explores fundamental laws of nature.',
  },
];

// Create departments
export const createDepartments = async (db: any) => {
  try {
    const departments = await db
      .insert(department)
      .values(DEPARTMENTS)
      .returning();
    return departments;
  } catch (error) {
    consola.error('Failed to create departments:', error);
    throw error;
  }
};

// Create course files (images, syllabus, POS)
const createCourseFiles = async (db: any) => {
  try {
    const COURSE_FILES = [
      {
        pathname: '',
        title: 'Bachelor of Computer Science',
        type: 'image/jpeg',
      },
      {
        pathname: '',
        title: 'Master of Computer Science',
        type: 'image/jpeg',
      },
      {
        pathname: '',
        title: 'BCS Syllabus',
        type: 'application/pdf',
      },
      {
        pathname: '',
        title: 'MCS Syllabus',
        type: 'application/pdf',
      },
      {
        pathname: '',
        title: 'BCS Program Outcomes',
        type: 'application/pdf',
      },
      {
        pathname: '',
        title: 'MCS Program Outcomes',
        type: 'application/pdf',
      },
    ];
    return await db.insert(files).values(COURSE_FILES).returning();
  } catch (error) {
    consola.error('Failed to create course files:', error);
    throw error;
  }
};

// Create course buttons
const createCourseButtons = async (db: any) => {
  try {
    const COURSE_BUTTONS = [
      {
        title: 'Apply Now',
        style: 'primary',
        icon: 'i-heroicons-arrow-right-20-solid',
        external_link: 'https://admissions.example.com/apply',
        new_tab: true,
      },
      {
        title: 'Download Brochure',
        style: 'secondary',
        icon: 'i-heroicons-document-arrow-down-20-solid',
        external_link: 'https://example.com/brochures/bcs.pdf',
        new_tab: true,
      },
      {
        title: 'Apply Now',
        style: 'primary',
        icon: 'i-heroicons-arrow-right-20-solid',
        external_link: 'https://admissions.example.com/apply-masters',
        new_tab: true,
      },
      {
        title: 'Download Brochure',
        style: 'secondary',
        icon: 'i-heroicons-document-arrow-down-20-solid',
        external_link: 'https://example.com/brochures/mcs.pdf',
        new_tab: true,
      },
    ];
    return await db.insert(linkButton).values(COURSE_BUTTONS).returning();
  } catch (error) {
    consola.error('Failed to create course buttons:', error);
    throw error;
  }
};

// Create courses for departments
export const createDepartmentCourses = async (
  db: any,
  departmentId: number
) => {
  try {
    // Create course files first
    const courseFiles = await createCourseFiles(db);
    // Create course buttons
    const courseButtons = await createCourseButtons(db);

    const COURSES = [
      {
        name: 'Bachelor of Computer Science',
        specialization: 'Software Development',
        durationInMonths: 36,
        semesterCount: 6,
        departmentId,
        title: 'BCS',
        type: 'ug',
        seatsCount: 60,
        content:
          'Comprehensive computer science program covering programming, algorithms, and software development.',
        imageId: courseFiles[0].id,
        syllabusId: courseFiles[2].id,
        posId: courseFiles[4].id,
        linkButtonId: courseButtons[0].id, // Apply Now button
      },
      {
        name: 'Master of Computer Science',
        specialization: 'Artificial Intelligence',
        durationInMonths: 24,
        semesterCount: 4,
        departmentId,
        title: 'MCS',
        type: 'pg',
        seatsCount: 30,
        content:
          'Advanced program focusing on AI, machine learning, and data science.',
        imageId: courseFiles[1].id,
        syllabusId: courseFiles[3].id,
        posId: courseFiles[5].id,
        linkButtonId: courseButtons[2].id, // Apply Now button
      },
      {
        name: 'Bachelor of Commerce',
        specialization: 'Artificial Intelligence',
        durationInMonths: 24,
        semesterCount: 4,
        departmentId,
        title: 'MCS',
        type: 'add_on',
        seatsCount: 30,
        content:
          'Advanced program focusing on AI, machine learning, and data science.',
        imageId: courseFiles[1].id,
        syllabusId: courseFiles[3].id,
        posId: courseFiles[5].id,
        linkButtonId: courseButtons[2].id,
      },
    ];

    return await db.insert(course).values(COURSES).returning();
  } catch (error) {
    consola.error('Failed to create courses:', error);
    throw error;
  }
};

// Create faculty images
const createFacultyImages = async (db: any) => {
  try {
    const FACULTY_IMAGES = [
      {
        pathname: '',
        title: 'Dr. John Smith',
        type: 'image/jpeg',
      },
      {
        pathname: '',
        title: 'Dr. Sarah Johnson',
        type: 'image/jpeg',
      },
    ];
    return await db.insert(files).values(FACULTY_IMAGES).returning();
  } catch (error) {
    consola.error('Failed to create faculty images:', error);
    throw error;
  }
};

// Create student images
const createStudentImages = async (db: any) => {
  try {
    const STUDENT_IMAGES = [
      {
        pathname: '',
        title: 'Alice Johnson',
        type: 'image/jpeg',
      },
      {
        pathname: '',
        title: 'Bob Wilson',
        type: 'image/jpeg',
      },
    ];
    return await db.insert(files).values(STUDENT_IMAGES).returning();
  } catch (error) {
    consola.error('Failed to create student images:', error);
    throw error;
  }
};

// Create faculty for departments
export const createDepartmentFaculty = async (
  db: any,
  departmentId: number
) => {
  try {
    // Create faculty images first
    const facultyImages = await createFacultyImages(db);

    const FACULTY = [
      {
        name: 'Dr. John Smith',
        departmentId,
        designation: 'Professor',
        startDate: '2020-01-01',
        imageId: facultyImages[0].id,
        education: [
          {
            degree: 'Ph.D',
            university: 'MIT',
            passOutYear: 2015,
          },
          {
            degree: 'M.Tech',
            university: 'IIT',
            passOutYear: 2013,
          },
          {
            degree: 'B.Tech',
            university: 'IIT',
            passOutYear: 2011,
          },
        ],
        experience: [
          {
            startYear: 2015,
            endYear: 2020,
            organization: 'Stanford',
            designation: 'Associate Professor',
          },
          {
            startYear: 2013,
            endYear: 2015,
            organization: 'IIT',
            designation: 'Assistant Professor',
          },
          {
            startYear: 2011,
            endYear: 2013,
            organization: 'IIT',
            designation: 'Assistant Professor',
          },
        ],
        areaOfInterest: ['Artificial Intelligence', 'Machine Learning'],
      },
      {
        name: 'Dr. Sarah Johnson',
        departmentId,
        designation: 'Associate Professor',
        startDate: '2019-01-01',
        imageId: facultyImages[1].id,
        education: [
          {
            degree: 'Ph.D',
            university: 'Stanford',
            passOutYear: 2018,
          },
          {
            degree: 'M.Tech',
            university: 'IIT',
            passOutYear: 2016,
          },
          {
            degree: 'B.Tech',
            university: 'IIT',
            passOutYear: 2014,
          },
        ],
        experience: [
          {
            startYear: 2018,
            endYear: 2020,
            organization: 'Berkeley',
            designation: 'Assistant Professor',
          },
          {
            startYear: 2016,
            endYear: 2018,
            organization: 'Stanford',
            designation: 'Associate Professor',
          },
          {
            startYear: 2014,
            endYear: 2016,
            organization: 'IIT',
            designation: 'Assistant Professor',
          },
        ],
        areaOfInterest: ['Data Mining', 'Big Data Analytics'],
      },
    ];

    return await db.insert(faculty).values(FACULTY).returning();
  } catch (error) {
    consola.error('Failed to create faculty:', error);
    throw error;
  }
};

// Create placements/toppers for departments
export const createDepartmentStudents = async (
  db: any,
  departmentId: number,
  courseId: number
) => {
  try {
    // Create student images first
    const studentImages = await createStudentImages(db);

    const STUDENTS = [
      {
        name: 'Alice Johnson',
        startDate: '2020-01-01',
        departmentId,
        courseId,
        startYear: 2020,
        passOutYear: 2024,
        mark: '9.8',
        placedAt: 'Google',
        type: 'placement',
        imageId: studentImages[0].id,
      },
      {
        name: 'Bob Wilson',
        startDate: '2020-01-01',
        departmentId,
        courseId,
        startYear: 2020,
        passOutYear: 2024,
        mark: '9.5',
        placedAt: 'Microsoft',
        type: 'placement',
        imageId: studentImages[1].id,
      },
    ];

    return await db.insert(student).values(STUDENTS).returning();
  } catch (error) {
    consola.error('Failed to create students:', error);
    throw error;
  }
};

// Create department files (downloads, question banks, projects)
export const createDepartmentFiles = async (
  db: any,
  departmentId: number,
  courseId: number
) => {
  // First create files
  const FILES = [
    {
      pathname: '',
      title: 'Sample Question Paper 2023',
      type: 'application/pdf',
    },
    {
      pathname: '',
      title: 'Project Guidelines',
      type: 'application/pdf',
    },
    {
      pathname: '',
      title: 'Course Material',
      type: 'application/pdf',
    },
  ];

  try {
    const createdFiles = await db.insert(files).values(FILES).returning();

    // Create department files relations
    const DEPARTMENT_FILES = [
      {
        title: 'Question Paper 2023',
        year: 2023,
        courseId,
        type: 'question_paper',
        semester: 1,
        departmentId,
        fileId: createdFiles[0].id,
      },
      {
        title: 'Final Year Project Guidelines',
        year: 2023,
        courseId,
        type: 'project',
        semester: 6,
        departmentId,
        fileId: createdFiles[1].id,
      },
      {
        title: 'Course Materials',
        year: 2023,
        courseId,
        type: 'download',
        semester: 1,
        departmentId,
        fileId: createdFiles[2].id,
      },
    ];

    return await db
      .insert(departmentFiles)
      .values(DEPARTMENT_FILES)
      .returning();
  } catch (error) {
    consola.error('Failed to create department files:', error);
    throw error;
  }
};

// Create department gallery
export const createDepartmentGallery = async (
  db: any,
  departmentId: number
) => {
  const GALLERY_ITEMS = [
    {
      departmentId,
      galleryId: 1,
      displayOrder: 1,
    },
    {
      departmentId,
      galleryId: 2,
      displayOrder: 2,
    },
  ];

  try {
    return await db.insert(departmentGallery).values(GALLERY_ITEMS).returning();
  } catch (error) {
    consola.error('Failed to create department gallery:', error);
    throw error;
  }
};

// Create department events
export const createDepartmentEvents = async (db: any, departmentId: number) => {
  const EVENTS = [
    {
      departmentId,
      eventId: 1,
      displayOrder: 1,
    },
    {
      departmentId,
      eventId: 2,
      displayOrder: 2,
    },
  ];

  try {
    return await db.insert(departmentEvent).values(EVENTS).returning();
  } catch (error) {
    consola.error('Failed to create department events:', error);
    throw error;
  }
};
