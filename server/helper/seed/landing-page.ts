import consola from 'consola';
import { eq } from 'drizzle-orm';
import type { ButtonStyle } from '~~/server/database/tables';
import type { Album } from '~~/server/database/tables/album';
import {
  album,
  announcement,
  card,
  cardFiles,
  files,
  menu,
  section,
  stats,
  linkButton,
  toggle,
  announcementFiles,
  cardContent,
  albumFiles,
} from '~~/server/database/tables';

const LANDING_PAGE_SECTION_ID = 1;

export const createMenu = async (db: any) => {
  const LANDING_PAGE_MENU = [
    {
      title: 'About Us',
      menuName: 'About Us',
      routeEnabled: false,
      slug: `about-us`,
      isActive: true,
      parentId: null,
    },
    {
      title: 'Campus',
      menuName: 'Campus',
      routeEnabled: false,
      slug: `campus`,
      isActive: true,
      parentId: null,
    },
    {
      title: 'Examinations',
      menuName: 'Examinations',
      routeEnabled: false,
      slug: `examinations`,
      isActive: true,
      parentId: null,
    },
    {
      title: 'Student Services',
      menuName: 'Student Services',
      routeEnabled: false,
      slug: `student-services`,
      isActive: true,
      parentId: null,
    },
  ];

  const CHUNK_SIZE = 4; // Safe chunk size for SQLite parameters

  // Helper function to chunk arrays
  const chunkArray = <T>(array: T[], size: number): T[][] => {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  };

  // Helper function to safely insert menu items
  const insertMenuItems = async (items: any[]) => {
    if (items.length <= CHUNK_SIZE) {
      await db.insert(menu).values(items).returning();
      return;
    }

    const chunks = chunkArray(items, CHUNK_SIZE);
    for (const chunk of chunks) {
      await db.insert(menu).values(chunk).returning();
    }
  };

  const SUB_MENUS_1 = [
    {
      title: 'Introduction',
      menuName: 'Introduction',
      routeEnabled: true,
      slug: `introduction`,
      isActive: true,
      parentId: 1,
    },
    {
      title: 'Vision and Mission',
      menuName: 'Vision and Mission',
      routeEnabled: true,
      slug: `vision-and-mission`,
      isActive: true,
      parentId: 1,
    },
    {
      title: 'Profile',
      menuName: 'Profile',
      routeEnabled: true,
      slug: `profile`,
      isActive: true,
      parentId: 1,
    },
    {
      title: 'Our Management',
      menuName: 'Our Management',
      routeEnabled: true,
      slug: `our-management`,
      isActive: true,
      parentId: 1,
    },
    {
      title: 'Former Managers',
      menuName: 'Former Managers',
      routeEnabled: true,
      slug: `former-managers`,
      isActive: true,
      parentId: 1,
    },
    {
      title: 'Former Principals',
      menuName: 'Former Principals',
      routeEnabled: true,
      slug: `former-principals`,
      isActive: true,
      parentId: 1,
    },
    {
      title: 'College Report',
      menuName: 'College Report',
      routeEnabled: true,
      slug: `college-report`,
      isActive: true,
      parentId: 1,
    },
    {
      title: 'Accolades',
      menuName: 'Accolades',
      routeEnabled: true,
      slug: `accolades`,
      isActive: true,
      parentId: 1,
    },
    {
      title: 'Certifications',
      menuName: 'Certifications',
      routeEnabled: true,
      slug: `certifications`,
      isActive: true,
      parentId: 1,
    },
  ];

  const SUB_MENUS_2 = [
    {
      title: 'UG Courses',
      menuName: 'UG Courses',
      routeEnabled: true,
      slug: `ug-courses`,
      isActive: true,
      parentId: 2,
    },
    {
      title: 'PG Courses',
      menuName: 'PG Courses',
      routeEnabled: true,
      slug: `pg-courses`,
      isActive: true,
      parentId: 2,
    },
    {
      title: 'Add on Courses',
      menuName: 'Add on Courses',
      routeEnabled: true,
      slug: `add-on-courses`,
      isActive: true,
      parentId: 2,
    },
  ];

  const SUB_MENUS_3 = [
    {
      title: 'Commerce',
      menuName: 'Commerce',
      routeEnabled: true,
      slug: `commerce`,
      isActive: true,
      parentId: 3,
    },
    {
      title: 'Arts',
      menuName: 'Arts',
      routeEnabled: true,
      slug: `arts`,
      isActive: true,
      parentId: 3,
    },
    {
      title: 'Science',
      menuName: 'Science',
      routeEnabled: true,
      slug: `science`,
      isActive: true,
      parentId: 3,
    },
    {
      title: 'Management Studies',
      menuName: 'Management Studies',
      routeEnabled: true,
      slug: `management-studies`,
      isActive: true,
      parentId: 3,
    },
    {
      title: 'Social Work',
      menuName: 'Social Work',
      routeEnabled: true,
      slug: `social-work`,
      isActive: true,
      parentId: 3,
    },
  ];

  const SUB_MENUS_5 = [
    {
      title: 'Internal Exam',
      menuName: 'Internal Exam',
      routeEnabled: true,
      slug: `internal-exam`,
      isActive: true,
      parentId: 5,
    },
    {
      title: 'University Exam',
      menuName: 'University Exam',
      routeEnabled: true,
      slug: `university-exam`,
      isActive: true,
      parentId: 5,
    },
  ];

  const SUB_MENUS_6 = [
    {
      title: 'About IQAC',
      menuName: 'About IQAC',
      routeEnabled: true,
      slug: `about-iqac`,
      isActive: true,
      parentId: 6,
    },
    {
      title: 'Composition of IQAC',
      menuName: 'Composition of IQAC',
      routeEnabled: true,
      slug: `composition-of-iqac`,
      isActive: true,
      parentId: 6,
    },
    {
      title: 'Minutes & Report',
      menuName: 'Minutes & Report',
      routeEnabled: true,
      slug: `minutes-and-report`,
      isActive: true,
      parentId: 6,
    },
    {
      title: 'Objectives & Initiatives',
      menuName: 'Objectives & Initiatives',
      routeEnabled: true,
      slug: `objectives-and-initiatives`,
      isActive: true,
      parentId: 6,
    },
    {
      title: 'SAAC',
      menuName: 'SAAC',
      routeEnabled: true,
      slug: `saac`,
      isActive: true,
      parentId: 6,
    },
    {
      title: 'MOU',
      menuName: 'MOU',
      routeEnabled: true,
      slug: `mou`,
      isActive: true,
      parentId: 6,
    },
    {
      title: 'SSS(Student Satisfaction Survey)',
      menuName: 'SSS(Student Satisfaction Survey)',
      routeEnabled: true,
      slug: `sss`,
      isActive: true,
      parentId: 6,
    },
    {
      title: 'Policy Documents',
      menuName: 'Policy Documents',
      routeEnabled: true,
      slug: `policy-documents`,
      isActive: true,
      parentId: 6,
    },
    {
      title: 'Institution Distinctiveness',
      menuName: 'Institution Distinctiveness',
      routeEnabled: true,
      slug: `institution-distinctiveness`,
      isActive: true,
      parentId: 6,
    },
    {
      title: 'SSR',
      menuName: 'SSR',
      routeEnabled: true,
      slug: `ssr`,
      isActive: true,
      parentId: 6,
    },
    {
      title: 'AQAR',
      menuName: 'AQAR',
      routeEnabled: true,
      slug: `aqar`,
      isActive: true,
      parentId: 6,
    },
    {
      title: 'Quality Audits',
      menuName: 'Quality Audits',
      routeEnabled: true,
      slug: `quality-audits`,
      isActive: true,
      parentId: 6,
    },
    {
      title: 'Best Practice',
      menuName: 'Best Practice',
      routeEnabled: true,
      slug: `best-practice`,
      isActive: true,
      parentId: 6,
    },
    {
      title: 'Feedback',
      menuName: 'Feedback',
      routeEnabled: true,
      slug: `feedback`,
      isActive: true,
      parentId: 6,
    },
  ];

  const SUB_MENUS_7 = [
    {
      title: 'Student Login',
      menuName: 'Student Login',
      routeEnabled: true,
      slug: `student-login`,
      isActive: true,
      parentId: 7,
    },
    {
      title: 'Parent Login',
      menuName: 'Parent Login',
      routeEnabled: true,
      slug: `parent-login`,
      isActive: true,
      parentId: 7,
    },
    {
      title: 'Staff Login',
      menuName: 'Staff Login',
      routeEnabled: true,
      slug: `staff-login`,
      isActive: true,
      parentId: 7,
    },
    {
      title: 'Digi locker Services',
      menuName: 'Digi locker Services',
      routeEnabled: true,
      slug: `digi-locker-services`,
      isActive: true,
      parentId: 7,
    },
    {
      title: 'Academic Bank of Credits',
      menuName: 'Academic Bank of Credits',
      routeEnabled: true,
      slug: `academic-bank-of-credits`,
      isActive: true,
      parentId: 7,
    },
    {
      title: 'Swayam Portal',
      menuName: 'Swayam Portal',
      routeEnabled: true,
      slug: `swayam-portal`,
      isActive: true,
      parentId: 7,
    },
    {
      title: 'My Bharath Portal',
      menuName: 'My Bharath Portal',
      routeEnabled: true,
      slug: `my-bharath-portal`,
      isActive: true,
      parentId: 7,
    },
  ];

  const SUB_MENUS_8 = [
    {
      title: 'Ragging',
      menuName: 'Ragging',
      routeEnabled: true,
      slug: `ragging`,
      isActive: true,
      parentId: 8,
    },
    {
      title: 'Sexual harassment',
      menuName: 'Sexual harassment',
      routeEnabled: true,
      slug: `sexual-harassment`,
      isActive: true,
      parentId: 8,
    },
    {
      title: 'Scholarship',
      menuName: 'Scholarship',
      routeEnabled: true,
      slug: `scholarship`,
      isActive: true,
      parentId: 8,
    },
    {
      title: 'Endowments',
      menuName: 'Endowments',
      routeEnabled: true,
      slug: `endowments`,
      isActive: true,
      parentId: 8,
    },
    {
      title: 'NCC',
      menuName: 'NCC',
      routeEnabled: true,
      slug: `ncc`,
      isActive: true,
      parentId: 8,
    },
    {
      title: 'NSS',
      menuName: 'NSS',
      routeEnabled: true,
      slug: `nss`,
      isActive: true,
      parentId: 8,
    },
    {
      title: 'PTA',
      menuName: 'PTA',
      routeEnabled: true,
      slug: `pta`,
      isActive: true,
      parentId: 8,
    },
    {
      title: 'College Student Union',
      menuName: 'College Student Union',
      routeEnabled: true,
      slug: `college-student-union`,
      isActive: true,
      parentId: 8,
    },
    {
      title: 'Clubs & Committees',
      menuName: 'Clubs & Committees',
      routeEnabled: true,
      slug: `clubs-and-committees`,
      isActive: true,
      parentId: 8,
    },
    {
      title: 'Women Cell',
      menuName: 'Women Cell',
      routeEnabled: true,
      slug: `women-cell`,
      isActive: true,
      parentId: 8,
    },
    {
      title: 'Placement',
      menuName: 'Placement',
      routeEnabled: true,
      slug: `placement`,
      isActive: true,
      parentId: 8,
    },
    {
      title: 'Mentoring',
      menuName: 'Mentoring',
      routeEnabled: true,
      slug: `mentoring`,
      isActive: true,
      parentId: 8,
    },
    {
      title: 'Question Paper',
      menuName: 'Question Paper',
      routeEnabled: true,
      slug: `question-paper`,
      isActive: true,
      parentId: 8,
    },
    {
      title: 'Forms',
      menuName: 'Forms',
      routeEnabled: true,
      slug: `forms`,
      isActive: true,
      parentId: 8,
    },
  ];

  try {
    // Insert main menu items
    await db.insert(menu).values(LANDING_PAGE_MENU).returning();

    // Insert all sub-menus with chunking where needed
    await insertMenuItems(SUB_MENUS_1);
    await insertMenuItems(SUB_MENUS_2);
    await insertMenuItems(SUB_MENUS_3);
    await insertMenuItems(SUB_MENUS_5);
    await insertMenuItems(SUB_MENUS_6);
    await insertMenuItems(SUB_MENUS_7);
    await insertMenuItems(SUB_MENUS_8);
  } catch (error) {
    consola.error('Failed to create menus:', error);
    throw error;
  }
};

export const createEvents = async (db: any) => {
  const LANDING_PAGE_EVENTS = [
    {
      title: 'College Day',
      content:
        'College day is a day when the college celebrates its founding and the achievements of the college. Let everyone know about the college day and the activities that will be held.',
      eventDate: new Date().getTime(),
      scheduledAt: new Date().getTime(),
      expiresAt: new Date().getTime() + 1000 * 60 * 60 * 24 * 7,
      isActive: true,
      priority: 1,
      announcementType: 'event',
    },
    {
      title: 'Sports Day',
      content:
        'Sports day is a day when the college celebrates its sports and the achievements of the college. Let everyone know about the sports day and the activities that will be held.',
      eventDate: new Date().getTime(),
      scheduledAt: new Date().getTime(),
      expiresAt: new Date().getTime() + 1000 * 60 * 60 * 24 * 7,
      isActive: true,
      priority: 2,
      announcementType: 'event',
    },
    {
      title: 'Arts Day',
      content:
        'Arts day is a day when the college celebrates its arts and the achievements of the college. Let everyone know about the arts day and the activities that will be held.',
      eventDate: new Date().getTime(),
      scheduledAt: new Date().getTime(),
      expiresAt: new Date().getTime() + 1000 * 60 * 60 * 24 * 7,
      isActive: true,
      priority: 3,
      announcementType: 'event',
    },
  ];
  const events = await db
    .insert(announcement)
    .values(LANDING_PAGE_EVENTS)
    .returning();
  const files = await createFiles(db);
  createEventFileRelations(db, events, files);
  createEventButtons(db, events);
};

export const createNotices = async (db: any) => {
  const LANDING_PAGE_NOTICES = [
    {
      title: 'College Day Notice',
      content:
        'College day is a day when the college celebrates its founding and the achievements of the college. Let everyone know about the college day and the activities that will be held.',
      eventDate: new Date().getTime(),
      scheduledAt: new Date().getTime(),
      expiresAt: new Date().getTime() + 1000 * 60 * 60 * 24 * 7,
      isActive: true,
      priority: 1,
      announcementType: 'notice',
    },
    {
      title: 'Sports Day Notice',
      content:
        'Sports day is a day when the college celebrates its sports and the achievements of the college. Let everyone know about the sports day and the activities that will be held.',
      eventDate: new Date().getTime(),
      scheduledAt: new Date().getTime(),
      expiresAt: new Date().getTime() + 1000 * 60 * 60 * 24 * 7,
      isActive: true,
      priority: 2,
      announcementType: 'notice',
    },
    {
      title: 'Arts Day Notice',
      content:
        'Arts day is a day when the college celebrates its arts and the achievements of the college. Let everyone know about the arts day and the activities that will be held.',
      eventDate: new Date().getTime(),
      scheduledAt: new Date().getTime(),
      expiresAt: new Date().getTime() + 1000 * 60 * 60 * 24 * 7,
      isActive: true,
      priority: 3,
      announcementType: 'notice',
    },
  ];

  const notices = await db
    .insert(announcement)
    .values(LANDING_PAGE_NOTICES)
    .returning();
  const files = await createFiles(db);
  //Add files only for few notices
  const filesToAdd = files.slice(0, 3);
  const noticesToAdd = notices.slice(0, 3);
  createEventFileRelations(db, noticesToAdd, filesToAdd);
  createEventButtons(db, noticesToAdd);
};

export const createUpdates = async (db: any) => {
  const LANDING_PAGE_UPDATES = [
    {
      title: 'Update 1',
      content:
        'III Semester MA, MSc, MCom, MCM & MSW Improvement/ Supplementary (CBCS- 2021 Admission) examinations.',
      eventDate: new Date().getTime(),
      scheduledAt: new Date().getTime(),
      expiresAt: new Date().getTime() + 1000 * 60 * 60 * 24 * 7,
      isActive: true,
      priority: 1,
      announcementType: 'update',
    },
    {
      title: 'Update 2',
      content:
        'III Semester MA, MSc, MCom, MCM & MSW Improvement/ Supplementary (CBCS- 2021 Admission) examinations.',
      eventDate: new Date().getTime(),
      scheduledAt: new Date().getTime(),
      expiresAt: new Date().getTime() + 1000 * 60 * 60 * 24 * 7,
      isActive: true,
      priority: 2,
      announcementType: 'update',
    },
    {
      title: 'Update 3',
      content:
        'III Semester MA, MSc, MCom, MCM & MSW Improvement/ Supplementary (CBCS- 2021 Admission) examinations.',
      eventDate: new Date().getTime(),
      scheduledAt: new Date().getTime(),
      expiresAt: new Date().getTime() + 1000 * 60 * 60 * 24 * 7,
      isActive: true,
      priority: 2,
      announcementType: 'update',
    },
  ];
  const updates = await db
    .insert(announcement)
    .values(LANDING_PAGE_UPDATES)
    .returning();
  createEventButtons(db, updates);
};

export const createGallery = async (db: any) => {
  const LANDING_PAGE_GALLERY = [
    {
      title: 'College Events',
    },
    {
      title: 'Campus Life',
    },
    {
      title: 'Student Activities',
    },
  ];

  // Create gallery albums
  const createdAlbums = await db
    .insert(album)
    .values(LANDING_PAGE_GALLERY)
    .returning();

  // Create dummy files for galleries
  const GALLERY_FILES = [
    // College Events images
    {
      pathname: '',
      title: 'Annual Day Celebration',
      type: 'image/jpeg',
    },
    {
      pathname: '',
      title: 'Cultural Festival',
      type: 'image/jpeg',
    },
    // Campus Life images
    {
      pathname: '',
      title: 'Library',
      type: 'image/jpeg',
    },
    {
      pathname: '',
      title: 'Sports Ground',
      type: 'image/jpeg',
    },
    // Student Activities images
    {
      pathname: '',
      title: 'NSS Camp',
      type: 'image/jpeg',
    },
    {
      pathname: '',
      title: 'Technical Workshop',
      type: 'image/jpeg',
    },
  ];

  // Create files
  const createdFiles = await db.insert(files).values(GALLERY_FILES).returning();

  // Create album-file relations (2 images per album)
  const albumFilesData: { albumId: number; fileId: number }[] = [];
  createdAlbums.forEach((createdAlbum: Album, albumIndex: number) => {
    // Add 2 files for each album
    albumFilesData.push(
      {
        albumId: createdAlbum.id,
        fileId: createdFiles[albumIndex * 2].id,
      },
      {
        albumId: createdAlbum.id,
        fileId: createdFiles[albumIndex * 2 + 1].id,
      }
    );
  });

  // Insert album-file relations
  await db.insert(albumFiles).values(albumFilesData);

  return createdAlbums;
};

export const createOrGetLandingPageSection = async (db: any) => {
  const existingSection = await db.query.section.findFirst({
    where: eq(section.id, LANDING_PAGE_SECTION_ID),
  });

  if (existingSection) return existingSection;

  try {
    const [newSection] = await db
      .insert(section)
      .values({
        type: 'card_list',
        priority: 1,
      })
      .returning();
    return newSection;
  } catch (error) {
    consola.error('Failed to create landing page section:', error);
    throw new Error('Section creation failed');
  }
};

export const getExistingCards = async (db: any, sectionId: number) => {
  return await db.query.card.findMany({
    where: eq(card.sectionId, sectionId),
  });
};

export const getExistingToggles = async (db: any, sectionId: number) => {
  return await db.query.toggle.findMany({
    where: eq(toggle.sectionId, sectionId),
  });
};

export const getExistingStats = async (db: any, sectionId: number) => {
  return await db.query.stats.findMany({
    where: eq(stats.sectionId, sectionId),
  });
};

export const createLandingPageContent = async (db: any) => {
  // Create files
  const createdFiles = await createFiles(db);

  // Create cards
  const createdCards = await createCards(db);

  // Create card-file relations
  await createCardFileRelations(db, createdCards, createdFiles);

  // Create buttons
  await createButtons(db, createdCards);
};

export const createLandingPageToggles = async (db: any) => {
  const LANDING_PAGE_TOGGLES = [
    {
      sectionId: LANDING_PAGE_SECTION_ID,
      type: 'programsWeOffer',
      title: 'Programs We Offer',
      isActive: true,
      menuId: 1,
    },
    {
      sectionId: LANDING_PAGE_SECTION_ID,
      type: 'event',
      title: 'Events',
      isActive: true,
    },
    {
      sectionId: LANDING_PAGE_SECTION_ID,
      type: 'gallery',
      title: 'Gallery',
      isActive: true,
    },
  ];
  // Create toggles
  const createdToggles = await db
    .insert(toggle)
    .values(LANDING_PAGE_TOGGLES)
    .returning();
  return createdToggles;
};

export const createLandingPageStats = async (db: any) => {
  const LANDING_PAGE_STATS = [
    {
      sectionId: LANDING_PAGE_SECTION_ID,
      title: 'Graduates',
      value: 4600,
      icon: 'i-lucide-graduation-cap',
    },
    {
      sectionId: LANDING_PAGE_SECTION_ID,
      title: 'Courses',
      value: 13,
      icon: 'i-lucide-book-open',
    },
    {
      sectionId: LANDING_PAGE_SECTION_ID,
      title: 'University Rank Holders',
      value: 252,
      icon: 'i-lucide-trophy',
    },
    {
      sectionId: LANDING_PAGE_SECTION_ID,
      title: 'Placement Offers',
      value: 2019,
      icon: 'i-lucide-briefcase-business',
    },
  ];
  // Create stats
  const createdStats = await db
    .insert(stats)
    .values(LANDING_PAGE_STATS)
    .returning();
  return createdStats;
};

const createFiles = async (db: any) => {
  try {
    const LANDING_PAGE_FILES = [
      {
        pathname: '',
        title: 'Principal of the Institution',
        type: 'image/jpeg',
      },
      {
        pathname: '',
        title: 'Principal of the Institution',
        type: 'image/jpeg',
      },
      {
        pathname: '',
        title: 'Campus Overview',
        type: 'image/jpeg',
      },
    ];
    const createdFiles = await db
      .insert(files)
      .values(LANDING_PAGE_FILES)
      .returning();
    return createdFiles;
  } catch (error) {
    consola.error('Failed to create files:', error);
    throw new Error('File creation failed');
  }
};

const createCards = async (db: any) => {
  try {
    const LANDING_PAGE_CARDS = [
      {
        sectionId: LANDING_PAGE_SECTION_ID,
        layout: 'standard',
        title: "Principal's Message",
        key: 'principal-message',
      },
      {
        sectionId: LANDING_PAGE_SECTION_ID,
        layout: 'standard',
        title: 'Our Campus',
        key: 'campus-details',
      },
      {
        sectionId: LANDING_PAGE_SECTION_ID,
        layout: 'standard',
        title: 'Research',
        key: 'research',
      },
    ];
    const cardsData = LANDING_PAGE_CARDS.map((card) => ({
      ...card,
      sectionId: LANDING_PAGE_SECTION_ID,
    }));
    const createdCards = await db.insert(card).values(cardsData).returning();

    // Create card content for each card
    await createCardContent(db, createdCards);

    return createdCards;
  } catch (error) {
    consola.error('Failed to create cards:', error);
    throw new Error('Card creation failed');
  }
};

const createCardContent = async (db: any, cards: any[]) => {
  try {
    const cardContentData = [];

    // Principal's Message content
    cardContentData.push({
      cardId: cards[0].id,
      priority: 1,
      title: 'Principal',
      content: 'Dr.Fr.Johnson Ponthempally SDB',
    });
    cardContentData.push({
      cardId: cards[0].id,
      priority: 2,
      title: 'Message',
      content:
        'To be intellectually competent, morally upright, psychologically integrated, physically able and socially responsible, is a long process of maturing into adulthood. Don Bosco College at Mannuthy, is aimed at bringing up the students and staff of this college to this maturity through creating an ecosystem adept for growth and excellence. Challenging everyone to be here and to be legends we remind ourselves to go to the next step of growth always.',
    });

    // Our Campus content
    cardContentData.push({
      cardId: cards[1].id,
      priority: 1,
      title: 'About Campus',
      content:
        'The "Salesians of Don Bosco" is an international society founded by St. John Bosco popularly known as Don Bosco. Catholic priests and Brothers who are members of the Society known as the Salesians of Don Bosco(SDB) work for the development and education of the young especially those at most risk.',
    });
    cardContentData.push({
      cardId: cards[1].id,
      priority: 2,
      title: 'Global Presence',
      content:
        'Through its 3000 Schools, Colleges, Technical schools and youth centers in 136 countries, the Salesians of Don Bosco serve all young people irrespective of religious differences or social inequalities. Don Bosco College Mannuthy forms part of this international society and is a project of the Salesians of Don Bosco (SDB) executed by Don Bosco Society of Bangalore Province.',
    });

    // Research content
    cardContentData.push({
      cardId: cards[2].id,
      priority: 1,
      title: 'Research Excellence',
      content:
        'At Don Bosco College Mannuthy, we foster a culture of curiosity and innovation through dedicated research initiatives. Our institution encourages students and faculty to engage in meaningful research that addresses real-world challenges.',
    });
    cardContentData.push({
      cardId: cards[2].id,
      priority: 2,
      title: 'Research Opportunities',
      content:
        'By providing access to state-of-the-art resources and fostering interdisciplinary collaboration, we aim to contribute to academic excellence and societal development. Join us in our journey of discovery and knowledge creation.',
    });

    await db.insert(cardContent).values(cardContentData);
  } catch (error) {
    consola.error('Failed to create card content:', error);
    throw new Error('Card content creation failed');
  }
};

const createCardFileRelations = async (db: any, cards: any[], files: any[]) => {
  try {
    const cardFilesData = cards.map((card, index) => ({
      cardId: card.id,
      fileId: files[index]?.id,
    }));
    await db.insert(cardFiles).values(cardFilesData);
  } catch (error) {
    consola.error('Failed to create card-file relations:', error);
    throw new Error(`Card-file relation creation failed: ${error}`);
  }
};

const createButtons = async (db: any, cards: any[]) => {
  try {
    const buttonsData = cards.map((card, index) => ({
      cardTemplateId: card.id,
      title: index === 2 ? 'Learn More' : 'Read More',
      style: 'primary' as ButtonStyle,
      internalLink: '/about-us',
      newTab: true,
    }));
    await db.insert(linkButton).values(buttonsData);
  } catch (error) {
    consola.error('Failed to create buttons:', error);
    throw new Error('Button creation failed');
  }
};

const createEventButtons = async (db: any, announcements: any[]) => {
  try {
    const buttonsData = announcements.map((announcement, index) => ({
      announcementId: announcement.id,
      title: index === 2 ? 'Learn More' : 'Read More',
      style: 'primary' as ButtonStyle,
      internalLink: '/about-us',
      newTab: true,
    }));
    await db.insert(linkButton).values(buttonsData);
  } catch (error) {
    consola.error('Failed to create buttons:', error);
    throw new Error('Button creation failed');
  }
};

const createEventFileRelations = async (
  db: any,
  announcements: any[],
  files: any[]
) => {
  try {
    const announcementFilesData = announcements.map((announcement, index) => ({
      announcementId: announcement.id,
      fileId: files[index]?.id,
    }));
    await db.insert(announcementFiles).values(announcementFilesData);
  } catch (error) {
    consola.error('Failed to create card-file relations:', error);
    throw new Error(`Card-file relation creation failed: ${error}`);
  }
};
