import consola from 'consola';
import { quickLink } from '~~/server/database/tables/quick-link';

const ESERVICES = [
  {
    title: 'Online Admission Portal',
    link: 'https://admission.example.edu',
    type: 'eService',
  },
  {
    title: 'Student Information System',
    link: 'https://sis.example.edu',
    type: 'eService',
  },
  {
    title: 'Library Management System',
    link: 'https://library.example.edu',
    type: 'eService',
  },
  {
    title: 'Fee Payment Portal',
    link: 'https://payment.example.edu',
    type: 'eService',
  },
  {
    title: 'E-Learning Platform',
    link: 'https://learn.example.edu',
    type: 'eService',
  },
  {
    title: 'Examination Portal',
    link: 'https://exam.example.edu',
    type: 'eService',
  },
  {
    title: 'Alumni Network',
    link: 'https://alumni.example.edu',
    type: 'eService',
  },
  {
    title: 'Career Services',
    link: 'https://career.example.edu',
    type: 'eService',
  },
  {
    title: 'Research Publications Repository',
    link: 'https://research.example.edu',
    type: 'eService',
  },
  {
    title: 'Student Grievance System',
    link: 'https://grievance.example.edu',
    type: 'eService',
  },
];

const QUICK_LINKS = [
  {
    title: 'Gallery',
    link: 'https://example.com/gallery',
    type: 'quickLink',
    icon: 'i-material-symbols:imagesmode-outline',
  },
  {
    title: 'Library',
    link: 'https://example.com/library',
    type: 'quickLink',
    icon: 'i-material-symbols:local-library',
  },
  {
    title: 'Alumini',
    link: 'https://example.com/alumini',
    type: 'quickLink',
    icon: 'i-material-symbols:person-celebrate',
  },
  {
    title: 'Contact',
    link: 'https://example.com/contact',
    type: 'quickLink',
    icon: 'i-material-symbols:contacts-rounded',
  },
  {
    title: 'Admission',
    link: 'https://example.com/admission',
    type: 'quickLink',
    icon: 'i-material-symbols:account-balance',
  },
  {
    title: 'Question Bank',
    link: 'https://example.com/question-bank',
    type: 'quickLink',
    icon: 'i-material-symbols:contact-support-rounded',
  },
  {
    title: 'Pay Online',
    link: 'https://example.com/pay-online',
    type: 'quickLink',
    icon: 'i-material-symbols:credit-card',
  },
];

export const createEServices = async (db: any) => {
  try {
    const eServices = await db.insert(quickLink).values(ESERVICES).returning();
    return eServices;
  } catch (error) {
    consola.error('Failed to create eServices:', error);
    throw error;
  }
};

export const createQuickLinks = async (db: any) => {
  try {
    const quickLinks = await db
      .insert(quickLink)
      .values(QUICK_LINKS)
      .returning();
    return quickLinks;
  } catch (error) {
    consola.error('Failed to create quick links:', error);
    throw error;
  }
};

export default {
  ESERVICES,
  QUICK_LINKS,
};
