import { eq } from 'drizzle-orm';
import { admin } from '../database/schema';
import type { Admin, NewAdmin } from '../database/tables/admin';

/**
 * Hashes a password using scrypt via Nuxt Auth Utils
 */
export const hashAdminPassword = async (password: string): Promise<string> => {
  return await hashPassword(password);
};

/**
 * Verifies admin credentials
 * @param username Admin username
 * @param password Plain text password to verify
 * @returns Admin object if credentials are valid, null otherwise
 */
export const verifyAdminCredentials = async (
  username: string,
  password: string
): Promise<Admin | null> => {
  // Find admin by username
  const adminUser = await useDrizzle().query.admin.findFirst({
    where: eq(admin.username, username),
  });

  // If admin not found or not active, return null
  if (!adminUser || !adminUser.isActive) {
    return null;
  }
  // Verify password using Nuxt Auth Utils verifyPassword
  const isValid = await verifyPassword(adminUser.password, password);

  if (!isValid) {
    return null;
  }

  return adminUser;
};

/**
 * Updates the last login timestamp for an admin
 * @param adminId Admin ID
 */
export const updateAdminLastLogin = async (adminId: number): Promise<void> => {
  await useDrizzle()
    .update(admin)
    .set({ lastLogin: Date.now() })
    .where(eq(admin.id, adminId));
};

/**
 * Seeds an admin user if none exists
 */
export const seedAdminIfNeeded = async (): Promise<void> => {
  // Check if any admin exists
  const existingAdmin = await useDrizzle().query.admin.findFirst();

  // If an admin already exists, do nothing
  if (existingAdmin) {
    return;
  }

  // Create default admin with scrypt hashed password
  const hashedPassword = await hashAdminPassword('donbosco@abcd');

  await useDrizzle().insert(admin).values({
    username: 'admin',
    email: '<EMAIL>',
    password: hashedPassword,
    fullName: 'System Administrator',
    role: 'admin',
    isActive: true,
    createdAt: Date.now(),
    updatedAt: Date.now(),
  });
};

/**
 * Get an admin by username
 */
export const getAdminByUsername = async (
  username: string
): Promise<Admin | null> => {
  const result = await useDrizzle().query.admin.findFirst({
    where: eq(admin.username, username),
  });

  return result || null;
};

/**
 * Get an admin by email
 */
export const getAdminByEmail = async (email: string): Promise<Admin | null> => {
  const result = await useDrizzle().query.admin.findFirst({
    where: eq(admin.email, email),
  });

  return result || null;
};

/**
 * Create a new admin
 */
export const createAdmin = async (
  data: Omit<NewAdmin, 'password'> & { password: string }
): Promise<Admin> => {
  // Hash the password using scrypt via Nuxt Auth Utils
  const hashedPassword = await hashAdminPassword(data.password);

  // Insert the admin with the hashed password
  const result = await useDrizzle()
    .insert(admin)
    .values({
      ...data,
      password: hashedPassword,
    })
    .returning();

  return result[0];
};
