# Database Seeding Tasks

This directory contains Nitro tasks for seeding the database with initial data. The seeding logic has been refactored to use a functional approach for better maintainability.

## Organization

- **seedFunctions.ts** - Contains all the individual seeding functions
- **seed.ts** - Main task that runs all seeding functions in sequence

## Seeding Functions

The following seeding functions are available in `seedFunctions.ts`:

- **seedAdmin()** - Seeds an admin user if one does not exist yet
- **seedLandingPage()** - Seeds the landing page content, toggles, and stats
- **seedDepartments()** - Seeds the departments with courses, faculty, students, files, gallery and events
- **seedEServices()** - Seeds the e-services for the website
- **seedQuickLinks()** - Seeds the quick links for the website

These functions can be imported and used individually if needed.

## How to Run

You can run the seeding task using the Nitro `ntc` CLI tool or programmatically via `runTask` method.

### Using CLI

```bash
# Run the seed task
npx nitropack task seed
```

### Using API

You can run the task programmatically:

```typescript
// In a Nitro plugin or elsewhere in your application
await runTask('seed');
```

Or use the functions directly:

```typescript
import { seedAdmin, seedLandingPage } from './tasks/seedFunctions';

// Run specific seeding functions
await seedAdmin();
await seedLandingPage();
```

## Development Auto-Seeding

During development, the `02.pre-seed.ts` plugin will automatically run the `seed` task when the server starts, but only in development mode.

## Benefits of This Approach

- **Reusability**: Functions can be imported and used anywhere in your application
- **Simplicity**: Cleaner organization with a single task and reusable functions
- **Maintainability**: Easier to update and test individual seeding functions
- **Flexibility**: Functions can be used individually or as part of the main task
