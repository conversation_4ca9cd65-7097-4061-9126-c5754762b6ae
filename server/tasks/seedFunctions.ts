import consola from 'consola';
import { eq } from 'drizzle-orm';
import { department, quickLink, dynamicTable } from '../database/tables';
import { seedAdminIfNeeded } from '../utils/admin';
import { useDrizzle } from '../utils/drizzle';
import {
  createOrGetLandingPageSection,
  getExistingCards,
  createLandingPageContent,
  getExistingToggles,
  getExistingStats,
  createLandingPageToggles,
  createLandingPageStats,
  createMenu,
  createGallery,
  createEvents,
  createUpdates,
  createNotices,
} from '../helper/seed/landing-page';
import {
  createDepartments,
  createDepartmentCourses,
  createDepartmentFaculty,
  createDepartmentStudents,
  createDepartmentFiles,
  createDepartmentGallery,
  createDepartmentEvents,
} from '../helper/seed/department';
import { createEServices, createQuickLinks } from '../helper/seed/e-service';

/**
 * Seeds an admin user if one does not exist yet
 */
export const seedAdmin = async () => {
  try {
    await seedAdminIfNeeded();
    return true;
  } catch (error) {
    consola.error('Failed to seed admin user:', error);
    throw error;
  }
};

/**
 * Seeds the IQAC tables
 */
export const seedIQACTables = async () => {
  try {
    const db = useDrizzle();

    // Create IQAC Content table
    let contentTable = await db
      .select()
      .from(dynamicTable)
      .where(eq(dynamicTable.slug, 'iqac_overview'));

    if (contentTable.length === 0) {
      contentTable = await db
        .insert(dynamicTable)
        .values({
          slug: 'iqac_overview',
          name: 'IQAC Overview',
          type: 'iqac_overview',
        })
        .returning();
    }

    // Create IQAC Downloads table
    let downloadsTable = await db
      .select()
      .from(dynamicTable)
      .where(eq(dynamicTable.slug, 'iqac_downloads'));

    if (downloadsTable.length === 0) {
      downloadsTable = await db
        .insert(dynamicTable)
        .values({
          slug: 'iqac_downloads',
          name: 'IQAC Downloads',
          type: 'iqac_downloads',
        })
        .returning();
    }

    // Create IQAC Attachments table
    let attachmentsTable = await db
      .select()
      .from(dynamicTable)
      .where(eq(dynamicTable.slug, 'iqac_attachments'));

    if (attachmentsTable.length === 0) {
      attachmentsTable = await db.insert(dynamicTable).values({
        slug: 'iqac_attachments',
        name: 'IQAC Attachments',
        type: 'iqac_attachments',
      });
    }

    // Create IQAC Team Members table
    let teamMembersTable = await db
      .select()
      .from(dynamicTable)
      .where(eq(dynamicTable.slug, 'iqac_team_members'));

    if (teamMembersTable.length === 0) {
      teamMembersTable = await db
        .insert(dynamicTable)
        .values({
          slug: 'iqac_team_members',
          name: 'IQAC Team Members',
          type: 'iqac_team_members',
        })
        .returning();
    }

    consola.success('IQAC tables created successfully');
    return true;
  } catch (error) {
    consola.error('Failed to seed IQAC tables:', error);
    throw error;
  }
};

/**
 * Seeds the landing page content, toggles, and stats
 */
export const seedLandingPage = async () => {
  try {
    const db = useDrizzle();

    const sectionData = await createOrGetLandingPageSection(db);
    const existingCards = await getExistingCards(db, sectionData.id);
    const existingToggles = await getExistingToggles(db, sectionData.id);
    const existingStats = await getExistingStats(db, sectionData.id);

    if (existingCards.length === 0) {
      await createLandingPageContent(db);
    }

    if (existingToggles.length === 0) {
      await createMenu(db);
      await createEvents(db);
      await createUpdates(db);
      await createNotices(db);
      await createGallery(db);
      await createLandingPageToggles(db);
    }

    if (existingStats.length === 0) {
      await createLandingPageStats(db);
    }

    return true;
  } catch (error) {
    consola.error('Failed to seed landing page:', error);
    throw error;
  }
};

/**
 * Seeds the departments with courses, faculty, students, files, gallery and events
 */
export const seedDepartments = async () => {
  try {
    const db = useDrizzle();

    // Check if departments already exist
    const existingDepartments = await db.select().from(department);

    if (existingDepartments.length === 0) {
      // Create base departments
      const departments = await createDepartments(db);

      // For each department, create related data
      for (const dept of departments) {
        // Create courses
        const courses = await createDepartmentCourses(db, dept.id);
        // Create faculty
        await createDepartmentFaculty(db, dept.id);

        // Create students (placements/toppers) - using first course
        if (courses.length > 0) {
          await createDepartmentStudents(db, dept.id, courses[0].id);
        }

        // Create files (downloads, question banks, projects)
        if (courses.length > 0) {
          await createDepartmentFiles(db, dept.id, courses[0].id);
        }

        // Create gallery items
        await createDepartmentGallery(db, dept.id);

        // Create events
        await createDepartmentEvents(db, dept.id);
      }
    }
    return true;
  } catch (error) {
    consola.error('Failed to seed departments:', error);
    throw error;
  }
};

/**
 * Seeds the e-services for the website
 */
export const seedEServices = async () => {
  try {
    const db = useDrizzle();

    const existingEServices = await db
      .select()
      .from(quickLink)
      .where(eq(quickLink.type, 'eService'));

    if (existingEServices.length === 0) {
      await createEServices(db);
    }
    return true;
  } catch (error) {
    consola.error('Failed to seed e-services:', error);
    throw error;
  }
};

/**
 * Seeds the quick links for the website
 */
export const seedQuickLinks = async () => {
  try {
    const db = useDrizzle();

    const existingQuickLinks = await db
      .select()
      .from(quickLink)
      .where(eq(quickLink.type, 'quickLink'));

    if (existingQuickLinks.length === 0) {
      await createQuickLinks(db);
    }

    return true;
  } catch (error) {
    consola.error('Failed to seed quick links:', error);
    throw error;
  }
};
