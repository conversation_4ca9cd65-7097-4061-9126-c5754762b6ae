import consola from 'consola';
import {
  seedAdmin,
  seedLandingPage,
  // seedDepartments,
  // seedEServices,
  seedQuickLinks,
  seedIQACTables,
} from './seedFunctions';

export default defineTask({
  meta: {
    name: 'seed',
    description: 'Runs all seed functions to populate the database',
  },
  async run() {
    try {
      consola.info('Starting full seeding process...');

      // Run admin seeding first
      await seedAdmin();
      // Run all seed functions in sequence
      await seedLandingPage();
      await seedIQACTables();
      // await seedDepartments();
      // await seedEServices();
      await seedQuickLinks();
      consola.success('All seeding processes completed successfully!');
      return { result: true };
    } catch (error) {
      consola.error('Failed to complete full seeding process:', error);
      throw error;
    }
  },
});
