import { tempFiles } from '~~/server/database/tables/files';
import { uploadSchema } from '~~/shared/schema/blob/upload';

export default eventHandler(async (event) => {
  const form = await readFormData(event);
  const files = form.getAll('files') as File[];
  const title = form.get('title') as string;

  // Validate the upload data
  const validatedData = await uploadSchema.parseAsync({
    files,
    title,
  });

  const results = await Promise.all(
    validatedData.files.map(async (file) => {
      ensureBlob(file, {
        maxSize: '1GB',
        types: ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],
      });

      const isImage = file.type.startsWith('image/');
      const isPdf = file.type === 'application/pdf';
      const { pathname } = await hubBlob().put(file.name, file, {
        addRandomSuffix: true,
        prefix: isImage ? 'images' : isPdf ? 'pdfs' : 'files',
      });

      const db = useDrizzle();
      await db.insert(tempFiles).values({
        pathname,
        title,
        type: file.type,
      });

      return {
        pathname,
        title,
        type: file.type,
        prefix: isImage ? 'images' : isPdf ? 'pdfs' : 'files',
      };
    })
  );

  return results;
});
