import { eq } from 'drizzle-orm';

import { quickLink } from '~~/server/database/tables/quick-link';
import { useDrizzle } from '~~/server/utils/drizzle';
import { updateEServiceSchema } from '~~/shared/schema/e-service/update';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');
    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'ID is required',
      });
    }

    const parsedId = parseInt(id, 10);
    if (isNaN(parsedId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid ID format',
      });
    }

    const body = await readBody(event);
    const data = updateEServiceSchema.parse(body);

    const db = useDrizzle();

    // Check if e-service exists
    const existing = await db
      .select()
      .from(quickLink)
      .where(eq(quickLink.id, parsedId))
      .get();

    if (!existing) {
      throw createError({
        statusCode: 404,
        message: 'E-service not found',
      });
    }

    // If title is being updated, check if it's unique
    if (data.title && data.title !== existing.title) {
      const titleExists = await db
        .select()
        .from(quickLink)
        .where(eq(quickLink.title, data.title))
        .get();

      if (titleExists) {
        throw createError({
          statusCode: 400,
          message: 'E-service with this title already exists',
        });
      }
    }

    const [updated] = await db
      .update(quickLink)
      .set(data)
      .where(eq(quickLink.id, parsedId))
      .returning();

    return updated;
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
