import { eq } from 'drizzle-orm';

import { quickLink } from '~~/server/database/tables/quick-link';
import { useDrizzle } from '~~/server/utils/drizzle';
import { createEServiceSchema } from '~~/shared/schema/e-service/create';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const data = {
      ...createEServiceSchema.parse(body),
      type: 'eService' as const,
    };

    const db = useDrizzle();

    // Check if title already exists
    const existing = await db
      .select()
      .from(quickLink)
      .where(eq(quickLink.title, data.title))
      .get();

    if (existing) {
      throw createError({
        statusCode: 400,
        message: 'E-service with this title already exists',
      });
    }

    const [created] = await db.insert(quickLink).values(data).returning();

    return created;
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
