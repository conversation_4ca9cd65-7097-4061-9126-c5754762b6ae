import { template } from '~~/server/database/schema';

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event);
    const id = query.id as number;

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Template ID is required',
      });
    }

    const db = useDrizzle();

    const templateRecord = await db.query.template.findFirst({
      where: eq(template.id, id),
      with: {
        sections: {
          columns: {
            templateId: false,
          },
        },
      },
    });

    if (!templateRecord) {
      throw createError({
        statusCode: 404,
        message: 'Template not found',
      });
    }

    return {
      success: true,
      data: templateRecord,
    };
  } catch (error: any) {
    event.context.validation.handleError(error);
  }
});
