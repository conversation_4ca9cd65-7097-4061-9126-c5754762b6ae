import type { DashboardItem } from '~/types/admin';
import { ANNOUNCEMENT_TYPES } from '~~/server/database/tables';

export default defineEventHandler(async () => {
  try {
    const db = useDrizzle();
    const dashboardData: DashboardItem[] = [];

    //Find the total number of albums
    const albums = await db.query.album.findMany({
      columns: {
        id: true,
      },
    });

    dashboardData.push({
      title: 'Albums',
      count: albums.length,
      showCount: false,
      activeCount: 0,
    });

    // Fetch E-Services and Quick Links
    const quickLinks = await db.query.quickLink.findMany({
      columns: {
        id: true,
        type: true,
      },
    });

    const eServicesCount = quickLinks.filter(
      (link) => link.type === 'eService'
    ).length;
    const quickLinksCount = quickLinks.filter(
      (link) => link.type === 'quickLink'
    ).length;

    dashboardData.push({
      title: 'E-Services',
      count: eServicesCount,
      showCount: false,
      activeCount: 0,
    });

    dashboardData.push({
      title: 'Quick Links',
      count: quickLinksCount,
      showCount: false,
      activeCount: 0,
    });

    // Find the total number of menus
    const menus = await db.query.menu.findMany({
      columns: {
        id: true,
        isActive: true,
      },
    });

    // Calculate the total number of menus and active menus
    const totalMenus = menus.length;
    const activeMenus = menus.filter((menu) => menu.isActive).length;

    dashboardData.push({
      title: 'Other Menus',
      count: totalMenus,
      showCount: true,
      activeCount: activeMenus,
    });

    // Find the total number of Notices, Updates, and Events
    const announcements = await db.query.announcement.findMany({
      columns: {
        id: true,
        isActive: true,
        announcementType: true,
      },
    });

    // Calculate counts for Notices, Updates, and Events
    const notices = announcements.filter(
      (announcement) => announcement.announcementType === ANNOUNCEMENT_TYPES[0] // 'notice'
    );
    const activeNotices = notices.filter((notice) => notice.isActive).length;

    const updates = announcements.filter(
      (announcement) => announcement.announcementType === ANNOUNCEMENT_TYPES[1] // 'update'
    );
    const activeUpdates = updates.filter((update) => update.isActive).length;

    const events = announcements.filter(
      (announcement) => announcement.announcementType === ANNOUNCEMENT_TYPES[2] // 'event'
    );
    const activeEvents = events.filter((event) => event.isActive).length;

    // Add Notices to dashboard data
    dashboardData.push({
      title: 'Notices',
      count: notices.length,
      showCount: true,
      activeCount: activeNotices,
    });

    // Add Updates to dashboard data
    dashboardData.push({
      title: 'Updates',
      count: updates.length,
      showCount: true,
      activeCount: activeUpdates,
    });

    // Add Events to dashboard data
    dashboardData.push({
      title: 'Events',
      count: events.length,
      showCount: true,
      activeCount: activeEvents,
    });

    return {
      success: true,
      data: dashboardData,
    };
  } catch (error) {
    console.error('Error fetching announcements:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch announcements',
    });
  }
});
