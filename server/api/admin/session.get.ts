export default defineEventHandler(async (event) => {
  try {
    // Get the current user session
    const session = await getUserSession(event);

    // If no user in session, return null
    if (!session.user) {
      return { user: null };
    }

    return {
      user: session.user,
      // Return only the user data
    };
  } catch (error) {
    console.error('Session error:', error);
    return { user: null };
  }
});
