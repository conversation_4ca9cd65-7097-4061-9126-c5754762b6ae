import {
  dynamicTableData,
  dynamicTable,
} from '~~/server/database/tables/dynamic-table';
import {
  validateTableData,
  type DynamicTableType,
  type IQAC_TEAM_MEMBERS,
} from '~~/server/database/tables/dynamic-types';
import { updateTeamMemberSchema } from '~~/shared/schema/iqac/team-members/update';
import { eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Update an existing IQAC team member',
    tags: ['IQAC'],
    parameters: [
      {
        in: 'path',
        name: 'itid',
        required: true,
        schema: {
          type: 'number',
        },
        description: 'Team member ID',
      },
    ],
    requestBody: {
      description: 'Team member data to update',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              title: { type: 'string' },
              titleOrder: { type: 'number' },
              name: { type: 'string' },
              designation: { type: 'string' },
              priority: { type: 'number' },
            },
            required: ['id'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Team member updated successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                data: {
                  type: 'object',
                  properties: {
                    id: { type: 'number' },
                    title: { type: 'string' },
                    titleOrder: { type: 'number' },
                    name: { type: 'string' },
                    designation: { type: 'string' },
                    priority: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      },
      400: {
        description: 'Invalid ID or data structure',
      },
      404: {
        description: 'Team member or table not found',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  const id = Number(event.context.params?.itid);
  if (isNaN(id)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid ID provided',
    });
  }

  const body = await readBody(event);
  const updateData = updateTeamMemberSchema.parse(body);
  const tableSlug = 'iqac_team_members' as DynamicTableType;

  const db = useDrizzle();

  // Find the team member record
  const teamMember = await db.query.dynamicTableData.findFirst({
    where: eq(dynamicTableData.id, id),
  });

  if (!teamMember) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Team member not found',
    });
  }

  // Get the table
  const table = await db.query.dynamicTable.findFirst({
    where: eq(dynamicTable.slug, tableSlug),
  });

  if (!table) {
    throw createError({
      statusCode: 404,
      statusMessage: `Table ${tableSlug} not found`,
    });
  }

  // Merge current data with updated data
  const currentData = teamMember.data as IQAC_TEAM_MEMBERS;
  const newData = {
    ...currentData,
    ...(updateData.title !== undefined && { title: updateData.title }),
    ...(updateData.titleOrder !== undefined && {
      titleOrder: updateData.titleOrder,
    }),
    ...(updateData.name !== undefined && { name: updateData.name }),
    ...(updateData.designation !== undefined && {
      designation: updateData.designation,
    }),
    ...(updateData.priority !== undefined && { priority: updateData.priority }),
  };

  // Validate merged data
  if (!validateTableData(tableSlug, newData)) {
    throw createError({
      statusCode: 400,
      statusMessage: `Invalid data structure for table type: ${tableSlug}`,
    });
  }

  // Update the record
  const [updated] = await db
    .update(dynamicTableData)
    .set({
      data: newData,
      updatedAt: Date.now(),
    })
    .where(eq(dynamicTableData.id, id))
    .returning();

  return {
    message: 'Team member updated successfully',
    data: {
      ...updated.data,
      id: updated.id,
    },
  };
});
