import { dynamicTableData } from '~~/server/database/tables/dynamic-table';
import { useDrizzle } from '~~/server/utils/drizzle';
import { eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Delete an IQAC team member',
    tags: ['IQAC'],
    parameters: [
      {
        in: 'path',
        name: 'itid',
        required: true,
        schema: {
          type: 'number',
        },
        description: 'Team member ID',
      },
    ],
    responses: {
      200: {
        description: 'Team member deleted successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                id: { type: 'number' },
              },
              required: ['message', 'id'],
            },
          },
        },
      },
      400: {
        description: 'Invalid ID',
      },
      404: {
        description: 'Team member not found',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  const id = Number(event.context.params?.itid);
  if (isNaN(id)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid ID provided',
    });
  }

  const db = useDrizzle();

  // Find the team member record to confirm it exists
  const teamMember = await db.query.dynamicTableData.findFirst({
    where: eq(dynamicTableData.id, id),
  });

  if (!teamMember) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Team member not found',
    });
  }

  // Delete the record
  await db.delete(dynamicTableData).where(eq(dynamicTableData.id, id));

  return {
    message: 'Team member deleted successfully',
    id,
  };
});
