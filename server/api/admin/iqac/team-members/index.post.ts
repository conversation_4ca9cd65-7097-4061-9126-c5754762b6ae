import {
  dynamicTableData,
  dynamicTable,
} from '~~/server/database/tables/dynamic-table';
import {
  validateTableData,
  type DynamicTableType,
} from '~~/server/database/tables/dynamic-types';
import { eq } from 'drizzle-orm';
import { useDrizzle } from '~~/server/utils/drizzle';
import { teamMemberSchema } from '~~/shared/schema/iqac/team-members/create';

defineRouteMeta({
  openAPI: {
    description: 'Create a new IQAC team member',
    tags: ['IQAC'],
    requestBody: {
      description: 'Team member data',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              titleOrder: { type: 'number' },
              name: { type: 'string' },
              designation: { type: 'string' },
              priority: { type: 'number' },
            },
            required: [
              'title',
              'titleOrder',
              'name',
              'designation',
              'priority',
            ],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Team member created successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                id: { type: 'number' },
              },
              required: ['message', 'id'],
            },
          },
        },
      },
      400: {
        description: 'Invalid data',
      },
      404: {
        description: 'Table not found',
      },
      500: {
        description: 'Server error',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const data = teamMemberSchema.parse(body);
  const tableSlug = 'iqac_team_members' as DynamicTableType;

  if (!validateTableData(tableSlug, data)) {
    throw new Error(`Invalid data structure for table type: ${tableSlug}`);
  }

  const db = useDrizzle();

  let iqacTeamMembersTable = await db
    .select()
    .from(dynamicTable)
    .where(eq(dynamicTable.slug, tableSlug));

  if (iqacTeamMembersTable.length === 0) {
    // Create the table
    iqacTeamMembersTable = await db
      .insert(dynamicTable)
      .values({
        slug: tableSlug,
        name: 'IQAC Team Members',
        type: tableSlug,
      })
      .returning();
  }

  const [created] = await db
    .insert(dynamicTableData)
    .values({
      tableId: iqacTeamMembersTable[0].id,
      data: data,
    })
    .returning();

  return {
    message: 'IQAC Team Member created successfully',
    data: created,
  };
});
