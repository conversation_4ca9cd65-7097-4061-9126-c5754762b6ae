import {
  dynamicTableData,
  dynamicTable,
} from '~~/server/database/tables/dynamic-table';
import type { DynamicTableType } from '~~/server/database/tables/dynamic-types';
import type { IQAC_TEAM_MEMBER_RESPONSE } from '~~/app/types/admin/iqac';

defineRouteMeta({
  openAPI: {
    description: 'Get all IQAC team members',
    tags: ['IQAC'],
    responses: {
      200: {
        description: 'List of all IQAC team members',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number' },
                  title: { type: 'string' },
                  titleOrder: { type: 'number' },
                  name: { type: 'string' },
                  designation: { type: 'string' },
                  priority: { type: 'number' },
                },
                required: [
                  'id',
                  'title',
                  'titleOrder',
                  'name',
                  'designation',
                  'priority',
                ],
              },
            },
          },
        },
      },
      404: {
        description: 'Table not found',
      },
      500: {
        description: 'Server error',
      },
    },
  },
});

export default defineEventHandler(
  async (): Promise<IQAC_TEAM_MEMBER_RESPONSE[]> => {
    const db = useDrizzle();
    const tableSlug = 'iqac_team_members' as DynamicTableType;
    const table = await db.query.dynamicTable.findFirst({
      where: eq(dynamicTable.slug, tableSlug),
    });

    if (!table) {
      throw createError({
        statusCode: 404,
        statusMessage: `Table ${tableSlug} not found`,
      });
    }

    const iqacTeamMembers = await db.query.dynamicTableData.findMany({
      where: eq(dynamicTableData.tableId, table.id),
      columns: {
        createdAt: false,
        updatedAt: false,
        tableId: false,
        id: true,
        data: true,
      },
    });

    return iqacTeamMembers.map((member) => ({
      ...member.data,
      id: member.id,
    })) as IQAC_TEAM_MEMBER_RESPONSE[];
  }
);
