import {
  dynamicTableData,
  dynamicTable,
} from '~~/server/database/tables/dynamic-table';
import { files, tempFiles } from '~~/server/database/tables/files';
import type {
  IQAC_ATTACHMENT_SLUGS,
  DynamicTableDataTypes,
} from '~~/server/database/tables/dynamic-types';
import { validateTableData } from '~~/server/database/tables/dynamic-types';
import { iqacAttachmentItemSchema } from '~~/shared/schema/iqac/attachment/create';
import { eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Create new IQAC attachment item',
    tags: ['IQAC'],
    parameters: [
      {
        in: 'path',
        name: 'slug',
        required: true,
        schema: {
          type: 'string',
        },
        description: 'Attachment category slug',
      },
    ],
    requestBody: {
      description: 'Attachment item data',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              files: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    size: { type: 'number' },
                    type: { type: 'string' },
                    pathname: { type: 'string' },
                    title: { type: 'string' },
                  },
                  required: ['id', 'name', 'size', 'type', 'pathname'],
                },
              },
              linkButton: {
                type: 'object',
                nullable: true,
                properties: {
                  text: { type: 'string' },
                  url: { type: 'string' },
                },
                required: ['text', 'url'],
              },
            },
            required: ['title', 'files'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Attachment item created successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                data: { type: 'object' },
              },
              required: ['message', 'data'],
            },
          },
        },
      },
      400: {
        description: 'Slug is required or invalid data',
      },
      404: {
        description: 'Table not found',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  const slug = getRouterParam(event, 'slug') as IQAC_ATTACHMENT_SLUGS;
  const body = await readBody(event);

  if (!slug) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Slug is required',
    });
  }

  // Parse and validate the attachment data - now as a single item directly
  const attachment = iqacAttachmentItemSchema.parse(body);

  if (!validateTableData('iqac_attachments', { slug, attachment })) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid content',
    });
  }

  const db = useDrizzle();

  // Get or create the dynamic table
  let table = await db
    .select()
    .from(dynamicTable)
    .where(eq(dynamicTable.slug, 'iqac_attachments'));

  if (!table || table.length === 0) {
    table = await db
      .insert(dynamicTable)
      .values({
        slug: 'iqac_attachments',
        name: 'IQAC Attachments',
        type: 'iqac_attachments',
      })
      .returning();
  }

  // Process all files from the attachment
  const filePathnames = attachment.files.map((file) => file.pathname);

  // Insert files into permanent storage
  if (filePathnames.length > 0) {
    const filesToInsert = attachment.files.map((file) => ({
      pathname: file.pathname,
      title: file.title || attachment.title,
      type: file.type,
    }));

    await db.insert(files).values(filesToInsert).returning();
  }

  // Insert the attachment data
  const [created] = await db
    .insert(dynamicTableData)
    .values({
      tableId: table[0].id,
      data: {
        slug,
        title: attachment.title,
        files: attachment.files.map((file) => ({
          ...file,
          title: file.title || '',
        })),
        linkButton: attachment.linkButton || null,
      } as unknown as DynamicTableDataTypes,
    })
    .returning();

  // Handle files cleanup
  if (filePathnames.length > 0) {
    for (const pathname of filePathnames) {
      await db.delete(tempFiles).where(eq(tempFiles.pathname, pathname));
    }
  }

  return {
    message: 'IQAC Attachment created successfully',
    data: created,
  };
});
