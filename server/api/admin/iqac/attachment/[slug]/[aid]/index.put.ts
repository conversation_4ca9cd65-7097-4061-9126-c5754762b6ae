import { dynamicTableData } from '~~/server/database/tables/dynamic-table';
import {
  files as filesTable,
  tempFiles,
} from '~~/server/database/tables/files';
import type {
  IQAC_ATTACHMENT,
  IQAC_ATTACHMENT_SLUGS,
  DynamicTableDataTypes,
} from '~~/server/database/tables/dynamic-types';
import { validateTableData } from '~~/server/database/tables/dynamic-types';
import { iqacAttachmentItemSchema } from '~~/shared/schema/iqac/attachment/create';
import { eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Update an existing IQAC attachment item',
    tags: ['IQAC'],
    parameters: [
      {
        in: 'path',
        name: 'slug',
        required: true,
        schema: {
          type: 'string',
        },
        description: 'Attachment category slug',
      },
      {
        in: 'path',
        name: 'aid',
        required: true,
        schema: {
          type: 'number',
        },
        description: 'Attachment item ID',
      },
    ],
    requestBody: {
      description: 'Attachment item data to update',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              files: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    size: { type: 'number' },
                    type: { type: 'string' },
                    pathname: { type: 'string' },
                    title: { type: 'string' },
                  },
                  required: ['id', 'name', 'size', 'type', 'pathname'],
                },
              },
              linkButton: {
                type: 'object',
                nullable: true,
                properties: {
                  text: { type: 'string' },
                  url: { type: 'string' },
                },
                required: ['text', 'url'],
              },
            },
            required: ['title', 'files'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Attachment item updated successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                data: { type: 'object' },
              },
              required: ['message', 'data'],
            },
          },
        },
      },
      400: {
        description: 'Slug or Attachment ID is required or invalid data',
      },
      404: {
        description: 'Attachment item not found',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  const slug = getRouterParam(event, 'slug') as IQAC_ATTACHMENT_SLUGS;
  const aidParam = getRouterParam(event, 'aid');
  const body = await readBody(event);

  // Parse a single attachment item directly
  const { title, files, linkButton } = iqacAttachmentItemSchema.parse(body);

  if (!slug) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Slug is required',
    });
  }

  if (!aidParam) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Attachment ID is required',
    });
  }

  const attachmentId = parseInt(aidParam, 10);
  if (isNaN(attachmentId)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid Attachment ID',
    });
  }

  if (
    !validateTableData('iqac_attachments', { slug, title, files, linkButton })
  ) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid content',
    });
  }

  const db = useDrizzle();

  // Find the existing attachment record
  const existingAttachment = await db.query.dynamicTableData.findFirst({
    where: eq(dynamicTableData.id, attachmentId),
  });

  if (!existingAttachment) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Attachment not found',
    });
  }

  // Extract existing file information
  const existingData = existingAttachment.data as IQAC_ATTACHMENT;
  const oldFilePathnames = existingData.files.map((file) => file.pathname);
  const newFilePathnames = files.map((file) => file.pathname);

  // Process files: add new files to files table and remove from temp files
  for (const file of files) {
    // Only add files that weren't already in the attachment
    if (!oldFilePathnames.includes(file.pathname)) {
      await db.insert(filesTable).values({
        pathname: file.pathname,
        title: file.title || title,
        type: file.type,
      });

      // Remove from temporary files table
      await db.delete(tempFiles).where(eq(tempFiles.pathname, file.pathname));
    }
  }

  // Remove files that are no longer in the attachment
  for (const oldPathname of oldFilePathnames) {
    if (!newFilePathnames.includes(oldPathname)) {
      await db.delete(filesTable).where(eq(filesTable.pathname, oldPathname));
    }
  }

  // Update the attachment record
  const [updated] = await db
    .update(dynamicTableData)
    .set({
      data: {
        slug,
        title,
        files: files.map((file) => ({
          ...file,
          title: file.title || '',
        })),
        linkButton,
      } as unknown as DynamicTableDataTypes,
    })
    .where(eq(dynamicTableData.id, attachmentId))
    .returning();

  return {
    message: 'IQAC Attachment updated successfully',
    data: updated,
  };
});
