import { dynamicTableData } from '~~/server/database/tables/dynamic-table';
import { files } from '~~/server/database/tables/files';
import type {
  IQAC_ATTACHMENT,
  IQAC_ATTACHMENT_SLUGS,
} from '~~/server/database/tables/dynamic-types';
import { eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Delete an IQAC attachment item',
    tags: ['IQAC'],
    parameters: [
      {
        in: 'path',
        name: 'slug',
        required: true,
        schema: {
          type: 'string',
        },
        description: 'Attachment category slug',
      },
      {
        in: 'path',
        name: 'aid',
        required: true,
        schema: {
          type: 'number',
        },
        description: 'Attachment item ID',
      },
    ],
    responses: {
      200: {
        description: 'Attachment item deleted successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                data: {
                  type: 'object',
                  properties: {
                    id: { type: 'number' },
                  },
                },
              },
              required: ['message', 'data'],
            },
          },
        },
      },
      400: {
        description: 'Slug or Attachment ID is required',
      },
      404: {
        description: 'Attachment item not found',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  const slug = getRouterParam(event, 'slug') as IQAC_ATTACHMENT_SLUGS;
  const aidParam = getRouterParam(event, 'aid');

  if (!slug) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Slug is required',
    });
  }

  if (!aidParam) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Attachment ID is required',
    });
  }

  const attachmentId = parseInt(aidParam, 10);
  if (isNaN(attachmentId)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid Attachment ID',
    });
  }

  const db = useDrizzle();

  // Find the attachment record first
  const attachmentRecord = await db.query.dynamicTableData.findFirst({
    where: eq(dynamicTableData.id, attachmentId),
  });

  // attachment slug is not matching
  if (attachmentRecord) {
    const attachmentData = attachmentRecord.data as IQAC_ATTACHMENT;
    if (attachmentData.slug !== slug) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Attachment not found',
      });
    }
  }

  if (!attachmentRecord) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Attachment not found',
    });
  }

  // Extract file pathnames to delete the file records
  const attachmentData = attachmentRecord.data as IQAC_ATTACHMENT;
  const filePathnames = attachmentData.files.map((file) => file.pathname);

  // Delete the attachment record
  const [deleted] = await db
    .delete(dynamicTableData)
    .where(eq(dynamicTableData.id, attachmentId))
    .returning();

  // Delete all associated file records
  if (filePathnames.length > 0) {
    for (const pathname of filePathnames) {
      await db.delete(files).where(eq(files.pathname, pathname));
    }
  }

  return {
    message: 'IQAC Attachment deleted successfully',
    data: deleted,
  };
});
