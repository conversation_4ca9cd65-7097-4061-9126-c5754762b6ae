import {
  dynamicTableData,
  dynamicTable,
} from '~~/server/database/tables/dynamic-table';
import type { DynamicTableType } from '~~/server/database/tables/dynamic-types';
import type { IQACTeamTitleResponse } from '~~/app/types/admin/iqac';

defineRouteMeta({
  openAPI: {
    description: 'Get all IQAC team titles',
    tags: ['IQAC'],
    responses: {
      200: {
        description: 'List of all IQAC team titles',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number' },
                  title: { type: 'string' },
                  priority: { type: 'number' },
                },
                required: ['id', 'title', 'priority'],
              },
            },
          },
        },
      },
      404: {
        description: 'Table not found',
      },
      500: {
        description: 'Server error',
      },
    },
  },
});

export default defineEventHandler(
  async (): Promise<IQACTeamTitleResponse[]> => {
    const db = useDrizzle();
    const tableSlug = 'iqac_team_titles' as DynamicTableType;
    const table = await db.query.dynamicTable.findFirst({
      where: eq(dynamicTable.slug, tableSlug),
    });

    if (!table) {
      throw createError({
        statusCode: 404,
        statusMessage: `Table ${tableSlug} not found`,
      });
    }

    const iqacTeamTitles = await db.query.dynamicTableData.findMany({
      where: eq(dynamicTableData.tableId, table.id),
      columns: {
        createdAt: false,
        updatedAt: false,
        tableId: false,
        id: true,
        data: true,
      },
    });

    return iqacTeamTitles.map((title) => ({
      ...title.data,
      id: title.id,
    })) as IQACTeamTitleResponse[];
  }
);
