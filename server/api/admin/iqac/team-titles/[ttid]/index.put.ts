import { dynamicTableData } from '~~/server/database/tables/dynamic-table';
import type { DynamicTableDataTypes } from '~~/server/database/tables/dynamic-types';
import { validateTableData } from '~~/server/database/tables/dynamic-types';
import { eq } from 'drizzle-orm';
import { updateTeamTitleSchema } from '~~/shared/schema/iqac/team-title/update';

defineRouteMeta({
  openAPI: {
    description: 'Update an existing IQAC team title',
    tags: ['IQAC'],
    parameters: [
      {
        in: 'path',
        name: 'ttid',
        required: true,
        schema: {
          type: 'string',
        },
        description: 'Team title id',
      },
    ],
    requestBody: {
      description: 'Team title data to update',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              priority: { type: 'number' },
            },
            required: ['title', 'priority'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Team title updated successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                data: { type: 'object' },
              },
              required: ['message', 'data'],
            },
          },
        },
      },
      400: {
        description: 'Team title id is required or invalid data',
      },
      404: {
        description: 'Team title not found',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  const ttidParam = getRouterParam(event, 'ttid');
  const body = await readBody(event);

  // Parse a single attachment item directly
  const { title, priority } = updateTeamTitleSchema.parse(body);

  if (!ttidParam) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Team title id is required',
    });
  }

  const teamTitleId = parseInt(ttidParam, 10);
  if (isNaN(teamTitleId)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid Team title id',
    });
  }

  if (!validateTableData('iqac_team_titles', { title, priority })) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid content',
    });
  }

  const db = useDrizzle();

  // Find the existing team title record
  const existingTeamTitle = await db.query.dynamicTableData.findFirst({
    where: eq(dynamicTableData.id, teamTitleId),
  });

  if (!existingTeamTitle) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Team title not found',
    });
  }

  // Update the team title record
  const [updated] = await db
    .update(dynamicTableData)
    .set({
      data: {
        title,
        priority,
      } as unknown as DynamicTableDataTypes,
    })
    .where(eq(dynamicTableData.id, teamTitleId))
    .returning();

  return {
    message: 'IQAC Team Title updated successfully',
    data: updated,
  };
});
