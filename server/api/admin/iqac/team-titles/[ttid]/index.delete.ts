import {
  dynamicTableData,
  dynamicTable,
} from '~~/server/database/tables/dynamic-table';
import type { IQAC_TEAM_TITLES } from '~~/server/database/tables/dynamic-types';
import type { IQACTeamTitleResponse } from '~~/app/types/admin/iqac';

defineRouteMeta({
  openAPI: {
    description: 'Delete IQAC team title by id',
    tags: ['IQAC'],
    parameters: [
      {
        in: 'path',
        name: 'ttid',
        required: true,
        schema: {
          type: 'number',
        },
        description: 'Team title id',
      },
    ],
    responses: {
      200: {
        description: 'IQAC team title deleted successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                title: { type: 'string' },
              },
              required: ['title', 'image', 'content', 'files', 'linkButton'],
            },
          },
        },
      },
      400: {
        description: 'Slug is required',
      },
      404: {
        description: 'Table not found',
      },
    },
  },
});

export default defineEventHandler(
  async (event): Promise<IQACTeamTitleResponse> => {
    const ttid = getRouterParam(event, 'ttid');

    if (!ttid) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Team title id is required',
      });
    }

    const db = useDrizzle();
    const table = await db.query.dynamicTable.findFirst({
      where: eq(dynamicTable.slug, 'iqac_team_titles'),
    });

    if (!table) {
      throw createError({
        statusCode: 404,
        statusMessage: `Table iqac_team_titles not found`,
      });
    }

    const iqacContent = (await db.query.dynamicTableData.findMany({
      where: eq(dynamicTableData.tableId, table.id),
      columns: {
        createdAt: false,
        updatedAt: false,
        tableId: false,
        id: true,
        data: true,
      },
    })) as {
      id: number;
      data: IQAC_TEAM_TITLES;
    }[];

    // Assert data type for TypeScript
    const content = iqacContent.find(
      (content) => content.id === parseInt(ttid)
    );

    return {
      id: content ? content.id : 0,
      title: content ? content.data.title : '',
      priority: content ? content.data.priority : 0,
    };
  }
);
