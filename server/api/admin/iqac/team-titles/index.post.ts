import {
  dynamicTableData,
  dynamicTable,
} from '~~/server/database/tables/dynamic-table';
import {
  validateTableData,
  type DynamicTableType,
} from '~~/server/database/tables/dynamic-types';
import { eq } from 'drizzle-orm';
import { useDrizzle } from '~~/server/utils/drizzle';
import { teamTitleSchema } from '~~/shared/schema/iqac/team-title/create';

defineRouteMeta({
  openAPI: {
    description: 'Create a new IQAC team title',
    tags: ['IQAC'],
    requestBody: {
      description: 'Team title data',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              priority: { type: 'number' },
            },
            required: ['title', 'priority'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Team title created successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                id: { type: 'number' },
              },
              required: ['message', 'id'],
            },
          },
        },
      },
      400: {
        description: 'Invalid data',
      },
      404: {
        description: 'Table not found',
      },
      500: {
        description: 'Server error',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const data = teamTitleSchema.parse(body);
  const tableSlug = 'iqac_team_titles' as DynamicTableType;

  if (!validateTableData(tableSlug, data)) {
    throw new Error(`Invalid data structure for table type: ${tableSlug}`);
  }

  const db = useDrizzle();

  let iqacTeamTitlesTable = await db
    .select()
    .from(dynamicTable)
    .where(eq(dynamicTable.slug, tableSlug));

  if (iqacTeamTitlesTable.length === 0) {
    // Create the table
    iqacTeamTitlesTable = await db
      .insert(dynamicTable)
      .values({
        slug: tableSlug,
        name: 'IQAC Team Titles',
        type: tableSlug,
      })
      .returning();
  }

  const [created] = await db
    .insert(dynamicTableData)
    .values({
      tableId: iqacTeamTitlesTable[0].id,
      data: data,
    })
    .returning();

  return {
    message: 'IQAC Team Title created successfully',
    data: created,
  };
});
