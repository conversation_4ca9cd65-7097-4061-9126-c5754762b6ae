import {
  dynamicTableData,
  dynamicTable,
} from '~~/server/database/tables/dynamic-table';
import { files, tempFiles } from '~~/server/database/tables/files';
import type { IQAC_DOWNLOAD_SLUGS } from '~~/server/database/tables/dynamic-types';
import { validateTableData } from '~~/server/database/tables/dynamic-types';
import { downloadSchema } from '~~/shared/schema/iqac/download/create';
import { eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Create a new IQAC download item',
    tags: ['IQAC'],
    parameters: [
      {
        in: 'path',
        name: 'slug',
        required: true,
        schema: {
          type: 'string',
        },
        description: 'Download category slug',
      },
    ],
    requestBody: {
      description: 'Download item data',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              file: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  size: { type: 'number' },
                  type: { type: 'string' },
                },
                required: ['id', 'name', 'size', 'type'],
              },
            },
            required: ['title', 'file'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Download item created successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                id: { type: 'number' },
              },
              required: ['message', 'id'],
            },
          },
        },
      },
      400: {
        description: 'Slug is required or invalid data',
      },
      404: {
        description: 'Table not found',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  const slug = getRouterParam(event, 'slug') as IQAC_DOWNLOAD_SLUGS;
  const body = await readBody(event);
  const { file, title } = downloadSchema.parse(body);
  if (!slug) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Slug is required',
    });
  }

  console.log({ slug, title, file });

  if (!validateTableData('iqac_downloads', { slug, title, file })) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid content',
    });
  }

  const db = useDrizzle();

  let table = await db
    .select()
    .from(dynamicTable)
    .where(eq(dynamicTable.slug, 'iqac_downloads'));

  if (!table) {
    table = await db.insert(dynamicTable).values({
      slug: 'iqac_downloads',
      name: 'IQAC Downloads',
      type: 'iqac_downloads',
    });
  }

  await db
    .insert(files)
    .values({
      pathname: file.pathname,
      title: file.title,
      type: file.type,
    })
    .returning();

  const [created] = await db
    .insert(dynamicTableData)
    .values({
      tableId: table[0].id,
      data: {
        slug,
        title,
        file,
      },
    })
    .returning();

  await db.delete(tempFiles).where(eq(tempFiles.pathname, file.pathname));

  return {
    message: 'IQAC Downloads created successfully',
    data: created,
  };
});
