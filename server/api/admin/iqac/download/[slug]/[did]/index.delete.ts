import { dynamicTableData } from '~~/server/database/tables/dynamic-table';
import { files } from '~~/server/database/tables/files';
import type {
  IQAC_DOWNLOAD,
  IQAC_DOWNLOAD_SLUGS,
} from '~~/server/database/tables/dynamic-types';
import { eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Delete an IQAC download item',
    tags: ['IQAC'],
    parameters: [
      {
        in: 'path',
        name: 'slug',
        required: true,
        schema: {
          type: 'string',
        },
        description: 'Download category slug',
      },
      {
        in: 'path',
        name: 'did',
        required: true,
        schema: {
          type: 'number',
        },
        description: 'Download item ID',
      },
    ],
    responses: {
      200: {
        description: 'Download item deleted successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                id: { type: 'number' },
              },
              required: ['message', 'id'],
            },
          },
        },
      },
      400: {
        description: 'Slug or Download ID is required',
      },
      404: {
        description: 'Download item not found',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  const slug = getRouterParam(event, 'slug') as IQAC_DOWNLOAD_SLUGS;
  const didParam = getRouterParam(event, 'did');

  if (!slug) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Slug is required',
    });
  }

  if (!didParam) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Download ID is required',
    });
  }

  const downloadId = parseInt(didParam, 10);
  if (isNaN(downloadId)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid Download ID',
    });
  }

  const db = useDrizzle();

  // Find the download record first
  const downloadRecord = await db.query.dynamicTableData.findFirst({
    where: eq(dynamicTableData.id, downloadId),
  });

  if (!downloadRecord) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Download not found',
    });
  }

  // download slug is not matching
  if (downloadRecord) {
    const downloadData = downloadRecord.data as IQAC_DOWNLOAD;
    if (downloadData.slug !== slug) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Download not found',
      });
    }
  }
  // Extract file pathname to delete the file record
  const downloadData = downloadRecord.data as IQAC_DOWNLOAD;
  const filePathname = downloadData.file?.pathname;

  // Delete the download record
  const [deleted] = await db
    .delete(dynamicTableData)
    .where(eq(dynamicTableData.id, downloadId))
    .returning();

  // If file exists, delete the file record as well
  if (filePathname) {
    await db.delete(files).where(eq(files.pathname, filePathname));
  }

  return {
    message: 'IQAC Download deleted successfully',
    data: deleted,
  };
});
