import { dynamicTableData } from '~~/server/database/tables/dynamic-table';
import { files, tempFiles } from '~~/server/database/tables/files';
import type {
  IQAC_DOWNLOAD,
  IQAC_DOWNLOAD_SLUGS,
} from '~~/server/database/tables/dynamic-types';
import { validateTableData } from '~~/server/database/tables/dynamic-types';
import { downloadSchema } from '~~/shared/schema/iqac/download/create';
import { eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Update an existing IQAC download item',
    tags: ['IQAC'],
    parameters: [
      {
        in: 'path',
        name: 'slug',
        required: true,
        schema: {
          type: 'string',
        },
        description: 'Download category slug',
      },
      {
        in: 'path',
        name: 'did',
        required: true,
        schema: {
          type: 'number',
        },
        description: 'Download item ID',
      },
    ],
    requestBody: {
      description: 'Download item data to update',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              file: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  size: { type: 'number' },
                  type: { type: 'string' },
                },
                required: ['id', 'name', 'size', 'type'],
              },
            },
            required: ['title', 'file'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Download item updated successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                id: { type: 'number' },
              },
              required: ['message', 'id'],
            },
          },
        },
      },
      400: {
        description: 'Slug or Download ID is required or invalid data',
      },
      404: {
        description: 'Download item not found',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  const slug = getRouterParam(event, 'slug') as IQAC_DOWNLOAD_SLUGS;
  const didParam = getRouterParam(event, 'did');
  const body = await readBody(event);
  const { file, title } = downloadSchema.parse(body);

  if (!slug) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Slug is required',
    });
  }

  if (!didParam) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Download ID is required',
    });
  }

  const downloadId = parseInt(didParam, 10);
  if (isNaN(downloadId)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid Download ID',
    });
  }

  if (!validateTableData('iqac_downloads', { slug, title, file })) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid content',
    });
  }

  const db = useDrizzle();

  // Find the existing download record
  const existingDownload = await db.query.dynamicTableData.findFirst({
    where: eq(dynamicTableData.id, downloadId),
  });

  if (!existingDownload) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Download not found',
    });
  }

  // Extract existing file information
  const existingData = existingDownload.data as IQAC_DOWNLOAD;
  const oldFilePathname = existingData.file?.pathname;

  // If a new file is uploaded, add it to the files table and remove the old file if exists
  if (file.pathname !== oldFilePathname) {
    // Add new file
    await db.insert(files).values({
      pathname: file.pathname,
      title: file.title,
      type: file.type,
    });

    // Remove the temporary file
    await db.delete(tempFiles).where(eq(tempFiles.pathname, file.pathname));

    // Remove old file if it exists
    if (oldFilePathname) {
      await db.delete(files).where(eq(files.pathname, oldFilePathname));
    }
  }

  // Update the download record
  const [updated] = await db
    .update(dynamicTableData)
    .set({
      data: {
        slug,
        title,
        file,
      },
    })
    .where(eq(dynamicTableData.id, downloadId))
    .returning();

  return {
    message: 'IQAC Download updated successfully',
    data: updated,
  };
});
