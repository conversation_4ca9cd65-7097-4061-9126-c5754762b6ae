import {
  dynamicTableData,
  dynamicTable,
} from '~~/server/database/tables/dynamic-table';
import type {
  IQAC_DOWNLOAD_SLUGS,
  IQAC_DOWNLOAD,
} from '~~/server/database/tables/dynamic-types';
import type { IQACDownloadsResponse } from '~~/app/types/admin/iqac';
import { paginationSchema } from '~~/shared/schema/pagination';
import { sql, and, eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Get IQAC downloads by category slug with pagination',
    tags: ['IQAC'],
    parameters: [
      {
        in: 'path',
        name: 'slug',
        required: true,
        schema: {
          type: 'string',
        },
        description: 'Download category slug',
      },
      {
        in: 'query',
        name: 'page',
        schema: {
          type: 'integer',
          default: 1,
        },
        description: 'Page number for pagination',
      },
      {
        in: 'query',
        name: 'limit',
        schema: {
          type: 'integer',
          default: 10,
        },
        description: 'Number of items per page',
      },
    ],
    responses: {
      200: {
        description: 'List of IQAC downloads for the specified category',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'number' },
                      title: { type: 'string' },
                      description: { type: 'string' },
                      fileUrl: { type: 'string' },
                      createdAt: { type: 'string', format: 'date-time' },
                    },
                  },
                },
                meta: {
                  type: 'object',
                  properties: {
                    total: { type: 'number' },
                    page: { type: 'number' },
                    limit: { type: 'number' },
                    lastPage: { type: 'number' },
                  },
                },
              },
              required: ['data', 'meta'],
            },
          },
        },
      },
      400: {
        description: 'Slug is required or invalid pagination parameters',
      },
      404: {
        description: 'Table not found',
      },
    },
  },
});

export default defineEventHandler(
  async (event): Promise<IQACDownloadsResponse> => {
    const slug = getRouterParam(event, 'slug') as IQAC_DOWNLOAD_SLUGS;
    const query = getQuery(event);
    const { page, limit } = await paginationSchema.parseAsync(query);
    const offset = (page - 1) * limit;

    if (!slug) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Slug is required',
      });
    }

    const db = useDrizzle();
    const table = await db.query.dynamicTable.findFirst({
      where: eq(dynamicTable.slug, 'iqac_downloads'),
    });

    if (!table) {
      throw createError({
        statusCode: 404,
        statusMessage: `Table iqac_downloads not found`,
      });
    }

    // Get total count for pagination with slug filter
    const totalCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(dynamicTableData)
      .where(
        and(
          eq(dynamicTableData.tableId, table.id),
          sql`json_extract(${dynamicTableData.data}, '$.slug') = ${slug}`
        )
      )
      .then((result) => result[0].count);

    const iqacContent = await db.query.dynamicTableData.findMany({
      where: and(
        eq(dynamicTableData.tableId, table.id),
        sql`json_extract(${dynamicTableData.data}, '$.slug') = ${slug}`
      ),
      columns: {
        createdAt: false,
        updatedAt: false,
        tableId: false,
        id: true,
        data: true,
      },
      limit,
      offset,
    });

    const list = iqacContent.map((content) => {
      return {
        id: content.id,
        title: (content.data as IQAC_DOWNLOAD).title,
        file: (content.data as IQAC_DOWNLOAD).file,
      };
    });

    const totalPages = Math.ceil(totalCount / limit);

    return {
      downloads: list,
      pagination: {
        page,
        limit,
        totalItems: totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    };
  }
);
