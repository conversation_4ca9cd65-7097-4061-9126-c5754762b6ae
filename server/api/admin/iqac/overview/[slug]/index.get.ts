import {
  dynamicTableData,
  dynamicTable,
} from '~~/server/database/tables/dynamic-table';
import type {
  IQAC_OVERVIEW,
  IQAC_OVERVIEW_SLUGS,
} from '~~/server/database/tables/dynamic-types';
import type { IQACOverviewResponse } from '~~/app/types/admin/iqac';

defineRouteMeta({
  openAPI: {
    description: 'Get IQAC content by slug',
    tags: ['IQAC'],
    parameters: [
      {
        in: 'path',
        name: 'slug',
        required: true,
        schema: {
          type: 'string',
        },
        description: 'Content slug identifier',
      },
    ],
    responses: {
      200: {
        description: 'IQAC content data',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                title: { type: 'string' },
                image: { type: 'object' },
                content: { type: 'string' },
                files: { type: 'array', items: { type: 'object' } },
                linkButton: { type: 'object' },
              },
              required: ['title', 'image', 'content', 'files', 'linkButton'],
            },
          },
        },
      },
      400: {
        description: 'Slug is required',
      },
      404: {
        description: 'Table not found',
      },
    },
  },
});

export default defineEventHandler(
  async (event): Promise<IQACOverviewResponse> => {
    const slug = getRouterParam(event, 'slug') as IQAC_OVERVIEW_SLUGS;

    if (!slug) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Slug is required',
      });
    }

    const db = useDrizzle();
    const table = await db.query.dynamicTable.findFirst({
      where: eq(dynamicTable.slug, 'iqac_overview'),
    });

    if (!table) {
      throw createError({
        statusCode: 404,
        statusMessage: `Table iqac_overview not found`,
      });
    }

    const iqacContent = (await db.query.dynamicTableData.findMany({
      where: eq(dynamicTableData.tableId, table.id),
      columns: {
        createdAt: false,
        updatedAt: false,
        tableId: false,
        id: true,
        data: true,
      },
    })) as {
      id: number;
      data: IQAC_OVERVIEW;
    }[];

    // Assert data type for TypeScript
    const content = iqacContent.find((content) => content.data.slug === slug);

    return {
      title: content ? content.data.title : '',
      image: content ? content.data.image : null,
      content: content ? content.data.content : '',
      files: content ? content.data.files : [],
      linkButton: content ? content.data.linkButton : null,
    };
  }
);
