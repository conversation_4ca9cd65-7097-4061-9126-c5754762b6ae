import {
  dynamicTableData,
  dynamicTable,
} from '~~/server/database/tables/dynamic-table';
import type {
  IQAC_OVERVIEW_SLUGS,
  IQAC_OVERVIEW,
} from '~~/server/database/tables/dynamic-types';
import { validateTableData } from '~~/server/database/tables/dynamic-types';
import { iqacOverviewSchema } from '~~/shared/schema/iqac/overview/create';
import { eq, inArray } from 'drizzle-orm';
import type { FileData } from '~/types/home';
import { tempFiles } from '~~/server/database/tables/files';

defineRouteMeta({
  openAPI: {
    description: 'Update IQAC overview by slug',
    tags: ['IQAC'],
    parameters: [
      {
        in: 'path',
        name: 'slug',
        required: true,
        schema: {
          type: 'string',
        },
        description: 'Overview slug identifier',
      },
    ],
    requestBody: {
      description: 'IQAC overview data',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              image: { type: 'object' },
              content: { type: 'string' },
              files: { type: 'array', items: { type: 'object' } },
              linkButton: { type: 'object' },
            },
            required: ['title', 'image', 'content', 'files', 'linkButton'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Overview updated successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                id: { type: 'number' },
              },
              required: ['message', 'id'],
            },
          },
        },
      },
      400: {
        description: 'Slug is required or invalid data',
      },
      404: {
        description: 'Table not found',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  const slug = getRouterParam(event, 'slug') as IQAC_OVERVIEW_SLUGS;
  const body = await readBody(event);
  const parsedBody = iqacOverviewSchema.parse(body);
  const { title, image, content, files, linkButton } = parsedBody;

  if (!slug) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Slug is required',
    });
  }

  // Collect pathnames of all temp files to delete later
  const tempPathnames: string[] = [];

  // Create compatible image data
  let imageData: FileData | null = null;
  if (image) {
    imageData = {
      pathname: image.pathname || '',
      title: image.title || '',
      type: image.type || '',
      prefix: image.prefix || 'iqac-overview',
    };

    if (image.pathname) {
      tempPathnames.push(image.pathname);
    }
  }

  // Create compatible files data
  const filesData: FileData[] = Array.isArray(files)
    ? files.map((file) => {
        if (file.pathname) {
          tempPathnames.push(file.pathname);
        }
        return {
          pathname: file.pathname || '',
          title: file.title || '',
          type: file.type || '',
          prefix: file.prefix || 'iqac-overview',
        };
      })
    : [];

  // Create compatible linkButton data
  const linkButtonData = linkButton || {
    type: 'link',
    title: 'View More',
    style: 'primary',
    internalLink: null,
    externalLink: null,
    newTab: false,
  };

  // Ensure all required fields are non-null to match IQAC_OVERVIEW type
  const data: IQAC_OVERVIEW = {
    slug,
    title: title || '',
    image: imageData,
    content: content || '',
    files: filesData,
    linkButton: linkButtonData,
  };

  if (!validateTableData('iqac_overview', data)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid overview',
    });
  }

  const db = useDrizzle();

  let table = await db
    .select()
    .from(dynamicTable)
    .where(eq(dynamicTable.slug, 'iqac_overview'));

  if (!table) {
    table = await db.insert(dynamicTable).values({
      slug: 'iqac_overview',
      name: 'IQAC Overview',
      type: 'iqac_overview',
    });
  }

  // Check if content with this slug already exists
  const existingContent = await db.query.dynamicTableData.findMany({
    where: eq(dynamicTableData.tableId, table[0].id),
    columns: {
      id: true,
      data: true,
    },
  });

  const matchingContent = existingContent.find(
    (content) => (content.data as IQAC_OVERVIEW).slug === slug
  );

  let result;

  if (matchingContent) {
    // Update existing content
    const [updated] = await db
      .update(dynamicTableData)
      .set({ data })
      .where(eq(dynamicTableData.id, matchingContent.id))
      .returning();

    result = {
      message: 'IQAC Overview updated successfully',
      data: updated,
    };
  } else {
    // Create new content
    const [created] = await db
      .insert(dynamicTableData)
      .values({
        tableId: table[0].id,
        data,
      })
      .returning();

    result = {
      message: 'IQAC Overview created successfully',
      data: created,
    };
  }

  // Delete temporary files after processing
  if (tempPathnames.length > 0) {
    await db
      .delete(tempFiles)
      .where(inArray(tempFiles.pathname, tempPathnames));
  }

  return result;
});
