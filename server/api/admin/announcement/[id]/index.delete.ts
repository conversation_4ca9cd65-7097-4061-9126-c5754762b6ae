import { eq } from 'drizzle-orm';
import { announcement } from '~~/server/database/schema';
import { idSchema } from '~~/shared/schema';

export default defineEventHandler(async (event) => {
  try {
    const params = getRouterParams(event);
    const { id } = idSchema.parse(params);

    const db = useDrizzle();

    await db.delete(announcement).where(eq(announcement.id, id));

    return {
      success: true,
      message: 'Announcement deleted successfully',
    };
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
