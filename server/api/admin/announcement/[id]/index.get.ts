import { eq } from 'drizzle-orm';
import { announcement } from '~~/server/database/schema';
import { idSchema } from '~~/shared/schema';
import { z } from 'zod';

export default defineEventHandler(async (event) => {
  try {
    const params = getRouterParams(event);
    const { id } = idSchema.parse(params);

    const validatedId = z.number().int().positive().safeParse(Number(id));
    if (!validatedId.success) {
      throw createError({
        statusCode: 400,
        message: 'Invalid announcement ID format',
      });
    }

    const db = useDrizzle();

    const announcementData = await db.query.announcement.findFirst({
      where: eq(announcement.id, id),
      with: {
        files: {
          columns: {
            id: true,
          },
          with: {
            file: {
              columns: {
                pathname: true,
                title: true,
                type: true,
                id: true,
              },
            },
          },
        },
        button: {
          columns: {
            announcementId: false,
            cardTemplateId: false,
          },
        },
      },
    });

    if (!announcementData) {
      throw createError({
        statusCode: 404,
        message: 'Announcement not found',
      });
    }

    return {
      success: true,
      data: announcementData,
    };
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
