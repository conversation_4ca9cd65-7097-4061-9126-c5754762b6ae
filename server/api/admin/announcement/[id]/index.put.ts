import { announcement, announcementFiles } from '~~/server/database/schema';
import { files, tempFiles } from '~~/server/database/tables/files';
import { linkButton } from '~~/server/database/tables/buttons/linkButton';
import { idSchema } from '~~/shared/schema';
import { updateAnnouncementSchema } from '~~/shared/schema/announcement/update';
import { inArray, eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const params = getRouterParams(event);
    const { id } = idSchema.parse(params);

    const body = await readBody(event);
    const validatedData = updateAnnouncementSchema.parse(body);
    const db = useDrizzle();

    await db
      .update(announcement)
      .set({
        title: validatedData.title,
        content: validatedData.content,
        link: validatedData.link,
        eventDate: validatedData.eventDate
          ? new Date(validatedData.eventDate).getTime()
          : null,
        scheduledAt: new Date(validatedData.scheduledAt).getTime(),
        expiresAt: new Date(validatedData.expiresAt).getTime(),
        isActive: validatedData.isActive,
        priority: validatedData.priority,
        announcementType: validatedData.announcementType,
      })
      .where(eq(announcement.id, id));

    if (validatedData.files && validatedData.files.length > 0) {
      const currentAnnouncementFiles = await db
        .select()
        .from(announcementFiles)
        .where(eq(announcementFiles.announcementId, id));

      const pathnames = validatedData.files.map((f) => f.pathname);
      const currentFileIds = currentAnnouncementFiles.map((f) => f.fileId);

      const currentFiles = await db
        .select()
        .from(files)
        .where(inArray(files.id, currentFileIds));

      const unwantedFiles = currentFiles
        .filter((f) => !pathnames.includes(f.pathname))
        .map((f) => f.pathname);

      if (currentFiles.length > 0) {
        await db
          .delete(announcementFiles)
          .where(eq(announcementFiles.announcementId, id));
      }

      const insertedFiles = await db
        .insert(files)
        .values(
          validatedData.files.map((file) => ({
            pathname: file.pathname,
            title: file.title,
            prefix: file.prefix || '',
            type: file.type,
          }))
        )
        .returning();

      await db.insert(announcementFiles).values(
        insertedFiles.map((file) => ({
          announcementId: id,
          fileId: file.id,
          fileType: file.type,
        }))
      );

      await db.delete(tempFiles).where(inArray(tempFiles.pathname, pathnames));

      if (unwantedFiles.length > 0) {
        await hubBlob().del(unwantedFiles);
      }
    }

    if (validatedData.button !== undefined) {
      if (validatedData.button) {
        // First check if a button exists
        const existingButton = await db
          .select()
          .from(linkButton)
          .where(eq(linkButton.announcementId, id))
          .get();

        if (existingButton) {
          // Update existing button
          await db
            .update(linkButton)
            .set({
              ...validatedData.button,
              announcementId: id,
            })
            .where(eq(linkButton.announcementId, id));
        } else {
          // Insert new button
          await db.insert(linkButton).values({
            ...validatedData.button,
            announcementId: id,
          });
        }
      } else {
        // Delete button if it exists
        await db.delete(linkButton).where(eq(linkButton.announcementId, id));
      }
    }

    const newAnnouncement = await db
      .select()
      .from(announcement)
      .where(eq(announcement.id, id))
      .get();

    return { success: true, data: newAnnouncement };
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
