import { inArray } from 'drizzle-orm';
import {
  announcement,
  announcementFiles,
} from '~~/server/database/tables/announcement';
import { linkButton } from '~~/server/database/tables/buttons/linkButton';
import { files, tempFiles } from '~~/server/database/tables/files';
import { createAnnouncementSchema } from '~~/shared/schema/announcement/create';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const validatedData = createAnnouncementSchema.parse(body);
    const db = useDrizzle();

    // Create the announcement
    const [newAnnouncement] = await db
      .insert(announcement)
      .values({
        title: validatedData.title,
        content: validatedData.content ?? '',
        link: validatedData.link,
        eventDate: validatedData.eventDate?.getTime() ?? null,
        scheduledAt: validatedData.scheduledAt.getTime(),
        expiresAt: validatedData.expiresAt.getTime(),
        isActive: validatedData.isActive,
        priority: validatedData.priority,
        announcementType: validatedData.announcementType,
      })
      .returning();

    // Create button if provided
    if (validatedData.button) {
      await db.insert(linkButton).values({
        ...validatedData.button,
        announcementId: newAnnouncement.id,
      });
    }

    // Handle file attachments if provided
    if (validatedData.files && validatedData.files.length > 0) {
      // Get temp files data to be able to delete them later
      const pathnames = validatedData.files.map((f) => f.pathname);

      // First, create file records
      const fileRecords = await db
        .insert(files)
        .values(
          validatedData.files.map((file) => ({
            pathname: file.pathname,
            title: file.title,
            prefix: file.prefix ?? '',
            type: file.type,
          }))
        )
        .returning();

      await db.delete(tempFiles).where(inArray(tempFiles.pathname, pathnames));

      // Then create the relationships in the junction table
      await db.insert(announcementFiles).values(
        fileRecords.map((file) => ({
          announcementId: newAnnouncement.id,
          fileId: file.id,
          fileType: file.type,
        }))
      );

      // // Delete temp files from storage and database
      // const tempFileRecords = await db.select()
      //   .from(tempFiles)
      //   .where(inArray(tempFiles.pathname, pathnames))
      // await Promise.all(tempFileRecords.map(async (tempFile) => {
      //   try {
      //     // Delete from blob storage
      //     await hubBlob().del(tempFile.pathname)

      //     // Delete from temp files table
      //     await db.delete(tempFiles)
      //       .where(eq(tempFiles.pathname, tempFile.pathname))
      //   }
      //   catch (error) {
      //     console.error(`Failed to delete temp file ${tempFile.pathname}:`, error)
      //   }
      // }))
    }

    return { success: true, data: newAnnouncement };
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
