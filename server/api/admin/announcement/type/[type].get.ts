import type { SQL } from 'drizzle-orm';
import { and, eq, gte, lt, lte } from 'drizzle-orm';
import { announcement } from '~~/server/database/schema';

export default defineEventHandler(async (event) => {
  try {
    const db = useDrizzle();
    const query = getQuery(event);
    const type = getRouterParam(event, 'type') as 'notice' | 'update' | 'event';
    const filterStatus = query.filterStatus as string | undefined;

    const currentTimestamp = Date.now();
    const conditions: SQL[] = [];

    // Type filter
    if (type) {
      conditions.push(eq(announcement.announcementType, type));
    }

    // Status filter
    if (filterStatus && filterStatus !== 'all') {
      switch (filterStatus) {
        case 'active': {
          conditions.push(
            eq(announcement.isActive, true),
            lte(announcement.scheduledAt, currentTimestamp),
            gte(announcement.expiresAt, currentTimestamp)
          );
          break;
        }
        case 'inactive': {
          conditions.push(eq(announcement.isActive, false));
          break;
        }
        case 'expired': {
          conditions.push(lt(announcement.expiresAt, currentTimestamp));
          break;
        }
      }
    }

    const announcementsList = await db.query.announcement.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
      with: {
        button: true,
        files: {
          with: {
            file: {
              columns: {
                id: true,
                pathname: true,
                title: true,
                type: true,
              },
            },
          },
        },
      },
      orderBy: (announcement, { desc }) => [desc(announcement.priority)],
    });

    return {
      success: true,
      data: announcementsList,
    };
  } catch (error) {
    console.error('Error fetching announcements:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch announcements',
    });
  }
});
