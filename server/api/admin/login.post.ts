import { z } from 'zod';
import type { H3Event } from 'h3';

// Login validation schema
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
});

export default defineEventHandler(async (event) => {
  try {
    // Parse and validate request body
    const body = await readBody(event);
    const validatedData = loginSchema.parse(body);

    const { username, password } = validatedData;

    // Verify admin credentials
    const admin = await verifyAdminCredentials(username, password);

    if (!admin) {
      throw createError({
        statusCode: 401,
        message: 'Invalid credentials',
      });
    }

    // Update last login time
    await updateAdminLastLogin(admin.id);

    // Set user session
    await setUserSession(event as unknown as H3Event, {
      user: {
        id: admin.id,
        username: admin.username,
        email: admin.email,
        fullName: admin.fullName,
        role: admin.role,
      },
      loggedInAt: new Date().toISOString(),
    });

    return {
      success: true,
    };
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
