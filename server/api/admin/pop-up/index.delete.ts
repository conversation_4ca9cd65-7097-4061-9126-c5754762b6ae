import { popUp } from '~~/server/database/tables/files';
import { eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Delete pop-up notification',
    tags: ['Admin'],
    responses: {
      200: {
        description: 'Pop-up deleted successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
              },
              required: ['message'],
            },
          },
        },
      },
      404: {
        description: 'Pop-up not found',
      },
    },
  },
});

export default defineEventHandler(async () => {
  const db = useDrizzle();
  const existingPopUp = await db.query.popUp.findFirst();

  if (!existingPopUp) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Pop-up not found',
    });
  }
  // Find and delete the pop-up file
  const [deletedFile] = await db
    .delete(popUp)
    .where(eq(popUp.id, existingPopUp.id))
    .returning();

  if (!deletedFile) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Pop-up not found',
    });
  }

  return {
    message: 'Pop-up deleted successfully',
  };
});
