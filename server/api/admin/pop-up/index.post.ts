import { eq } from 'drizzle-orm';
import { popUp } from '~~/server/database/tables/files';
import { fileSchema } from '~~/shared/schema';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);

    // Validate the request body
    const validatedData = fileSchema.parse(body);

    // Always set the title to 'Pop-Up Notification'
    const popUpData = {
      pathname: validatedData.pathname,
      title: 'Pop-Up Notification',
      type: validatedData.type,
      prefix: 'pop-up',
    };

    const db = useDrizzle();

    // Check if a pop-up already exists
    const existingPopUp = await db.query.popUp.findFirst();

    if (existingPopUp) {
      // Delete the existing file from blob storage
      try {
        await hubBlob().del(existingPopUp.pathname);
      } catch (error) {
        console.error(
          'Error deleting existing pop-up file from blob storage:',
          error
        );
        // Continue with the update even if blob deletion fails
      }

      // Update the existing pop-up
      await db
        .update(popUp)
        .set({
          pathname: popUpData.pathname,
          type: popUpData.type,
        })
        .where(eq(popUp.id, existingPopUp.id));

      return { success: true, message: 'Pop-up updated successfully' };
    } else {
      // Create a new pop-up
      await db.insert(popUp).values({
        pathname: popUpData.pathname,
        title: popUpData.title,
        type: popUpData.type,
        prefix: popUpData.prefix,
      });

      return { success: true, message: 'Pop-up created successfully' };
    }
  } catch (error) {
    console.error('Error creating/updating pop-up:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to create/update pop-up',
    });
  }
});
