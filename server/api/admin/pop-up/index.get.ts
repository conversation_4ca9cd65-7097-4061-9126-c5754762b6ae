import { eq, desc } from 'drizzle-orm';
import { popUp } from '~~/server/database/tables/files';

export default defineEventHandler(async (_event) => {
  try {
    const db = useDrizzle();

    // Use the imported popUp table in the query
    const popUpData = await db
      .select()
      .from(popUp)
      .where(eq(popUp.title, 'Pop-Up Notification'))
      .orderBy(desc(popUp.createdAt))
      .limit(1);

    return popUpData[0] || null;
  } catch (error) {
    console.error('Error fetching pop-up data:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch pop-up data',
    });
  }
});
