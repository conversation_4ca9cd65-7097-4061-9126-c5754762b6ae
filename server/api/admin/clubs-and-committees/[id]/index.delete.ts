import { eq } from 'drizzle-orm';
import { dynamicTable, dynamicTableData } from '~~/server/database/tables';

export default defineEventHandler(async (event) => {
  const id = getRouterParam(event, 'id');

  if (!id) {
    throw createError({
      statusCode: 400,
      statusMessage: 'ID is required',
    });
  }

  const db = useDrizzle();

  // Step 1: Find the table by slug
  const table = await db
    .select()
    .from(dynamicTable)
    .where(eq(dynamicTable.slug, 'club_and_committee'))
    .get(); // Use .get() to fetch a single record

  if (!table) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Table not found',
    });
  }

  // Step 2: Fetch the data by ID
  const dataRecord = await db.query.dynamicTableData.findFirst({
    where: eq(dynamicTableData.id, parseInt(id)),
  });

  if (!dataRecord) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Record not found',
    });
  }

  // Step 3: Validate the table type
  if (dataRecord.tableId !== table.id) {
    throw createError({
      statusCode: 403,
      statusMessage:
        'This record does not belong to the club_and_committee table',
    });
  }

  // Step 4: Delete the data
  await db
    .delete(dynamicTableData)
    .where(eq(dynamicTableData.id, parseInt(id)));

  return {
    message: 'Record deleted successfully',
    data: dataRecord,
  };
});
