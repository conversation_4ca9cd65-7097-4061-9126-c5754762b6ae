import { eq, inArray } from 'drizzle-orm';
import type { FileData } from '~/types/home';
import { dynamicTable, dynamicTableData } from '~~/server/database/tables';
import { tempFiles } from '~~/server/database/tables/files';
import type {
  CLUB_COMMITTEE,
  DynamicTableType,
} from '~~/server/database/tables/dynamic-types';
import { validateTableData } from '~~/server/database/tables/dynamic-types';
import { updateClubSchema } from '~~/shared/schema/clubs-and-committees/update';

export default defineEventHandler(async (event) => {
  // Get the ID from the route parameter
  const cid = getRouterParam(event, 'id');
  const tableSlug = 'club_and_committee' as DynamicTableType;
  if (!cid) {
    throw createError({
      statusCode: 400,
      statusMessage: 'ID is required',
    });
  }

  const body = await readBody(event);
  const parsedBody = updateClubSchema.parse(body);
  const {
    overview,
    title,
    attachments,
    linkButton,
    galleries,
    events,
    type,
    image,
    slug, // Slug is now part of the parsed body
  } = parsedBody;

  // Collect pathnames of all temp files to delete later
  const tempPathnames: string[] = [];

  // Create compatible image data
  let imageData: FileData | null = null;
  if (image) {
    imageData = {
      pathname: image.pathname || '',
      title: image.title || '',
      type: image.type || '',
      prefix: image.prefix || '',
    };

    if (image.pathname) {
      tempPathnames.push(image.pathname);
    }
  }

  // Create compatible files data
  const filesData: FileData[] = Array.isArray(attachments)
    ? attachments.map((file) => {
        if (file.pathname) {
          tempPathnames.push(file.pathname);
        }
        return {
          pathname: file.pathname || '',
          title: file.title || '',
          type: file.type || '',
          prefix: file.prefix || 'club-and-committee',
        };
      })
    : [];

  // Create compatible linkButton data
  const linkButtonData = linkButton || {
    type: 'link',
    title: 'View More',
    style: 'primary',
    internalLink: null,
    externalLink: null,
    newTab: false,
  };

  const db = useDrizzle();

  // Find the club/committee record
  const record = await db.query.dynamicTableData.findFirst({
    where: eq(dynamicTableData.id, Number(cid)),
  });

  if (!record) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Club/Committee not found',
    });
  }

  // Get the table
  const table = await db.query.dynamicTable.findFirst({
    where: eq(dynamicTable.slug, tableSlug),
  });

  if (!table) {
    throw createError({
      statusCode: 404,
      statusMessage: `Table ${tableSlug} not found`,
    });
  }

  // Merge current data with updated data
  const currentData = record.data as CLUB_COMMITTEE;
  const newData = {
    ...currentData,
    ...(slug !== undefined && { slug }),
    ...(title !== undefined && { title }),
    ...(overview !== undefined && { overview }),
    ...(image !== undefined && { image: imageData }),
    ...(attachments !== undefined && { attachments: filesData }),
    ...(linkButton !== undefined && { linkButton: linkButtonData }),
    ...(galleries !== undefined && { galleries: galleries || [] }),
    ...(events !== undefined && { events: events || [] }),
    ...(type !== undefined && { type }),
  };

  // Validate merged data
  if (!validateTableData(tableSlug, newData)) {
    throw createError({
      statusCode: 400,
      statusMessage: `Invalid data structure for table type: ${tableSlug}`,
    });
  }

  // Update the record
  const [updated] = await db
    .update(dynamicTableData)
    .set({
      data: newData,
    })
    .where(eq(dynamicTableData.id, Number(cid)))
    .returning();

  // Delete temporary files after processing
  if (tempPathnames.length > 0) {
    await db
      .delete(tempFiles)
      .where(inArray(tempFiles.pathname, tempPathnames));
  }

  return {
    message: 'Club/Committee updated successfully',
    data: {
      ...updated.data,
      id: updated.id,
    },
  };
});
