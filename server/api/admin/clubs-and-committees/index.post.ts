import { inArray, eq } from 'drizzle-orm';
import type { FileData } from '~/types/home';
import { dynamicTable, dynamicTableData } from '~~/server/database/tables';
import { tempFiles } from '~~/server/database/tables/files';
import {
  validateTableData,
  type CLUB_COMMITTEE,
} from '~~/server/database/tables/dynamic-types';
import { createClubSchema } from '~~/shared/schema/clubs-and-committees/create';

export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const parsedBody = createClubSchema.parse(body);
  const {
    overview,
    title,
    attachments,
    linkButton,
    galleries,
    events,
    type,
    image,
    slug, // Slug is now part of the parsed body
  } = parsedBody;

  // Collect pathnames of all temp files to delete later
  const tempPathnames: string[] = [];

  // Create compatible image data
  let imageData: FileData | null = null;
  if (image) {
    imageData = {
      pathname: image.pathname || '',
      title: image.title || '',
      type: image.type || '',
      prefix: image.prefix || '',
    };

    if (image.pathname) {
      tempPathnames.push(image.pathname);
    }
  }

  // Create compatible files data
  const filesData: FileData[] = Array.isArray(attachments)
    ? attachments.map((file) => {
        if (file.pathname) {
          tempPathnames.push(file.pathname);
        }
        return {
          pathname: file.pathname || '',
          title: file.title || '',
          type: file.type || '',
          prefix: file.prefix || 'club-and-committee',
        };
      })
    : [];

  // Create compatible linkButton data
  const linkButtonData = linkButton || {
    type: 'link',
    title: 'View More',
    style: 'primary',
    internalLink: null,
    externalLink: null,
    newTab: false,
  };

  const data: CLUB_COMMITTEE = {
    slug,
    title: title || '',
    image: imageData || null,
    overview: overview || '',
    attachments: filesData,
    linkButton: linkButtonData,
    galleries: galleries || [],
    events: events || [],
    type: type || '',
  };

  if (!validateTableData('club_and_committee', data)) {
    throw new Error('Invalid data structure for club and committee');
  }

  const db = useDrizzle();

  // Ensure the dynamic table exists
  let table = await db
    .select()
    .from(dynamicTable)
    .where(eq(dynamicTable.slug, 'club_and_committee'));

  if (!table || table.length === 0) {
    const inserted = await db
      .insert(dynamicTable)
      .values({
        slug: 'club_and_committee',
        name: 'Club and Committee',
        type: 'club_and_committee',
      })
      .returning();

    table = inserted;
  }

  // Make sure we have a table record
  if (!table || table.length === 0) {
    throw createError({
      statusCode: 500,
      message: 'Failed to create or find club_and_committee table',
    });
  }

  // Insert the new data into dynamicTableData
  const [created] = await db
    .insert(dynamicTableData)
    .values({
      tableId: table[0].id,
      data,
    })
    .returning();

  // Delete temporary files after processing
  if (tempPathnames.length > 0) {
    await db
      .delete(tempFiles)
      .where(inArray(tempFiles.pathname, tempPathnames));
  }

  return {
    message: 'Club and Committee created successfully',
    data: created,
  };
});
