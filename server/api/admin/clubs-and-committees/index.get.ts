import type { ClubCommitteeResponse } from '~/types/admin/clubs-and-committees';
import { dynamicTable, dynamicTableData } from '~~/server/database/tables';
import type { CLUB_COMMITTEE } from '~~/server/database/tables/dynamic-types';

export default defineEventHandler(
  async (): Promise<ClubCommitteeResponse[]> => {
    const db = useDrizzle();

    // Fetch the club_and_committee table
    const table = await db.query.dynamicTable.findFirst({
      where: eq(dynamicTable.slug, 'club_and_committee'),
    });

    if (!table) {
      throw createError({
        statusCode: 404,
        statusMessage: `Table club_and_committee not found`,
      });
    }

    // Fetch all club/committee data
    const clubCommitteeContent = (await db.query.dynamicTableData.findMany({
      where: eq(dynamicTableData.tableId, table.id),
      columns: {
        createdAt: false,
        updatedAt: false,
        tableId: false,
        id: true,
        data: true,
      },
    })) as {
      id: number;
      data: CLUB_COMMITTEE;
    }[];

    // Map the data to the response format
    const response = clubCommitteeContent.map((content) => ({
      id: content.id, // Include the id property
      title: content.data.title,
      slug: content.data.slug, // Add the missing slug property
      image: content.data.image,
      attachments: content.data.attachments,
      linkButton: content.data.linkButton,
      galleries: content.data.galleries,
      events: content.data.events,
      overview: content.data.overview,
      type: content.data.type || 'club', // Default to 'club' if type is missing
    }));

    return response;
  }
);
