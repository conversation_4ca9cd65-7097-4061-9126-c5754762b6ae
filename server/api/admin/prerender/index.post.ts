// Define the response type
export interface PrerenderResponse {
  success: boolean;
  message: string;
  taskKey?: string;
  error?: string;
}

export default defineEventHandler(async (event): Promise<PrerenderResponse> => {
  try {
    // Ensure user is authenticated as admin
    await requireUserSession(event);

    // No need to parse body - we'll render all APIs

    // Store the pre-rendering request in KV storage
    const timestamp = Date.now();
    const prerenderKey = `prerender-request-${timestamp}`;

    try {
      // Save the pre-rendering request to KV storage
      await hubKV().set(prerenderKey, {
        routes: [],
        all: true,
        status: 'pending',
        createdAt: timestamp,
      });

      // Trigger a background task to handle pre-rendering
      // This uses <PERSON><PERSON>'s built-in task system which is compatible with Cloudflare Workers
      runTask('prerender', {
        payload: {
          key: prerenderKey,
          routes: [],
          all: true,
        },
      });

      // We don't await the task as it will run in the background

      return {
        success: true,
        message: `Pre-rendering task has been scheduled with key: ${prerenderKey}`,
        taskKey: prerenderKey,
      };
    } catch (error: any) {
      console.error('Error scheduling prerender task:', error);
      throw createError({
        statusCode: 500,
        message: 'Failed to schedule pre-rendering task',
        data: error.message,
      });
    }
  } catch (error: any) {
    console.error('Error in prerender API:', error);
    return {
      success: false,
      message: 'Failed to process pre-rendering request',
      error: error.message || 'Unknown error',
    };
  }
});
