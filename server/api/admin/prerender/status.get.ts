// No imports needed

export interface PrerenderStatusResponse {
  success: boolean;
  data?: {
    key: string;
    status?: 'pending' | 'processing' | 'completed' | 'failed';
    routes?: string[];
    all?: boolean;
    createdAt?: number;
    processingStartedAt?: number;
    completedAt?: number;
    failedAt?: number;
    deploymentId?: string;
    deploymentUrl?: string;
    error?: string;
    [key: string]: any; // Allow for additional properties
  };
  message?: string;
  error?: string;
}

export default defineEventHandler(
  async (event): Promise<PrerenderStatusResponse> => {
    try {
      // Ensure user is authenticated as admin
      await requireUserSession(event);

      // Get the query parameters
      const query = getQuery(event);
      const key = query.key as string;

      if (!key) {
        throw createError({
          statusCode: 400,
          message: 'Key parameter is required',
        });
      }

      // Get the pre-rendering status from KV storage
      const status = await hubKV().get(key);

      if (!status) {
        throw createError({
          statusCode: 404,
          message: 'Pre-rendering task not found',
        });
      }

      return {
        success: true,
        data: {
          key,
          ...(status as Record<string, any>),
        },
      };
    } catch (error: any) {
      console.error('Error in prerender status API:', error);
      return {
        success: false,
        message: 'Failed to get pre-rendering status',
        error: error.message || 'Unknown error',
      };
    }
  }
);
