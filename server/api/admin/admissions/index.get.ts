import type { AdmissionResponse } from '~/types/admin/admissions';
import { dynamicTable, dynamicTableData } from '~~/server/database/tables';
import { eq } from 'drizzle-orm';
import type { ADMISSIONS } from '~~/server/database/tables/dynamic-types';

export default defineEventHandler(async (): Promise<AdmissionResponse> => {
  const db = useDrizzle();

  // Fetch the admissions table
  const table = await db.query.dynamicTable.findFirst({
    where: eq(dynamicTable.slug, 'admissions'),
  });

  if (!table) {
    throw createError({
      statusCode: 404,
      statusMessage: `Table admissions not found`,
    });
  }

  // Fetch all admissions data
  const admissionsContent = (await db.query.dynamicTableData.findMany({
    where: eq(dynamicTableData.tableId, table.id),
    columns: {
      createdAt: false,
      updatedAt: false,
      tableId: false,
      id: true,
      data: true,
    },
  })) as unknown as {
    id: number;
    data: ADMISSIONS;
  }[];

  console.log('🚀 ~ admissionsContent:', admissionsContent);

  // Map the data to the response format
  const admissionData = admissionsContent[0].data;
  const response: AdmissionResponse = {
    id: admissionsContent[0].id,
    title: admissionData.title,
    image: admissionData.image,
    content: admissionData.content,
  };

  return response;
});
