import { inArray, eq } from 'drizzle-orm';
import type { FileData } from '~/types/home';
import { dynamicTable, dynamicTableData } from '~~/server/database/tables';
import { tempFiles } from '~~/server/database/tables/files';
import type { DynamicTableDataTypes } from '~~/server/database/tables/dynamic-types';
import { createAdmissionSchema } from '~~/shared/schema/admissions/create';

export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const parsedBody = createAdmissionSchema.parse(body);
  const { title, image, content } = parsedBody;

  // Collect pathnames of all temp files to delete later
  const tempPathnames: string[] = [];

  // Create compatible image data
  let imageData: FileData | null = null;
  if (image) {
    imageData = {
      pathname: image.pathname || '',
      title: image.title || '',
      type: image.type || '',
      prefix: image.prefix || '',
    };

    if (image.pathname) {
      tempPathnames.push(image.pathname);
    }
  }

  // Process content files
  const processedContent = content.map((item) => {
    let fileData: FileData | null = null;

    if (item.file) {
      fileData = {
        pathname: item.file.pathname || '',
        title: item.file.title || '',
        type: item.file.type || '',
        prefix: item.file.prefix || 'admissions',
      };

      if (item.file.pathname) {
        tempPathnames.push(item.file.pathname);
      }
    }

    return {
      title: item.title || '',
      content: item.content || '',
      linkButton: item.linkButton || null,
      file: fileData,
    };
  });

  const data = {
    title: title || '',
    image: imageData || null,
    content: processedContent,
  } as unknown as DynamicTableDataTypes;

  const db = useDrizzle();

  // Ensure the dynamic table exists
  let table = await db
    .select()
    .from(dynamicTable)
    .where(eq(dynamicTable.slug, 'admissions'));

  if (!table || table.length === 0) {
    const inserted = await db
      .insert(dynamicTable)
      .values({
        slug: 'admissions',
        name: 'Admissions',
        type: 'admissions',
      })
      .returning();

    table = inserted;
  }

  // Make sure we have a table record
  if (!table || table.length === 0) {
    throw createError({
      statusCode: 500,
      message: 'Failed to create or find admissions table',
    });
  }

  // Insert the new data into dynamicTableData
  const [created] = await db
    .insert(dynamicTableData)
    .values({
      tableId: table[0].id,
      data,
    })
    .returning();

  // Delete temporary files after processing
  if (tempPathnames.length > 0) {
    await db
      .delete(tempFiles)
      .where(inArray(tempFiles.pathname, tempPathnames));
  }

  return {
    message: 'Admission created successfully',
    data: created,
  };
});
