import { eq, inArray } from 'drizzle-orm';
import type { FileData } from '~/types/home';
import { dynamicTable, dynamicTableData } from '~~/server/database/tables';
import { tempFiles } from '~~/server/database/tables/files';
import type {
  DynamicTableType,
  DynamicTableDataTypes,
} from '~~/server/database/tables/dynamic-types';
import { updateAdmissionSchema } from '~~/shared/schema/admissions/update';

export default defineEventHandler(async (event) => {
  // Get the ID from the route parameter
  const tableSlug = 'admissions' as DynamicTableType;
  const body = await readBody(event);
  const parsedBody = updateAdmissionSchema.parse(body);
  const { id, title, image, content } = parsedBody;

  // Collect pathnames of all temp files to delete later
  const tempPathnames: string[] = [];

  // Create compatible image data
  let imageData: FileData | null = null;
  if (image) {
    imageData = {
      pathname: image.pathname || '',
      title: image.title || '',
      type: image.type || '',
      prefix: image.prefix || '',
    };

    if (image.pathname) {
      tempPathnames.push(image.pathname);
    }
  }

  // Process content files if content is provided
  let processedContent;
  if (content) {
    processedContent = content.map((item) => {
      let fileData: FileData | null = null;

      if (item.file) {
        fileData = {
          pathname: item.file.pathname || '',
          title: item.file.title || '',
          type: item.file.type || '',
          prefix: item.file.prefix || 'admissions',
        };

        if (item.file.pathname) {
          tempPathnames.push(item.file.pathname);
        }
      }

      return {
        title: item.title || '',
        content: item.content || '',
        linkButton: item.linkButton || null,
        file: fileData,
      };
    });
  }

  const db = useDrizzle();

  // Find the admission record
  const record = await db.query.dynamicTableData.findFirst({
    where: eq(dynamicTableData.id, Number(id)),
  });

  if (!record) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Admission not found',
    });
  }

  // Get the table
  const table = await db.query.dynamicTable.findFirst({
    where: eq(dynamicTable.slug, tableSlug),
  });

  if (!table) {
    throw createError({
      statusCode: 404,
      statusMessage: `Table ${tableSlug} not found`,
    });
  }

  // Merge current data with updated data
  const currentData = record.data;
  const newData = {
    ...currentData,
    ...(title !== undefined && { title }),
    ...(image !== undefined && { image: imageData }),
    ...(content !== undefined && { content: processedContent }),
  } as unknown as DynamicTableDataTypes;

  // Update the record
  const [updated] = await db
    .update(dynamicTableData)
    .set({
      data: newData,
    })
    .where(eq(dynamicTableData.id, Number(id)))
    .returning();

  // Delete temporary files after processing
  if (tempPathnames.length > 0) {
    await db
      .delete(tempFiles)
      .where(inArray(tempFiles.pathname, tempPathnames));
  }

  return {
    message: 'Admission updated successfully',
    data: {
      ...updated.data,
      id: updated.id,
    },
  };
});
