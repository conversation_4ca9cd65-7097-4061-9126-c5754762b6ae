import { stats } from '~~/server/database/tables';
import { card } from '~~/server/database/tables/sections/card';
import { useDrizzle } from '~~/server/utils/drizzle';
import type { LandingPageData } from '~/types/admin/landing-page';

// interface ToggleQueryResult {
//   id: number;
//   title: string;
//   isActive: boolean;
//   type: ToggleType;
//   menuId: number | null;
// }

export default defineEventHandler(async (): Promise<LandingPageData> => {
  const db = useDrizzle();
  // Fetch all cards for landing page section (sectionId: 1)
  const pageCards = await db.query.card.findMany({
    where: eq(card.sectionId, 1),
    columns: {
      id: true,
      key: true,
      title: true,
      layout: true,
      createdAt: false,
      updatedAt: false,
    },
    with: {
      cardFiles: {
        columns: {
          id: true,
          createdAt: false,
          updatedAt: false,
        },
        with: {
          file: {
            columns: {
              id: true,
              pathname: true,
              title: true,
              type: true,
            },
          },
        },
      },
      contents: {
        columns: {
          id: true,
          title: true,
          content: true,
          priority: true,
        },
      },
      linkButton: {
        columns: {
          id: true,
          title: true,
          enabled: true,
          style: true,
          icon: true,
          announcementId: true,
          cardTemplateId: true,
          externalLink: true,
          internalLink: true,
          newTab: true,
          createdAt: false,
          updatedAt: false,
        },
      },
    },
  });

  // Fetch stats data
  const statData = await db.query.stats.findMany({
    where: eq(stats.sectionId, 1),
    columns: {
      id: true,
      icon: true,
      value: true,
      title: true,
      sectionId: false,
      createdAt: false,
      updatedAt: false,
    },
  });

  // Fetch toggle data for section visibility
  // const toggleData = (await db.query.toggle.findMany({
  //   where: eq(toggle.sectionId, 1),
  //   columns: {
  //     id: true,
  //     title: true,
  //     isActive: true,
  //     type: true,
  //     menuId: true,
  //   },
  // })) as ToggleQueryResult[];

  // Find principal's message card
  const principalCard = pageCards.find(
    (card) => card.key === 'principal-message'
  );
  // Find campus details card
  const campusCard = pageCards.find((card) => card.key === 'campus-details');
  // Find research card
  const researchCard = pageCards.find((card) => card.key === 'research');

  // Helper function to extract image data safely
  const getImageData = (cardFiles: any) => {
    if (!cardFiles) {
      return undefined;
    }

    // Handle case when cardFiles is a single object with file property
    if (!Array.isArray(cardFiles) && cardFiles.file) {
      const fileData = cardFiles.file;
      return {
        pathname: fileData.pathname,
        title: fileData.title || '',
        type: fileData.type,
      };
    }

    // Handle case when cardFiles is an array
    if (Array.isArray(cardFiles) && cardFiles.length > 0) {
      const fileData = cardFiles[0].file;
      if (!fileData) return undefined;

      return {
        pathname: fileData.pathname,
        title: fileData.title || '',
        type: fileData.type,
      };
    }

    return undefined;
  };

  // Format the response according to LandingPageConfiguration type
  const response: LandingPageData = {
    principalsMessage: {
      title: principalCard?.title || '',
      name:
        principalCard?.contents?.find((c) => c.priority === 1)?.content || '',
      designation:
        principalCard?.contents?.find((c) => c.priority === 2)?.content || '', // Set default or find from contents if available
      message:
        principalCard?.contents?.find((c) => c.priority === 3)?.content || '',
      image: getImageData(principalCard?.cardFiles),
      callToAction: principalCard?.linkButton
        ? {
            enabled: principalCard.linkButton.enabled ?? undefined,
            announcementId: principalCard.linkButton.announcementId || null,
            cardTemplateId:
              principalCard.linkButton.cardTemplateId ?? undefined,
            title: principalCard.linkButton.title || undefined,
            style: principalCard.linkButton.style || undefined,
            icon: principalCard.linkButton.icon ?? undefined,
            internalLink: principalCard.linkButton.internalLink ?? undefined,
            externalLink: principalCard.linkButton.externalLink ?? undefined,
            newTab: principalCard.linkButton.newTab ?? undefined,
          }
        : undefined,
    },

    campusDetails: {
      title: campusCard?.title || '',
      description:
        campusCard?.contents?.find((c) => c.priority === 2)?.content ||
        campusCard?.contents?.find((c) => c.priority === 1)?.content ||
        '',
      image: getImageData(campusCard?.cardFiles),
      callToAction: campusCard?.linkButton
        ? {
            enabled: campusCard.linkButton.enabled ?? undefined,
            announcementId: campusCard.linkButton.announcementId || null,
            cardTemplateId: campusCard.linkButton.cardTemplateId ?? undefined,
            title: campusCard.linkButton.title || undefined,
            style: campusCard.linkButton.style || undefined,
            icon: campusCard.linkButton.icon ?? undefined,
            internalLink: campusCard.linkButton.internalLink ?? undefined,
            externalLink: campusCard.linkButton.externalLink ?? undefined,
            newTab: campusCard.linkButton.newTab ?? undefined,
          }
        : undefined,
    },

    research: {
      title: researchCard?.title || '',
      description:
        researchCard?.contents?.find((c) => c.priority === 2)?.content ||
        researchCard?.contents?.find((c) => c.priority === 1)?.content ||
        '',
      callToAction: researchCard?.linkButton
        ? {
            enabled: researchCard.linkButton.enabled ?? undefined,
            announcementId: researchCard.linkButton.announcementId || null,
            cardTemplateId: researchCard.linkButton.cardTemplateId ?? undefined,
            title: researchCard.linkButton.title || undefined,
            style: researchCard.linkButton.style || undefined,
            icon: researchCard.linkButton.icon ?? undefined,
            internalLink: researchCard.linkButton.internalLink ?? undefined,
            externalLink: researchCard.linkButton.externalLink ?? undefined,
            newTab: researchCard.linkButton.newTab ?? undefined,
          }
        : undefined,
    },

    stats: {
      items: statData.map((stat) => ({
        icon: stat.icon,
        value: stat.value,
        title: stat.title,
      })),
    },

    // sectionVisibility: {
    //   programsWeOffer:
    //     toggleData.find((t) => t.title === 'programsWeOffer')?.isActive ||
    //     false,
    //   events: toggleData.find((t) => t.title === 'events')?.isActive || false,
    //   gallery: toggleData.find((t) => t.title === 'gallery')?.isActive || false,
    // },
  };

  return response;
});
