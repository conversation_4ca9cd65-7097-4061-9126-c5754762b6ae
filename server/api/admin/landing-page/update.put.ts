import { eq } from 'drizzle-orm';
import {
  card,
  cardContent,
  cardFiles,
  files,
  stats,
  linkButton,
  toggle,
  tempFiles,
} from '~~/server/database/schema';
import { landingPageConfigurationSchema } from '~~/shared/schema/section/landing-page/update';
import { useDrizzle } from '~~/server/utils/drizzle';

// Define types for our refactored code
type ImageData = {
  pathname: string;
  title: string;
  type: string;
};

// Based on the schema's BUTTON_STYLES enum
type ButtonStyle = 'primary' | 'secondary' | 'outline';

// Based on the schema's CARD_LAYOUTS enum
// type CardLayout = 'profile' | 'standard' | 'vision';

type CallToAction = {
  buttonText: string;
  buttonStyle: ButtonStyle;
  icon?: string;
  externalLink?: string;
  internalLink?: string;
  newTab: boolean;
  enabled?: boolean;
  linkType?: 'internal' | 'external';
};

type StatItem = {
  value: number;
  title: string;
  icon: string;
};

// Type for the database
type Database = ReturnType<typeof useDrizzle>;

/**
 * API endpoint for updating landing page configuration
 */
export default defineEventHandler(async (event) => {
  // Validate incoming data
  const body = await readBody(event);
  const validatedData = landingPageConfigurationSchema.parse(body);
  const db = useDrizzle();
  const SECTION_ID = 1; // Landing page section ID

  // Clear existing data for clean update
  await clearExistingData(db, SECTION_ID);

  // Process each section if provided
  if (validatedData.principalsMessage) {
    const [cardResult] = await db
      .insert(card)
      .values({
        sectionId: SECTION_ID,
        layout: 'standard',
        title: validatedData.principalsMessage.title,
        key: 'principal-message',
      })
      .returning();

    await db.insert(cardContent).values({
      cardId: cardResult.id,
      priority: 1,
      title: 'name',
      content: validatedData.principalsMessage.name,
    });

    await db.insert(cardContent).values({
      cardId: cardResult.id,
      priority: 2,
      title: 'designation',
      content: validatedData.principalsMessage.designation,
    });

    await db.insert(cardContent).values({
      cardId: cardResult.id,
      priority: 3,
      title: 'message',
      content: validatedData.principalsMessage.message,
    });

    if (validatedData.principalsMessage.image) {
      await processCardImage(
        db,
        cardResult.id,
        validatedData.principalsMessage.image
      );
    }

    if (validatedData.principalsMessage.callToAction) {
      await processCallToAction(
        db,
        cardResult.id,
        validatedData.principalsMessage.callToAction
      );
    }
  }

  if (validatedData.campusDetails) {
    const [cardResult] = await db
      .insert(card)
      .values({
        sectionId: SECTION_ID,
        layout: 'standard',
        title: validatedData.campusDetails.title,
        key: 'campus-details',
      })
      .returning();

    await db.insert(cardContent).values({
      cardId: cardResult.id,
      priority: 1,
      title: 'description',
      content: validatedData.campusDetails.description,
    });

    if (validatedData.campusDetails.image) {
      await processCardImage(
        db,
        cardResult.id,
        validatedData.campusDetails.image
      );
    }

    if (validatedData.campusDetails.callToAction) {
      await processCallToAction(
        db,
        cardResult.id,
        validatedData.campusDetails.callToAction
      );
    }
  }

  if (validatedData.research) {
    const [cardResult] = await db
      .insert(card)
      .values({
        sectionId: SECTION_ID,
        layout: 'standard',
        title: validatedData.research.title,
        key: 'research',
      })
      .returning();

    await db.insert(cardContent).values({
      cardId: cardResult.id,
      priority: 1,
      title: 'description',
      content: validatedData.research.description,
    });

    if (validatedData.research.callToAction) {
      await processCallToAction(
        db,
        cardResult.id,
        validatedData.research.callToAction
      );
    }
  }

  // Process stats section
  if (validatedData.stats && Array.isArray(validatedData.stats.items)) {
    await processStatsSection(db, SECTION_ID, validatedData.stats.items);
  }

  // Process section visibility
  // await processSectionVisibility(
  //   db,
  //   SECTION_ID,
  //   validatedData.sectionVisibility
  // );

  return { success: true };
});

/**
 * Clears existing data for the section
 */
const clearExistingData = async (db: Database, sectionId: number) => {
  await db.delete(card).where(eq(card.sectionId, sectionId));
  await db.delete(stats).where(eq(stats.sectionId, sectionId));
  await db.delete(toggle).where(eq(toggle.sectionId, sectionId));
};

/**
 * Process and save an image for a card
 */
const processCardImage = async (
  db: Database,
  cardId: number,
  image: ImageData
) => {
  if (!image) return;

  const [newFile] = await db
    .insert(files)
    .values({
      pathname: image.pathname,
      title: image.title,
      type: image.type,
    })
    .returning();

  await db.insert(cardFiles).values({
    cardId,
    fileId: newFile.id,
  });

  // Clean up temp file
  await db.delete(tempFiles).where(eq(tempFiles.pathname, image.pathname));
};

/**
 * Process and save call to action buttons
 */
const processCallToAction = async (
  db: Database,
  cardId: number,
  callToAction: CallToAction
) => {
  if (!callToAction) return;

  await db.insert(linkButton).values({
    enabled: callToAction.enabled,
    title: callToAction.buttonText,
    style: callToAction.buttonStyle as ButtonStyle,
    icon: callToAction.icon,
    externalLink: callToAction.externalLink,
    internalLink: callToAction.internalLink,
    newTab: callToAction.newTab,
    cardTemplateId: cardId,
  });
};

/**
 * Process stats section
 */
const processStatsSection = async (
  db: Database,
  sectionId: number,
  statsItems: StatItem[]
) => {
  for (const statItem of statsItems) {
    await db.insert(stats).values({
      sectionId,
      value: statItem.value,
      title: statItem.title,
      icon: statItem.icon,
    });
  }
};

/**
 * Process section visibility toggles
 */
// const processSectionVisibility = async (
//   db: Database,
//   sectionId: number,
//   sectionVisibility: Record<string, boolean>
// ) => {
//   type MenuKey = 'programsWeOffer' | 'events' | 'gallery';

//   const menuMappings: Record<MenuKey, string> = {
//     programsWeOffer: 'Programs We Offer',
//     events: 'Events',
//     gallery: 'Gallery',
//   };

//   for (const [key, isActive] of Object.entries(sectionVisibility)) {
//     // Skip if not in our mappings
//     if (!(key in menuMappings)) continue;

//     // Type assertion to ensure key is one of the valid menu keys
//     const menuKey = key as MenuKey;
//     const menuTitle = menuMappings[menuKey];

//     const menuResults = await db
//       .select()
//       .from(menu)
//       .where(eq(menu.title, menuTitle));

//     if (menuResults.length === 0) {
//       throw new Error(`Menu "${menuTitle}" not found`);
//     }

//     await db.insert(toggle).values({
//       sectionId,
//       type: 'menu',
//       title: menuTitle,
//       isActive,
//       menuId: menuResults[0].id,
//     });
//   }
// };
