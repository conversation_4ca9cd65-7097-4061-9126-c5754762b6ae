import { isNull } from 'drizzle-orm';
import { menu } from '~~/server/database/tables';

export default defineEventHandler(async () => {
  try {
    const db = useDrizzle();

    // Fetch all menus
    const menus = await db.query.menu.findMany({
      where: isNull(menu.parentId),
      columns: {
        slug: true,
      },
    });

    const slugList = menus.map((menu) => menu.slug) as string[];

    return slugList;
  } catch (error) {
    console.error('Error fetching menus', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch menus',
    });
  }
});
