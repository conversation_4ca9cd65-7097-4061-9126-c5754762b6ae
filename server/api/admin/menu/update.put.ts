import { menu } from '@@/server/database/schema';
import { eq } from 'drizzle-orm';
import { updateMenuSchema } from '~~/shared/schema/menu/update';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const validatedData = updateMenuSchema.parse(body);

    const db = useDrizzle();
    const id = validatedData.id;

    await db
      .update(menu)
      .set({
        title: validatedData.title,
        menuName: validatedData.menuName,
        routeEnabled: validatedData.routeEnabled,
        slug: validatedData.slug,
        isActive: validatedData.isActive,
      })
      .where(eq(menu.id, id));

    return {
      success: true,
    };
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
