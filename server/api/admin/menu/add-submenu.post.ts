import { menu, template } from '~~/server/database/tables';
import { addSubmenuSchema } from '~~/shared/schema/menu/create';

export default defineEventHandler(async (event) => {
  try {
    // Validate input data
    const { menuId, title, menuName, isActive, routeEnabled, slug } =
      addSubmenuSchema.parse(await readBody(event));

    const db = useDrizzle();

    const parentMenu = await db.query.menu.findFirst({
      where: eq(menu.id, menuId),
    });

    if (!parentMenu) {
      throw createError({
        statusCode: 404,
        message: 'Parent menu not found',
      });
    }

    const [newSubmenu] = await db
      .insert(menu)
      .values({
        parentId: parentMenu.id,
        title,
        menuName,
        isActive,
        routeEnabled,
        slug,
      })
      .returning();

    await db.insert(template).values({
      menuId: newSubmenu.id,
      showMenuLinks: false,
    });
    return newSubmenu;
  } catch (error) {
    event.context.validation.handleError(error);
  }
});
