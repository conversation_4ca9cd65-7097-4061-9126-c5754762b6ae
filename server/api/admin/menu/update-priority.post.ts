import { eq } from 'drizzle-orm';
import { menu } from '~~/server/database/tables';
import { updatePrioritySchema } from '~~/shared/schema/menu/create';

export default defineEventHandler(async (event) => {
  try {
    // Validate input data
    const { id, priority } = updatePrioritySchema.parse(await readBody(event));

    const db = useDrizzle();

    // Check if menu item exists
    const menuItem = await db.query.menu.findFirst({
      where: eq(menu.id, id),
    });

    if (!menuItem) {
      throw createError({
        statusCode: 404,
        message: 'Menu item not found',
      });
    }

    // Update the priority
    const [updatedMenu] = await db
      .update(menu)
      .set({
        priority,
      })
      .where(eq(menu.id, id))
      .returning();

    return updatedMenu;
  } catch (error) {
    event.context.validation.handleError(error);
  }
});
