import { menu, template } from '@@/server/database/schema';
import { createMenuSchema } from '~~/shared/schema/menu/create';

export default defineEventHandler(async (event) => {
  try {
    // Parse the request body
    const body = await readBody(event);

    // Validate the request body
    const validatedData = createMenuSchema.parse(body);

    // Initialize drizzle
    const db = useDrizzle();

    // Insert the menu into the database
    const result = await db
      .insert(menu)
      .values({
        title: validatedData.title,
        menuName: validatedData.menuName,
        routeEnabled: validatedData.routeEnabled,
        slug: validatedData.slug,
        isActive: validatedData.isActive,
      })
      .returning();

    await db.insert(template).values({
      menuId: result[0].id,
      showMenuLinks: true,
    });

    return {
      success: true,
      data: result[0],
    };
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
