import { menu } from '~~/server/database/tables';
import { checkMenuSchema } from '~~/shared/schema/menu/check-menu';

export default defineEventHandler(async (event) => {
  // Get query parameters
  const query = getQuery(event);

  // Validate query parameters against schema
  const result = checkMenuSchema.safeParse(query);
  if (!result.success) {
    throw createError({
      statusCode: 400,
      message: result.error.message,
    });
  }

  const { path, title } = result.data;
  const db = useDrizzle();

  // Check for existing menu based on provided parameters
  const existingMenu = await db.query.menu.findFirst({
    where: path ? eq(menu.slug, path) : eq(menu.title, title as string),
  });

  return {
    exists: !!existingMenu,
  };
});
