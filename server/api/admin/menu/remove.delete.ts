import {
  menu,
  toggle,
  template,
  section,
  card,
  cardContent,
  cardFiles,
  linkButton,
  accordion,
  cardList,
  stats,
} from '@@/server/database/schema';
import { eq } from 'drizzle-orm';
import { downloadButton } from '~~/server/database/tables/buttons/downloadButton';
// import { deleteMenuSchema } from '~~/shared/schema/menu/remove';

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event);
    const id = parseInt(query.id as string, 10);

    // Validate ID
    if (isNaN(id) || id <= 0) {
      throw createError({
        statusCode: 400,
        message: 'Invalid menu ID provided',
      });
    }

    const db = useDrizzle();

    // First check if the menu exists
    const menuExists = await db.query.menu.findFirst({
      where: eq(menu.id, id),
    });

    if (!menuExists) {
      throw createError({
        statusCode: 404,
        message: 'Menu not found',
      });
    }

    try {
      // First, handle toggle templates that reference this menu
      const togglesWithMenu = await db.query.toggle.findMany({
        where: eq(toggle.menuId, id),
      });

      // Update any toggle templates to remove the reference to this menu
      if (togglesWithMenu.length > 0) {
        console.log(
          `Updating ${togglesWithMenu.length} toggle templates to remove menu reference`
        );
        for (const toggleItem of togglesWithMenu) {
          await db
            .update(toggle)
            .set({ menuId: null })
            .where(eq(toggle.id, toggleItem.id));
        }
      }

      // Check for templates that reference this menu
      const templatesWithMenu = await db.query.template.findMany({
        where: eq(template.menuId, id),
      });

      // Explicitly delete templates before deleting the menu
      if (templatesWithMenu.length > 0) {
        console.log(
          `Deleting ${templatesWithMenu.length} templates associated with menu ID ${id}`
        );

        // For each template, we need to find and delete related sections first
        for (const templateItem of templatesWithMenu) {
          const sections = await db.query.section.findMany({
            where: eq(section.templateId, templateItem.id),
          });

          // Delete each section and its related configurations
          for (const sectionItem of sections) {
            // Handle different section types
            if (sectionItem.type === 'cardList') {
              const cards = await db.query.card.findMany({
                where: eq(card.sectionId, sectionItem.id),
              });

              for (const cardItem of cards) {
                await db
                  .delete(cardContent)
                  .where(eq(cardContent.cardId, cardItem.id));
                await db
                  .delete(cardFiles)
                  .where(eq(cardFiles.cardId, cardItem.id));
                await db
                  .delete(linkButton)
                  .where(eq(linkButton.cardTemplateId, cardItem.id));
                await db
                  .delete(downloadButton)
                  .where(eq(downloadButton.cardTemplateId, cardItem.id));
              }

              await db.delete(card).where(eq(card.sectionId, sectionItem.id));
              await db
                .delete(cardList)
                .where(eq(cardList.sectionId, sectionItem.id));
            } else if (sectionItem.type === 'card') {
              const cardItem = await db.query.card.findFirst({
                where: eq(card.sectionId, sectionItem.id),
              });

              if (cardItem) {
                await db
                  .delete(cardContent)
                  .where(eq(cardContent.cardId, cardItem.id));
                await db
                  .delete(cardFiles)
                  .where(eq(cardFiles.cardId, cardItem.id));
                await db
                  .delete(linkButton)
                  .where(eq(linkButton.cardTemplateId, cardItem.id));
                await db
                  .delete(downloadButton)
                  .where(eq(downloadButton.cardTemplateId, cardItem.id));
                await db.delete(card).where(eq(card.id, cardItem.id));
              }
            } else if (sectionItem.type === 'accordion') {
              await db
                .delete(accordion)
                .where(eq(accordion.sectionId, sectionItem.id));
            } else if (sectionItem.type === 'stats') {
              await db.delete(stats).where(eq(stats.sectionId, sectionItem.id));
            } else if (sectionItem.type === 'toggle') {
              await db
                .delete(toggle)
                .where(eq(toggle.sectionId, sectionItem.id));
            }

            // Delete the section itself
            await db.delete(section).where(eq(section.id, sectionItem.id));
          }

          // Now delete the template
          await db.delete(template).where(eq(template.id, templateItem.id));
        }
      }
    } catch (error: any) {
      console.error(
        'Error handling related records before menu deletion:',
        error
      );
      throw createError({
        statusCode: 500,
        message: `Failed to handle related records: ${error.message || 'Unknown error'}`,
      });
    }

    // Check for any remaining references to this menu in the database
    try {
      // Check for any remaining toggle templates that reference this menu
      const remainingToggles = await db.query.toggle.findMany({
        where: eq(toggle.menuId, id),
      });

      if (remainingToggles.length > 0) {
        console.log(
          `Found ${remainingToggles.length} remaining toggle templates referencing menu ID ${id}`
        );
        // Force update these toggles to remove the menu reference
        for (const toggleItem of remainingToggles) {
          await db
            .update(toggle)
            .set({ menuId: null })
            .where(eq(toggle.id, toggleItem.id));
        }
      }

      // Check for any remaining templates that reference this menu
      const remainingTemplates = await db.query.template.findMany({
        where: eq(template.menuId, id),
      });

      if (remainingTemplates.length > 0) {
        console.log(
          `Found ${remainingTemplates.length} remaining templates referencing menu ID ${id}`
        );
        // Force delete these templates
        for (const templateItem of remainingTemplates) {
          await db.delete(template).where(eq(template.id, templateItem.id));
        }
      }
    } catch (checkError: any) {
      console.error(
        `Error checking for remaining references to menu ID ${id}:`,
        checkError
      );
      // Continue with deletion attempt even if this check fails
    }

    // Delete the menu (and its submenus due to cascade constraint)
    console.log(`Deleting menu with ID ${id}`);
    try {
      await db.delete(menu).where(eq(menu.id, id));
      console.log(`Successfully deleted menu with ID ${id}`);
    } catch (deleteError: any) {
      console.error(`Error deleting menu with ID ${id}:`, deleteError);
      throw createError({
        statusCode: 500,
        message: `Failed to delete menu: ${deleteError.message || 'Unknown error'}`,
      });
    }

    // Determine message based on whether it was a submenu or main menu
    const successMessage = menuExists.parentId
      ? 'Submenu deleted successfully'
      : 'Menu and all its submenus deleted successfully';

    return {
      success: true,
      message: successMessage,
      deletedItemId: id,
    };
  } catch (error: any) {
    console.error('Failed to delete menu:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || 'Failed to delete menu',
    });
  }
});
