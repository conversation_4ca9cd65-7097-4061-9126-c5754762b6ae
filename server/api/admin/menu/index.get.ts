import { isNull } from 'drizzle-orm';
import { menu } from '~~/server/database/tables';

export default defineEventHandler(async () => {
  try {
    const db = useDrizzle();

    // Fetch all menus
    const menus = await db.query.menu.findMany({
      where: isNull(menu.parentId),
      columns: {
        id: true,
        title: true,
        menuName: true,
        routeEnabled: true,
        slug: true,
        isActive: true,
      },
    });

    return {
      success: true,
      data: menus,
    };
  } catch (error) {
    console.error('Error fetching menus', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch menus',
    });
  }
});
