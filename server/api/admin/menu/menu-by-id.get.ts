// /server/api/admin/menu/menu-by-id.ts
import { menu } from '@@/server/database/schema';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event);
    const id = query.id as number;

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Menu ID is required',
      });
    }

    const db = useDrizzle();

    // Fetch menu with all relations
    const menuData = await db.query.menu.findFirst({
      where: eq(menu.id, id),
      with: {
        submenus: {
          columns: {
            parentId: false,
          },
        },
        template: {
          columns: {
            menuId: false,
          },
        },
      },
    });

    if (!menuData) {
      throw createError({
        statusCode: 404,
        message: 'Menu not found',
      });
    }

    return {
      success: true,
      data: menuData,
    };
  } catch (error: any) {
    console.error('Failed to fetch menu:', error);
    throw createError({
      statusCode: error?.statusCode || 500,
      message: error?.messages || 'Failed to fetch menu',
    });
  }
});
