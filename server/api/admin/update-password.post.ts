import { z } from 'zod';
import { eq } from 'drizzle-orm';
import { admin } from '~~/server/database/tables';
import type { H3Event } from 'h3';

// Update password validation schema
const updatePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(1, 'New password is required'),
});

export default defineEventHandler(async (event) => {
  try {
    // Parse and validate request body
    const body = await readBody(event);
    const validatedData = updatePasswordSchema.parse(body);

    const { currentPassword, newPassword } = validatedData;

    // Get the current admin user from the session
    const session = await getUserSession(event as unknown as H3Event);
    const adminId = session.user?.id;

    if (!adminId) {
      throw createError({
        statusCode: 401,
        message: 'Unauthorized',
      });
    }

    // Fetch the admin user from the database
    const adminUser = await useDrizzle().query.admin.findFirst({
      where: eq(admin.id, adminId),
    });

    if (!adminUser) {
      throw createError({
        statusCode: 404,
        message: 'Admin user not found',
      });
    }

    // Verify the current password
    const isValid = await verifyPassword(adminUser.password, currentPassword);

    if (!isValid) {
      throw createError({
        statusCode: 401,
        message: 'Invalid current password',
      });
    }

    // Hash the new password
    const hashedNewPassword = await hashPassword(newPassword);

    // Update the password in the database
    await useDrizzle()
      .update(admin)
      .set({ password: hashedNewPassword })
      .where(eq(admin.id, adminId));

    return {
      success: true,
      message: 'Password updated successfully',
    };
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
