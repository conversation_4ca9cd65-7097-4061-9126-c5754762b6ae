import { eq } from 'drizzle-orm';
import type { ButtonStyle } from '~~/server/database/tables';
import {
  accordion,
  card,
  section,
  template,
  cardContent,
  cardFiles,
  linkButton,
  files,
  tempFiles,
  stats,
  toggle,
} from '~~/server/database/tables';
import { cardList } from '~~/server/database/tables/sections';
import {
  downloadButton,
  downloadFiles,
} from '~~/server/database/tables/buttons/downloadButton';
import { createSectionSchema } from '~~/shared/schema/section/create';
import type {
  ButtonConfig,
  ContentEntry,
  FileInsert,
  CardFiles,
  CardConfig,
  CardListConfig,
} from '~~/server/helper/section';
import {
  createContentEntry,
  isButtonIncludedCard,
} from '~~/server/helper/section';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { configuration, templateId, type } = createSectionSchema.parse(body);
    const db = useDrizzle();

    // Check if template exists
    const templateRecord = await db.query.template.findFirst({
      where: eq(template.id, templateId),
    });

    if (!templateRecord) {
      throw createError({
        statusCode: 404,
        message: `Template with ID ${templateId} not found`,
      });
    }

    // Check if a section already exists for this template
    const existingSection = await db.query.section.findFirst({
      where: (section, { eq, and }) =>
        and(eq(section.templateId, templateId), eq(section.priority, 0)),
    });

    // If a section already exists, delete it and its related configurations
    if (existingSection) {
      // Delete related configurations based on section type
      if (existingSection.type === 'cardList') {
        const existingCards = await db.query.card.findMany({
          where: eq(card.sectionId, existingSection.id),
        });

        for (const existingCard of existingCards) {
          await db
            .delete(cardContent)
            .where(eq(cardContent.cardId, existingCard.id));
          await db
            .delete(cardFiles)
            .where(eq(cardFiles.cardId, existingCard.id));
          await db
            .delete(linkButton)
            .where(eq(linkButton.cardTemplateId, existingCard.id));
          await db
            .delete(downloadButton)
            .where(eq(downloadButton.cardTemplateId, existingCard.id));
        }

        await db.delete(card).where(eq(card.sectionId, existingSection.id));
        await db
          .delete(cardList)
          .where(eq(cardList.sectionId, existingSection.id));
      } else if (existingSection.type === 'card') {
        const existingCard = await db.query.card.findFirst({
          where: eq(card.sectionId, existingSection.id),
        });

        if (existingCard) {
          await db
            .delete(cardContent)
            .where(eq(cardContent.cardId, existingCard.id));
          await db
            .delete(cardFiles)
            .where(eq(cardFiles.cardId, existingCard.id));
          await db
            .delete(linkButton)
            .where(eq(linkButton.cardTemplateId, existingCard.id));
          await db
            .delete(downloadButton)
            .where(eq(downloadButton.cardTemplateId, existingCard.id));
          await db.delete(card).where(eq(card.id, existingCard.id));
        }
      } else if (existingSection.type === 'accordion') {
        await db
          .delete(accordion)
          .where(eq(accordion.sectionId, existingSection.id));
      } else if (existingSection.type === 'stats') {
        await db.delete(stats).where(eq(stats.sectionId, existingSection.id));
      } else if (existingSection.type === 'toggle') {
        await db.delete(toggle).where(eq(toggle.sectionId, existingSection.id));
      }

      // Delete the existing section
      await db.delete(section).where(eq(section.id, existingSection.id));
    }

    // Create the new section with priority 0
    const [newSection] = await db
      .insert(section)
      .values({
        templateId,
        type,
        priority: 0, // Always set priority to 0 for single section
      })
      .returning();
    // Create the corresponding configuration based on type
    if (type === 'accordion') {
      await db
        .insert(accordion)
        .values({
          sectionId: newSection.id,
          ...configuration,
        })
        .returning();
    } else if (type === 'card') {
      const cardType = configuration.layout;

      // Create base card entry
      const [newCard] = await db
        .insert(card)
        .values({
          sectionId: newSection.id,
          layout: configuration.layout,
          title: 'title' in configuration ? configuration.title : null,
          key: configuration.key || 'default-key', // Provide a default key if not present
        })
        .returning();

      // Handle content blocks based on layout
      const contentEntries: ContentEntry[] = [];
      switch (cardType) {
        case 'standard':
          // Always insert content1, using empty string defaults if missing
          contentEntries.push(
            createContentEntry(
              'content1',
              configuration.content1?.title ?? '',
              configuration.content1?.content ?? ''
            )
          );
          break;
        case 'vision':
          if (
            configuration.content1?.title &&
            configuration.content1?.content
          ) {
            contentEntries.push(
              createContentEntry(
                'content1',
                configuration.content1.title,
                configuration.content1.content
              )
            );
          }
          break;
        case 'profile':
          // Make content1 and content2 individually optional
          if (
            configuration.content1?.title &&
            configuration.content1?.content
          ) {
            contentEntries.push(
              createContentEntry(
                'content1',
                configuration.content1.title,
                configuration.content1.content
              )
            );
          }

          if (
            configuration.content2?.title &&
            configuration.content2?.content
          ) {
            contentEntries.push(
              createContentEntry(
                'content2',
                configuration.content2.title,
                configuration.content2.content
              )
            );
          }
          break;
        default:
          throw createError({
            statusCode: 400,
            message: `Invalid card type: ${cardType}`,
          });
      }

      // Insert content blocks
      await db.insert(cardContent).values(
        contentEntries.map((entry) => ({
          cardId: newCard.id,
          title: entry.title,
          content: entry.content,
          type: entry.type,
          priority: contentEntries.indexOf(entry) + 1,
        }))
      );

      // Handle image file relation
      if (configuration.image) {
        const [newFile] = await db
          .insert(files)
          .values({
            pathname: configuration.image.pathname,
            title: configuration.image.title,
            type: configuration.image.type,
          } as FileInsert)
          .returning();
        await db.insert(cardFiles).values({
          cardId: newCard.id,
          fileId: newFile.id,
        } as CardFiles);
        await db
          .delete(tempFiles)
          .where(eq(tempFiles.pathname, configuration.image.pathname));
      }

      // Handle files array (additional files)
      if (
        configuration.layout === 'standard' &&
        Array.isArray((configuration as any).files)
      ) {
        for (const file of (configuration as any).files) {
          const [newFile] = await db
            .insert(files)
            .values({
              pathname: file.pathname,
              title: file.title,
              type: file.type,
            } as FileInsert)
            .returning();
          await db.insert(cardFiles).values({
            cardId: newCard.id,
            fileId: newFile.id,
          } as CardFiles);
          await db
            .delete(tempFiles)
            .where(eq(tempFiles.pathname, file.pathname));
        }
      }

      // Handle buttons
      if (isButtonIncludedCard(cardType)) {
        const buttonConfig = [];

        // First make a type-safe check if the properties exist
        const hasLinkButton =
          'linkButton' in configuration && configuration.linkButton;
        const hasDownloadButton =
          'downloadButton' in configuration && configuration.downloadButton;

        // Standard and profile cards can have both linkButton and downloadButton
        if (hasLinkButton) {
          buttonConfig.push(configuration.linkButton as ButtonConfig);
        }
        if (hasDownloadButton) {
          buttonConfig.push(configuration.downloadButton as ButtonConfig);
        }

        for (const button of buttonConfig) {
          if (button.type === 'link') {
            await db.insert(linkButton).values({
              cardTemplateId: newCard.id,
              title: button.title,
              style: button.style as ButtonStyle,
              icon: button.icon,
              externalLink: button.externalLink,
              internalLink: button.internalLink,
              newTab: button.newTab,
            });
          } else if (button.type === 'download') {
            const [newDownloadButton] = await db
              .insert(downloadButton)
              .values({
                cardTemplateId: newCard.id,
                title: button.title,
                style: button.style as ButtonStyle,
                icon: button.icon || null,
                newTab: button.newTab,
              })
              .returning();

            // Create file record for the download button
            const [newFile] = await db
              .insert(files)
              .values({
                pathname: button.file.pathname,
                title: button.file.title,
                type: button.file.type,
              } as FileInsert)
              .returning();

            await db.insert(downloadFiles).values({
              downloadButtonId: newDownloadButton.id,
              fileId: newFile.id,
            });

            // Clean up temp file
            await db
              .delete(tempFiles)
              .where(eq(tempFiles.pathname, button.file.pathname));
          }
        }
      }
    } else if (type === 'cardList') {
      // Create cardList configuration
      const cardListConfig: Omit<CardListConfig, 'sectionId'> = {
        title: configuration.title || '',
        description: configuration.description || null,
        maxCards: configuration.maxCards,
        minCards: configuration.minCards,
        layout: configuration.cards[0].layout,
      };
      await db
        .insert(cardList)
        .values({
          sectionId: newSection.id,
          ...cardListConfig,
        })
        .returning();

      // Create each card in the list
      for (const cardConfig of configuration.cards as CardConfig[]) {
        const cardLayout = cardConfig.layout;
        const [newCard] = await db
          .insert(card)
          .values({
            sectionId: newSection.id,
            layout: cardLayout,
            title: cardConfig.title || null,
            key: cardConfig.key || 'default-key', // Provide a default key if not present
          })
          .returning();

        // Handle content blocks based on layout
        const contentEntries: ContentEntry[] = [];
        switch (cardLayout) {
          case 'standard': {
            const standardConfig = cardConfig as {
              content1?: { title: string; content: string };
            };
            // Always insert content1, using empty string defaults if missing
            contentEntries.push(
              createContentEntry(
                'content1',
                standardConfig.content1?.title ?? '',
                standardConfig.content1?.content ?? ''
              )
            );
            break;
          }
          case 'profile': {
            const profileConfig = cardConfig as {
              content1?: { title: string; content: string };
              content2?: { title: string; content: string };
            };

            // Make content1 and content2 individually optional
            if (
              profileConfig.content1?.title &&
              profileConfig.content1?.content
            ) {
              contentEntries.push(
                createContentEntry(
                  'content1',
                  profileConfig.content1.title,
                  profileConfig.content1.content
                )
              );
            }

            if (
              profileConfig.content2?.title &&
              profileConfig.content2?.content
            ) {
              contentEntries.push(
                createContentEntry(
                  'content2',
                  profileConfig.content2.title,
                  profileConfig.content2.content
                )
              );
            }
            break;
          }
          case 'vision': {
            const simpleConfig = cardConfig as {
              content1?: { title: string; content: string };
            };
            if (
              simpleConfig.content1?.title &&
              simpleConfig.content1?.content
            ) {
              contentEntries.push(
                createContentEntry(
                  'content1',
                  simpleConfig.content1.title,
                  simpleConfig.content1.content
                )
              );
            }
            break;
          }
          default:
            throw createError({
              statusCode: 400,
              message: `Invalid card layout type: ${cardLayout}`,
            });
        }

        // Insert content blocks
        if (contentEntries.length > 0) {
          await db.insert(cardContent).values(
            contentEntries.map((entry) => ({
              cardId: newCard.id,
              title: entry.title,
              content: entry.content,
              type: entry.type,
              priority: contentEntries.indexOf(entry) + 1,
            }))
          );
        }

        // Handle image file relation
        if (cardConfig.image) {
          const [newFile] = await db
            .insert(files)
            .values({
              pathname: cardConfig.image.pathname,
              title: cardConfig.image.title,
              type: cardConfig.image.type,
            })
            .returning();
          await db.insert(cardFiles).values({
            cardId: newCard.id,
            fileId: newFile.id,
          });
          await db
            .delete(tempFiles)
            .where(eq(tempFiles.pathname, cardConfig.image.pathname));
        }

        // Handle files array (additional files)
        if (
          cardConfig.layout === 'standard' &&
          Array.isArray((cardConfig as any).files)
        ) {
          for (const file of (cardConfig as any).files) {
            const [newFile] = await db
              .insert(files)
              .values({
                pathname: file.pathname,
                title: file.title,
                type: file.type,
              })
              .returning();
            await db.insert(cardFiles).values({
              cardId: newCard.id,
              fileId: newFile.id,
            });
            await db
              .delete(tempFiles)
              .where(eq(tempFiles.pathname, file.pathname));
          }
        }

        // Handle buttons
        if (isButtonIncludedCard(cardLayout)) {
          const buttonConfig: ButtonConfig[] = [];

          // Handle standard and profile card buttons
          if (cardLayout === 'standard' || cardLayout === 'profile') {
            // Type-safe check if properties exist
            const hasLinkButton =
              'linkButton' in cardConfig && cardConfig.linkButton;
            const hasDownloadButton =
              'downloadButton' in cardConfig && cardConfig.downloadButton;

            if (hasLinkButton) {
              buttonConfig.push(cardConfig.linkButton as ButtonConfig);
            }
            if (hasDownloadButton) {
              buttonConfig.push(cardConfig.downloadButton as ButtonConfig);
            }
          }

          for (const button of buttonConfig) {
            if (button.type === 'link') {
              await db.insert(linkButton).values({
                cardTemplateId: newCard.id,
                title: button.title,
                style: button.style as ButtonStyle,
                icon: button.icon,
                externalLink: button.externalLink,
                internalLink: button.internalLink,
                newTab: button.newTab,
              });
            } else if (button.type === 'download') {
              const [newDownloadButton] = await db
                .insert(downloadButton)
                .values({
                  cardTemplateId: newCard.id,
                  title: button.title,
                  style: button.style as ButtonStyle,
                  icon: button.icon || null,
                  newTab: button.newTab,
                })
                .returning();

              // Create file record for the download button
              const [newFile] = await db
                .insert(files)
                .values({
                  pathname: button.file.pathname,
                  title: button.file.title,
                  type: button.file.type,
                })
                .returning();

              await db.insert(downloadFiles).values({
                downloadButtonId: newDownloadButton.id,
                fileId: newFile.id,
              });

              await db
                .delete(tempFiles)
                .where(eq(tempFiles.pathname, button.file.pathname));
            }
          }
        }
      }
    }

    return {
      success: true,
      data: {
        id: newSection.id,
        type: newSection.type,
        priority: newSection.priority,
        templateId: newSection.templateId,
      },
    };
  } catch (error) {
    console.error('Error creating section', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to create section',
    });
  }
});
