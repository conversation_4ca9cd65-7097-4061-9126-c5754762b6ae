import {
  processCardConfig,
  processSectionConfig,
} from '~~/server/helper/section';
import type { CardContent } from '~~/server/database/tables/sections/card';
import type { Files } from '~~/server/database/tables/files';
import { z } from 'zod';
import type { SectionResponse } from '~~/app/types/api/section';

// Define simplified types for database records with relations
type CardWithRelationsSimplified = {
  id: number;
  title: string | null;
  layout: string;
  key: string;
  createdAt: number | null;
  updatedAt: number | null;
  sectionId: number | null;
  contents: CardContent[];
  cardFiles?: { file: Files }[] | null;
  linkButton?: any | null;
};

type CardListWithRelationsSimplified = {
  id: number;
  title: string;
  description: string | null;
  maxCards: number;
  minCards: number;
  layout: string;
  sectionId: number | null;
};

export type SectionWithRelationsSimplified = {
  id: number;
  type: 'card' | 'cardList' | 'accordion';
  priority: number;
  templateId: number | null;
  createdAt: number | null;
  updatedAt: number | null;
  template?: any;
  cardConfig?: CardWithRelationsSimplified;
  cardListConfig?: CardListWithRelationsSimplified;
  accordionConfig?: any;
};

// Define query parameter schema
const querySchema = z.object({
  templateId: z.coerce.number().optional(),
});

export type SectionWithConfigData = {
  id: number;
  type: string;
  priority: number;
  templateId: number | null;
  createdAt: number | null;
  updatedAt: number | null;
  configuration: any;
};

export default defineEventHandler(async (event): Promise<SectionResponse> => {
  try {
    const db = useDrizzle();

    // Get and validate query parameters
    const query = getQuery(event);
    const { templateId } = querySchema.parse(query);

    if (!templateId) {
      throw createError({
        statusCode: 400,
        message: 'Template ID is required',
      });
    }
    // Get only the first section for the template (priority 0)
    const sections = await db.query.section.findMany({
      where: (section, { eq, and }) =>
        and(eq(section.templateId, templateId), eq(section.priority, 0)),
      with: {
        template: true,
        cardConfig: {
          with: {
            contents: true,
            cardFiles: {
              with: {
                file: true,
              },
            },
            linkButton: true,
          },
        },
        cardListConfig: true,
        accordionConfig: true,
      },
      limit: 1,
    });

    // Type assertion for TypeScript
    const typedSections =
      sections as unknown as SectionWithRelationsSimplified[];

    // Create a list to hold our processed sections
    let sectionData: SectionWithConfigData | null = null;

    // Process each section individually
    for (const section of typedSections) {
      let configuration = null;

      // Process based on section type
      if (section.type === 'card' && section.cardConfig) {
        configuration = processCardConfig(section.cardConfig);
      } else if (section.type === 'cardList' && section.cardListConfig) {
        // For cardList, we need to fetch cards separately
        const cardList = section.cardListConfig;

        // Fetch cards related to this cardList
        const cards = await db.query.card.findMany({
          where: (card, { eq }) => eq(card.sectionId, section.id),
          with: {
            contents: true,
            cardFiles: {
              with: {
                file: true,
              },
            },
            linkButton: true,
          },
        });

        // Process the cardList with its cards
        configuration = processSectionConfig(section, cardList, cards);
      } else if (section.type === 'accordion' && section.accordionConfig) {
        configuration = processSectionConfig(section, section.accordionConfig);
      }

      sectionData = {
        id: section.id,
        type: section.type,
        priority: section.priority,
        templateId: section.templateId,
        createdAt: section.createdAt,
        updatedAt: section.updatedAt,
        configuration: {
          ...configuration,
          files:
            configuration?.files?.length > 1
              ? configuration?.files?.slice(1)
              : [],
        },
      };
    }

    // If no section exists for this template, return an empty array
    return {
      success: true,
      data: sectionData,
    };
  } catch (error: any) {
    console.error('Error fetching sections', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch sections',
    });
  }
});
