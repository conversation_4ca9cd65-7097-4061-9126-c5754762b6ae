import { eq } from 'drizzle-orm';
import {
  section,
  card,
  cardContent,
  cardFiles,
  linkButton,
  accordion,
  cardList,
  stats,
  toggle,
} from '~~/server/database/tables';
import { downloadButton } from '~~/server/database/tables/buttons/downloadButton';
import { removeSectionSchema } from '~~/shared/schema/section/create';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id } = removeSectionSchema.parse(body);

    const db = useDrizzle();

    // Check if section exists
    const existingSection = await db.query.section.findFirst({
      where: eq(section.id, id),
    });

    if (!existingSection) {
      throw createError({
        statusCode: 404,
        message: `Section with ID ${id} not found`,
      });
    }

    // Delete related configurations based on section type
    try {
      if (existingSection.type === 'cardList') {
        const existingCards = await db.query.card.findMany({
          where: eq(card.sectionId, id),
        });

        for (const existingCard of existingCards) {
          // Delete card content first (child records)
          await db
            .delete(cardContent)
            .where(eq(cardContent.cardId, existingCard.id));

          // Delete card files
          await db
            .delete(cardFiles)
            .where(eq(cardFiles.cardId, existingCard.id));

          // Delete buttons associated with the card
          await db
            .delete(linkButton)
            .where(eq(linkButton.cardTemplateId, existingCard.id));
          await db
            .delete(downloadButton)
            .where(eq(downloadButton.cardTemplateId, existingCard.id));
        }

        // Delete all cards associated with this section
        await db.delete(card).where(eq(card.sectionId, id));

        // Delete the card list configuration
        await db.delete(cardList).where(eq(cardList.sectionId, id));
      } else if (existingSection.type === 'card') {
        const existingCard = await db.query.card.findFirst({
          where: eq(card.sectionId, id),
        });

        if (existingCard) {
          // Delete card content first (child records)
          await db
            .delete(cardContent)
            .where(eq(cardContent.cardId, existingCard.id));

          // Delete card files
          await db
            .delete(cardFiles)
            .where(eq(cardFiles.cardId, existingCard.id));

          // Delete buttons associated with the card
          await db
            .delete(linkButton)
            .where(eq(linkButton.cardTemplateId, existingCard.id));
          await db
            .delete(downloadButton)
            .where(eq(downloadButton.cardTemplateId, existingCard.id));

          // Delete the card itself
          await db.delete(card).where(eq(card.id, existingCard.id));
        }
      } else if (existingSection.type === 'accordion') {
        // Delete the accordion configuration
        await db.delete(accordion).where(eq(accordion.sectionId, id));
      } else if (existingSection.type === 'stats') {
        // Delete the stats configuration
        await db.delete(stats).where(eq(stats.sectionId, id));
      } else if (existingSection.type === 'toggle') {
        // Delete the toggle configuration
        await db.delete(toggle).where(eq(toggle.sectionId, id));
      }
    } catch (error: any) {
      console.error('Error deleting section related data:', error);
      throw createError({
        statusCode: 500,
        message: `Failed to delete section related data: ${error.message || 'Unknown error'}`,
      });
    }

    // Delete the section
    await db.delete(section).where(eq(section.id, id));

    return {
      success: true,
      message: `Section with ID ${id} removed successfully`,
    };
  } catch (error: any) {
    console.error('Error removing section', error);
    throw createError({
      statusCode: 500,
      message: `Failed to remove section: ${error.message || 'Unknown error'}`,
    });
  }
});
