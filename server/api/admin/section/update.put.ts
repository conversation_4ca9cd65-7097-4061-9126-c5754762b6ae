import { eq } from 'drizzle-orm';
import type { ButtonStyle } from '~~/server/database/tables';
import {
  accordion,
  card,
  cardList,
  section,
  cardContent,
  cardFiles,
  linkButton,
  files,
  stats,
  toggle,
  tempFiles,
} from '~~/server/database/tables';
import {
  downloadButton,
  downloadFiles,
} from '~~/server/database/tables/buttons/downloadButton';
import { updateSectionSchema } from '~~/shared/schema/section/update';
import type {
  CardConfig,
  FileInsert,
  CardFiles,
  CardContent,
  CardLayout,
  LinkButtonConfig,
  DownloadButtonConfig,
  StandardCard,
} from '~~/server/helper/section';
import {
  isButtonIncludedCard,
  isStandardCard,
  isVisionCard,
  isProfileCard,
  processSectionConfig,
} from '~~/server/helper/section';

function createContentEntry(
  cardId: number,
  priority: number,
  title: string,
  content: string
): CardContent {
  return {
    cardId,
    priority,
    title,
    content,
  };
}

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id, configuration, type } = updateSectionSchema.parse(body);
    const db = useDrizzle();

    // Check if section exists
    const existingSection = await db.query.section.findFirst({
      where: eq(section.id, id),
    });

    if (!existingSection) {
      throw createError({
        statusCode: 404,
        message: `Section with ID ${id} not found`,
      });
    }

    // Update the section - always keep priority as 0 for single section
    const [updatedSection] = await db
      .update(section)
      .set({
        type: type || existingSection.type,
        priority: 0, // Always set priority to 0 for single section
      })
      .where(eq(section.id, id))
      .returning();

    // This will hold the configuration result based on section type
    let configResult: Record<string, unknown> | null = null;

    if (configuration && type) {
      // Delete existing configuration first
      if (existingSection.type === 'cardList') {
        const existingCards = await db.query.card.findMany({
          where: eq(card.sectionId, id),
        });

        for (const existingCard of existingCards) {
          await db
            .delete(cardContent)
            .where(eq(cardContent.cardId, existingCard.id));
          await db
            .delete(cardFiles)
            .where(eq(cardFiles.cardId, existingCard.id));
          if (isButtonIncludedCard(existingCard.layout as CardLayout)) {
            await db
              .delete(linkButton)
              .where(eq(linkButton.cardTemplateId, existingCard.id));
            await db
              .delete(downloadButton)
              .where(eq(downloadButton.cardTemplateId, existingCard.id));
          }
        }

        await db.delete(card).where(eq(card.sectionId, id));
        await db.delete(cardList).where(eq(cardList.sectionId, id));
      } else if (existingSection.type === 'accordion') {
        await db.delete(accordion).where(eq(accordion.sectionId, id));
      } else if (existingSection.type === 'stats') {
        await db.delete(stats).where(eq(stats.sectionId, id));
      } else if (existingSection.type === 'toggle') {
        await db.delete(toggle).where(eq(toggle.sectionId, id));
      } else if (existingSection.type === 'card') {
        const existingCard = await db.query.card.findFirst({
          where: eq(card.sectionId, id),
        });

        if (existingCard) {
          await db
            .delete(cardContent)
            .where(eq(cardContent.cardId, existingCard.id));
          await db
            .delete(cardFiles)
            .where(eq(cardFiles.cardId, existingCard.id));
          await db
            .delete(linkButton)
            .where(eq(linkButton.cardTemplateId, existingCard.id));
          await db
            .delete(downloadButton)
            .where(eq(downloadButton.cardTemplateId, existingCard.id));
          await db.delete(card).where(eq(card.id, existingCard.id));
        }
      }

      // Insert new configuration based on type
      if (type === 'accordion') {
        [configResult] = await db
          .insert(accordion)
          .values({
            title: configuration.title || '',
            description: configuration.description || null,
            content: configuration.content || null,
            sectionId: id,
          })
          .returning();
      } else if (type === 'card') {
        const cardConfig = configuration as CardConfig;
        const [newCard] = await db
          .insert(card)
          .values({
            layout: cardConfig.layout,
            title: cardConfig.title || null,
            key: cardConfig.key || 'default-key', // Provide a default key if not present
            sectionId: id,
          })
          .returning();

        // Handle content based on layout type
        const contentEntries: CardContent[] = [];
        if (isStandardCard(cardConfig)) {
          // Always insert content1, using empty string defaults if missing
          contentEntries.push(
            createContentEntry(
              newCard.id,
              1,
              cardConfig.content1?.title ?? '',
              cardConfig.content1?.content ?? ''
            )
          );
        } else if (isProfileCard(cardConfig)) {
          if (cardConfig.content1?.title && cardConfig.content1?.content) {
            contentEntries.push(
              createContentEntry(
                newCard.id,
                1,
                cardConfig.content1.title,
                cardConfig.content1.content
              )
            );
          }
          if (cardConfig.content2?.title && cardConfig.content2?.content) {
            contentEntries.push(
              createContentEntry(
                newCard.id,
                2,
                cardConfig.content2.title,
                cardConfig.content2.content
              )
            );
          }
        } else if (isVisionCard(cardConfig)) {
          if (cardConfig.content1?.title && cardConfig.content1?.content) {
            contentEntries.push(
              createContentEntry(
                newCard.id,
                1,
                cardConfig.content1.title,
                cardConfig.content1.content
              )
            );
          }
        }

        if (contentEntries.length > 0) {
          await db.insert(cardContent).values(
            contentEntries.map((entry) => ({
              ...entry,
            }))
          );
        }

        // Delete all existing card files before handling new files
        await db.delete(cardFiles).where(eq(cardFiles.cardId, newCard.id));

        // Handle image
        if (cardConfig.image?.pathname) {
          // Insert new file
          const [newFile] = await db
            .insert(files)
            .values({
              pathname: cardConfig.image.pathname,
              title: cardConfig.image.title || '',
              type: cardConfig.image.type || '',
            } as FileInsert)
            .returning();

          // Link file to card
          await db.insert(cardFiles).values({
            cardId: newCard.id,
            fileId: newFile.id,
          } as CardFiles);

          // Clean up temp file
          await db
            .delete(tempFiles)
            .where(eq(tempFiles.pathname, cardConfig.image.pathname));
        }

        // Handle files array (additional files) for standard cards only
        if (
          cardConfig.layout === 'standard' &&
          Array.isArray((cardConfig as StandardCard).files)
        ) {
          // Get the primary image pathname to avoid duplication
          const primaryImagePathname = cardConfig.image?.pathname || '';

          for (const file of (cardConfig as StandardCard).files || []) {
            // Skip if this file is the same as the primary image
            if (file?.pathname && file.pathname !== primaryImagePathname) {
              const [newFile] = await db
                .insert(files)
                .values({
                  pathname: file.pathname,
                  title: file.title || '',
                  type: file.type || '',
                } as FileInsert)
                .returning();

              await db.insert(cardFiles).values({
                cardId: newCard.id,
                fileId: newFile.id,
              } as CardFiles);

              // Clean up temp file
              await db
                .delete(tempFiles)
                .where(eq(tempFiles.pathname, file.pathname));
            } else if (file?.pathname === primaryImagePathname) {
              console.log('Skipping primary image from files array:', file);
            }
          }
        }

        // Handle buttons based on layout type
        // isButtonIncludedCard checks if layout is 'standard' or 'profile'
        if (isButtonIncludedCard(cardConfig.layout)) {
          if (isStandardCard(cardConfig) || cardConfig.layout === 'profile') {
            // Use proper type guards to check for button properties
            const hasLinkButton =
              'linkButton' in cardConfig && cardConfig.linkButton;
            const hasDownloadButton =
              'downloadButton' in cardConfig && cardConfig.downloadButton;

            if (hasLinkButton) {
              const button = cardConfig.linkButton as LinkButtonConfig;
              await db.insert(linkButton).values({
                cardTemplateId: newCard.id,
                title: button.title,
                style: button.style as ButtonStyle,
                icon: button.icon,
                externalLink: button.externalLink,
                internalLink: button.internalLink,
                newTab: button.newTab,
              });
            }

            if (hasDownloadButton) {
              const button = cardConfig.downloadButton as DownloadButtonConfig;
              const [newDownloadButton] = await db
                .insert(downloadButton)
                .values({
                  cardTemplateId: newCard.id,
                  title: button.title,
                  style: button.style as ButtonStyle,
                  icon: button.icon,
                  newTab: button.newTab,
                })
                .returning();

              if (button.file) {
                const [newFile] = await db
                  .insert(files)
                  .values({
                    pathname: button.file.pathname,
                    title: button.file.title,
                    type: button.file.type,
                  } as FileInsert)
                  .returning();

                await db.insert(downloadFiles).values({
                  downloadButtonId: newDownloadButton.id,
                  fileId: newFile.id,
                });
              }
            }
          }
        }

        configResult = newCard;
      } else if (
        type === 'cardList' &&
        Array.isArray(configuration.cards) &&
        configuration.cards.length > 0
      ) {
        // Create cardList configuration
        const [newCardList] = await db
          .insert(cardList)
          .values({
            sectionId: id,
            title: configuration.title || '',
            description: configuration.description || null,
            maxCards: configuration.maxCards || 1,
            minCards: configuration.minCards || 0,
            layout: configuration?.cards?.[0]?.layout || 'standard',
          })
          .returning();

        configResult = newCardList;
        // Process each card in the cardList
        for (const cardConfig of configuration.cards) {
          // Create base card entry
          const [newCard] = await db
            .insert(card)
            .values({
              sectionId: id,
              layout: cardConfig.layout,
              title: 'title' in cardConfig ? cardConfig.title : null,
              key: cardConfig.key || 'default-key', // Provide a default key if not present
            })
            .returning();

          // Handle content blocks based on layout
          const contentEntries: CardContent[] = [];
          const cardLayout = cardConfig.layout;

          // Process content based on card layout type
          if (cardLayout === 'standard') {
            // For standard card, handle content1
            if (cardConfig.content1?.title && cardConfig.content1?.content) {
              contentEntries.push(
                createContentEntry(
                  newCard.id,
                  1,
                  cardConfig.content1.title,
                  cardConfig.content1.content
                )
              );
            }
          } else if (cardLayout === 'vision') {
            // For vision card, handle content1
            if (cardConfig.content1?.title && cardConfig.content1?.content) {
              contentEntries.push(
                createContentEntry(
                  newCard.id,
                  1,
                  cardConfig.content1.title,
                  cardConfig.content1.content
                )
              );
            }
          } else if (cardLayout === 'profile') {
            // For profile card, handle content1
            if (cardConfig.content1?.title && cardConfig.content1?.content) {
              contentEntries.push(
                createContentEntry(
                  newCard.id,
                  1,
                  cardConfig.content1.title,
                  cardConfig.content1.content
                )
              );
            }
            // Check if content2 exists in this specific card config type
            if (
              'content2' in cardConfig &&
              cardConfig.content2?.title &&
              cardConfig.content2?.content
            ) {
              contentEntries.push(
                createContentEntry(
                  newCard.id,
                  2,
                  cardConfig.content2.title,
                  cardConfig.content2.content
                )
              );
            }
          } else {
            throw createError({
              statusCode: 400,
              message: `Invalid card layout type: ${cardLayout}`,
            });
          }

          // Insert content blocks
          if (contentEntries.length > 0) {
            await db.insert(cardContent).values(contentEntries);
          }

          // Handle image file relation
          if (cardConfig.image) {
            const [newFile] = await db
              .insert(files)
              .values({
                pathname: cardConfig.image.pathname,
                title: cardConfig.image.title,
                type: cardConfig.image.type,
              } as FileInsert)
              .returning();

            await db.insert(cardFiles).values({
              cardId: newCard.id,
              fileId: newFile.id,
            } as CardFiles);
          }

          // Handle buttons
          if (isButtonIncludedCard(cardLayout)) {
            const buttonConfig: (LinkButtonConfig | DownloadButtonConfig)[] =
              [];

            // Type-safe check if properties exist
            const hasLinkButton =
              'linkButton' in cardConfig && cardConfig.linkButton;
            const hasDownloadButton =
              'downloadButton' in cardConfig && cardConfig.downloadButton;

            if (hasLinkButton) {
              buttonConfig.push(cardConfig.linkButton as LinkButtonConfig);
            }
            if (hasDownloadButton) {
              buttonConfig.push(
                cardConfig.downloadButton as DownloadButtonConfig
              );
            }

            for (const button of buttonConfig) {
              if (button.type === 'link') {
                await db.insert(linkButton).values({
                  cardTemplateId: newCard.id,
                  title: button.title,
                  style: button.style as ButtonStyle,
                  icon: button.icon,
                  externalLink: button.externalLink,
                  internalLink: button.internalLink,
                  newTab: button.newTab,
                });
              } else if (button.type === 'download' && button.file) {
                const [newDownloadButton] = await db
                  .insert(downloadButton)
                  .values({
                    cardTemplateId: newCard.id,
                    title: button.title,
                    style: button.style as ButtonStyle,
                    icon: button.icon || null,
                    newTab: button.newTab,
                  })
                  .returning();

                // Create file record for the download button
                const [newFile] = await db
                  .insert(files)
                  .values({
                    pathname: button.file.pathname,
                    title: button.file.title,
                    type: button.file.type,
                  } as FileInsert)
                  .returning();

                await db.insert(downloadFiles).values({
                  downloadButtonId: newDownloadButton.id,
                  fileId: newFile.id,
                });
              }
            }
          }
        }
      }
    }

    let processedConfig = null;

    if (configResult) {
      if (updatedSection.type === 'card') {
        // For card type, we need to fetch additional data
        const cardContents = await db.query.cardContent.findMany({
          where: eq(cardContent.cardId, configResult.id as number),
        });

        // Get all card files
        const cardFilesResult = await db.query.cardFiles.findMany({
          where: eq(cardFiles.cardId, configResult.id as number),
          with: {
            file: true,
          },
        });

        const cardLinkButton = await db.query.linkButton.findFirst({
          where: eq(linkButton.cardTemplateId, configResult.id as number),
        });

        // Create a complete card config object
        const completeCardConfig = {
          ...configResult,
          contents: cardContents,
          cardFiles: cardFilesResult,
          linkButton: cardLinkButton,
        };

        // Process the card config using the shared function
        processedConfig = processSectionConfig(
          updatedSection,
          completeCardConfig
        );
      } else if (updatedSection.type === 'cardList') {
        // For cardList, we need to fetch cards separately
        const cards = await db.query.card.findMany({
          where: eq(card.sectionId, updatedSection.id),
          with: {
            contents: true,
            cardFiles: {
              with: {
                file: true,
              },
            },
            linkButton: true,
          },
        });

        // Process the cardList with its cards using the shared function
        processedConfig = processSectionConfig(
          updatedSection,
          configResult,
          cards
        );
        console.log(
          '🚀 ~ defineEventHandler ~ processedConfig:',
          processedConfig
        );
      } else if (updatedSection.type === 'accordion') {
        // For accordion, just use the config as is
        processedConfig = configResult;
      } else {
        // For other types, use the shared function
        processedConfig = processSectionConfig(updatedSection, configResult);
      }
    }

    return {
      success: true,
      data: {
        id: updatedSection.id,
        type: updatedSection.type,
        priority: updatedSection.priority,
        templateId: updatedSection.templateId,
        createdAt: updatedSection.createdAt,
        updatedAt: updatedSection.updatedAt,
        configuration: processedConfig,
      },
    };
  } catch (error) {
    console.error('Error updating section', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to update section',
    });
  }
});
