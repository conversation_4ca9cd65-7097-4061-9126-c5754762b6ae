import { album } from '~~/server/database/tables/album';
import { createAlbumSchema } from '~~/shared/schema/album/create';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const validatedData = createAlbumSchema.parse(body);
    const db = useDrizzle();

    // Create the album
    const [newAlbum] = await db
      .insert(album)
      .values({
        title: validatedData.title,
      })
      .returning();

    return { success: true, data: newAlbum };
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
