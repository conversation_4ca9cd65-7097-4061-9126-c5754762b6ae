import { eq } from 'drizzle-orm';
import { album } from '~~/server/database/schema';
import { idSchema } from '~~/shared/schema';

export default defineEventHandler(async (event) => {
  try {
    const params = getRouterParams(event);
    const { id } = idSchema.parse(params);

    const db = useDrizzle();

    await db.delete(album).where(eq(album.id, id));

    return {
      success: true,
      message: 'Album deleted successfully',
    };
  } catch (error: any) {
    console.error('Failed to delete album:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || 'Failed to delete album',
    });
  }
});
