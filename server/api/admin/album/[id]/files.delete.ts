import { eq, inArray, and } from 'drizzle-orm';
import { album, albumFiles } from '~~/server/database/schema';
import { idSchema } from '~~/shared/schema';
import { deleteFileSchema } from '~~/shared/schema/album/update';

export default defineEventHandler(async (event) => {
  try {
    const params = getRouterParams(event);
    const { id } = idSchema.parse(params);

    const db = useDrizzle();
    const albumData = await db.select().from(album).where(eq(album.id, id));

    // Check if album exists (array has items)
    if (!albumData || albumData.length === 0) {
      return {
        success: false,
        statusCode: 404,
        message: 'Album not found',
      };
    }

    const body = await readBody(event);
    const validatedData = deleteFileSchema.parse(body);

    // Validate fileIds array
    if (!validatedData.fileIds || validatedData.fileIds.length === 0) {
      return {
        success: false,
        message: 'No file IDs provided',
      };
    }

    try {
      const albumFilesData = await db.query.albumFiles.findMany({
        where: and(
          eq(albumFiles.albumId, id),
          inArray(albumFiles.fileId, validatedData.fileIds)
        ),
        with: {
          file: {
            columns: {
              pathname: true,
              title: true,
              type: true,
              id: true,
            },
          },
        },
      });

      // Check if any files were found
      if (!albumFilesData || albumFilesData.length === 0) {
        return {
          success: false,
          message: 'No matching files found',
        };
      }

      const deletePromises = albumFilesData.map(async (file) => {
        try {
          // Delete from database first
          await db.delete(albumFiles).where(eq(albumFiles.id, file.id));

          // Then attempt to delete the actual file
          if (file.file && file.file.pathname) {
            await hubBlob().del(file.file.pathname);
          }
          return { success: true, fileId: file.id };
        } catch (fileError: unknown) {
          console.error(`Failed to delete file ${file.id}:`, fileError);
          const errorMessage =
            fileError instanceof Error
              ? fileError.message
              : 'Unknown error occurred during file deletion';
          return { success: false, fileId: file.id, error: errorMessage };
        }
      });

      const results = await Promise.allSettled(deletePromises);
      const successCount = results.filter(
        (r) => r.status === 'fulfilled' && r.value.success
      ).length;

      return {
        success: true,
        message: `${successCount} of ${albumFilesData.length} files deleted successfully`,
        details: results.map((r) =>
          r.status === 'fulfilled'
            ? r.value
            : {
                success: false,
                error:
                  r.reason instanceof Error
                    ? r.reason.message
                    : String(r.reason),
              }
        ),
      };
    } catch (queryError: unknown) {
      console.error('Failed to query album files:', queryError);
      return {
        success: false,
        statusCode: 500,
        message:
          queryError instanceof Error
            ? queryError.message
            : 'Failed to query album files',
      };
    }
  } catch (error: unknown) {
    console.error('Failed to delete files:', error);
    // Ensure we always return a response, even in error cases
    return {
      success: false,
      statusCode:
        error instanceof Error && 'statusCode' in error
          ? (error as any).statusCode || 500
          : 500,
      message:
        error instanceof Error ? error.message : 'Failed to delete files',
    };
  }
});
