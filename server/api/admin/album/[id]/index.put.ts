import { album, albumFiles } from '~~/server/database/schema';
import { files, tempFiles } from '~~/server/database/tables/files';
import { uploadFileSchema } from '~~/shared/schema/album/create';
import { idSchema } from '~~/shared/schema';
import { inArray } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const params = getRouterParams(event);
    const { id } = idSchema.parse(params);

    const db = useDrizzle();
    const albumData = await db.select().from(album).where(eq(album.id, id));
    if (!albumData) {
      throw createError({
        statusCode: 404,
        message: 'Album not found',
      });
    }

    const body = await readBody(event);
    const validatedData = uploadFileSchema.parse(body);
    // Handle file attachments if provided
    if (validatedData.files && validatedData.files.length > 0) {
      // Get temp files data to be able to delete them later
      const pathnames = validatedData.files.map((f) => f.pathname);
      // First, create file records
      const fileRecords = await db
        .insert(files)
        .values(
          validatedData.files.map((file) => ({
            pathname: file.pathname,
            title: file.title,
            type: file.type,
          }))
        )
        .returning();

      await db.delete(tempFiles).where(inArray(tempFiles.pathname, pathnames));

      // Then create the relationships in the junction table
      await db.insert(albumFiles).values(
        fileRecords.map((file) => ({
          albumId: id,
          fileId: file.id,
          fileType: file.type,
        }))
      );

      return { success: true, data: albumData };

      // // Delete temp files from storage and database
      // const tempFileRecords = await db.select()
      //   .from(tempFiles)
      //   .where(inArray(tempFiles.pathname, pathnames))
      // await Promise.all(tempFileRecords.map(async (tempFile) => {
      //   try {
      //     // Delete from blob storage
      //     await hubBlob().del(tempFile.pathname)

      //     // Delete from temp files table
      //     await db.delete(tempFiles)
      //       .where(eq(tempFiles.pathname, tempFile.pathname))
      //   }
      //   catch (error) {
      //     console.error(`Failed to delete temp file ${tempFile.pathname}:`, error)
      //   }
      // }))
    }
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
