import { eq } from 'drizzle-orm';
import { album } from '~~/server/database/schema';
import { idSchema } from '~~/shared/schema';
import type { AlbumData } from '~~/shared/schema/album/get';

export default defineEventHandler(async (event) => {
  try {
    const params = getRouterParams(event);
    const { id } = idSchema.parse(params);

    const db = useDrizzle();

    const albumData = await db.query.album.findFirst({
      where: eq(album.id, id),
      with: {
        files: {
          columns: {
            id: true,
          },
          with: {
            file: {
              columns: {
                pathname: true,
                title: true,
                type: true,
                id: true,
              },
            },
          },
        },
      },
    });

    if (!albumData) {
      throw createError({
        statusCode: 404,
        message: 'Album not found',
      });
    }

    // Transform album data
    const transformedData: AlbumData = {
      id: albumData.id,
      title: albumData.title,
      photoCount: albumData.files.length,
      createdAt: albumData.createdAt,
      photos: albumData.files.map((item) => ({
        id: item.file.id,
        title: item.file?.title ?? '',
        pathname: item.file.pathname,
        type: item.file.type,
      })),
    };

    return {
      success: true,
      data: transformedData,
    };
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
