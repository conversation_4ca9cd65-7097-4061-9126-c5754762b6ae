import { eq } from 'drizzle-orm';

import { quickLink } from '~~/server/database/tables/quick-link';
import { useDrizzle } from '~~/server/utils/drizzle';
import { createQuickLinkSchema } from '~~/shared/schema/quick-link/create';

export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const data = {
    ...createQuickLinkSchema.parse(body),
    type: 'quickLink' as const,
  };

  const db = useDrizzle();

  // Check if title already exists
  const existing = await db
    .select()
    .from(quickLink)
    .where(eq(quickLink.title, data.title))
    .get();

  if (existing) {
    throw createError({
      statusCode: 400,
      message: 'Quick link with this title already exists',
    });
  }

  const [created] = await db.insert(quickLink).values(data).returning();

  return created;
});
