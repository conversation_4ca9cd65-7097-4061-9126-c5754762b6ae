import { desc, sql } from 'drizzle-orm';
import { z } from 'zod';

import { quickLink } from '~~/server/database/tables/quick-link';
import { useDrizzle } from '~~/server/utils/drizzle';

export default defineEventHandler(async (event) => {
  const query = getQuery(event);

  const schema = z.object({
    page: z.coerce.number().min(1).default(1),
    limit: z.coerce.number().min(1).max(100).default(10),
  });

  const { page, limit } = schema.parse(query);
  const offset = (page - 1) * limit;

  const db = useDrizzle();

  const [total, items] = await Promise.all([
    db
      .select({ count: sql<number>`count(*)` })
      .from(quickLink)
      .where(eq(quickLink.type, 'quickLink'))
      .then((res: { count: number }[]) => res[0].count),
    db
      .select()
      .from(quickLink)
      .where(eq(quickLink.type, 'quickLink'))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(quickLink.createdAt)),
  ]);

  return {
    items,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
  };
});
