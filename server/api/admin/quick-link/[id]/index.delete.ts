import { eq } from 'drizzle-orm';

import { quickLink } from '~~/server/database/tables/quick-link';
import { useDrizzle } from '~~/server/utils/drizzle';

export default defineEventHandler(async (event) => {
  const id = getRouterParam(event, 'id');
  if (!id) {
    throw createError({
      statusCode: 400,
      message: 'ID is required',
    });
  }

  const db = useDrizzle();

  // Check if quick link exists
  const existing = await db
    .select()
    .from(quickLink)
    .where(eq(quickLink.id, parseInt(id, 10)))
    .get();

  if (!existing) {
    throw createError({
      statusCode: 404,
      message: 'Quick link not found',
    });
  }

  const [deleted] = await db
    .delete(quickLink)
    .where(eq(quickLink.id, parseInt(id, 10)))
    .returning();

  return deleted;
});
