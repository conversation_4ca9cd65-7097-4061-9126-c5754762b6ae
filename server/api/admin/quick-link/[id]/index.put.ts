import { eq } from 'drizzle-orm';

import { quickLink } from '~~/server/database/tables/quick-link';
import { useDrizzle } from '~~/server/utils/drizzle';
import { updateQuickLinkSchema } from '~~/shared/schema/quick-link/update';

export default defineEventHandler(async (event) => {
  const id = getRouterParam(event, 'id');
  if (!id) {
    throw createError({
      statusCode: 400,
      message: 'ID is required',
    });
  }

  const parsedId = parseInt(id, 10);
  if (isNaN(parsedId)) {
    throw createError({
      statusCode: 400,
      message: 'Invalid ID format',
    });
  }

  const body = await readBody(event);
  const data = updateQuickLinkSchema.parse(body);

  const db = useDrizzle();

  // Check if quick link exists
  const existing = await db
    .select()
    .from(quickLink)
    .where(eq(quickLink.id, parsedId))
    .get();

  if (!existing) {
    throw createError({
      statusCode: 404,
      message: 'Quick link not found',
    });
  }

  // If title is being updated, check if it's unique
  if (data.title && data.title !== existing.title) {
    const titleExists = await db
      .select()
      .from(quickLink)
      .where(eq(quickLink.title, data.title))
      .get();

    if (titleExists) {
      throw createError({
        statusCode: 400,
        message: 'Quick link with this title already exists',
      });
    }
  }

  const [updated] = await db
    .update(quickLink)
    .set(data)
    .where(eq(quickLink.id, parsedId))
    .returning();

  return updated;
});
