import { dynamicTableData } from '~~/server/database/tables/dynamic-table';
import { eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Delete a file upload',
    tags: ['File Uploads'],
    parameters: [
      {
        in: 'path',
        name: 'uid',
        required: true,
        schema: {
          type: 'string',
        },
        description: 'File upload ID',
      },
    ],
    responses: {
      200: {
        description: 'File upload deleted successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                message: { type: 'string' },
                id: { type: 'number' },
              },
              required: ['success', 'message', 'id'],
            },
          },
        },
      },
      400: {
        description: 'Invalid ID',
      },
      404: {
        description: 'File upload not found',
      },
      500: {
        description: 'Internal server error',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  try {
    const uid = getRouterParam(event, 'uid');

    if (!uid) {
      return sendError(
        event,
        createError({
          statusCode: 400,
          statusMessage: 'Missing or invalid "uid" parameter',
        })
      );
    }

    const fileUploadId = parseInt(uid, 10);
    if (isNaN(fileUploadId)) {
      return sendError(
        event,
        createError({
          statusCode: 400,
          statusMessage: 'Invalid file upload ID',
        })
      );
    }

    const db = useDrizzle();

    // Find the file upload record first
    const fileUploadRecord = await db.query.dynamicTableData.findFirst({
      where: eq(dynamicTableData.id, fileUploadId),
    });

    if (!fileUploadRecord) {
      return sendError(
        event,
        createError({
          statusCode: 404,
          statusMessage: 'File upload not found',
        })
      );
    }

    // Delete the file upload record
    const [deleted] = await db
      .delete(dynamicTableData)
      .where(eq(dynamicTableData.id, fileUploadId))
      .returning();

    return {
      success: true,
      message: 'File upload deleted successfully',
      id: deleted.id,
    };
  } catch (err: any) {
    return sendError(
      event,
      createError({
        statusCode: 500,
        statusMessage: 'Internal server error',
        data: err?.message || err,
      })
    );
  }
});
