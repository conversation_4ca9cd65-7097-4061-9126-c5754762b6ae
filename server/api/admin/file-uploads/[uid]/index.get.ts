import { dynamicTableData } from '~~/server/database/tables/dynamic-table';
import type { FILE_UPLOADS } from '~~/server/database/tables/dynamic-types';
import { eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Get a specific file upload by ID',
    tags: ['File Uploads'],
    parameters: [
      {
        in: 'path',
        name: 'uid',
        required: true,
        schema: {
          type: 'string',
        },
        description: 'File upload ID',
      },
    ],
    responses: {
      200: {
        description: 'File upload details',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                data: {
                  type: 'object',
                  properties: {
                    id: { type: 'number' },
                    title: { type: 'string' },
                    type: { type: 'string' },
                    file: {
                      type: 'object',
                      properties: {
                        pathname: { type: 'string' },
                        title: { type: 'string' },
                        type: { type: 'string' },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      400: {
        description: 'Invalid ID',
      },
      404: {
        description: 'File upload not found',
      },
      500: {
        description: 'Internal server error',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  try {
    const uid = getRouterParam(event, 'uid');
    
    if (!uid) {
      return sendError(
        event,
        createError({
          statusCode: 400,
          statusMessage: 'Missing or invalid "uid" parameter',
        })
      );
    }

    const fileUploadId = parseInt(uid, 10);
    if (isNaN(fileUploadId)) {
      return sendError(
        event,
        createError({
          statusCode: 400,
          statusMessage: 'Invalid file upload ID',
        })
      );
    }

    const db = useDrizzle();

    // Find the file upload record
    const fileUploadRecord = await db.query.dynamicTableData.findFirst({
      where: eq(dynamicTableData.id, fileUploadId),
    });

    if (!fileUploadRecord) {
      return sendError(
        event,
        createError({
          statusCode: 404,
          statusMessage: 'File upload not found',
        })
      );
    }

    // Extract the data
    const fileUploadData = fileUploadRecord.data as FILE_UPLOADS;

    return {
      success: true,
      data: {
        id: fileUploadRecord.id,
        title: fileUploadData.title,
        type: fileUploadData.type,
        file: fileUploadData.file,
      },
    };
  } catch (err: any) {
    return sendError(
      event,
      createError({
        statusCode: 500,
        statusMessage: 'Internal server error',
        data: err?.message || err,
      })
    );
  }
});
