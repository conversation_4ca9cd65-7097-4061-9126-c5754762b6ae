import {
  dynamicTableData,
  dynamicTable,
} from '~~/server/database/tables/dynamic-table';
import { files, tempFiles } from '~~/server/database/tables/files';
import type { FILE_UPLOADS } from '~~/server/database/tables/dynamic-types';
import {
  isFILE_UPLOADS,
  validateTableData,
} from '~~/server/database/tables/dynamic-types';
import { downloadSchema } from '~~/shared/schema/file-uploads/create';
import { eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Create a new file upload',
    tags: ['File Uploads'],
    requestBody: {
      description: 'File upload data',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              type: { type: 'string' },
              file: {
                type: 'object',
                properties: {
                  pathname: { type: 'string' },
                  title: { type: 'string' },
                  type: { type: 'string' },
                },
                required: ['pathname', 'type'],
              },
            },
            required: ['title', 'type', 'file'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'File upload created successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                data: {
                  type: 'object',
                  properties: {
                    id: { type: 'number' },
                    title: { type: 'string' },
                    type: { type: 'string' },
                    file: {
                      type: 'object',
                      properties: {
                        pathname: { type: 'string' },
                        title: { type: 'string' },
                        type: { type: 'string' },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      400: {
        description: 'Invalid request body',
      },
      500: {
        description: 'Internal server error',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    // Validate with zod schema
    const parsed = downloadSchema.safeParse(body);
    if (!parsed.success) {
      return sendError(
        event,
        createError({
          statusCode: 400,
          statusMessage: 'Invalid request body',
          data: parsed.error.flatten(),
        })
      );
    }

    // Compose the FILE_UPLOADS object (requires .type at top level)
    const { title, file, type: providedType } = { ...parsed.data, ...body };

    // Determine file type if not provided
    let fileType = providedType;
    if (!fileType) {
      // Extract MIME type from file.type
      const mimeType = file.type;

      // Determine file type category based on MIME type
      if (mimeType.startsWith('image/')) {
        fileType = 'image';
      } else if (mimeType.startsWith('video/')) {
        fileType = 'video';
      } else if (mimeType.startsWith('audio/')) {
        fileType = 'audio';
      } else if (mimeType === 'application/pdf') {
        fileType = 'document';
      } else if (
        mimeType === 'text/plain' ||
        mimeType === 'text/html' ||
        mimeType === 'text/css' ||
        mimeType === 'text/javascript'
      ) {
        fileType = 'text';
      } else if (
        mimeType === 'application/msword' ||
        mimeType ===
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
        mimeType === 'application/vnd.ms-excel' ||
        mimeType ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        mimeType === 'application/vnd.ms-powerpoint' ||
        mimeType ===
          'application/vnd.openxmlformats-officedocument.presentationml.presentation'
      ) {
        fileType = 'document';
      } else {
        fileType = 'other';
      }
    }

    if (typeof fileType !== 'string' || !fileType) {
      return sendError(
        event,
        createError({
          statusCode: 400,
          statusMessage: 'Could not determine file type',
        })
      );
    }

    const fileUpload: FILE_UPLOADS = { title, type: fileType, file };

    // Type guard
    if (!isFILE_UPLOADS(fileUpload)) {
      return sendError(
        event,
        createError({
          statusCode: 400,
          statusMessage: 'Request does not match FILE_UPLOADS type',
        })
      );
    }

    // Validate the data
    if (!validateTableData('file_uploads', fileUpload)) {
      return sendError(
        event,
        createError({
          statusCode: 400,
          statusMessage: 'Invalid file upload data',
        })
      );
    }

    const db = useDrizzle();

    // Get or create the file_uploads table
    let table = await db.query.dynamicTable.findFirst({
      where: eq(dynamicTable.slug, 'file_uploads'),
    });

    if (!table) {
      // Create the table if it doesn't exist
      const [newTable] = await db
        .insert(dynamicTable)
        .values({
          slug: 'file_uploads',
          name: 'File Uploads',
          type: 'file_uploads',
        })
        .returning();

      table = newTable;
    }

    // Add the file to the files table if it's not already there
    await db
      .insert(files)
      .values({
        pathname: file.pathname,
        title: file.title,
        type: file.type,
      })
      .onConflictDoNothing();

    // Insert the file upload record
    const [created] = await db
      .insert(dynamicTableData)
      .values({
        tableId: table.id,
        data: fileUpload,
      })
      .returning();

    // Remove the temporary file if it exists
    await db.delete(tempFiles).where(eq(tempFiles.pathname, file.pathname));

    return {
      success: true,
      data: {
        id: created.id,
        ...fileUpload,
      },
    };
  } catch (err: any) {
    return sendError(
      event,
      createError({
        statusCode: 500,
        statusMessage: 'Internal server error',
        data: err?.message || err,
      })
    );
  }
});
