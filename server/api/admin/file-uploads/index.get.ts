import {
  dynamicTableData,
  dynamicTable,
} from '~~/server/database/tables/dynamic-table';
import type { FILE_UPLOADS } from '~~/server/database/tables/dynamic-types';
import { paginationSchema } from '~~/shared/schema/pagination';
import { sql, and, eq } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Get all file uploads with pagination',
    tags: ['File Uploads'],
    parameters: [
      {
        in: 'query',
        name: 'page',
        required: false,
        schema: {
          type: 'number',
          default: 1,
        },
        description: 'Page number',
      },
      {
        in: 'query',
        name: 'limit',
        required: false,
        schema: {
          type: 'number',
          default: 10,
        },
        description: 'Number of items per page',
      },
      {
        in: 'query',
        name: 'type',
        required: false,
        schema: {
          type: 'string',
        },
        description: 'Filter by file type',
      },
    ],
    responses: {
      200: {
        description: 'List of file uploads',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'number' },
                      title: { type: 'string' },
                      type: { type: 'string' },
                      file: {
                        type: 'object',
                        properties: {
                          pathname: { type: 'string' },
                          title: { type: 'string' },
                          type: { type: 'string' },
                        },
                      },
                    },
                  },
                },
                pagination: {
                  type: 'object',
                  properties: {
                    page: { type: 'number' },
                    limit: { type: 'number' },
                    totalItems: { type: 'number' },
                    totalPages: { type: 'number' },
                    hasNextPage: { type: 'boolean' },
                    hasPrevPage: { type: 'boolean' },
                  },
                },
              },
            },
          },
        },
      },
      404: {
        description: 'Table not found',
      },
    },
  },
});

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event);
    const { page, limit } = await paginationSchema.parseAsync(query);
    const offset = (page - 1) * limit;
    const fileType = query.type as string | undefined;

    const db = useDrizzle();
    const table = await db.query.dynamicTable.findFirst({
      where: eq(dynamicTable.slug, 'file_uploads'),
    });

    if (!table) {
      // Create the table if it doesn't exist
      await db.insert(dynamicTable).values({
        slug: 'file_uploads',
        name: 'File Uploads',
        type: 'file_uploads',
      });

      return {
        success: true,
        data: [],
        pagination: {
          page,
          limit,
          totalItems: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPrevPage: false,
        },
      };
    }

    // Build the where clause based on filters
    let whereClause: any = eq(dynamicTableData.tableId, table.id);

    // Add type filter if provided
    if (fileType) {
      whereClause = and(
        whereClause,
        sql`json_extract(${dynamicTableData.data}, '$.type') = ${fileType}`
      );
    }

    // Get total count for pagination
    const totalCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(dynamicTableData)
      .where(whereClause)
      .then((result) => result[0].count);

    const totalPages = Math.ceil(totalCount / limit);

    // Get the file uploads with pagination
    const fileUploads = await db.query.dynamicTableData.findMany({
      where: whereClause,
      columns: {
        createdAt: false,
        updatedAt: false,
        tableId: false,
        id: true,
        data: true,
      },
      limit,
      offset,
    });

    // Map the results to the expected format
    const list = fileUploads.map((item) => {
      const data = item.data as FILE_UPLOADS;
      return {
        id: item.id,
        title: data.title,
        type: data.type,
        file: data.file,
      };
    });

    return {
      success: true,
      data: list,
      pagination: {
        page,
        limit,
        totalItems: totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    };
  } catch (err: any) {
    return createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
      data: err?.message || err,
    });
  }
});
