import { student } from '~~/server/database/tables';
import { eq, and, count } from 'drizzle-orm';
import type { Pagination } from '~~/app/types/home/<USER>';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');
    const query = getQuery(event);
    const page = Number(query.page) || 1;
    const limit = Number(query.limit) || 10;

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const db = useDrizzle();

    // Get total count for pagination
    const [{ value: totalItems }] = await db
      .select({
        value: count(),
      })
      .from(student)
      .where(
        and(
          eq(student.departmentId, departmentId),
          eq(student.type, 'placement')
        )
      );

    // Calculate pagination values
    const totalPages = Math.ceil(totalItems / limit);
    const offset = (page - 1) * limit;

    // Get paginated students with their images
    const students = await db.query.student.findMany({
      where: and(
        eq(student.departmentId, departmentId),
        eq(student.type, 'placement')
      ),
      with: {
        image: {
          columns: {
            pathname: true,
            type: true,
          },
        },
        course: {
          columns: {
            name: true,
          },
        },
      },
      limit,
      offset,
      orderBy: (student, { desc }) => [desc(student.createdAt)],
    });

    // Prepare pagination metadata
    const pagination: Pagination = {
      currentPage: page,
      totalPages,
      totalItems,
      itemsPerPage: limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };

    return {
      success: true,
      data: students,
      pagination,
    };
  } catch (error) {
    console.error('Failed to fetch placement students:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch placement students',
    });
  }
});
