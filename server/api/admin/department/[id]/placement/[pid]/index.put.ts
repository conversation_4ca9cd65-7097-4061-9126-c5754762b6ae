import { student, files } from '~~/server/database/tables';
import { updateStudentSchema } from '~~/shared/schema/department/student/update';
import { eq } from 'drizzle-orm';
import type { Files } from '~~/server/database/tables/files';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');
    const pid = getRouterParam(event, 'pid');

    if (!id || !pid) {
      throw createError({
        statusCode: 400,
        message: 'Department ID and Student ID are required',
      });
    }

    const departmentId = parseInt(id, 10);
    const studentId = parseInt(pid, 10);
    if (isNaN(departmentId) || isNaN(studentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid ID format',
      });
    }

    const body = await readBody(event);
    const validatedData = updateStudentSchema.parse({
      ...body,
      type: 'placement',
    });

    const db = useDrizzle();

    let fileId: number | undefined;

    // If there's a new image, insert it first
    if (validatedData.image) {
      const [fileResult] = (await db
        .insert(files)
        .values({
          pathname: validatedData.image.pathname,
          title: validatedData.image.title,
          type: validatedData.image.type,
          createdAt: new Date().getTime(),
          updatedAt: new Date().getTime(),
        })
        .returning()) as Files[];
      fileId = fileResult.id;
    }

    // Update the student record
    const [updatedStudent] = await db
      .update(student)
      .set({
        name: validatedData.name,
        courseId: validatedData.courseId,
        startYear: validatedData.startYear,
        passOutYear: validatedData.passOutYear,
        mark: validatedData.mark,
        placedAt: validatedData.placedAt,
        ...(fileId && { imageId: fileId }),
        updatedAt: new Date().getTime(),
      })
      .where(eq(student.id, studentId))
      .returning();

    if (!updatedStudent) {
      throw createError({
        statusCode: 404,
        message: 'Student not found',
      });
    }

    return {
      success: true,
      data: updatedStudent,
    };
  } catch (error) {
    console.error('Failed to update placement student:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to update placement student',
    });
  }
});
