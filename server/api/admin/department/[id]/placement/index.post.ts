import { student, files } from '~~/server/database/tables';
import { createStudentSchema } from '~~/shared/schema/department/student/create';
import type { Files } from '~~/server/database/tables/files';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const body = await readBody(event);
    const validatedData = createStudentSchema.parse({
      ...body,
      type: 'placement',
    });

    const db = useDrizzle();

    // First insert the image
    const [fileResult] = (await db
      .insert(files)
      .values({
        pathname: validatedData.image?.pathname || '',
        title: validatedData.image?.title || '',
        type: validatedData.image?.type || '',
        createdAt: new Date().getTime(),
        updatedAt: new Date().getTime(),
      })
      .returning()) as Files[];

    // Then insert the student with the file id
    const [studentResult] = await db
      .insert(student)
      .values({
        name: validatedData.name,
        type: validatedData.type,
        courseId: validatedData.courseId,
        startYear: validatedData.startYear,
        passOutYear: validatedData.passOutYear,
        mark: validatedData.mark,
        placedAt: validatedData.placedAt,
        imageId: fileResult.id,
        departmentId,
        createdAt: new Date().getTime(),
        updatedAt: new Date().getTime(),
      })
      .returning();

    return {
      success: true,
      data: studentResult,
    };
  } catch (error) {
    console.error('Failed to add placement student:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to add placement student',
    });
  }
});
