export default defineEventHandler(async () => {
  try {
    const db = useDrizzle();
    const albums = await db.query.album.findMany({
      columns: {
        id: true,
        title: true,
      },
      with: {
        files: {
          columns: {
            id: true,
          },
          with: {
            file: {
              columns: {
                id: true,
                pathname: true,
                title: true,
                type: true,
              },
            },
          },
        },
      },
    });

    const transformedAlbums = albums.map((album) => ({
      id: album.id,
      title: album.title,
      primaryImage: album.files[0]?.file
        ? {
            id: album.files[0].file.id,
            title: album.files[0].file.title,
            pathname: album.files[0].file.pathname,
            type: album.files[0].file.type,
          }
        : null,
      count: album.files.length,
    }));

    return {
      success: true,
      data: transformedAlbums,
    };
  } catch (error) {
    console.error('Error fetching announcements:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch announcements',
    });
  }
});
