import { album, departmentGallery } from '@@/server/database/schema';
import { eq, inArray } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const db = useDrizzle();

    // Get department galleries with albums
    const galleries = await db.query.departmentGallery.findMany({
      where: eq(departmentGallery.departmentId, departmentId),
      orderBy: departmentGallery.displayOrder,
    });

    const albumIds = galleries.map((gallery) => gallery.galleryId);

    const albums =
      albumIds.length > 0
        ? await db.query.album.findMany({
            where: inArray(album.id, albumIds),
            with: {
              files: {
                with: {
                  file: true,
                },
              },
            },
          })
        : [];

    // Map albums to galleries maintaining the order
    const galleryWithAlbums = galleries.map((gallery) => {
      const relatedAlbum = albums.find((a) => a.id === gallery.galleryId);
      const albumData = relatedAlbum
        ? {
            departmentId: gallery.departmentId,
            id: relatedAlbum.id,
            title: relatedAlbum.title,
            primaryImage: relatedAlbum.files[0]?.file
              ? {
                  id: relatedAlbum.files[0].file.id,
                  title: relatedAlbum.files[0].file.title,
                  pathname: relatedAlbum.files[0].file.pathname,
                  type: relatedAlbum.files[0].file.type,
                }
              : null,
            count: relatedAlbum.files.length,
          }
        : null;
      return albumData;
    });

    return {
      success: true,
      data: galleryWithAlbums,
    };
  } catch (error) {
    console.error('Failed to fetch department gallery:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch department gallery',
    });
  }
});
