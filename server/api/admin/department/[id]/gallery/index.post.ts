import {
  album,
  department,
  departmentGallery,
} from '@@/server/database/schema';
import { updateDepartmentGallerySchema } from '~~/shared/schema/department/gallery/update';
import { inArray } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const id = getRouterParam(event, 'id');

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }
    const db = useDrizzle();
    const existingDepartment = await db.query.department.findFirst({
      where: eq(department.id, parseInt(id, 10)),
    });

    if (!existingDepartment) {
      throw createError({
        statusCode: 404,
        message: 'Department not found',
      });
    }

    const { albumIds } = updateDepartmentGallerySchema.parse(body);

    const existingAlbums = await db.query.album.findMany({
      where: inArray(album.id, albumIds),
    });

    if (existingAlbums.length !== albumIds.length) {
      throw createError({
        statusCode: 400,
        message: 'Invalid album IDs',
      });
    }

    // delete existing gallery
    await db
      .delete(departmentGallery)
      .where(eq(departmentGallery.departmentId, parseInt(id, 10)));

    // create new gallery
    await db.insert(departmentGallery).values(
      existingAlbums.map((album, index) => ({
        departmentId: parseInt(id, 10),
        galleryId: album.id,
        displayOrder: index,
      }))
    );

    return {
      success: true,
      message: 'Department gallery updated successfully',
    };
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
