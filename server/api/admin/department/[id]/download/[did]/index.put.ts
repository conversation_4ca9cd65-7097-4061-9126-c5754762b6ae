import { updateDownloadSchema } from '@@/shared/schema/department/files/update';
import { departmentFiles } from '@@/server/database/tables/department';
import { files } from '@@/server/database/tables/files';
import { eq } from 'drizzle-orm';
import { z } from 'zod';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const id = getRouterParam(event, 'id');
    const did = getRouterParam(event, 'did');

    if (!id || !did) {
      throw createError({
        statusCode: 400,
        message: 'Department ID and Download ID are required',
      });
    }

    const departmentId = parseInt(id, 10);
    const downloadId = parseInt(did, 10);
    if (isNaN(departmentId) || isNaN(downloadId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid ID format',
      });
    }

    const validatedData = updateDownloadSchema.parse(body);

    const db = useDrizzle();

    // First update the file record if provided
    if (validatedData.file) {
      await db
        .update(files)
        .set({
          pathname: validatedData.file.pathname,
          title: validatedData.file.title,
          type: validatedData.file.type,
        })
        .where(eq(files.id, downloadId));
    }

    // Then update the department file record
    const result = await db
      .update(departmentFiles)
      .set({
        title: validatedData.title,
      })
      .where(eq(departmentFiles.id, downloadId))
      .returning();

    if (!result.length) {
      throw createError({
        statusCode: 404,
        message: 'Download not found',
      });
    }

    return {
      success: true,
      data: result[0],
    };
  } catch (error) {
    console.error('Failed to update download:', error);
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        message: 'Validation failed',
        data: error.errors,
      });
    }

    throw createError({
      statusCode: 500,
      message: 'Failed to update download',
    });
  }
});
