import { department, departmentFiles } from '@@/server/database/schema';
import { and, eq, desc, sql } from 'drizzle-orm';
import { paginationSchema } from '@@/shared/schema/pagination';
import type { ZodError } from 'zod';
import type { DepartmentFileResponse } from '~/types/admin';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');
    const query = getQuery(event);
    const { page, limit } = await paginationSchema.parseAsync(query);
    const offset = (page - 1) * limit;

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const db = useDrizzle();
    const [departmentData, downloadFiles, totalCount] = await Promise.all([
      db.query.department.findFirst({
        where: eq(department.id, departmentId),
      }),
      db.query.departmentFiles.findMany({
        where: and(
          eq(departmentFiles.departmentId, departmentId),
          eq(departmentFiles.type, 'download')
        ),
        with: {
          file: true,
          course: true,
        },
        orderBy: (files) => [desc(files.createdAt)],
        limit,
        offset,
      }),
      db
        .select({ count: sql<number>`count(*)` })
        .from(departmentFiles)
        .where(
          and(
            eq(departmentFiles.departmentId, departmentId),
            eq(departmentFiles.type, 'download')
          )
        )
        .then((result) => result[0].count),
    ]);

    if (!departmentData) {
      throw createError({
        statusCode: 404,
        message: 'Department not found',
      });
    }

    const totalPages = Math.ceil(totalCount / limit);

    const response: DepartmentFileResponse = {
      success: true,
      data: {
        files: downloadFiles.map((file) => ({
          id: file.id,
          title: file.title ?? '',
          file: {
            title: file.file.title ?? '',
            pathname: file.file.pathname ?? '',
            type: file.file.type ?? '',
            prefix: 'download',
          },
        })),
        pagination: {
          page,
          limit,
          totalItems: totalCount,
          totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      },
    };

    return response;
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      const zodError = error as ZodError;
      throw createError({
        statusCode: 400,
        message: 'Invalid pagination parameters',
        data: zodError.errors,
      });
    }
    console.error('Failed to fetch download:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch download',
    });
  }
});
