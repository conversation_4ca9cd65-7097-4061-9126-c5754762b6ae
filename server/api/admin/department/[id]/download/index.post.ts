import { createDownloadSchema } from '@@/shared/schema/department/files/create';
import { departmentFiles } from '@@/server/database/tables/department';
import { files } from '@@/server/database/tables/files';
import { z } from 'zod';
import type { DownloadData } from '~/types/admin';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const id = getRouterParam(event, 'id');

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const validatedData = createDownloadSchema.parse(body);

    const db = useDrizzle();

    // First create the file record
    const fileResult = await db
      .insert(files)
      .values({
        pathname: validatedData.file.pathname,
        title: validatedData.file.title,
        type: validatedData.file.type,
      })
      .returning();

    // Then create the department file record
    const result = await db.insert(departmentFiles).values({
      title: validatedData.title,
      type: 'download',
      departmentId,
      fileId: fileResult[0].id,
    });

    const download: DownloadData = {
      id: result.id,
      title: validatedData.title ?? '',
      file: {
        pathname: fileResult[0].pathname,
        title: fileResult[0]?.title ?? '',
        type: fileResult[0].type,
        prefix: 'download',
      },
    };

    return {
      success: true,
      data: download,
    };
  } catch (error) {
    console.error('Failed to create download:', error);
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        message: 'Validation failed',
        data: error.errors,
      });
    }

    throw createError({
      statusCode: 500,
      message: 'Failed to create download',
    });
  }
});
