import { eq } from 'drizzle-orm';
import { announcement } from '~~/server/database/schema';

export default defineEventHandler(async () => {
  try {
    const db = useDrizzle();

    const announcementsList = await db.query.announcement.findMany({
      where: eq(announcement.announcementType, 'event'),
      with: {
        button: true,
        files: {
          with: {
            file: {
              columns: {
                id: true,
                pathname: true,
                title: true,
                type: true,
              },
            },
          },
        },
      },
      orderBy: (announcement, { desc }) => [desc(announcement.priority)],
    });

    return {
      success: true,
      data: announcementsList,
    };
  } catch (error) {
    console.error('Error fetching announcements:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch announcements',
    });
  }
});
