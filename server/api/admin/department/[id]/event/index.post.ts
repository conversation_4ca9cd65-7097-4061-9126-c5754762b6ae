import { inArray } from 'drizzle-orm';
import { z } from 'zod';
import { announcement, departmentEvent } from '~~/server/database/schema';
import { updateDepartmentEventSchema } from '~~/shared/schema/department/gallery/update';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const id = getRouterParam(event, 'id');

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const db = useDrizzle();

    const { eventIds } = updateDepartmentEventSchema.parse(body);

    const existingEvents = await db.query.announcement.findMany({
      where: inArray(announcement.id, eventIds),
    });

    if (existingEvents.length !== eventIds.length) {
      throw createError({
        statusCode: 400,
        message: 'Invalid event IDs',
      });
    }

    await db
      .delete(departmentEvent)
      .where(eq(departmentEvent.departmentId, departmentId));

    await db.insert(departmentEvent).values(
      existingEvents.map((event, index) => ({
        departmentId,
        eventId: event.id,
        displayOrder: index,
      }))
    );

    return {
      success: true,
    };
  } catch (error) {
    console.error('Failed to update department overview:', error);
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        message: 'Validation failed',
        data: error.errors,
      });
    }

    throw createError({
      statusCode: 500,
      message: 'Failed to update department overview',
    });
  }
});
