import {
  department,
  departmentEvent,
  announcement,
} from '@@/server/database/schema';
import { eq, inArray } from 'drizzle-orm';
import type { DepartmentEvent, DepartmentEventResponse } from '~/types/home';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const db = useDrizzle();
    const result = await db.query.department.findFirst({
      where: eq(department.id, departmentId),
    });

    if (!result) {
      throw createError({
        statusCode: 404,
        message: 'Department not found',
      });
    }

    const departmentEvents = await db.query.departmentEvent.findMany({
      where: eq(departmentEvent.departmentId, departmentId),
      orderBy: departmentEvent.displayOrder,
    });

    const eventIds = departmentEvents.map((event) => event.eventId);

    const events =
      eventIds.length > 0
        ? await db.query.announcement.findMany({
            where: inArray(announcement.id, eventIds),
            with: {
              button: true,
              files: {
                columns: {
                  id: true,
                  createdAt: true,
                  updatedAt: true,
                  announcementId: true,
                  fileId: true,
                },
                with: {
                  file: {
                    columns: {
                      id: true,
                      title: true,
                      type: true,
                      pathname: true,
                    },
                  },
                },
              },
            },
          })
        : [];

    const eventsWithOrder = departmentEvents
      .map((deptEvent) => {
        const relatedEvent = events.find((e) => e.id === deptEvent.eventId);
        if (!relatedEvent) return null;

        return {
          ...relatedEvent,
          displayOrder: deptEvent.displayOrder,
          departmentId: deptEvent.departmentId,
        } as DepartmentEvent;
      })
      .filter((event): event is DepartmentEvent => event !== null);

    return {
      success: true,
      data: eventsWithOrder,
    } satisfies DepartmentEventResponse;
  } catch (error) {
    console.error('Failed to fetch department events:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch department events',
    });
  }
});
