import { createQuestionBankSchema } from '@@/shared/schema/department/files/create';
import { departmentFiles } from '@@/server/database/tables/department';
import { files } from '@@/server/database/tables/files';
import type { QuestionBankData } from '~/types/admin';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const id = getRouterParam(event, 'id');

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const validatedData = createQuestionBankSchema.parse(body);
    console.log('🚀 ~ defineEventHandler ~ validatedData:', validatedData);
    console.log('🚀 ~ defineEventHandler ~ id:', id);
    console.log('🚀 ~ defineEventHandler ~ departmentId:', departmentId);

    const db = useDrizzle();

    // First create the file record
    const [fileResult] = await db
      .insert(files)
      .values({
        pathname: validatedData.file.pathname,
        title: validatedData.file.title,
        type: validatedData.file.type,
      })
      .returning();

    console.table({
      title: validatedData.title,
      type: 'question_paper',
      year: validatedData.year,
      courseId: validatedData.courseId,
      semester: validatedData.semester,
      departmentId,
      fileId: fileResult.id,
    });

    // Then create the department file record
    const result = await db.insert(departmentFiles).values({
      title: validatedData.title,
      type: 'question_paper',
      year: validatedData.year,
      courseId: validatedData.courseId,
      semester: validatedData.semester,
      departmentId,
      fileId: fileResult.id,
    });

    const questionBank: QuestionBankData = {
      id: result.id,
      title: validatedData.title ?? '',
      year: validatedData.year ?? null,
      semester: validatedData.semester ?? null,
      course: null,
      file: {
        pathname: fileResult.pathname,
        title: fileResult.title ?? '',
        type: fileResult.type,
        prefix: 'question-bank',
      },
    };

    return {
      success: true,
      data: questionBank,
    };
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
