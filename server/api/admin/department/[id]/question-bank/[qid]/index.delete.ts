import { departmentFiles } from '@@/server/database/tables/department';
import { files } from '@@/server/database/tables/files';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');
    const qid = getRouterParam(event, 'qid');

    if (!id || !qid) {
      throw createError({
        statusCode: 400,
        message: 'Department ID and Question Bank ID are required',
      });
    }

    const departmentId = parseInt(id, 10);
    const questionBankId = parseInt(qid, 10);
    if (isNaN(departmentId) || isNaN(questionBankId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid ID format',
      });
    }

    const db = useDrizzle();

    // First delete the department file record
    const result = await db
      .delete(departmentFiles)
      .where(eq(departmentFiles.id, questionBankId))
      .returning();

    if (!result.length) {
      throw createError({
        statusCode: 404,
        message: 'Question Bank not found',
      });
    }

    // Then delete the file record
    await db.delete(files).where(eq(files.id, result[0].fileId));

    return {
      success: true,
      data: result[0],
    };
  } catch (error) {
    console.error('Failed to delete question bank:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to delete question bank',
    });
  }
});
