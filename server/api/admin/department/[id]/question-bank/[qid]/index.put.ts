import { updateQuestionBankSchema } from '@@/shared/schema/department/files/update';
import { departmentFiles } from '@@/server/database/tables/department';
import { files } from '@@/server/database/tables/files';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const id = getRouterParam(event, 'id');
    const qid = getRouterParam(event, 'qid');

    if (!id || !qid) {
      throw createError({
        statusCode: 400,
        message: 'Department ID and Question Bank ID are required',
      });
    }

    const departmentId = parseInt(id, 10);
    const questionBankId = parseInt(qid, 10);
    if (isNaN(departmentId) || isNaN(questionBankId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid ID format',
      });
    }

    const validatedData = updateQuestionBankSchema.parse(body);

    const db = useDrizzle();

    // First update the file record if provided
    if (validatedData.file) {
      await db
        .update(files)
        .set({
          pathname: validatedData.file.pathname,
          title: validatedData.file.title,
          type: validatedData.file.type,
        })
        .where(eq(files.id, questionBankId));
    }

    // Then update the department file record
    const result = await db
      .update(departmentFiles)
      .set({
        title: validatedData.title,
        year: validatedData.year,
        courseId: validatedData.courseId,
        semester: validatedData.semester,
      })
      .where(eq(departmentFiles.id, questionBankId))
      .returning();

    if (!result.length) {
      throw createError({
        statusCode: 404,
        message: 'Question Bank not found',
      });
    }

    return {
      success: true,
      data: result[0],
    };
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
