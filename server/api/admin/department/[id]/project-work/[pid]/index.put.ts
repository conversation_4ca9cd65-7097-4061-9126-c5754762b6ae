import { updateProjectSchema } from '@@/shared/schema/department/files/update';
import { departmentFiles } from '@@/server/database/tables/department';
import { files } from '@@/server/database/tables/files';
import { eq } from 'drizzle-orm';
import { z } from 'zod';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const id = getRouterParam(event, 'id');
    const pid = getRouterParam(event, 'pid');

    if (!id || !pid) {
      throw createError({
        statusCode: 400,
        message: 'Department ID and Project Work ID are required',
      });
    }

    const departmentId = parseInt(id, 10);
    const projectWorkId = parseInt(pid, 10);
    if (isNaN(departmentId) || isNaN(projectWorkId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid ID format',
      });
    }

    const validatedData = updateProjectSchema.parse(body);

    const db = useDrizzle();

    // First update the file record if provided
    if (validatedData.file) {
      await db
        .update(files)
        .set({
          pathname: validatedData.file.pathname,
          title: validatedData.file.title,
          type: validatedData.file.type,
        })
        .where(eq(files.id, projectWorkId));
    }

    // Then update the department file record
    const result = await db
      .update(departmentFiles)
      .set({
        title: validatedData.title,
        year: validatedData.year,
        courseId: validatedData.courseId,
        semester: validatedData.semester,
      })
      .where(eq(departmentFiles.id, projectWorkId))
      .returning();

    if (!result.length) {
      throw createError({
        statusCode: 404,
        message: 'Project Work not found',
      });
    }

    return {
      success: true,
      data: result[0],
    };
  } catch (error) {
    console.error('Failed to update project work:', error);
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        message: 'Validation failed',
        data: error.errors,
      });
    }

    throw createError({
      statusCode: 500,
      message: 'Failed to update project work',
    });
  }
});
