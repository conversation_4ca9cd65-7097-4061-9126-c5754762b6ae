import { department } from '@@/server/database/schema';
import { updateDepartmentSchema } from '~~/shared/schema/department/create';
import { eq } from 'drizzle-orm';
import { z } from 'zod';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { id, name, slug } = await updateDepartmentSchema.parseAsync(body);

    const db = useDrizzle();

    await db
      .update(department)
      .set({
        name,
        slug,
      })
      .where(eq(department.id, id));

    return {
      success: true,
    };
  } catch (error) {
    console.error('Failed to update department:', error);
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        message: 'Validation failed',
        data: error.errors,
      });
    }

    throw createError({
      statusCode: 500,
      message: 'Failed to update department',
    });
  }
});
