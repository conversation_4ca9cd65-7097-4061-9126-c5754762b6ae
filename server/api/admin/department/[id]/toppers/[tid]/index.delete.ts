import { student, files } from '~~/server/database/tables';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');
    const tid = getRouterParam(event, 'tid');

    if (!id || !tid) {
      throw createError({
        statusCode: 400,
        message: 'Department ID and Student ID are required',
      });
    }

    const departmentId = parseInt(id, 10);
    const studentId = parseInt(tid, 10);
    if (isNaN(departmentId) || isNaN(studentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid ID format',
      });
    }

    const db = useDrizzle();

    // First get the student to check if it exists and get the image ID
    const studentToDelete = await db.query.student.findFirst({
      where: eq(student.id, studentId),
      columns: {
        id: true,
        imageId: true,
      },
    });

    if (!studentToDelete) {
      throw createError({
        statusCode: 404,
        message: 'Student not found',
      });
    }

    // Delete the student record
    await db.delete(student).where(eq(student.id, studentId));

    // If there was an image, delete it too
    if (studentToDelete.imageId) {
      await db.delete(files).where(eq(files.id, studentToDelete.imageId));
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error('Failed to delete topper student:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to delete topper student',
    });
  }
});
