import { department } from '@@/server/database/schema';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event);
    const id = query.id as number;

    const db = useDrizzle();

    // Delete the department
    await db.delete(department).where(eq(department.id, id));

    return {
      success: true,
      message: 'Department deleted successfully',
    };
  } catch (error: any) {
    console.error('Failed to delete department:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || 'Failed to delete department',
    });
  }
});
