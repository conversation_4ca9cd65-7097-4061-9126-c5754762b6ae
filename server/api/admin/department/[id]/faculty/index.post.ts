import { facultySchema } from '~~/shared/schema/department/faculty/create';
import { faculty } from '~~/server/database/tables/faculty';
import { files, tempFiles } from '~~/server/database/tables/files';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');
    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({ statusCode: 400, message: 'Invalid Department ID' });
    }

    const body = await readBody(event);
    const parsedData = facultySchema.parse(body);
    const db = useDrizzle();

    // Handle resume file
    let resumeId = null;
    if (parsedData.resume) {
      // Create file entry for resume if provided
      const [resumeEntry] = await db
        .insert(files)
        .values({
          pathname: parsedData.resume.pathname,
          title: parsedData.resume.title,
          type: parsedData.resume.type,
        })
        .returning();

      resumeId = resumeEntry.id;

      // Clean up temp file for resume
      await db
        .delete(tempFiles)
        .where(eq(tempFiles.pathname, parsedData.resume.pathname));
    }

    // Create file entry for image (required)
    const [imageEntry] = await db
      .insert(files)
      .values({
        pathname: parsedData.image.pathname,
        title: parsedData.image.title,
        type: parsedData.image.type,
      })
      .returning();

    // Create the faculty entry with the file IDs
    const result = await db
      .insert(faculty)
      .values({
        name: parsedData.name,
        departmentId,
        designation: parsedData.designation,
        startDate: parsedData.startDate,
        endDate: parsedData.endDate,
        priority: parsedData.priority || 0,
        resumeId, // Will be null if no resume provided
        imageId: imageEntry.id,
        education: parsedData.education,
        experience: parsedData.experience,
        areaOfInterest: parsedData.areaOfInterest,
      })
      .returning();

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error('Error creating faculty:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to create faculty',
      data: error,
    });
  }
});
