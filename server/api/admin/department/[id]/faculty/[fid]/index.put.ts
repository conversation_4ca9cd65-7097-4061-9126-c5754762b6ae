import { faculty } from '~~/server/database/tables/faculty';
import { useDrizzle } from '~~/server/utils/drizzle';
import { eq } from 'drizzle-orm';
import { facultyUpdateSchema } from '~~/shared/schema/department/faculty/update';
import { files, tempFiles } from '~~/server/database/tables/files';

export default defineEventHandler(async (event) => {
  try {
    const facultyId = parseInt(getRouterParam(event, 'fid') ?? '', 10);

    if (isNaN(facultyId)) {
      throw createError({ statusCode: 400, message: 'Invalid faculty ID' });
    }

    const body = await readBody(event);
    const updateData = facultyUpdateSchema.parse(body);
    const db = useDrizzle();

    // If resume is being updated, handle file creation first
    let resumeId: number | undefined;
    if (updateData.resume) {
      const [fileEntry] = await db
        .insert(files)
        .values({
          pathname: updateData.resume.pathname,
          title: updateData.resume.title,
          type: updateData.resume.type,
        })
        .returning();

      resumeId = fileEntry.id;

      // Clean up temp file
      await db
        .delete(tempFiles)
        .where(eq(tempFiles.pathname, updateData.resume.pathname));
    }

    const updateValues = {
      ...updateData,
      ...(resumeId && { resumeId }),
    };

    const result = await db
      .update(faculty)
      .set(updateValues)
      .where(eq(faculty.id, facultyId))
      .returning();

    if (!result.length) {
      throw createError({ statusCode: 404, message: 'Faculty not found' });
    }

    return { success: true, data: result[0] };
  } catch (error) {
    console.error('Error updating faculty:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to update faculty',
      data: error,
    });
  }
});
