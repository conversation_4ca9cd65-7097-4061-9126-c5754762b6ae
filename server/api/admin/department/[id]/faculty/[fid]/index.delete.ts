import { faculty } from '~~/server/database/tables/faculty';
import { useDrizzle } from '~~/server/utils/drizzle';

export default defineEventHandler(async (event) => {
  const facultyId = parseInt(getRouterParam(event, 'fid') ?? '', 10);

  if (isNaN(facultyId)) {
    throw createError({
      statusCode: 400,
      message: 'Invalid faculty ID',
    });
  }

  const db = useDrizzle();
  const result = await db
    .delete(faculty)
    .where(eq(faculty.id, facultyId))
    .returning();

  if (!result.length) {
    throw createError({
      statusCode: 404,
      message: 'Faculty not found',
    });
  }

  return { success: true, message: 'Faculty deleted successfully' };
});
