import { faculty } from '@@/server/database/schema';
import { eq } from 'drizzle-orm';
import type {
  FacultyEducation,
  FacultyExperience,
  FacultyListData,
} from '~/types/admin';
import type { FileData } from '~/types/home';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({ statusCode: 400, message: 'Invalid department ID' });
    }

    const db = useDrizzle();
    const result = await db.query.faculty.findMany({
      where: eq(faculty.departmentId, departmentId),
      with: { resume: true, image: true },
    });

    const facultyListData: FacultyListData[] = result.map((faculty) => ({
      id: faculty.id,
      name: faculty.name,
      designation: faculty.designation,
      departmentId: faculty.departmentId,
      startDate: faculty.startDate,
      endDate: faculty?.endDate ?? null,
      priority: faculty.priority ?? 0,
      areaOfInterest: faculty.areaOfInterest,
      education: faculty.education.map(
        (education) =>
          ({
            degree: education.degree,
            university: education.university,
            passOutYear: education.passOutYear,
          }) as FacultyEducation
      ),
      experience: faculty.experience.map(
        (experience) =>
          ({
            startYear: experience.startYear,
            organization: experience.organization,
            designation: experience.designation,
          }) as FacultyExperience
      ),
      resume: {
        type: faculty.resume?.type,
        title: faculty.resume?.title,
        pathname: faculty.resume?.pathname,
        prefix: 'resume',
      } as FileData,
      image: {
        type: faculty.image?.type,
        title: faculty.image?.title,
        pathname: faculty.image?.pathname,
        prefix: 'image',
      } as FileData,
    }));

    if (!result) {
      throw createError({ statusCode: 404, message: 'Faculty not found' });
    }

    return { success: true, data: facultyListData };
  } catch (error) {
    console.error('Failed to fetch department overview:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch department overview',
    });
  }
});
