import { course, department } from '@@/server/database/tables';
import { eq } from 'drizzle-orm';
import type { CourseListData, LinkButtonConfig } from '~/types/admin';
import type { FileData } from '~/types/home';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const db = useDrizzle();

    // Check if department exists
    const departmentExists = await db.query.department.findFirst({
      where: eq(department.id, departmentId),
    });

    if (!departmentExists) {
      throw createError({
        statusCode: 404,
        message: 'Department not found',
      });
    }

    // Get all courses for the department
    const courses = await db.query.course.findMany({
      where: eq(course.departmentId, departmentId),
      with: {
        syllabus: true,
        pos: true,
        linkButton: true,
        image: true,
      },
    });
    const transformedCourses: CourseListData[] = courses.map((course) => ({
      id: course.id,
      name: course.name,
      specialization: course.specialization ?? '',
      durationInMonths: course.durationInMonths,
      semesterCount: course.semesterCount,
      title: course.title,
      type: course.type,
      seatsCount: course.seatsCount,
      content: course.content,
      departmentId: course.departmentId,
      image: {
        pathname: course.image?.pathname,
        title: course.image?.title,
        type: course.image?.type,
        prefix: 'course',
      } as FileData,
      syllabus: {
        pathname: course.syllabus?.pathname,
        title: course.syllabus?.title,
        type: course.syllabus?.type,
        prefix: 'syllabus',
      } as FileData,
      pos: {
        pathname: course.pos?.pathname,
        title: course.pos?.title,
        type: course.pos?.type,
        prefix: 'pos',
      } as FileData,
      linkButton: course?.linkButton
        ? ({
            id: course?.linkButton?.id,
            title: course?.linkButton?.title,
            externalLink: course?.linkButton?.externalLink,
            internalLink: course?.linkButton?.internalLink,
            newTab: course?.linkButton?.newTab,
            type: 'link',
            style: course?.linkButton?.style,
          } as LinkButtonConfig)
        : null,
    }));
    return {
      success: true,
      data: transformedCourses,
    };
  } catch (error) {
    console.error('Failed to fetch courses:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch courses',
    });
  }
});
