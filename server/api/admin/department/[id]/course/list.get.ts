import { course, department } from '@@/server/database/tables';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const db = useDrizzle();

    // Check if department exists
    const departmentExists = await db.query.department.findFirst({
      where: eq(department.id, departmentId),
    });

    if (!departmentExists) {
      throw createError({
        statusCode: 404,
        message: 'Department not found',
      });
    }

    // Get all courses for the department
    const courses = await db.query.course.findMany({
      where: eq(course.departmentId, departmentId),
      columns: {
        name: true,
        id: true,
      },
    });
    const transformedCourses: { name: string; id: number }[] = courses.map(
      (course) => ({ name: course.name, id: course.id })
    );

    return {
      success: true,
      data: transformedCourses,
    };
  } catch (error) {
    console.error('Failed to fetch courses:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch courses',
    });
  }
});
