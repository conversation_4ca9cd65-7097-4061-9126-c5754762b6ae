import { course, files, linkButton } from '@@/server/database/tables';
import { courseUpdateSchema } from '@@/shared/schema/department/course/update';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');
    const cid = getRouterParam(event, 'cid');

    if (!id || !cid) {
      throw createError({
        statusCode: 400,
        message: 'Department ID and Course ID are required',
      });
    }

    const departmentId = parseInt(id, 10);
    const courseId = parseInt(cid, 10);
    if (isNaN(departmentId) || isNaN(courseId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID or course ID',
      });
    }

    const body = await readBody(event);
    const validatedData = courseUpdateSchema.parse({ ...body, departmentId });

    const db = useDrizzle();

    // Check if course exists and belongs to the department
    const existingCourse = await db.query.course.findFirst({
      where: eq(course.id, courseId),
      with: {
        syllabus: true,
        pos: true,
        linkButton: true,
      },
    });

    if (!existingCourse || existingCourse.departmentId !== departmentId) {
      throw createError({
        statusCode: 404,
        message: 'Course not found in this department',
      });
    }

    // Update files and link button if provided
    let syllabusId = existingCourse.syllabusId;
    let posId = existingCourse.posId;
    let linkButtonId = existingCourse.linkButtonId;
    let imageId = existingCourse.imageId;

    if (validatedData.image) {
      if (imageId) {
        await db
          .update(files)
          .set(validatedData.image)
          .where(eq(files.id, imageId));
      } else {
        const image = await db
          .insert(files)
          .values(validatedData.image)
          .returning()
          .get();
        imageId = image.id;
      }
    }

    if (validatedData.syllabus) {
      if (syllabusId) {
        await db
          .update(files)
          .set(validatedData.syllabus)
          .where(eq(files.id, syllabusId));
      } else {
        const syllabus = await db
          .insert(files)
          .values(validatedData.syllabus)
          .returning()
          .get();
        syllabusId = syllabus.id;
      }
    }

    if (validatedData.pos) {
      if (posId) {
        await db
          .update(files)
          .set(validatedData.pos)
          .where(eq(files.id, posId));
      } else {
        const pos = await db
          .insert(files)
          .values(validatedData.pos)
          .returning()
          .get();
        posId = pos.id;
      }
    }

    if (validatedData.linkButton !== undefined) {
      if (validatedData.linkButton === null) {
        if (linkButtonId) {
          await db.delete(linkButton).where(eq(linkButton.id, linkButtonId));
          linkButtonId = null;
        }
      } else if (linkButtonId) {
        await db
          .update(linkButton)
          .set(validatedData.linkButton)
          .where(eq(linkButton.id, linkButtonId));
      } else {
        const button = await db
          .insert(linkButton)
          .values(validatedData.linkButton)
          .returning()
          .get();
        linkButtonId = button.id;
      }
    }

    // Update the course
    const updatedCourse = await db
      .update(course)
      .set({
        name: validatedData.name,
        specialization: validatedData.specialization,
        durationInMonths: validatedData.durationInMonths,
        semesterCount: validatedData.semesterCount,
        title: validatedData.title,
        type: validatedData.type,
        seatsCount: validatedData.seatsCount,
        content: validatedData.content,
        syllabusId,
        posId,
        linkButtonId,
        imageId,
      })
      .where(eq(course.id, courseId))
      .returning()
      .get();

    return {
      success: true,
      data: updatedCourse,
    };
  } catch (error) {
    console.error('Failed to update course:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to update course',
    });
  }
});
