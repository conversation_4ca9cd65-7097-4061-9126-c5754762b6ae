import { course, student, departmentFiles } from '@@/server/database/tables';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');
    const cid = getRouterParam(event, 'cid');

    if (!id || !cid) {
      throw createError({
        statusCode: 400,
        message: 'Department ID and Course ID are required',
      });
    }

    const departmentId = parseInt(id, 10);
    const courseId = parseInt(cid, 10);
    if (isNaN(departmentId) || isNaN(courseId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID or course ID',
      });
    }

    const db = useDrizzle();

    // Check if course exists and belongs to the department
    const existingCourse = await db.query.course.findFirst({
      where: eq(course.id, courseId),
      with: {
        syllabus: true,
        pos: true,
        linkButton: true,
      },
    });

    if (!existingCourse || existingCourse.departmentId !== departmentId) {
      throw createError({
        statusCode: 404,
        message: 'Course not found in this department',
      });
    }

    // Delete related records first
    await db.delete(student).where(eq(student.courseId, courseId));
    await db
      .delete(departmentFiles)
      .where(eq(departmentFiles.courseId, courseId));

    // Delete the course
    await db.delete(course).where(eq(course.id, courseId));

    return {
      success: true,
    };
  } catch (error) {
    console.error('Failed to delete course:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to delete course',
    });
  }
});
