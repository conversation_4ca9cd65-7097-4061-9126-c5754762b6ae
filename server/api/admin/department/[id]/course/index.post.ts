import {
  course,
  department,
  files,
  linkButton,
} from '@@/server/database/tables';
import { courseSchema } from '@@/shared/schema/department/course/create';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');
    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const body = await readBody(event);
    const validatedData = courseSchema.parse(body);

    const db = useDrizzle();

    // Check if department exists
    const departmentExists = await db.query.department.findFirst({
      where: eq(department.id, departmentId),
    });

    if (!departmentExists) {
      throw createError({
        statusCode: 404,
        message: 'Department not found',
      });
    }

    // Create files and link button if provided
    let syllabusId = null;
    let posId = null;
    let linkButtonId = null;
    let imageId = null;

    if (validatedData.image) {
      const image = await db
        .insert(files)
        .values(validatedData.image)
        .returning()
        .get();
      imageId = image.id;
    }

    if (validatedData.syllabus) {
      const syllabus = await db
        .insert(files)
        .values(validatedData.syllabus)
        .returning()
        .get();
      syllabusId = syllabus.id;
    }

    if (validatedData.pos) {
      const pos = await db
        .insert(files)
        .values(validatedData.pos)
        .returning()
        .get();
      posId = pos.id;
    }

    if (validatedData.linkButton) {
      const button = await db
        .insert(linkButton)
        .values(validatedData.linkButton)
        .returning()
        .get();
      linkButtonId = button.id;
    }

    // Create the course
    const newCourse = await db
      .insert(course)
      .values({
        name: validatedData.name,
        specialization: validatedData.specialization,
        durationInMonths: validatedData.durationInMonths,
        semesterCount: validatedData.semesterCount,
        departmentId,
        title: validatedData.title,
        type: validatedData.type,
        seatsCount: validatedData.seatsCount,
        content: validatedData.content,
        syllabusId,
        posId,
        linkButtonId,
        imageId,
      })
      .returning()
      .get();

    return {
      success: true,
      data: newCourse,
    };
  } catch (error) {
    console.error('Failed to create course:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to create course',
    });
  }
});
