import { department } from '@@/server/database/schema';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const db = useDrizzle();
    await db.delete(department).where(eq(department.id, departmentId));

    return {
      success: true,
    };
  } catch (error) {
    console.error('Failed to delete department overview:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to delete department overview',
    });
  }
});
