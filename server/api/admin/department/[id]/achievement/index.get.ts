import { department } from '@@/server/database/schema';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const db = useDrizzle();
    const result = await db.query.department.findFirst({
      where: eq(department.id, departmentId),
    });

    if (!result) {
      throw createError({
        statusCode: 404,
        message: 'Department not found',
      });
    }

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error('Failed to fetch department overview:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch department overview',
    });
  }
});
