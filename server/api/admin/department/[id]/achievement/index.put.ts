import { department } from '@@/server/database/schema';
import { eq } from 'drizzle-orm';
import { z } from 'zod';

const updateOverviewSchema = z.object({
  overview: z.string().min(1),
});

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id');
    const body = await readBody(event);

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid department ID',
      });
    }

    const validatedData = updateOverviewSchema.parse(body);

    const db = useDrizzle();
    await db
      .update(department)
      .set({
        overview: validatedData.overview,
      })
      .where(eq(department.id, departmentId));

    return {
      success: true,
    };
  } catch (error) {
    console.error('Failed to update department overview:', error);
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        message: 'Validation failed',
        data: error.errors,
      });
    }

    throw createError({
      statusCode: 500,
      message: 'Failed to update department overview',
    });
  }
});
