import { z } from 'zod';
import { department } from '@@/server/database/schema';
import { eq } from 'drizzle-orm';

const newOverviewSchema = z.object({ overview: z.string().min(1) });

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const id = getRouterParam(event, 'id');

    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Department ID is required',
      });
    }

    const departmentId = parseInt(id, 10);
    if (isNaN(departmentId)) {
      throw createError({ statusCode: 400, message: 'Invalid department ID' });
    }

    const validatedData = newOverviewSchema.parse(body);

    const db = useDrizzle();
    // Check if the department exists
    const existingDepartment = await db.query.department.findFirst({
      where: eq(department.id, departmentId),
    });
    if (!existingDepartment) {
      throw createError({ statusCode: 404, message: 'Department not found' });
    }

    await db
      .update(department)
      .set({ overview: validatedData.overview })
      .where(eq(department.id, departmentId));

    return { success: true, message: 'Overview created successfully' };
  } catch (error) {
    console.error('Failed to create department overview:', error);
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        message: 'Validation failed',
        data: error.errors,
      });
    }
    throw createError({
      statusCode: 500,
      message: 'Failed to create department overview',
    });
  }
});
