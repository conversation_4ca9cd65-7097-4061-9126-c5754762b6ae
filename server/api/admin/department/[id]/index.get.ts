import { department } from '~~/server/database/schema';
import type { DepartmentDetails } from '~/types/admin';

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id') ?? '';
    // Initialize drizzle
    const db = useDrizzle();

    // Fetch all departments
    const response = await db.query.department.findFirst({
      where: eq(department.id, parseInt(id)),
      columns: {
        id: true,
        name: true,
        slug: true,
      },
    });

    if (!response) {
      throw createError({
        statusCode: 404,
        message: 'Department not found',
      });
    }

    // This is a mock response for now
    const departmentData: DepartmentDetails = {
      id: response.id,
      name: response.name,
      slug: response.slug,
    };

    // Validate the response
    return departmentData;
  } catch (error) {
    console.error('Error fetching departments', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch departments',
    });
  }
});
