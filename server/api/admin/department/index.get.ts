import type { DepartmentListData } from '../../../../app/types/admin/department';

export default defineEventHandler(async () => {
  try {
    // Initialize drizzle
    const db = useDrizzle();

    // Fetch all departments
    const departments = await db.query.department.findMany({
      columns: {
        id: true,
        name: true,
        slug: true,
      },
    });

    // This is a mock response for now
    const departmentData: DepartmentListData[] = departments.map(
      (department) => ({
        id: department.id,
        name: department.name,
        slug: department.slug,
      })
    );

    // Validate the response
    return departmentData;
  } catch (error) {
    console.error('Error fetching departments', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch departments',
    });
  }
});
