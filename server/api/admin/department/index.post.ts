import type {
  CreateDepartmentData,
  CreateDepartmentResponse,
} from '~/types/admin';
import { department } from '~~/server/database/schema';
import { departmentSchema } from '~~/shared/schema/department/create';

export default defineEventHandler(async (event) => {
  try {
    // Parse and validate the request body
    const body = await readBody(event);
    const { name, slug } = await departmentSchema.parseAsync(body);

    // Initialize drizzle
    const db = useDrizzle();

    // Insert the menu into the database
    const result = await db
      .insert(department)
      .values({
        name,
        slug,
      })
      .returning();

    // This is a mock response for now
    const departmentData: CreateDepartmentData = {
      id: result[0].id,
      name: result[0].name,
      slug: result[0].slug,
    };

    const response: CreateDepartmentResponse = {
      success: true,
      data: departmentData,
    };

    // Validate the response
    return response;
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
