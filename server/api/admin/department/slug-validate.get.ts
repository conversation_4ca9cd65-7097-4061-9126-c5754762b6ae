import { eq } from 'drizzle-orm';
import { department } from '~~/server/database/schema';
import type { SlugValidateResponse } from '~~/app/types/admin/department';
export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event);
    const slug = query.slug as string;

    if (!slug) {
      throw createError({
        statusCode: 400,
        message: 'Slug is required',
      });
    }

    const db = useDrizzle();
    const existingDepartment = await db.query.department.findFirst({
      where: eq(department.slug, slug),
      columns: {
        id: true,
      },
    });

    return {
      isAvailable: !existingDepartment,
    } as SlugValidateResponse;
  } catch (error: any) {
    throw event.context.validation.handleError(error);
  }
});
