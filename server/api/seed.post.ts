export default defineEventHandler(async (_event) => {
  try {
    // Run the seed task
    const result = await runTask('seed');

    // Return success response
    return {
      success: true,
      message: 'Database seeding completed successfully',
      result,
    };
  } catch (error) {
    // Handle errors
    return {
      success: false,
      message: 'Failed to seed database',
      error: error instanceof Error ? error.message : String(error),
    };
  }
});
