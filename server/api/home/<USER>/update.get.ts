import type { SQL } from 'drizzle-orm';
import { and, eq, gte } from 'drizzle-orm';
import type { UpdateListResponse, UpdateListData } from '~/types/home';
import { announcement } from '~~/server/database/schema';

export default defineEventHandler(async () => {
  try {
    const db = useDrizzle();
    const type = 'update';

    const currentTimestamp = Date.now();
    const conditions: SQL[] = [];

    // Type filter
    if (type) {
      conditions.push(eq(announcement.announcementType, type));
    }

    conditions.push(
      eq(announcement.isActive, true),
      gte(announcement.expiresAt, currentTimestamp)
    );

    // Get paginated announcements
    const announcementsList = await db.query.announcement.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
      orderBy: (announcement, { desc }) => [desc(announcement.priority)],
    });

    // Transform the data to AnnouncementListData format
    const announcements: UpdateListData[] = announcementsList.map(
      (announcement) => ({
        id: announcement.id,
        title: announcement.title,
        link: announcement.link ?? null,
      })
    );

    return {
      success: true,
      data: announcements,
    } as UpdateListResponse;
  } catch (error) {
    console.error('Error fetching announcements:', error);
    throw createError({
      statusCode: 500,
      message:
        error instanceof Error
          ? error.message
          : 'Failed to fetch announcements',
    });
  }
});
