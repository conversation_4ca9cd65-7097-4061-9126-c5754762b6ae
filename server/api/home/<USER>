import { desc, inArray } from 'drizzle-orm';
import {
  dynamicTable,
  dynamicTableData,
  menu,
  quickLink,
} from '~~/server/database/schema';
import type { CLUB_COMMITTEE } from '~~/server/database/tables/dynamic-types';

export type MenuItem = {
  label: string; // The name of the menu item
  link?: string; // Optional: The URL for the menu item (only for leaf nodes or direct navigation)
  children?: MenuItem[]; // Optional: Nested sub-menu items
  icon?: string; // Optional: Icon name for the menu item
};

export type NavbarMenu = MenuItem[]; // The main menu is an array of MenuItem

export type HomeMenusResponse = {
  menus: NavbarMenu;
  quickLinks: NavbarMenu;
};

// Helper function to assign icons based on menu item labels
function assignIconToMenuItem(item: MenuItem): MenuItem {
  // Clone the item to avoid mutating the original
  const itemWithIcon = { ...item };

  // Map common menu labels to appropriate icons
  const labelToIconMap: Record<string, string> = {
    // Main menu categories
    Academics: 'GraduationCap',
    About: 'Info',
    Admission: 'FileText',
    Alumni: 'Users',
    Campus: 'Building',
    'Clubs & Committees': 'Users',
    Contact: 'Contact',
    Departments: 'BookOpen',
    'E-Services': 'Rocket',
    Events: 'Calendar',
    Examination: 'FileText',
    Facilities: 'Building2',
    Faculty: 'Users',
    Gallery: 'Image',
    Home: 'Home',
    IQAC: 'Award',
    Library: 'Library',
    News: 'Newspaper',
    Placement: 'Briefcase',
    Programs: 'BookOpen',
    'Quick Links': 'Link',
    Research: 'Microscope',
    Sports: 'Trophy',
    'Student Corner': 'GraduationCap',

    // Common submenu items
    'UG Courses': 'BookOpen',
    'PG Courses': 'BookOpen',
    'Add-on Courses': 'BookCopy',
    'Programs Offered': 'GraduationCap',
    Objectives: 'Target',
    'Institution Distinctiveness': 'Award',
    MOU: 'FileText',
    SSR: 'FileText',
    AQAR: 'FileText',
    'Quality Audits': 'ClipboardCheck',
    Minutes: 'Clock',
    Reports: 'FileText',
    'Best Practices': 'Star',
    Feedback: 'MessageSquare',
    Composition: 'Users',
    'Policy Documents': 'FileText',
    SAAC: 'Award',
    SSS: 'BarChart',
    NIRF: 'Award',
  };

  // Assign icon based on label
  const normalizedLabel = item.label.trim();
  if (labelToIconMap[normalizedLabel]) {
    itemWithIcon.icon = labelToIconMap[normalizedLabel];
  } else {
    // Default icons based on patterns in the label
    if (
      normalizedLabel.includes('Course') ||
      normalizedLabel.includes('Program')
    ) {
      itemWithIcon.icon = 'BookOpen';
    } else if (normalizedLabel.includes('Department')) {
      itemWithIcon.icon = 'Building';
    } else if (
      normalizedLabel.includes('Faculty') ||
      normalizedLabel.includes('Staff')
    ) {
      itemWithIcon.icon = 'Users';
    } else if (
      normalizedLabel.includes('Event') ||
      normalizedLabel.includes('Calendar')
    ) {
      itemWithIcon.icon = 'Calendar';
    } else if (
      normalizedLabel.includes('Gallery') ||
      normalizedLabel.includes('Photo')
    ) {
      itemWithIcon.icon = 'Image';
    } else if (
      normalizedLabel.includes('Contact') ||
      normalizedLabel.includes('Reach')
    ) {
      itemWithIcon.icon = 'Contact';
    } else if (normalizedLabel.includes('About')) {
      itemWithIcon.icon = 'Info';
    } else {
      // Fallback icon if no match is found
      itemWithIcon.icon = 'ArrowUpRight';
    }
  }

  // Recursively assign icons to children
  if (itemWithIcon.children && itemWithIcon.children.length > 0) {
    itemWithIcon.children = itemWithIcon.children.map(assignIconToMenuItem);
  }

  return itemWithIcon;
}

export default defineEventHandler(async (): Promise<HomeMenusResponse> => {
  const db = useDrizzle();

  // Get all the menus from the database
  const menus = await db.query.menu.findMany({
    where: and(isNull(menu.parentId), eq(menu.isActive, true)),
  });

  // Get all the submenus of the menus
  const submenus = await db.query.menu.findMany({
    where: and(
      inArray(
        menu.parentId,
        menus.map((menu) => menu.id)
      ),
      eq(menu.isActive, true)
    ),
    orderBy: [desc(menu.priority)],
  });

  //Arrange the menus and respective submenus as NavbarMenu and sort them by alphabetically
  const otherMenus: NavbarMenu = menus.map((menu) => {
    return {
      label: menu.menuName || '',
      children: submenus
        .filter((submenu) => submenu.parentId === menu.id)
        .map((submenu) => ({
          label: submenu.menuName || '',
          //Link menu slug to the submenu
          link: `/${menu.slug}/${submenu.slug}`,
        })),
    };
  });

  const table = await db.query.dynamicTable.findFirst({
    where: eq(dynamicTable.slug, 'club_and_committee'),
  });
  // Fetch all club/committee data where slug matches
  const clubCommitteeContent = (await db.query.dynamicTableData.findMany({
    where: eq(dynamicTableData.tableId, table?.id ?? 0),
    columns: {
      id: true,
      data: true,
    },
  })) as {
    id: number;
    data: CLUB_COMMITTEE;
  }[];

  const clubLinks = clubCommitteeContent.map((content) => ({
    label: content.data.title,
    link: `/clubs-and-committees/${content.data.slug}`,
  }));

  const clubsAndCommittees: NavbarMenu = [
    {
      label: 'Clubs & Committees',
      children: clubLinks,
    },
  ];

  const departments = await db.query.department.findMany();

  const formattedDepartments = departments.map((department) => {
    return {
      label: department.name,
      link: `/departments/${department.slug}`,
    };
  });

  const academics: NavbarMenu = [
    {
      label: 'Academics',
      children: [
        {
          label: 'Programs Offered',
          children: [
            {
              label: 'UG Courses',
              link: '/programs-offered/ug',
            },
            {
              label: 'PG Courses',
              link: '/programs-offered/pg',
            },
            {
              label: 'Add-on Courses',
              link: '/programs-offered/add-on',
            },
          ],
        },
        {
          label: 'Departments',
          children: formattedDepartments,
        },
      ],
    },
  ];
  const iqac: NavbarMenu = [
    {
      label: 'IQAC',
      children: [
        {
          label: 'About',
          link: '/iqac/about',
        },
        {
          label: 'Objectives',
          link: '/iqac/objectives',
        },
        {
          label: 'Institution Distinctiveness',
          link: '/iqac/institution',
        },
        {
          label: 'MOU',
          link: '/iqac/mou',
        },
        {
          label: 'SSR',
          link: '/iqac/self_study_report',
        },
        {
          label: 'AQAR',
          link: '/iqac/aqar',
        },
        {
          label: 'Quality Audits',
          link: '/iqac/quality_audits',
        },
        {
          label: 'Minutes',
          link: '/iqac/minutes',
        },
        {
          label: 'Reports',
          link: '/iqac/reports',
        },
        {
          label: 'Best Practices',
          link: '/iqac/best_practice',
        },
        {
          label: 'Feedback',
          link: '/iqac/feedback',
        },
        {
          label: 'Composition',
          link: '/iqac/composition',
        },
        {
          label: 'Policy Documents',
          link: '/iqac/policy_documents',
        },
        {
          label: 'SAAC',
          link: '/iqac/saac',
        },
        {
          label: 'SSS',
          link: '/iqac/sss',
        },
        {
          label: 'NIRF',
          link: '/iqac/nirf',
        },
      ],
    },
  ];

  const eServices = await db.query.quickLink.findMany({
    where: eq(quickLink.type, 'eService'),
  });

  const formateEServices: NavbarMenu = [
    {
      label: 'E-Services',
      children: eServices.map((service) => {
        return {
          label: service.title,
          link: service.link || '',
        };
      }),
    },
  ];

  const quickLinks = await db.query.quickLink.findMany({
    where: eq(quickLink.type, 'quickLink'),
  });

  const formateQuickLinks: NavbarMenu = [
    {
      label: 'Quick Links',
      children: quickLinks.map((link) => ({
        label: link.title,
        link: link.link || '',
      })),
    },
  ];

  const menusList = [
    ...otherMenus,
    ...academics,
    ...clubsAndCommittees,
    ...iqac,
    ...formateEServices,
  ];

  // Sort the menus list by label alphabetically
  menusList.sort((a, b) => a.label.localeCompare(b.label));

  // Apply icons to all menu items
  const menusWithIcons = menusList.map(assignIconToMenuItem);
  const quickLinksWithIcons = formateQuickLinks.map(assignIconToMenuItem);

  return {
    menus: menusWithIcons,
    quickLinks: quickLinksWithIcons,
  };
});
