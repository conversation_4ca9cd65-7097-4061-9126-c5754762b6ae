import { department, course, departmentFiles } from '~~/server/database/schema';
import type {
  CourseFilter,
  QuestionBankFilterResponse,
} from '~~/app/types/home/<USER>';
import { and, eq, sql } from 'drizzle-orm';
import type { SQL } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event);
    const departmentId = parseInt(query.departmentId as string);

    const db = useDrizzle();

    const isDepartmentExists = await db.query.department.findFirst({
      where: eq(department.id, departmentId),
    });

    const courseConditions: SQL[] = [];
    const departmentFilesConditions: SQL[] = [];
    departmentFilesConditions.push(eq(departmentFiles.type, 'question_paper'));
    if (isDepartmentExists) {
      courseConditions.push(eq(course.departmentId, isDepartmentExists.id));
      departmentFilesConditions.push(
        eq(departmentFiles.departmentId, isDepartmentExists.id)
      );
    }

    const result = await db.query.course.findMany({
      where: and(...courseConditions),
      columns: {
        id: true,
        name: true,
      },
    });

    const courses: CourseFilter[] = result.map((course) => ({
      id: course.id,
      name: course.name,
    }));

    // Get the max year in the question banks of the department and course

    const minYear = await db
      .select({ min: sql<number>`MIN(${departmentFiles.year})` })
      .from(departmentFiles)
      .where(and(...departmentFilesConditions))
      .then((result) => result[0].min ?? 0);

    // Generate years list
    const currentYear = new Date().getFullYear();
    const lastTenYears = Array.from({ length: 10 }, (_, i) => currentYear - i);

    // if minYear is less than the first year in the lastTenYears, then start from minYear
    if (minYear && minYear < lastTenYears[lastTenYears.length - 1]) {
      // Generate range from minYear to currentYear
      const yearRange = Array.from(
        { length: currentYear - minYear + 1 },
        (_, i) => minYear + i
      );
      lastTenYears.push(...yearRange);
    }

    const years = [...new Set(lastTenYears)].sort((a, b) => b - a);

    // Generate semesters list based on semesterCount
    const maxSemesterCount = await db
      .select({ max: sql<number>`MAX(${course.semesterCount})` })
      .from(course)
      .where(and(...courseConditions))
      .then((result) => result[0].max ?? 0);

    const semesters = Array.from({ length: maxSemesterCount }, (_, i) => i + 1);

    const response: QuestionBankFilterResponse = {
      courses,
      semesters,
      years,
    };

    return {
      success: true,
      data: response,
    };
  } catch (error) {
    console.error('Error fetching courses:', error);
    throw createError({
      statusCode: 500,
      message:
        error instanceof Error ? error.message : 'Failed to fetch courses',
    });
  }
});
