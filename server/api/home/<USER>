import { stats } from '~~/server/database/tables';
import { announcement } from '~~/server/database/tables/announcement';
import { card } from '~~/server/database/tables/sections/card';
import { useDrizzle } from '~~/server/utils/drizzle';
import { quickLink } from '~~/server/database/tables/quick-link';
import { album } from '~~/server/database/tables/album';
import { eq, gte, lte, and } from 'drizzle-orm';
import type {
  NotificationData,
  UpdateData,
  LandingPage,
  PrincipalMessage,
  CampusDetails,
  Research,
  QuickLink,
  Stat,
  Course,
  FileData,
  LinkButton,
  AnnouncementListData,
} from '~/types/home';

const transformFileData = (file: any): FileData => ({
  pathname: file.pathname,
  title: file.title || '',
  type: file.type,
  prefix: file.prefix || '',
});

const transformLinkButton = (button: any): LinkButton => {
  if (!button) return null;
  return {
    enabled: button.enabled ?? undefined,
    title: button.title,
    style: button.style,
    newTab: button.newTab ?? false,
    icon: button.icon || undefined,
    link: button.externalLink || button.internalLink || '',
  };
};

const getFirstContent = (
  contents: { content: string }[] | null | undefined
): string => {
  if (!contents?.length) return '';
  return contents[0].content;
};

const getFileFromArray = (files: any): FileData => {
  if (!files) return {} as FileData;
  if (Array.isArray(files)) {
    if (!files.length) return {} as FileData;
    const firstFile = files[0];
    return transformFileData(firstFile.file || firstFile);
  }
  if (files.file) {
    return transformFileData(files.file);
  }
  return transformFileData(files);
};

export default defineEventHandler(async (): Promise<LandingPage> => {
  const db = useDrizzle();

  // Get quick links
  const quickLinksRaw = await db.query.quickLink.findMany({
    where: eq(quickLink.type, 'quickLink'),
    columns: {
      id: true,
      title: true,
      link: true,
      icon: true,
    },
  });

  const quickLinks: QuickLink[] = quickLinksRaw
    .filter(
      (link): link is QuickLink => link.link !== null && link.icon !== null
    )
    .map((link) => ({
      id: link.id,
      title: link.title,
      link: link.link,
      icon: link.icon,
    }));

  // Get principal message card
  const principalMessageCard = await db.query.card.findFirst({
    where: eq(card.key, 'principal-message'),
    with: {
      cardFiles: {
        with: {
          file: true,
        },
      },
      linkButton: true,
      contents: true,
    },
  });

  const principalMessage: PrincipalMessage = {
    title: principalMessageCard?.title || '',
    name: principalMessageCard?.contents?.[0]?.content || '',
    designation: principalMessageCard?.contents?.[1]?.content || '',
    message: principalMessageCard?.contents?.[2]?.content || '',
    primaryImage: getFileFromArray(principalMessageCard?.cardFiles),
    button: transformLinkButton(principalMessageCard?.linkButton),
  };

  // Get campus details card
  const campusDetailsCard = await db.query.card.findFirst({
    where: eq(card.key, 'campus-details'),
    with: {
      cardFiles: {
        with: {
          file: true,
        },
      },
      linkButton: true,
      contents: true,
    },
  });

  const campusDetails: CampusDetails = {
    title: campusDetailsCard?.title || '',
    description: getFirstContent(campusDetailsCard?.contents),
    button: transformLinkButton(campusDetailsCard?.linkButton),
    primaryImage: getFileFromArray(campusDetailsCard?.cardFiles),
  };

  // Get stats
  const statDataRaw = await db.query.stats.findMany({
    where: eq(stats.sectionId, 1),
    columns: {
      icon: true,
      value: true,
      title: true,
    },
  });

  const statData: Stat[] = statDataRaw.map((stat) => ({
    icon: stat.icon,
    value: String(stat.value),
    title: stat.title,
  }));

  // Get research card
  const researchCard = await db.query.card.findFirst({
    where: eq(card.key, 'research'),
    with: {
      linkButton: true,
      contents: true,
    },
  });

  const research: Research = {
    title: researchCard?.title || '',
    description: getFirstContent(researchCard?.contents),
    button: transformLinkButton(researchCard?.linkButton),
  };

  // Get programs (courses)
  const allProgramsRaw = await db.query.course.findMany({
    columns: {
      id: true,
      departmentId: true,
      name: true,
      specialization: true,
      durationInMonths: true,
      semesterCount: true,
      seatsCount: true,
      title: true,
      content: true,
      type: true,
    },
    with: {
      image: true,
      linkButton: true,
      syllabus: true,
      pos: true,
    },
  });

  const departmentSlugs = await db.query.department.findMany({
    columns: {
      id: true,
      slug: true,
    },
  });

  const departmentSlugsMap = new Map(
    departmentSlugs.map((department) => [department.id, department.slug])
  );

  const mapCourse = (program: (typeof allProgramsRaw)[0]): Course => ({
    id: program.id,
    departmentId: program.departmentId,
    departmentSlug: departmentSlugsMap.get(program.departmentId),
    name: program.name,
    image: getFileFromArray(program.image),
    specialization: program.specialization || '',
    durationInMonths: program.durationInMonths,
    semesterCount: program.semesterCount,
    seatsCount: program.seatsCount,
    content: {
      title: program.title,
      description: program.content,
    },
    syllabus: program.syllabus ? getFileFromArray(program.syllabus) : null,
    pos: program.pos ? getFileFromArray(program.pos) : null,
    primaryButton: transformLinkButton(program.linkButton),
  });

  const programs = {
    ug: allProgramsRaw.filter((p) => p.type === 'ug').map(mapCourse),
    pg: allProgramsRaw.filter((p) => p.type === 'pg').map(mapCourse),
    addOn: allProgramsRaw.filter((p) => p.type === 'add_on').map(mapCourse),
  };

  const currentTimestamp = Date.now();


  const eventsCondition = [
    eq(announcement.announcementType, 'event'),
    eq(announcement.isActive, true),
    gte(announcement.expiresAt, currentTimestamp),
    lte(announcement.scheduledAt, currentTimestamp),
  ];

  const notificationsCondition = [
    eq(announcement.announcementType, 'notice'),
    eq(announcement.isActive, true),
    gte(announcement.expiresAt, currentTimestamp),
    lte(announcement.scheduledAt, currentTimestamp),
  ];

  const updatesCondition = [
    eq(announcement.announcementType, 'update'),
    eq(announcement.isActive, true),
    gte(announcement.expiresAt, currentTimestamp),
    lte(announcement.scheduledAt, currentTimestamp),
  ];

  // Get events
  const eventsRaw = await db.query.announcement.findMany({
    where: and(...eventsCondition),
    with: {
      files: {
        with: {
          file: {
            columns: {
              id: true,
              pathname: true,
              title: true,
              type: true,
            },
          },
        },
      },
    },
    limit: 15,
    orderBy: (announcement, { desc }) => [desc(announcement.priority)],
  });

  const events: AnnouncementListData[] = eventsRaw.map((event) => ({
    id: event.id,
    title: event.title,
    date: event.eventDate,
    description: event.content,
    primaryImage: getFileFromArray(event.files),
  }));

  // Get notifications
  const notificationsRaw = await db.query.announcement.findMany({
    where: and(...notificationsCondition),
    with: {
      files: {
        with: {
          file: true,
        },
      },
      button: true,
    },
    orderBy: (announcement, { desc }) => [desc(announcement.priority)],
  });

  const notifications: NotificationData[] = notificationsRaw.map(
    (notification) => ({
      id: notification.id,
      title: notification.title,
      date: notification.eventDate,
    })
  );

  // Get updates
  const updatesRaw = await db.query.announcement.findMany({
    where: and(...updatesCondition),
    with: {
      files: {
        with: {
          file: true,
        },
      },
      button: true,
    },
    orderBy: (announcement, { desc }) => [desc(announcement.priority)],
  });

  const updates: UpdateData[] = updatesRaw.map((update) => ({
    id: update.id,
    title: update.content,
    buttonTitle: update?.title || null,
    link: update?.link || null,
  }));

  // Get gallery
  const galleryRaw = await db.query.album.findMany({
    where: eq(album.isActive, true),
    with: {
      files: {
        with: {
          file: true,
        },
      },
    },
    limit: 6,
  });

  //Combine athe gallery images and randomly choose 6
  const galleryImages = galleryRaw
    .flatMap((item) => item.files.map((file) => file.file))
    .sort(() => Math.random() - 0.5)
    .slice(0, 6);

  const gallery: FileData[] = galleryImages.map((file) => ({
    pathname: file.pathname,
    title: file.title ?? '',
    type: file.type,
    prefix: 'album',
  }));

  // Return all data
  const result: LandingPage = {
    principalMessage,
    campusDetails,
    stats: statData,
    research,
    quickLinks,
    programs,
    events,
    notifications,
    updates,
    gallery,
  };

  return result;
});
