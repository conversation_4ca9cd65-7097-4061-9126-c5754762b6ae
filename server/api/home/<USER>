import { department, departmentFiles } from '~~/server/database/schema';
import { and, eq, sql } from 'drizzle-orm';
import type { SQL } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event);
    const departmentId = parseInt(query.departmentId as string);

    const db = useDrizzle();

    const isDepartmentExists = await db.query.department.findFirst({
      where: eq(department.id, departmentId),
    });

    const departmentFilesConditions: SQL[] = [];
    departmentFilesConditions.push(eq(departmentFiles.type, 'project'));
    if (isDepartmentExists) {
      departmentFilesConditions.push(
        eq(departmentFiles.departmentId, isDepartmentExists.id)
      );
    }

    // Get the max year in the question banks of the department and course
    const minYear = await db
      .select({ min: sql<number>`MIN(${departmentFiles.year})` })
      .from(departmentFiles)
      .where(and(...departmentFilesConditions))
      .then((result) => result[0].min ?? 0);

    // Generate years list
    const currentYear = new Date().getFullYear();
    const lastTenYears = Array.from({ length: 10 }, (_, i) => currentYear - i);

    // if minYear is less than the first year in the lastTenYears, then start from minYear
    if (minYear && minYear < lastTenYears[lastTenYears.length - 1]) {
      // Generate range from minYear to currentYear
      const yearRange = Array.from(
        { length: currentYear - minYear + 1 },
        (_, i) => minYear + i
      );
      lastTenYears.push(...yearRange);
    }

    const years = [...new Set(lastTenYears)].sort((a, b) => b - a);

    return {
      success: true,
      data: years,
    };
  } catch (error) {
    console.error('Error fetching courses:', error);
    throw createError({
      statusCode: 500,
      message:
        error instanceof Error ? error.message : 'Failed to fetch courses',
    });
  }
});
