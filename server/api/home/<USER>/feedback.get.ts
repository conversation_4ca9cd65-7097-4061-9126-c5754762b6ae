import type { AttachmentResponse } from '~~/app/types/home';
import type { IQAC_ATTACHMENT } from '~~/server/database/tables/dynamic-types';
import {
  dynamicTable,
  dynamicTableData,
} from '~~/server/database/tables/dynamic-table';

export default defineEventHandler(
  async (event): Promise<AttachmentResponse> => {
    const query = getQuery(event);
    const page = parseInt((query.page as string) || '1', 10);
    const limit = parseInt((query.limit as string) || '10', 10);

    const db = useDrizzle();

    const table = await db.query.dynamicTable.findFirst({
      where: eq(dynamicTable.slug, 'iqac_attachments'),
    });

    if (!table) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Table not found',
      });
    }

    const totalCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(dynamicTableData)
      .where(
        and(
          eq(dynamicTableData.tableId, table.id),
          sql`json_extract(${dynamicTableData.data}, '$.slug') = 'feedback'`
        )
      )
      .then((result) => result[0].count);

    const totalPages = Math.ceil(totalCount / limit);

    const attachments = await db.query.dynamicTableData.findMany({
      where: and(
        eq(dynamicTableData.tableId, table.id),
        sql`json_extract(${dynamicTableData.data}, '$.slug') = 'feedback'`
      ),
      limit,
      offset: (page - 1) * limit,
    });

    const list = attachments.map((attachment) => {
      return {
        id: attachment.id,
        title: (attachment.data as IQAC_ATTACHMENT).title,
        linkButton: (attachment.data as IQAC_ATTACHMENT).linkButton,
        files: (attachment.data as IQAC_ATTACHMENT).files,
      };
    });

    return {
      attachments: list,
      pagination: {
        page,
        limit,
        totalItems: totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    };
  }
);
