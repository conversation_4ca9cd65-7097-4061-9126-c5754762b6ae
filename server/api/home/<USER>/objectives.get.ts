import type { OverviewResponse } from '~~/app/types/home/<USER>';
import type { IQAC_OVERVIEW } from '~~/server/database/tables/dynamic-types';
import {
  dynamicTable,
  dynamicTableData,
} from '~~/server/database/tables/dynamic-table';

export default defineEventHandler(async (): Promise<OverviewResponse> => {
  const db = useDrizzle();

  const table = await db.query.dynamicTable.findFirst({
    where: eq(dynamicTable.slug, 'iqac_overview'),
  });

  if (!table) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Table not found',
    });
  }

  const overviewData = await db.query.dynamicTableData.findFirst({
    where: and(
      eq(dynamicTableData.tableId, table.id),
      sql`json_extract(${dynamicTableData.data}, '$.slug') = 'objectives'`
    ),
  });

  if (!overviewData) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Overview data not found',
    });
  }

  const response: OverviewResponse = {
    id: overviewData.id,
    title: (overviewData.data as IQAC_OVERVIEW).title,
    content: (overviewData.data as IQAC_OVERVIEW).content,
    image: (overviewData.data as IQAC_OVERVIEW).image,
    files: (overviewData.data as IQAC_OVERVIEW).files,
    linkButton: (overviewData.data as IQAC_OVERVIEW).linkButton,
  };

  return response;
});
