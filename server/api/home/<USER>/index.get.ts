import { menu } from '~~/server/database/tables';
import { desc } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Get menu data',
    tags: ['Home'],
    parameters: [
      {
        name: 'menu',
        in: 'path',
        required: true,
        schema: { type: 'string' },
        description: 'Menu identifier',
      },
    ],
  },
});

export default defineEventHandler(
  async (
    event
  ): Promise<{
    label: string;
    link: string;
  }> => {
    const { menu: menuSlug } = getRouterParams(event);

    const db = useDrizzle();

    const menuData = await db.query.menu.findFirst({
      where: and(eq(menu.slug, menuSlug), eq(menu.isActive, true)),
      with: {
        submenus: {
          orderBy: [desc(menu.priority)],
          columns: {
            slug: true,
            menuName: true,
            isActive: true,
            priority: true,
          },
        },
      },
      columns: {
        id: true,
        menuName: true,
        slug: true,
      },
    });

    if (!menuData || !menuData.submenus) {
      return { label: '', link: '' };
    }

    const relatedLinks = menuData.submenus
      .sort((a, b) => (a.priority ?? 0) - (b.priority ?? 0))
      .filter((submenu) => submenu.isActive)
      .map((submenu) => ({
        label: submenu.menuName ?? '',
        link: `/${menuData.slug}/${submenu.slug ?? ''}`,
      }));

    return relatedLinks?.[0] || { label: '', link: '' };
  }
);
