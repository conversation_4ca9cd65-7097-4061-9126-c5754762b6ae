import type { SQL } from 'drizzle-orm';
import { and, like } from 'drizzle-orm';
import type { GalleryListData, GalleryListResponse } from '~/types/home';
import { album } from '~~/server/database/schema';
export default defineEventHandler(async (event) => {
  try {
    const db = useDrizzle();
    const query = getQuery(event);
    const page = parseInt((query.page as string) || '1', 10);
    const limit = parseInt((query.limit as string) || '10', 10);
    const search = (query.search as string) || '';

    if (isNaN(page) || page < 1) {
      throw createError({
        statusCode: 400,
        message: 'Invalid page number',
      });
    }

    if (isNaN(limit) || limit < 1 || limit > 50) {
      throw createError({
        statusCode: 400,
        message: 'Invalid limit. Must be between 1 and 50',
      });
    }

    const offset = (page - 1) * limit;
    const conditions: SQL[] = [];

    conditions.push(eq(album.isActive, true));

    if (search) {
      conditions.push(like(album.title, `%${search}%`));
    }

    // Get total count for pagination
    const totalCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(album)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .then((result) => result[0].count);

    const totalPages = Math.ceil(totalCount / limit);

    if (page > totalPages) {
      return {
        success: true,
        data: [],
        pagination: {
          currentPage: totalPages,
          totalPages,
          totalItems: totalCount,
          itemsPerPage: limit,
          hasNextPage: false,
          hasPreviousPage: totalPages > 1,
        },
      };
    }

    // Get paginated albums with photo count
    const albumsList = await db.query.album.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
      with: {
        files: {
          with: {
            file: {
              columns: {
                id: true,
                pathname: true,
                title: true,
                type: true,
              },
            },
          },
        },
      },
      limit,
      offset,
    });

    // Transform the data to albumListData format
    const albums: GalleryListData[] = albumsList.map((album) => {
      const files = album.files.map((file) => file.file);
      const photosCount = files.length;

      return {
        id: album.id,
        title: album.title,
        photosCount,
        photos: files.slice(0, 4).map((file) => ({
          pathname: file.pathname,
          title: file.title ?? '',
          type: file.type,
          prefix: 'album',
        })),
      };
    });

    const response: GalleryListResponse = {
      success: true,
      data: albums,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: totalCount,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
    return response;
  } catch (error) {
    console.error('Error fetching albums:', error);
    throw createError({
      statusCode: 500,
      message:
        error instanceof Error ? error.message : 'Failed to fetch albums',
    });
  }
});
