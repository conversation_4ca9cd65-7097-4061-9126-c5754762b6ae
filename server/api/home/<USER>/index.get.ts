import { eq, desc } from 'drizzle-orm';
import type { FileData } from '~/types/home';
import { popUp } from '~~/server/database/tables/files';

export default defineEventHandler(async (_event) => {
  try {
    const db = useDrizzle();

    // Use the imported popUp table in the query
    const popUpResponse = await db
      .select()
      .from(popUp)
      .where(eq(popUp.title, 'Pop-Up Notification'))
      .orderBy(desc(popUp.createdAt))
      .limit(1);

    const popUpData: FileData | null = popUpResponse[0]
      ? {
          pathname: popUpResponse[0]?.pathname || '',
          title: popUpResponse[0]?.title || '',
          type: popUpResponse[0]?.type || '',
          prefix: popUpResponse[0]?.prefix || 'Pop-Up',
        }
      : null;

    return popUpData;
  } catch (error) {
    console.error('Error fetching pop-up data:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch pop-up data',
    });
  }
});
