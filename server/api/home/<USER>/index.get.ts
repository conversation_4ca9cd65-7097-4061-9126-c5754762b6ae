import type { DepartmentListData } from '~/types/admin/department';

export default defineEventHandler(async () => {
  try {
    // Initialize drizzle
    const db = useDrizzle();

    // Fetch all departments
    const departments = (await db.query.department.findMany({
      columns: {
        id: true,
        name: true,
        slug: true,
        overview: true,
      },
    })) as DepartmentListData[];

    // Validate the response
    return departments;
  } catch (error) {
    console.error('Error fetching departments', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch departments',
    });
  }
});
