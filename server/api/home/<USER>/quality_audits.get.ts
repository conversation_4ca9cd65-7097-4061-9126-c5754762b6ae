import type { Download, DownloadResponse } from '~~/app/types/home/<USER>';
import type { IQAC_DOWNLOAD } from '~~/server/database/tables/dynamic-types';
import {
  dynamicTable,
  dynamicTableData,
} from '~~/server/database/tables/dynamic-table';
import { eq, sql } from 'drizzle-orm';

export default defineEventHandler(async (event): Promise<DownloadResponse> => {
  const query = getQuery(event);
  const page = parseInt((query.page as string) || '1', 10);
  const limit = parseInt((query.limit as string) || '10', 10);

  const db = useDrizzle();

  const table = await db.query.dynamicTable.findFirst({
    where: eq(dynamicTable.slug, 'iqac_downloads'),
  });

  if (!table) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Table not found',
    });
  }

  const totalCount = await db
    .select({ count: sql<number>`count(*)` })
    .from(dynamicTableData)
    .where(
      and(
        eq(dynamicTableData.tableId, table.id),
        sql`json_extract(${dynamicTableData.data}, '$.slug') = 'quality_audits'`
      )
    )
    .then((result) => result[0].count);

  const totalPages = Math.ceil(totalCount / limit);

  const downloadItems = await db.query.dynamicTableData.findMany({
    where: and(
      eq(dynamicTableData.tableId, table.id),
      sql`json_extract(${dynamicTableData.data}, '$.slug') = 'quality_audits'`
    ),
    limit,
    offset: (page - 1) * limit,
  });

  const downloads: Download[] = downloadItems.map((item) => {
    const data = item.data as IQAC_DOWNLOAD;
    return {
      title: data.title,
      file: data.file,
    };
  });

  return {
    downloads,
    pagination: {
      page,
      limit,
      totalItems: totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    },
  };
});
