import type { SectionWithRelationsSimplified } from '~~/server/api/admin/section/index.get';
import type { MenuBySlugData } from '~~/app/types/home/<USER>';
import { menu } from '~~/server/database/tables';
import { processCardConfigHome } from '~~/server/helper/section';
import { desc } from 'drizzle-orm';

defineRouteMeta({
  openAPI: {
    description: 'Get home page data',
    tags: ['Home'],
    parameters: [
      {
        name: 'menu',
        in: 'path',
        required: true,
        schema: { type: 'string' },
        description: 'Menu identifier',
      },
      {
        name: 'submenu',
        in: 'path',
        required: true,
        schema: { type: 'string' },
        description: 'Submenu identifier',
      },
    ],
  },
});

export default defineEventHandler(async (event): Promise<MenuBySlugData> => {
  const { menu: menuSlug, submenu: submenuSlug } = getRouterParams(event);

  const db = useDrizzle();

  const menuData = await db.query.menu.findFirst({
    where: and(eq(menu.slug, menuSlug), eq(menu.isActive, true)),
    with: {
      submenus: {
        orderBy: [desc(menu.priority)],
        columns: {
          slug: true,
          menuName: true,
          isActive: true,
        },
      },
    },
    columns: {
      id: true,
      menuName: true,
      slug: true,
    },
  });

  const submenuData = await db.query.menu.findFirst({
    where: and(eq(menu.slug, submenuSlug), eq(menu.isActive, true)),
    columns: {
      id: true,
      menuName: true,
      slug: true,
      parentId: true,
    },
    with: {
      template: {
        columns: {
          id: true,
        },
        with: {
          sections: {
            with: {
              cardConfig: {
                with: {
                  contents: true,
                  cardFiles: {
                    with: {
                      file: true,
                    },
                  },
                  linkButton: true,
                },
              },
              cardListConfig: true,
              accordionConfig: true,
            },
          },
        },
      },
    },
  });

  if (menuData?.id !== submenuData?.parentId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid menu or submenu',
    });
  }

  const sections = submenuData!.template!.sections;

  // Type assertion for TypeScript
  const typedSections = sections as unknown as SectionWithRelationsSimplified[];

  // Create a variable to hold our processed configuration
  let configuration: any;

  // If we found a section, process it based on its type
  if (typedSections.length > 0) {
    const section = typedSections[0]; // Get the first section

    if (section.type === 'card' && section.cardConfig) {
      configuration = processCardConfigHome(section.cardConfig);
    } else if (section.type === 'cardList' && section.cardListConfig) {
      // For cardList, we need to fetch cards separately
      const cardList = section.cardListConfig;

      // Fetch cards related to this cardList
      const cards = await db.query.card.findMany({
        where: (card, { eq }) => eq(card.sectionId, section.id),
        with: {
          contents: true,
          cardFiles: {
            with: {
              file: true,
            },
          },
          linkButton: true,
        },
      });

      // Process the cardList and its cards using processCardConfigHome
      configuration = {
        title: cardList.title,
        description: cardList.description || '',
        layout: cardList.layout as 'standard' | 'profile' | 'vision',
        cards: cards.map((card) => processCardConfigHome(card)),
      };
    } else if (section.type === 'accordion' && section.accordionConfig) {
      // For accordion, we'll create a standard card as a default
      configuration = {
        title: section.accordionConfig.title || '',
        subtitle: '',
        content: section.accordionConfig.description || '',
        image: {
          pathname: '',
          title: '',
          type: '',
        },
        layout: 'standard',
      };
    } else {
      // Default configuration if no matching section type
      configuration = {
        title: '',
        subtitle: '',
        content: '',
        image: {
          pathname: '',
          title: '',
          type: '',
        },
        layout: 'standard',
      };
    }
  } else {
    // Default configuration if no sections
    configuration = {
      title: '',
      subtitle: '',
      content: '',
      image: {
        pathname: '',
        title: '',
        type: '',
      },
      layout: 'standard',
    };
  }

  return {
    menuData: {
      name: menuData!.menuName!,
      slug: menuData!.slug!,
      relatedLinks: menuData!.submenus
        .filter((submenu) => submenu.isActive)
        .map((submenu) => ({
          label: submenu.menuName!,
          link: `/${menuData!.slug}/${submenu.slug!}`,
        })),
    },
    configuration,
  };
});
