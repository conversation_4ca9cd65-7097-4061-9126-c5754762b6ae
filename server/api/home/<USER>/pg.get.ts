import { course } from '~~/server/database/schema';
import type { Course, CourseListResponse } from '~~/app/types/home/<USER>';
import { and, eq } from 'drizzle-orm';
import type { SQL } from 'drizzle-orm';

export default defineEventHandler(async () => {
  try {
    const db = useDrizzle();

    const conditions: SQL[] = [];
    conditions.push(eq(course.type, 'pg'));

    const result = await db.query.course.findMany({
      where: and(...conditions),
      with: {
        image: {
          columns: {
            pathname: true,
            type: true,
            title: true,
          },
        },
        syllabus: {
          columns: {
            pathname: true,
            type: true,
            title: true,
          },
        },
        pos: {
          columns: {
            pathname: true,
            type: true,
            title: true,
          },
        },
        linkButton: true,
      },
    });

    const courses: Course[] = result.map((course) => ({
      id: course.id,
      departmentId: course.departmentId,
      name: course.name,
      image: {
        pathname: course.image?.pathname ?? '',
        type: course.image?.type ?? '',
        title: course.image?.title ?? '',
        prefix: 'course',
      },
      specialization: course.specialization ?? '',
      durationInMonths: course.durationInMonths,
      semesterCount: course.semesterCount,
      seatsCount: course.seatsCount,
      content: {
        title: course.title,
        description: course.content,
      },
      syllabus: course.syllabus
        ? {
            pathname: course.syllabus.pathname ?? '',
            type: course.syllabus.type ?? '',
            title: course.syllabus.title ?? '',
            prefix: 'syllabus',
          }
        : null,
      pos: course.pos
        ? {
            pathname: course.pos.pathname ?? '',
            type: course.pos.type ?? '',
            title: course.pos.title ?? '',
            prefix: 'pos',
          }
        : null,
      primaryButton: course.linkButton
        ? {
            title: course.linkButton.title,
            style: course.linkButton.style,
            newTab: course.linkButton.newTab ?? false,
            icon: course.linkButton.icon ?? undefined,
            link:
              course.linkButton?.externalLink ??
              course.linkButton?.internalLink ??
              '',
          }
        : null,
    }));

    const response: CourseListResponse = {
      success: true,
      data: courses,
    };

    return response;
  } catch (error) {
    console.error('Error fetching courses:', error);
    throw createError({
      statusCode: 500,
      message:
        error instanceof Error ? error.message : 'Failed to fetch courses',
    });
  }
});
