import type {
  Composition,
  CompositionResponse,
  Download,
} from '~~/app/types/home/<USER>';
import type {
  DynamicTableType,
  IQAC_DOWNLOAD,
  IQAC_TEAM_MEMBERS,
  IQAC_TEAM_TITLES,
} from '~~/server/database/tables/dynamic-types';
import {
  dynamicTable,
  dynamicTableData,
} from '~~/server/database/tables/dynamic-table';
import { desc, eq, and, sql } from 'drizzle-orm';

export default defineEventHandler(async (): Promise<CompositionResponse> => {
  const db = useDrizzle();

  const table = await db.query.dynamicTable.findFirst({
    where: eq(dynamicTable.slug, 'iqac_team_members'),
  });

  const titleTableSlug = 'iqac_team_titles' as DynamicTableType;
  const titleTable = await db.query.dynamicTable.findFirst({
    where: eq(dynamicTable.slug, titleTableSlug),
  });

  const attachmentsTable = await db.query.dynamicTable.findFirst({
    where: eq(dynamicTable.slug, 'iqac_downloads'),
  });

  if (!table) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Table not found',
    });
  }

  const iqacTeamTitles = await db.query.dynamicTableData.findMany({
    where: eq(dynamicTableData.tableId, titleTable?.id || 0),
    columns: {
      createdAt: false,
      updatedAt: false,
      tableId: false,
      id: true,
      data: true,
    },
  });

  const teamMembers = await db.query.dynamicTableData.findMany({
    where: eq(dynamicTableData.tableId, table.id),
    orderBy: [desc(dynamicTableData.id)],
  });

  // IQAC Title data
  const teamTitlesList = iqacTeamTitles
    .map((title) => title.data as IQAC_TEAM_TITLES)
    .sort((a, b) => b.priority - a.priority);

  // Group members by title
  const compositionMap = new Map<
    string,
    {
      name: string;
      designation: string;
      designationOrder: number;
      priority: number;
    }[]
  >();

  for (const member of teamMembers) {
    const data = member.data as IQAC_TEAM_MEMBERS;
    const title = data.title;
    const titleIndex = teamTitlesList.findIndex((t) => t.title === title);
    const titleOrder = titleIndex !== -1 ? titleIndex : teamTitlesList.length;
    const memberData = {
      name: data.name,
      designation: data.designation,
      designationOrder: titleOrder,
      priority: data.priority,
    };

    if (compositionMap.has(title)) {
      compositionMap.get(title)?.push(memberData);
    } else {
      compositionMap.set(title, [memberData]);
    }
  }

  // Sort the members by designationOrder
  for (const members of compositionMap.values()) {
    members.sort((a, b) => b.priority - a.priority);
  }

  console.log('compositionMap', compositionMap);

  // Convert map to array of Composition objects
  const compositions: Composition[] = Array.from(compositionMap.entries())
    .map(([title, members]) => {
      const priority =
        teamTitlesList.find((t) => t.title === title)?.priority || 0;
      return {
        title,
        priority,
        members: members.map((member) => ({
          ...member,
        })),
      };
    })
    .sort((a, b) => b.priority - a.priority)
    .map((c) => ({
      title: c.title,
      members: c.members,
    }));

  const attachmentResponse = await db.query.dynamicTableData.findMany({
    where: and(
      eq(dynamicTableData.tableId, attachmentsTable?.id || 0),
      sql`json_extract(${dynamicTableData.data}, '$.slug') = 'team_members_attachments'`
    ),
    orderBy: [desc(dynamicTableData.id)],
  });

  const attachments: Download[] = attachmentResponse.map((attachment) => {
    const data = attachment.data as IQAC_DOWNLOAD;
    return {
      title: data.title,
      file: data.file,
    };
  });

  const response: CompositionResponse = {
    compositions,
    attachments,
  };

  return response;
});
