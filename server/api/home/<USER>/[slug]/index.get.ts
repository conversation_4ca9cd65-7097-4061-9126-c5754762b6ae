import type {
  AnnouncementListData,
  ClubCommitteeData,
  GalleryListData,
  HomeClubCommitteeResponse,
} from '~/types/home';
import {
  album,
  announcement,
  dynamicTable,
  dynamicTableData,
} from '~~/server/database/tables';
import type { CLUB_COMMITTEE } from '~~/server/database/tables/dynamic-types';
import { eq, and, sql, inArray } from 'drizzle-orm';

export default defineEventHandler(
  async (event): Promise<HomeClubCommitteeResponse> => {
    const db = useDrizzle();
    const slug = getRouterParam(event, 'slug') ?? '';

    // Fetch the club_and_committee table
    const table = await db.query.dynamicTable.findFirst({
      where: eq(dynamicTable.slug, 'club_and_committee'),
    });

    if (!table) {
      throw createError({
        statusCode: 404,
        statusMessage: `Table club_and_committee not found`,
      });
    }

    // Fetch all club/committee data where slug matches
    const clubCommitteeContent = (await db.query.dynamicTableData.findMany({
      where: eq(dynamicTableData.tableId, table.id),
      columns: {
        id: true,
        data: true,
      },
    })) as {
      id: number;
      data: CLUB_COMMITTEE;
    }[];

    const clubLinks = clubCommitteeContent.map((content) => ({
      label: content.data.title,
      link: content.data.slug,
    }));

    // Get the current club/committee data
    const _currentClubCommitteeData = await db.query.dynamicTableData.findFirst(
      {
        where: and(
          eq(dynamicTableData.tableId, table.id),
          sql`json_extract(${dynamicTableData.data}, '$.slug') = ${slug}`
        ),
        columns: {
          id: true,
          data: true,
        },
      }
    );

    const committeeDataValue =
      _currentClubCommitteeData?.data as CLUB_COMMITTEE;

    const galleries = await db.query.album.findMany({
      where: inArray(album.id, committeeDataValue.galleries),
      columns: {
        id: true,
        title: true,
      },
      with: {
        files: {
          with: {
            file: {
              columns: {
                id: true,
                title: true,
                type: true,
                pathname: true,
              },
            },
          },
        },
      },
    });
    // Transform the data to albumListData format
    const formattedGalleries: GalleryListData[] = galleries.map((gallery) => {
      const files = gallery.files.map((file) => file.file);
      const photosCount = files.length;

      return {
        id: gallery.id,
        title: gallery.title,
        photosCount,
        photos: files.slice(0, 4).map((file) => ({
          pathname: file.pathname,
          title: file.title ?? '',
          type: file.type,
          prefix: 'album',
        })),
      };
    });

    const _events = await db.query.announcement.findMany({
      where: inArray(announcement.id, committeeDataValue.events),
      columns: {
        id: true,
        title: true,
        eventDate: true,
        content: true,
      },
      with: {
        files: {
          with: {
            file: true,
          },
        },
      },
    });

    const formattedEvents: AnnouncementListData[] = _events.map((eventItem) => {
      const file = eventItem.files[0].file;
      const primaryImage = {
        pathname: file.pathname,
        title: file.title ?? '',
        type: file.type,
        prefix: '',
      };

      return {
        id: eventItem.id,
        title: eventItem.title,
        date: eventItem.eventDate,
        description: eventItem.content,
        primaryImage,
      };
    });

    const _updatedClubCommitteeData: ClubCommitteeData = {
      id: _currentClubCommitteeData?.id ?? 0,
      title: committeeDataValue.title,
      overview: committeeDataValue.overview,
      slug: committeeDataValue.slug,
      image: committeeDataValue.image,
      type: committeeDataValue.type,
      attachments: committeeDataValue.attachments,
      linkButton: committeeDataValue.linkButton,
      galleries: formattedGalleries,
      events: formattedEvents,
    };

    // Map the data to the response format
    const response: HomeClubCommitteeResponse = {
      menuData: {
        name: 'Clubs & Committees',
        slug: 'clubs-and-committees',
        relatedLinks: clubLinks,
      },
      data: _updatedClubCommitteeData,
    };

    return response;
  }
);
