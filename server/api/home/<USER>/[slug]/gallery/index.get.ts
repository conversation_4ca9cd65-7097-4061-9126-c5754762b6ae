import {
  album,
  department,
  departmentGallery,
} from '~~/server/database/schema';
import { eq, inArray } from 'drizzle-orm';
import type { GalleryListResponse, GalleryListData } from '~/types/home';

// Define types for the gallery data structure
type GalleryRecord = {
  id: number;
  title: string;
  files?: {
    id: number;
    createdAt: number | null;
    updatedAt: number | null;
    fileId: number;
    albumId: number;
    file: {
      id: number;
      title: string | null;
      type: string;
      pathname: string;
    };
  }[];
};

export default defineEventHandler(async (event) => {
  const departmentSlug = getRouterParam(event, 'slug') ?? '';

  const db = useDrizzle();

  const departmentRecord = await db.query.department.findFirst({
    where: eq(department.slug, departmentSlug),
  });

  if (!departmentRecord) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Department not found',
    });
  }

  const galleryIdRecords = await db.query.departmentGallery.findMany({
    where: eq(departmentGallery.departmentId, departmentRecord.id),
    orderBy: departmentGallery.displayOrder,
    columns: {
      galleryId: true,
    },
  });

  // Get and validate pagination parameters
  const { page = '1', limit = '10' } = getQuery(event) as {
    page?: string;
    limit?: string;
  };

  const pageNumber = Math.max(1, parseInt(page) || 1);
  const limitNumber = Math.min(50, Math.max(1, parseInt(limit) || 10));
  const offset = (pageNumber - 1) * limitNumber;
  const totalCount = galleryIdRecords.length;
  const totalPages = Math.ceil(totalCount / limitNumber);

  // Validate page number against total pages
  if (pageNumber > totalPages && totalPages > 0) {
    throw createError({
      statusCode: 400,
      statusMessage: `Page ${pageNumber} exceeds total pages ${totalPages}`,
    });
  }

  // Paginate gallery IDs
  const paginatedGalleryIds = galleryIdRecords
    .slice(offset, offset + limitNumber)
    .map((record) => record.galleryId);

  if (paginatedGalleryIds.length === 0) {
    return {
      success: true,
      data: [],
      pagination: {
        currentPage: pageNumber,
        itemsPerPage: limitNumber,
        totalItems: totalCount,
        totalPages,
        hasNextPage: pageNumber < totalPages,
        hasPreviousPage: pageNumber > 1,
      },
    } as GalleryListResponse;
  }

  const galleries = await db.query.album.findMany({
    where: inArray(album.id, paginatedGalleryIds),
    columns: {
      id: true,
      title: true,
    },
    with: {
      files: {
        with: {
          file: {
            columns: {
              id: true,
              title: true,
              type: true,
              pathname: true,
            },
          },
        },
      },
    },
  });

  // Reorder galleries to match the paginatedGalleryIds order
  const galleryMap = new Map<number, GalleryRecord>();
  galleries.forEach((gallery) => {
    galleryMap.set(gallery.id, gallery);
  });

  const orderedGalleries = paginatedGalleryIds
    .map((id) => galleryMap.get(id))
    .filter((gallery): gallery is GalleryRecord => gallery !== undefined);

  const formattedGalleries: GalleryListData[] = orderedGalleries.map(
    (gallery) => ({
      id: gallery.id,
      title: gallery.title,
      photosCount: gallery.files?.length ?? 0,
      photos:
        gallery.files?.map((item) => ({
          ...item.file,
          title: item.file.title ?? '',
          prefix: 'album',
        })) ?? [],
    })
  );

  const response: GalleryListResponse = {
    success: true,
    data: formattedGalleries,
    pagination: {
      currentPage: pageNumber,
      itemsPerPage: limitNumber,
      totalItems: totalCount,
      totalPages,
      hasNextPage: pageNumber < totalPages,
      hasPreviousPage: pageNumber > 1,
    },
  };

  return response;
});
