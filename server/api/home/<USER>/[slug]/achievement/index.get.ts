import { department, faculty } from '~~/server/database/schema';
import type { Faculty } from '~~/app/types/home/<USER>';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  const departmentSlug = getRouterParam(event, 'slug') ?? '';

  const db = useDrizzle();

  const isDepartmentExists = await db.query.department.findFirst({
    where: eq(department.slug, departmentSlug),
  });

  if (!isDepartmentExists) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Department not found',
    });
  }

  const facultyMembers = (await db.query.faculty.findMany({
    where: eq(faculty.departmentId, isDepartmentExists.id),
    columns: {
      id: true,
      name: true,
      designation: true,
    },
    with: {
      image: {
        columns: {
          pathname: true,
          type: true,
        },
      },
    },
  })) as Faculty[];

  return facultyMembers;
});
