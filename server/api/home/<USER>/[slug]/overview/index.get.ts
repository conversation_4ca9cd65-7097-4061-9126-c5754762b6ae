import { department } from '~~/server/database/schema';
import { eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  const departmentSlug = getRouterParam(event, 'slug') ?? '';

  const db = useDrizzle();

  const departmentData = await db.query.department.findFirst({
    where: eq(department.slug, departmentSlug),
    columns: {
      overview: true,
    },
  });

  if (!departmentData) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Department not found',
    });
  }
  const overview = departmentData.overview || '';

  return { content: overview };
});
