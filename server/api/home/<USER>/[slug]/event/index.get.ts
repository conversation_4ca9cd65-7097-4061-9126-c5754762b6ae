import { department, departmentEvent } from '~~/server/database/schema';
import { eq } from 'drizzle-orm';
import type {
  AnnouncementListData,
  AnnouncementListResponse,
} from '~/types/home';

export default defineEventHandler(async (event) => {
  const departmentSlug = getRouterParam(event, 'slug') ?? '';

  const db = useDrizzle();

  const isDepartmentExists = await db.query.department.findFirst({
    where: eq(department.slug, departmentSlug),
  });

  if (!isDepartmentExists) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Department not found',
    });
  }

  const events = await db.query.departmentEvent.findMany({
    where: eq(departmentEvent.departmentId, isDepartmentExists.id),
    orderBy: departmentEvent.displayOrder,
    columns: {
      id: true,
      displayOrder: true,
      eventId: true,
    },
    with: {
      events: {
        columns: {
          id: true,
          title: true,
          content: true,
          link: true,
          eventDate: true,
          scheduledAt: true,
          expiresAt: true,
          priority: true,
          isActive: true,
          announcementType: true,
        },
        with: {
          files: {
            with: {
              file: true,
            },
          },
        },
      },
    },
  });

  // Transform the queried events to match AnnouncementListData
  const announcementListData = events
    .map((deptEvent) => {
      const eventItem = deptEvent.events;
      if (!eventItem?.files?.[0]?.file) return null;

      const file = eventItem.files[0].file;
      const primaryImage = {
        pathname: file.pathname,
        title: file.title ?? '',
        type: file.type,
        prefix: '',
      };

      return {
        id: eventItem.id,
        title: eventItem.title,
        date: eventItem.eventDate,
        description: eventItem.content,
        primaryImage,
      };
    })
    .filter((item): item is AnnouncementListData => item !== null);

  // Create a simple pagination object
  const pagination = {
    currentPage: 1,
    totalPages: 1,
    totalItems: announcementListData.length,
    itemsPerPage: announcementListData.length,
    hasNextPage: false,
    hasPrevPage: false,
    hasPreviousPage: false,
  };

  const response: AnnouncementListResponse = {
    success: true,
    data: announcementListData,
    pagination,
  };

  return response;
});
