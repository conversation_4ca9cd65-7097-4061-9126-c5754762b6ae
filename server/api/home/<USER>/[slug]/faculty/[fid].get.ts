import { department, faculty } from '~~/server/database/schema';
import { eq } from 'drizzle-orm';
import type { FacultyFullData } from '~/types/home';

export default defineEventHandler(async (event) => {
  const departmentSlug = getRouterParam(event, 'slug') ?? '';
  const facultyId = parseInt(getRouterParam(event, 'fid') ?? '0');
  const db = useDrizzle();

  const isDepartmentExists = await db.query.department.findFirst({
    where: eq(department.slug, departmentSlug),
  });

  if (!isDepartmentExists) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Department not found',
    });
  }

  const isFacultyExists = await db.query.faculty.findFirst({
    where: eq(faculty.id, facultyId),
  });

  if (!isFacultyExists) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Faculty not found',
    });
  }

  const result = await db.query.faculty.findFirst({
    where: eq(faculty.id, facultyId),
    columns: {
      id: true,
      name: true,
      designation: true,
      startDate: true,
      endDate: true,
      education: true,
      experience: true,
      areaOfInterest: true,
    },
    with: {
      resume: { columns: { pathname: true, type: true, title: true } },
      image: { columns: { pathname: true, type: true, title: true } },
    },
  });
  const facultyMember: FacultyFullData = {
    name: result?.name ?? '',
    designation: result?.designation ?? '',
    startDate: result?.startDate ?? '',
    education:
      result?.education.map((education) => ({
        degree: education.degree,
        university: education.university,
        passOutYear: education.passOutYear,
      })) ?? [],
    experience:
      result?.experience.map((experience) => ({
        startYear: experience.startYear,
        endYear: experience.endYear,
        organization: experience.organization,
        designation: experience.designation,
      })) ?? [],
    areaOfInterest: result?.areaOfInterest ?? [],
    image: result?.image
      ? {
          pathname: result?.image?.pathname ?? '',
          type: result?.image?.type ?? '',
          title: result?.image?.title ?? '',
          prefix: 'profile',
        }
      : null,
    resume: result?.resume
      ? {
          pathname: result?.resume?.pathname ?? '',
          type: result?.resume?.type ?? '',
          title: result?.resume?.title ?? '',
          prefix: 'resume',
        }
      : null,
  };
  return facultyMember;
});
