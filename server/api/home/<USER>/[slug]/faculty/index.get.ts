import { department, faculty } from '~~/server/database/schema';
import type { Faculty, FacultyListResponse } from '~~/app/types/home/<USER>';
import { and, eq } from 'drizzle-orm';
import type { SQL } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const departmentSlug = getRouterParam(event, 'slug') ?? '';

    const db = useDrizzle();

    const isDepartmentExists = await db.query.department.findFirst({
      where: eq(department.slug, departmentSlug),
    });

    if (!isDepartmentExists) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Department not found',
      });
    }

    const conditions: SQL[] = [];
    conditions.push(eq(faculty.departmentId, isDepartmentExists.id));

    const result = await db.query.faculty.findMany({
      where: and(...conditions),
      columns: {
        id: true,
        name: true,
        designation: true,
      },
      with: {
        image: {
          columns: {
            pathname: true,
            type: true,
            title: true,
          },
        },
      },
      orderBy: (faculty, { desc }) => [desc(faculty.priority)],
    });

    const facultyMembers: Faculty[] = result.map((faculty) => ({
      id: faculty.id,
      name: faculty.name,
      designation: faculty.designation,
      image: {
        pathname: faculty.image?.pathname ?? '',
        type: faculty.image?.type ?? '',
        title: faculty.image?.title ?? '',
        prefix: 'profile',
      },
    }));

    const response: FacultyListResponse = {
      success: true,
      data: facultyMembers,
    };

    return response;
  } catch (error) {
    console.error('Error fetching faculty members:', error);
    throw createError({
      statusCode: 500,
      message:
        error instanceof Error
          ? error.message
          : 'Failed to fetch faculty members',
    });
  }
});
