import { department, student } from '~~/server/database/schema';
import { eq } from 'drizzle-orm';
import type { Topper } from '~~/app/types/home/<USER>';

export default defineEventHandler(async (event): Promise<Topper> => {
  const departmentSlug = getRouterParam(event, 'slug') ?? '';
  const topperId = parseInt(getRouterParam(event, 'tid') ?? '0');
  const db = useDrizzle();

  const isDepartmentExists = await db.query.department.findFirst({
    where: eq(department.slug, departmentSlug),
  });

  if (!isDepartmentExists) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Department not found',
    });
  }

  const isTopperExists = await db.query.student.findFirst({
    where: eq(student.id, topperId),
  });

  if (!isTopperExists) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Topper not found',
    });
  }

  const topperRecord = await db.query.student.findFirst({
    where: eq(student.id, topperId),
    columns: {
      id: true,
      name: true,
      courseId: true,
      startYear: true,
      passOutYear: true,
      mark: true,
    },
    with: {
      image: { columns: { pathname: true, type: true } },
    },
  });

  if (!topperRecord) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Topper record not found',
    });
  }

  if (!topperRecord.image) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Topper image not found',
    });
  }

  const mappedTopper: Topper = {
    id: topperRecord.id,
    name: topperRecord.name,
    course: String(topperRecord.courseId),
    department: isDepartmentExists.name || departmentSlug,
    startYear: topperRecord.startYear,
    endYear: topperRecord.passOutYear ?? 0,
    percentage: topperRecord.mark ?? 0,
    image: {
      pathname: topperRecord.image.pathname,
      type: topperRecord.image.type,
      title: '',
      prefix: '',
    },
  };

  return mappedTopper;
});
