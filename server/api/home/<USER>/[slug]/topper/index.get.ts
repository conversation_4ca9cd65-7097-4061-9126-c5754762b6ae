import { department, student } from '~~/server/database/schema';
import { eq, and } from 'drizzle-orm';
import type { ToppersListResponse, Topper } from '~~/app/types/home/<USER>';

export default defineEventHandler(
  async (event): Promise<ToppersListResponse> => {
    const departmentSlug = getRouterParam(event, 'slug') ?? '';
    const query = getQuery(event);
    const page = Math.max(1, parseInt((query.page as string) || '1'));
    const limit = Math.min(
      50,
      Math.max(1, parseInt((query.limit as string) || '10'))
    );
    const offset = (page - 1) * limit;

    const db = useDrizzle();

    const isDepartmentExists = await db.query.department.findFirst({
      where: eq(department.slug, departmentSlug),
    });

    if (!isDepartmentExists) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Department not found',
      });
    }

    // Get total count first
    const totalToppers = await db.query.student.findMany({
      where: and(
        eq(student.departmentId, isDepartmentExists.id),
        eq(student.type, 'topper')
      ),
    });

    const totalItems = totalToppers.length;
    const totalPages = Math.ceil(totalItems / limit);

    // Get paginated toppers
    const topperRecords = await db.query.student.findMany({
      where: and(
        eq(student.departmentId, isDepartmentExists.id),
        eq(student.type, 'topper')
      ),
      columns: {
        id: true,
        name: true,
        courseId: true,
        startYear: true,
        passOutYear: true,
        mark: true,
      },
      with: {
        image: { columns: { pathname: true, type: true } },
      },
      limit,
      offset,
    });

    const toppers: Topper[] = topperRecords
      .filter((record) => record.image) // Filter out records without images
      .map((record) => ({
        id: record.id,
        name: record.name,
        course: String(record.courseId),
        department: isDepartmentExists.name || departmentSlug,
        startYear: record.startYear,
        endYear: record.passOutYear ?? 0,
        percentage: record.mark ?? 0,
        image: {
          pathname: record.image!.pathname,
          type: record.image!.type,
          title: '',
          prefix: '',
        },
      }));

    return {
      success: true,
      data: toppers,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }
);
