import { department, course } from '~~/server/database/schema';
import type { Course, CourseListResponse } from '~~/app/types/home/<USER>';
import { and, eq, sql } from 'drizzle-orm';
import type { SQL } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  try {
    const departmentSlug = getRouterParam(event, 'slug') ?? '';
    const query = getQuery(event);
    const page = parseInt((query.page as string) || '1', 10);
    const limit = parseInt((query.limit as string) || '10', 10);

    if (isNaN(page) || page < 1) {
      throw createError({
        statusCode: 400,
        message: 'Invalid page number',
      });
    }

    if (isNaN(limit) || limit < 1 || limit > 50) {
      throw createError({
        statusCode: 400,
        message: 'Invalid limit. Must be between 1 and 50',
      });
    }

    const db = useDrizzle();

    const isDepartmentExists = await db.query.department.findFirst({
      where: eq(department.slug, departmentSlug),
    });

    if (!isDepartmentExists) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Department not found',
      });
    }

    const conditions: SQL[] = [];
    conditions.push(eq(course.departmentId, isDepartmentExists.id));

    // Get total count for pagination
    const totalCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(course)
      .where(and(...conditions))
      .then((result) => result[0].count);

    const totalPages = Math.ceil(totalCount / limit);
    const offset = (page - 1) * limit;

    if (page > totalPages && totalPages > 0) {
      return {
        success: true,
        data: [],
        pagination: {
          currentPage: totalPages,
          totalPages,
          totalItems: totalCount,
          itemsPerPage: limit,
          hasNextPage: false,
          hasPreviousPage: totalPages > 1,
        },
      };
    }

    const result = await db.query.course.findMany({
      where: and(...conditions),
      with: {
        image: {
          columns: {
            pathname: true,
            type: true,
            title: true,
          },
        },
        syllabus: {
          columns: {
            pathname: true,
            type: true,
            title: true,
          },
        },
        pos: {
          columns: {
            pathname: true,
            type: true,
            title: true,
          },
        },
        linkButton: true,
      },
      limit,
      offset,
    });

    const courses: Course[] = result.map((course) => ({
      id: course.id,
      departmentId: course.departmentId,
      name: course.name,
      image: {
        pathname: course.image?.pathname ?? '',
        type: course.image?.type ?? '',
        title: course.image?.title ?? '',
        prefix: 'course',
      },
      specialization: course.specialization ?? '',
      durationInMonths: course.durationInMonths,
      semesterCount: course.semesterCount,
      seatsCount: course.seatsCount,
      content: {
        title: course.title,
        description: course.content,
      },
      syllabus: course.syllabus
        ? {
            pathname: course.syllabus.pathname ?? '',
            type: course.syllabus.type ?? '',
            title: course.syllabus.title ?? '',
            prefix: 'syllabus',
          }
        : null,
      pos: course.pos
        ? {
            pathname: course.pos.pathname ?? '',
            type: course.pos.type ?? '',
            title: course.pos.title ?? '',
            prefix: 'pos',
          }
        : null,
      primaryButton: course.linkButton
        ? {
            enabled: course.linkButton.enabled ?? false,
            title: course.linkButton.title,
            style: course.linkButton.style,
            newTab: course.linkButton.newTab ?? false,
            icon: course.linkButton.icon ?? undefined,
            link:
              course.linkButton?.externalLink ??
              course.linkButton?.internalLink ??
              '',
          }
        : null,
    }));

    const response: CourseListResponse = {
      success: true,
      data: courses,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: totalCount,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };

    return response;
  } catch (error) {
    console.error('Error fetching courses:', error);
    throw createError({
      statusCode: 500,
      message:
        error instanceof Error ? error.message : 'Failed to fetch courses',
    });
  }
});
