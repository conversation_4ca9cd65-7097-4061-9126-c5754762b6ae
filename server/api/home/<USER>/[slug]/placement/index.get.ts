import { department, student } from '~~/server/database/schema';
import { eq, and } from 'drizzle-orm';
import type {
  PlacementListResponse,
  Placement,
} from '~~/app/types/home/<USER>';

export default defineEventHandler(
  async (event): Promise<PlacementListResponse> => {
    const departmentSlug = getRouterParam(event, 'slug') ?? '';
    const query = getQuery(event);
    const page = Math.max(1, parseInt((query.page as string) || '1'));
    const limit = Math.min(
      50,
      Math.max(1, parseInt((query.limit as string) || '10'))
    );
    const offset = (page - 1) * limit;

    const db = useDrizzle();

    const isDepartmentExists = await db.query.department.findFirst({
      where: eq(department.slug, departmentSlug),
    });

    if (!isDepartmentExists) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Department not found',
      });
    }

    // Get total count first
    const totalPlacements = await db.query.student.findMany({
      where: and(
        eq(student.departmentId, isDepartmentExists.id),
        eq(student.type, 'placement')
      ),
    });

    const totalItems = totalPlacements.length;
    const totalPages = Math.ceil(totalItems / limit);

    // Get paginated placements
    const placementRecords = await db.query.student.findMany({
      where: and(
        eq(student.departmentId, isDepartmentExists.id),
        eq(student.type, 'placement')
      ),
      columns: {
        id: true,
        name: true,
        courseId: true,
        startYear: true,
        passOutYear: true,
        placedAt: true,
      },
      with: {
        image: { columns: { pathname: true, type: true } },
      },
      limit,
      offset,
    });

    const placements: Placement[] = placementRecords
      .filter((record) => record.image) // Filter out records without images
      .map((record) => ({
        id: record.id,
        name: record.name,
        course: String(record.courseId),
        department: isDepartmentExists.name || departmentSlug,
        startYear: record.startYear,
        endYear: record.passOutYear ?? 0,
        company: record.placedAt ?? 'N/A',
        image: {
          pathname: record.image!.pathname,
          type: record.image!.type,
          title: '',
          prefix: '',
        },
      }));

    return {
      success: true,
      data: placements,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }
);
