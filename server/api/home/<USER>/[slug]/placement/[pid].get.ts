import { department, student } from '~~/server/database/schema';
import { eq } from 'drizzle-orm';
import type { Placement } from '~~/app/types/home/<USER>';

export default defineEventHandler(async (event): Promise<Placement> => {
  const departmentSlug = getRouterParam(event, 'slug') ?? '';
  const placementId = parseInt(getRouterParam(event, 'pid') ?? '0');
  const db = useDrizzle();

  const isDepartmentExists = await db.query.department.findFirst({
    where: eq(department.slug, departmentSlug),
  });

  if (!isDepartmentExists) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Department not found',
    });
  }

  const isPlacementExists = await db.query.student.findFirst({
    where: eq(student.id, placementId),
  });

  if (!isPlacementExists) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Placement not found',
    });
  }

  const placementRecord = await db.query.student.findFirst({
    where: eq(student.id, placementId),
    columns: {
      id: true,
      name: true,
      courseId: true,
      startYear: true,
      passOutYear: true,
      placedAt: true,
    },
    with: {
      image: { columns: { pathname: true, type: true } },
    },
  });

  if (!placementRecord) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Placement record not found',
    });
  }

  if (!placementRecord.image) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Placement image not found',
    });
  }

  const placement: Placement = {
    id: placementRecord.id,
    name: placementRecord.name,
    course: String(placementRecord.courseId),
    department: isDepartmentExists.name || departmentSlug,
    startYear: placementRecord.startYear,
    endYear: placementRecord.passOutYear ?? 0,
    company: placementRecord.placedAt ?? 'N/A',
    image: {
      pathname: placementRecord.image.pathname,
      type: placementRecord.image.type,
      title: '',
      prefix: '',
    },
  };

  return placement;
});
