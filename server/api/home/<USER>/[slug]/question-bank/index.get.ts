import { department, departmentFiles } from '~~/server/database/schema';
import type {
  DepartmentFilesResponse,
  QuestionBankFiles,
} from '~~/app/types/home/<USER>';
import type { SQL } from 'drizzle-orm';
import { and, eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  const departmentSlug = getRouterParam(event, 'slug') ?? '';
  //Add filters course, year, semester
  const query = getQuery(event);
  const courseId = query.courseId ?? '';
  const year = query.year ?? '';
  const semester = query.semester ?? '';
  const page = parseInt((query.page as string) || '1', 10);
  const limit = parseInt((query.limit as string) || '10', 10);

  if (isNaN(page) || page < 1) {
    throw createError({
      statusCode: 400,
      message: 'Invalid page number',
    });
  }

  if (isNaN(limit) || limit < 1 || limit > 50) {
    throw createError({
      statusCode: 400,
      message: 'Invalid limit. Must be between 1 and 50',
    });
  }

  const db = useDrizzle();

  const isDepartmentExists = await db.query.department.findFirst({
    where: eq(department.slug, departmentSlug),
  });

  if (!isDepartmentExists) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Department not found',
    });
  }

  const conditions: SQL[] = [];
  conditions.push(eq(departmentFiles.departmentId, isDepartmentExists.id));
  conditions.push(eq(departmentFiles.type, 'question_paper'));
  if (courseId) {
    conditions.push(eq(departmentFiles.courseId, Number(courseId)));
  }
  if (year) {
    conditions.push(eq(departmentFiles.year, Number(year)));
  }
  if (semester) {
    conditions.push(eq(departmentFiles.semester, Number(semester)));
  }

  // Get total count for pagination
  const totalCount = await db
    .select({ count: sql<number>`count(*)` })
    .from(departmentFiles)
    .where(and(...conditions))
    .then((result) => result[0].count);

  const totalPages = Math.ceil(totalCount / limit);
  const offset = (page - 1) * limit;

  if (page > totalPages && totalPages > 0) {
    return {
      success: true,
      data: [],
      pagination: {
        currentPage: totalPages,
        totalPages,
        totalItems: totalCount,
        itemsPerPage: limit,
        hasNextPage: false,
        hasPreviousPage: totalPages > 1,
      },
    };
  }

  const result = await db.query.departmentFiles.findMany({
    where: conditions.length > 0 ? and(...conditions) : undefined,
    columns: {
      id: true,
      title: true,
      year: true,
      semester: true,
    },
    with: {
      department: {
        columns: {
          id: true,
          name: true,
        },
      },
      file: {
        columns: {
          pathname: true,
          type: true,
        },
      },
      course: {
        columns: {
          name: true,
        },
      },
    },
    limit,
    offset,
  });

  const questionBanks: QuestionBankFiles[] = result.map((qb) => ({
    id: qb.id,
    title: qb.title,
    year: qb.year ?? 0,
    semester: qb.semester ?? 0,
    courseName: qb.course?.name ?? '',
    department: {
      id: qb.department.id,
      name: qb.department.name,
    },
    file: {
      title: qb.file.pathname,
      prefix: 'question-paper',
      pathname: qb.file.pathname,
      type: qb.file.type ?? '',
    },
  }));

  const response: DepartmentFilesResponse = {
    success: true,
    data: questionBanks,
    pagination: {
      currentPage: page,
      totalPages,
      totalItems: totalCount,
      itemsPerPage: limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };

  return response;
});
