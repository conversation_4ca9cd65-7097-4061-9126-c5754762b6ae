import type { SQL } from 'drizzle-orm';
import { and, eq, gte, lt, lte } from 'drizzle-orm';
import type {
  AnnouncementListData,
  AnnouncementListResponse,
} from '~/types/home';
import { announcement } from '~~/server/database/schema';

export default defineEventHandler(async (event) => {
  try {
    const db = useDrizzle();
    const query = getQuery(event);
    const type = 'event';
    const filterStatus = query.filterStatus as string | undefined;
    const page = parseInt((query.page as string) || '1', 10);
    const limit = parseInt((query.limit as string) || '10', 10);

    const currentTimestamp = Date.now();
    const conditions: SQL[] = [];
    const offset = (page - 1) * limit;

    if (isNaN(page) || page < 1) {
      throw createError({
        statusCode: 400,
        message: 'Invalid page number',
      });
    }

    if (isNaN(limit) || limit < 1 || limit > 50) {
      throw createError({
        statusCode: 400,
        message: 'Invalid limit. Must be between 1 and 50',
      });
    }

    // Type filter
    if (type) {
      conditions.push(eq(announcement.announcementType, type));
    }

    // Status filter
    if (filterStatus && filterStatus !== 'all') {
      switch (filterStatus) {
        case 'active': {
          conditions.push(
            eq(announcement.isActive, true),
            lte(announcement.scheduledAt, currentTimestamp),
            gte(announcement.expiresAt, currentTimestamp)
          );
          break;
        }
        case 'inactive': {
          conditions.push(eq(announcement.isActive, false));
          break;
        }
        case 'expired': {
          conditions.push(lt(announcement.expiresAt, currentTimestamp));
          break;
        }
      }
    }

    // Get total count for pagination
    const totalCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(announcement)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .then((result) => result[0].count);

    const totalPages = Math.ceil(totalCount / limit);

    // Get paginated announcements
    const announcementsList = await db.query.announcement.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
      with: {
        button: true,
        files: {
          with: {
            file: {
              columns: {
                id: true,
                pathname: true,
                title: true,
                type: true,
              },
            },
          },
        },
      },
      limit,
      offset,
      orderBy: (announcement, { desc }) => [desc(announcement.priority)],
    });

    // Transform the data to AnnouncementListData format
    const announcements: AnnouncementListData[] = announcementsList.map(
      (announcement) => ({
        id: announcement.id,
        title: announcement.title,
        date: announcement?.eventDate ?? null,
        description: announcement.content,
        primaryImage: {
          pathname: announcement.files[0].file.pathname,
          title: announcement.files[0].file.title ?? '',
          type: announcement.files[0].file.type,
          prefix: type ?? 'announcement',
        },
        attachments: announcement.files.slice(1).map((file) => ({
          pathname: file.file.pathname,
          title: file.file.title ?? '',
          type: file.file.type,
          prefix: type ?? 'announcement',
        })),
        button: announcement.button
          ? {
              title: announcement.button.title,
              style: announcement.button.style,
              newTab: announcement.button.newTab ?? false,
              ...(announcement.button.icon && {
                icon: announcement.button.icon,
              }),
              link:
                announcement.button?.externalLink ??
                announcement.button?.internalLink ??
                '',
            }
          : undefined,
      })
    );

    return {
      success: true,
      data: announcements,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: totalCount,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    } as AnnouncementListResponse;
  } catch (error) {
    console.error('Error fetching announcements:', error);
    throw createError({
      statusCode: 500,
      message:
        error instanceof Error
          ? error.message
          : 'Failed to fetch announcements',
    });
  }
});
