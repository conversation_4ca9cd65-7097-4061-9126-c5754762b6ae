import type { SQL } from 'drizzle-orm';
import { and, eq } from 'drizzle-orm';
import type { GalleryData, GalleryResponse } from '~/types/home';
import { album } from '~~/server/database/schema';
import { idSchema } from '~~/shared/schema';

export default defineEventHandler(async (event) => {
  try {
    const db = useDrizzle();
    const params = getRouterParams(event);
    const { id } = idSchema.parse(params);

    if (!id || isNaN(id)) {
      throw createError({
        statusCode: 400,
        message: 'Valid gallery ID is required',
      });
    }

    const conditions: SQL[] = [eq(album.id, id)];

    const galleryData = await db.query.album.findFirst({
      where: and(...conditions),
      with: {
        files: {
          with: {
            file: {
              columns: {
                id: true,
                pathname: true,
                title: true,
                type: true,
              },
            },
          },
        },
      },
    });
    if (!galleryData) {
      throw createError({
        statusCode: 404,
        message: 'Gallery not found',
      });
    }

    const transformedData: GalleryData = {
      id: galleryData.id,
      title: galleryData.title,
      photosCount: galleryData.files.length,
      photos: galleryData.files.map((file) => ({
        pathname: file.file.pathname,
        title: file.file.title ?? '',
        type: file.file.type,
        prefix: 'album',
      })),
    };

    const response: GalleryResponse = {
      success: true,
      data: transformedData,
    };

    return response;
  } catch (error) {
    console.error('Error fetching gallery:', error);
    throw error;
  }
});
