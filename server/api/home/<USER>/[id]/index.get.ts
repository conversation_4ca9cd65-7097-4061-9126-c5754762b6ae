import type { SQL } from 'drizzle-orm';
import { and, eq, gte } from 'drizzle-orm';
import type { AnnouncementData } from '~/types/home';
import { announcement } from '~~/server/database/schema';
import { idSchema } from '~~/shared/schema';

export default defineEventHandler(async (event) => {
  try {
    const db = useDrizzle();
    const params = getRouterParams(event);
    const { id } = idSchema.parse(params);

    if (!id || isNaN(id)) {
      throw createError({
        statusCode: 400,
        message: 'Valid announcement ID is required',
      });
    }

    const currentTimestamp = Date.now();
    const conditions: SQL[] = [
      eq(announcement.id, id),
      eq(announcement.isActive, true),
      gte(announcement.expiresAt, currentTimestamp),
    ];

    const announcementData = await db.query.announcement.findFirst({
      where: and(...conditions),
      with: {
        button: true,
        files: {
          with: {
            file: {
              columns: {
                id: true,
                pathname: true,
                title: true,
                type: true,
              },
            },
          },
        },
      },
      orderBy: (announcement, { desc }) => [desc(announcement.priority)],
    });
    if (!announcementData) {
      throw createError({
        statusCode: 404,
        message: 'Announcement not found',
      });
    }

    // Transform the data to AnnouncementListData format
    const transformedData: AnnouncementData = {
      id: announcementData.id,
      title: announcementData.title,
      date: announcementData?.eventDate ?? null,
      description: announcementData.content,
      primaryImage: announcementData.files[0]?.file
        ? {
            pathname: announcementData.files[0].file.pathname,
            title: announcementData.files[0].file.title ?? '',
            type: announcementData.files[0].file.type,
            prefix: announcementData.announcementType ?? 'announcement',
          }
        : null,
      attachments: announcementData.files
        .slice(1)
        .filter((file) => file?.file)
        .map((file) => ({
          pathname: file.file.pathname,
          title: file.file.title ?? '',
          type: file.file.type,
          prefix: announcementData.announcementType ?? 'announcement',
        })),
      button: announcementData?.button
        ? {
            enabled: announcementData.button.enabled ?? true,
            title: announcementData.button.title,
            style: announcementData.button.style,
            newTab: announcementData.button.newTab ?? false,
            ...(announcementData.button.icon && {
              icon: announcementData.button.icon,
            }),
            link:
              announcementData.button?.externalLink ??
              announcementData.button?.internalLink ??
              '',
          }
        : null,
    };

    return {
      success: true,
      data: transformedData,
    };
  } catch (error) {
    console.error('Error fetching announcement:', error);
    throw error;
  }
});
