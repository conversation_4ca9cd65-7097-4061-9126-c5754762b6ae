import {
  album,
  albumFiles,
  albumFilesRelations,
  albumRelations,
  announcement,
  announcementFiles,
  announcementFilesRelations,
  announcementRelations,
  files,
  menu,
  menuRelations,
  tempFiles,
  stats,
  toggle,
  downloadButton,
  downloadButtonRelations,
  linkButton,
  linkButtonRelations,
  department,
  departmentFiles,
  departmentRelations,
  departmentFilesRelations,
  faculty,
  facultyRelations,
  course,
  courseRelations,
  student,
  studentRelations,
  quickLink,
  departmentEvent,
  departmentGallery,
  departmentEventRelations,
  departmentGalleryRelations,
  admin,
  dynamicTable,
  dynamicTableData,
  dynamicTableDataRelations,
  dynamicTableRelations,
  popUp,
} from './tables';

import {
  accordion,
  accordionRelations,
  card,
  cardFiles,
  cardFilesRelations,
  cardList,
  cardListRelations,
  cardRelations,
  section,
  sectionRelations,
  toggleRelations,
  statsRelations,
} from './tables/sections';
import { template, templateRelations } from './tables/template';

export * from './tables';

// Export schema for drizzle-kit
export const schema = {
  // Core tables with no or minimal dependencies
  files,
  tempFiles,
  admin,
  popUp,

  // Tables with circular dependencies need careful ordering
  // First create menu which template depends on
  menu,

  // Section components needed by other tables
  accordion,
  section,
  stats,
  toggle,

  // Core content tables
  album,
  announcement,
  quickLink,

  // Card components - depend on files
  card,
  cardList,

  // UI components that depend on cards/sections
  cardFiles,
  linkButton,
  downloadButton,

  // Template depends on menu
  template,

  // Department hierarchy - department first, then related tables
  department,
  faculty,
  course,
  student,

  // Join tables with multiple dependencies - come last
  departmentFiles,
  departmentGallery,
  departmentEvent,
  announcementFiles,
  albumFiles,

  // Dynamic tables
  dynamicTable,
  dynamicTableData,
};

// Export relations for drizzle-orm
export const dbRelations = {
  templateRelations,
  menuRelations,
  announcementRelations,

  // Component relations
  sectionRelations,
  cardRelations,
  cardFilesRelations,
  accordionRelations,
  cardListRelations,
  linkButtonRelations,
  downloadButtonRelations,
  announcementFilesRelations,
  albumRelations,
  albumFilesRelations,
  toggleRelations,
  statsRelations,

  // Department related relations
  departmentRelations,
  departmentFilesRelations,
  departmentGalleryRelations,
  departmentEventRelations,
  facultyRelations,
  courseRelations,
  studentRelations,

  // Dynamic table relations
  dynamicTableDataRelations,
  dynamicTableRelations,
};
