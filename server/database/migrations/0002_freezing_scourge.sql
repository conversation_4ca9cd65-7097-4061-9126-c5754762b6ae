PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_toggle_template` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`section_id` integer,
	`title` text NOT NULL,
	`is_active` integer NOT NULL,
	`toggle_type` text NOT NULL,
	`menu_id` integer,
	FOREIGN KEY (`section_id`) REFERENCES `section`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`menu_id`) REFERENCES `menu`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_toggle_template`("id", "created_at", "updated_at", "section_id", "title", "is_active", "toggle_type", "menu_id") SELECT "id", "created_at", "updated_at", "section_id", "title", "is_active", "toggle_type", "menu_id" FROM `toggle_template`;--> statement-breakpoint
DROP TABLE `toggle_template`;--> statement-breakpoint
ALTER TABLE `__new_toggle_template` RENAME TO `toggle_template`;--> statement-breakpoint
PRAGMA foreign_keys=ON;