{"version": "6", "dialect": "sqlite", "id": "4b6017a5-a584-4e8d-b38f-52df791d98ad", "prevId": "42ca9a94-0da4-443d-b769-9c780578c518", "tables": {"admin": {"name": "admin", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'admin'"}, "last_login": {"name": "last_login", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}}, "indexes": {"admin_username_unique": {"name": "admin_username_unique", "columns": ["username"], "isUnique": true}, "admin_email_unique": {"name": "admin_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "album": {"name": "album", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "album_files": {"name": "album_files", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "album_id": {"name": "album_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_id": {"name": "file_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"album_files_album_id_album_id_fk": {"name": "album_files_album_id_album_id_fk", "tableFrom": "album_files", "tableTo": "album", "columnsFrom": ["album_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "album_files_file_id_files_id_fk": {"name": "album_files_file_id_files_id_fk", "tableFrom": "album_files", "tableTo": "files", "columnsFrom": ["file_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "announcement": {"name": "announcement", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "link": {"name": "link", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "event_date": {"name": "event_date", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "scheduled_at": {"name": "scheduled_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "announcement_type": {"name": "announcement_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "announcement_files": {"name": "announcement_files", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "announcement_id": {"name": "announcement_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_id": {"name": "file_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"announcement_files_announcement_id_announcement_id_fk": {"name": "announcement_files_announcement_id_announcement_id_fk", "tableFrom": "announcement_files", "tableTo": "announcement", "columnsFrom": ["announcement_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "announcement_files_file_id_files_id_fk": {"name": "announcement_files_file_id_files_id_fk", "tableFrom": "announcement_files", "tableTo": "files", "columnsFrom": ["file_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "course": {"name": "course", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "specialization": {"name": "specialization", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "duration_in_months": {"name": "duration_in_months", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "semester_count": {"name": "semester_count", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "department_id": {"name": "department_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "image_id": {"name": "image_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "seats_count": {"name": "seats_count", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "syllabus_id": {"name": "syllabus_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "pos_id": {"name": "pos_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "link_button_id": {"name": "link_button_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"course_department_id_department_id_fk": {"name": "course_department_id_department_id_fk", "tableFrom": "course", "tableTo": "department", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "course_image_id_files_id_fk": {"name": "course_image_id_files_id_fk", "tableFrom": "course", "tableTo": "files", "columnsFrom": ["image_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "course_syllabus_id_files_id_fk": {"name": "course_syllabus_id_files_id_fk", "tableFrom": "course", "tableTo": "files", "columnsFrom": ["syllabus_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "course_pos_id_files_id_fk": {"name": "course_pos_id_files_id_fk", "tableFrom": "course", "tableTo": "files", "columnsFrom": ["pos_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "course_link_button_id_link_button_id_fk": {"name": "course_link_button_id_link_button_id_fk", "tableFrom": "course", "tableTo": "link_button", "columnsFrom": ["link_button_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "department": {"name": "department", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "overview": {"name": "overview", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "achievement_id": {"name": "achievement_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"department_slug_unique": {"name": "department_slug_unique", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {"department_achievement_id_accordion_id_fk": {"name": "department_achievement_id_accordion_id_fk", "tableFrom": "department", "tableTo": "accordion", "columnsFrom": ["achievement_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "department_event": {"name": "department_event", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "department_id": {"name": "department_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "event_id": {"name": "event_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {"department_event_department_id_department_id_fk": {"name": "department_event_department_id_department_id_fk", "tableFrom": "department_event", "tableTo": "department", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "department_event_event_id_announcement_id_fk": {"name": "department_event_event_id_announcement_id_fk", "tableFrom": "department_event", "tableTo": "announcement", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "department_files": {"name": "department_files", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "year": {"name": "year", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "course_id": {"name": "course_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "semester": {"name": "semester", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "department_id": {"name": "department_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_id": {"name": "file_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"department_files_course_id_course_id_fk": {"name": "department_files_course_id_course_id_fk", "tableFrom": "department_files", "tableTo": "course", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "department_files_department_id_department_id_fk": {"name": "department_files_department_id_department_id_fk", "tableFrom": "department_files", "tableTo": "department", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "department_files_file_id_files_id_fk": {"name": "department_files_file_id_files_id_fk", "tableFrom": "department_files", "tableTo": "files", "columnsFrom": ["file_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "department_gallery": {"name": "department_gallery", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "department_id": {"name": "department_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "gallery_id": {"name": "gallery_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {"department_gallery_department_id_department_id_fk": {"name": "department_gallery_department_id_department_id_fk", "tableFrom": "department_gallery", "tableTo": "department", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "dynamicTable": {"name": "dynamicTable", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"slug_idx": {"name": "slug_idx", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "dynamicTableData": {"name": "dynamicTableData", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "table_id": {"name": "table_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"dynamicTableData_table_id_dynamicTable_id_fk": {"name": "dynamicTableData_table_id_dynamicTable_id_fk", "tableFrom": "dynamicTableData", "tableTo": "dynamicTable", "columnsFrom": ["table_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "faculty": {"name": "faculty", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "department_id": {"name": "department_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "designation": {"name": "designation", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "image_id": {"name": "image_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "resume_id": {"name": "resume_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "education": {"name": "education", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'[]'"}, "experience": {"name": "experience", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'[]'"}, "area_of_interest": {"name": "area_of_interest", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'[]'"}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {"faculty_department_id_department_id_fk": {"name": "faculty_department_id_department_id_fk", "tableFrom": "faculty", "tableTo": "department", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "faculty_image_id_files_id_fk": {"name": "faculty_image_id_files_id_fk", "tableFrom": "faculty", "tableTo": "files", "columnsFrom": ["image_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "faculty_resume_id_files_id_fk": {"name": "faculty_resume_id_files_id_fk", "tableFrom": "faculty", "tableTo": "files", "columnsFrom": ["resume_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "files": {"name": "files", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "pathname": {"name": "pathname", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "pop_up": {"name": "pop_up", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "pathname": {"name": "pathname", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'Pop-Up Notification'"}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "prefix": {"name": "prefix", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pop-up'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "temp_files": {"name": "temp_files", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "pathname": {"name": "pathname", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "link_button": {"name": "link_button", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "card_template_id": {"name": "card_template_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "announcement_id": {"name": "announcement_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "style": {"name": "style", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "enabled": {"name": "enabled", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "external_link": {"name": "external_link", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "internal_link": {"name": "internal_link", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "new_tab": {"name": "new_tab", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"link_button_card_template_id_card_template_id_fk": {"name": "link_button_card_template_id_card_template_id_fk", "tableFrom": "link_button", "tableTo": "card_template", "columnsFrom": ["card_template_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "link_button_announcement_id_announcement_id_fk": {"name": "link_button_announcement_id_announcement_id_fk", "tableFrom": "link_button", "tableTo": "announcement", "columnsFrom": ["announcement_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "download_button": {"name": "download_button", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "card_template_id": {"name": "card_template_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "announcement_id": {"name": "announcement_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "style": {"name": "style", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "enabled": {"name": "enabled", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "new_tab": {"name": "new_tab", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"download_button_card_template_id_card_template_id_fk": {"name": "download_button_card_template_id_card_template_id_fk", "tableFrom": "download_button", "tableTo": "card_template", "columnsFrom": ["card_template_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "download_button_announcement_id_announcement_id_fk": {"name": "download_button_announcement_id_announcement_id_fk", "tableFrom": "download_button", "tableTo": "announcement", "columnsFrom": ["announcement_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "download_files": {"name": "download_files", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "download_button_id": {"name": "download_button_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_id": {"name": "file_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"download_files_download_button_id_download_button_id_fk": {"name": "download_files_download_button_id_download_button_id_fk", "tableFrom": "download_files", "tableTo": "download_button", "columnsFrom": ["download_button_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "download_files_file_id_files_id_fk": {"name": "download_files_file_id_files_id_fk", "tableFrom": "download_files", "tableTo": "files", "columnsFrom": ["file_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "menu": {"name": "menu", "columns": {"title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "menu_name": {"name": "menu_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "route_enabled": {"name": "route_enabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"menu_slug_unique": {"name": "menu_slug_unique", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {"menu_parent_id_menu_id_fk": {"name": "menu_parent_id_menu_id_fk", "tableFrom": "menu", "tableTo": "menu", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "section": {"name": "section", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "template_id": {"name": "template_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"section_template_id_template_id_fk": {"name": "section_template_id_template_id_fk", "tableFrom": "section", "tableTo": "template", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "accordion": {"name": "accordion", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "section_id": {"name": "section_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"accordion_section_id_section_id_fk": {"name": "accordion_section_id_section_id_fk", "tableFrom": "accordion", "tableTo": "section", "columnsFrom": ["section_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "card_template": {"name": "card_template", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "section_id": {"name": "section_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "layout": {"name": "layout", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"card_template_section_id_section_id_fk": {"name": "card_template_section_id_section_id_fk", "tableFrom": "card_template", "tableTo": "section", "columnsFrom": ["section_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "card_content": {"name": "card_content", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "card_id": {"name": "card_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"card_content_card_id_card_template_id_fk": {"name": "card_content_card_id_card_template_id_fk", "tableFrom": "card_content", "tableTo": "card_template", "columnsFrom": ["card_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "card_files": {"name": "card_files", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "card_id": {"name": "card_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_id": {"name": "file_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"card_files_card_id_card_template_id_fk": {"name": "card_files_card_id_card_template_id_fk", "tableFrom": "card_files", "tableTo": "card_template", "columnsFrom": ["card_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "card_files_file_id_files_id_fk": {"name": "card_files_file_id_files_id_fk", "tableFrom": "card_files", "tableTo": "files", "columnsFrom": ["file_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "card_list_template": {"name": "card_list_template", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "section_id": {"name": "section_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "max_cards": {"name": "max_cards", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "min_cards": {"name": "min_cards", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "layout": {"name": "layout", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"card_list_template_section_id_section_id_fk": {"name": "card_list_template_section_id_section_id_fk", "tableFrom": "card_list_template", "tableTo": "section", "columnsFrom": ["section_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "stats_template": {"name": "stats_template", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "section_id": {"name": "section_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"stats_template_section_id_section_id_fk": {"name": "stats_template_section_id_section_id_fk", "tableFrom": "stats_template", "tableTo": "section", "columnsFrom": ["section_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "toggle_template": {"name": "toggle_template", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "section_id": {"name": "section_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "toggle_type": {"name": "toggle_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "menu_id": {"name": "menu_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"toggle_template_section_id_section_id_fk": {"name": "toggle_template_section_id_section_id_fk", "tableFrom": "toggle_template", "tableTo": "section", "columnsFrom": ["section_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "template": {"name": "template", "columns": {"menu_id": {"name": "menu_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "show_menu_links": {"name": "show_menu_links", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"template_menu_id_idx": {"name": "template_menu_id_idx", "columns": ["menu_id"], "isUnique": false}}, "foreignKeys": {"template_menu_id_menu_id_fk": {"name": "template_menu_id_menu_id_fk", "tableFrom": "template", "tableTo": "menu", "columnsFrom": ["menu_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "student": {"name": "student", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "department_id": {"name": "department_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "course_id": {"name": "course_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "start_year": {"name": "start_year", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "pass_out_year": {"name": "pass_out_year", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "mark": {"name": "mark", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "placed_at": {"name": "placed_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "image_id": {"name": "image_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"student_department_id_department_id_fk": {"name": "student_department_id_department_id_fk", "tableFrom": "student", "tableTo": "department", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_course_id_course_id_fk": {"name": "student_course_id_course_id_fk", "tableFrom": "student", "tableTo": "course", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_image_id_files_id_fk": {"name": "student_image_id_files_id_fk", "tableFrom": "student", "tableTo": "files", "columnsFrom": ["image_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "quick_link ": {"name": "quick_link ", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "link": {"name": "link", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"quick_link _title_unique": {"name": "quick_link _title_unique", "columns": ["title"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}