CREATE TABLE `admin` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`username` text NOT NULL,
	`email` text NOT NULL,
	`password` text NOT NULL,
	`full_name` text NOT NULL,
	`role` text DEFAULT 'admin' NOT NULL,
	`last_login` integer,
	`is_active` integer DEFAULT true NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `admin_username_unique` ON `admin` (`username`);--> statement-breakpoint
CREATE UNIQUE INDEX `admin_email_unique` ON `admin` (`email`);--> statement-breakpoint
CREATE TABLE `album` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`title` text NOT NULL,
	`is_active` integer DEFAULT true NOT NULL
);
--> statement-breakpoint
CREATE TABLE `album_files` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`album_id` integer NOT NULL,
	`file_id` integer NOT NULL,
	FOREIGN KEY (`album_id`) REFERENCES `album`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`file_id`) REFERENCES `files`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `announcement` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`title` text NOT NULL,
	`link` text,
	`content` text NOT NULL,
	`event_date` integer,
	`scheduled_at` integer,
	`expires_at` integer,
	`is_active` integer NOT NULL,
	`priority` integer DEFAULT 0 NOT NULL,
	`announcement_type` text NOT NULL
);
--> statement-breakpoint
CREATE TABLE `announcement_files` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`announcement_id` integer NOT NULL,
	`file_id` integer NOT NULL,
	FOREIGN KEY (`announcement_id`) REFERENCES `announcement`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`file_id`) REFERENCES `files`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `course` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`name` text NOT NULL,
	`specialization` text,
	`duration_in_months` integer NOT NULL,
	`semester_count` integer NOT NULL,
	`department_id` integer NOT NULL,
	`image_id` integer,
	`title` text NOT NULL,
	`seats_count` integer NOT NULL,
	`content` text NOT NULL,
	`type` text NOT NULL,
	`syllabus_id` integer,
	`pos_id` integer,
	`link_button_id` integer,
	FOREIGN KEY (`department_id`) REFERENCES `department`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`image_id`) REFERENCES `files`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`syllabus_id`) REFERENCES `files`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`pos_id`) REFERENCES `files`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`link_button_id`) REFERENCES `link_button`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `department` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`name` text NOT NULL,
	`slug` text NOT NULL,
	`overview` text,
	`achievement_id` integer,
	FOREIGN KEY (`achievement_id`) REFERENCES `accordion`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE UNIQUE INDEX `department_slug_unique` ON `department` (`slug`);--> statement-breakpoint
CREATE TABLE `department_event` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`department_id` integer NOT NULL,
	`event_id` integer NOT NULL,
	`display_order` integer DEFAULT 0 NOT NULL,
	FOREIGN KEY (`department_id`) REFERENCES `department`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`event_id`) REFERENCES `announcement`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `department_files` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`title` text NOT NULL,
	`year` integer,
	`course_id` integer,
	`type` text NOT NULL,
	`semester` integer,
	`department_id` integer NOT NULL,
	`file_id` integer NOT NULL,
	FOREIGN KEY (`course_id`) REFERENCES `course`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`department_id`) REFERENCES `department`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`file_id`) REFERENCES `files`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `department_gallery` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`department_id` integer NOT NULL,
	`gallery_id` integer NOT NULL,
	`display_order` integer DEFAULT 0 NOT NULL,
	FOREIGN KEY (`department_id`) REFERENCES `department`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `dynamicTable` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`name` text NOT NULL,
	`type` text NOT NULL,
	`slug` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `slug_idx` ON `dynamicTable` (`slug`);--> statement-breakpoint
CREATE TABLE `dynamicTableData` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`table_id` integer NOT NULL,
	`data` text NOT NULL,
	FOREIGN KEY (`table_id`) REFERENCES `dynamicTable`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `faculty` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`name` text NOT NULL,
	`department_id` integer NOT NULL,
	`designation` text NOT NULL,
	`start_date` text NOT NULL,
	`end_date` text,
	`image_id` integer,
	`resume_id` integer,
	`education` text DEFAULT '[]' NOT NULL,
	`experience` text DEFAULT '[]' NOT NULL,
	`area_of_interest` text DEFAULT '[]' NOT NULL,
	FOREIGN KEY (`department_id`) REFERENCES `department`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`image_id`) REFERENCES `files`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`resume_id`) REFERENCES `files`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `files` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`pathname` text NOT NULL,
	`title` text,
	`type` text NOT NULL
);
--> statement-breakpoint
CREATE TABLE `pop_up` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`pathname` text NOT NULL,
	`title` text DEFAULT 'Pop-Up Notification' NOT NULL,
	`type` text NOT NULL,
	`prefix` text DEFAULT 'pop-up' NOT NULL
);
--> statement-breakpoint
CREATE TABLE `temp_files` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`pathname` text NOT NULL,
	`title` text,
	`type` text NOT NULL
);
--> statement-breakpoint
CREATE TABLE `link_button` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`card_template_id` integer,
	`announcement_id` integer,
	`title` text NOT NULL,
	`style` text NOT NULL,
	`icon` text,
	`enabled` integer,
	`external_link` text,
	`internal_link` text,
	`new_tab` integer,
	FOREIGN KEY (`card_template_id`) REFERENCES `card_template`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`announcement_id`) REFERENCES `announcement`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `download_button` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`card_template_id` integer,
	`announcement_id` integer,
	`title` text NOT NULL,
	`style` text NOT NULL,
	`icon` text,
	`enabled` integer,
	`new_tab` integer,
	FOREIGN KEY (`card_template_id`) REFERENCES `card_template`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`announcement_id`) REFERENCES `announcement`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `download_files` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`download_button_id` integer NOT NULL,
	`file_id` integer NOT NULL,
	FOREIGN KEY (`download_button_id`) REFERENCES `download_button`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`file_id`) REFERENCES `files`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `menu` (
	`title` text NOT NULL,
	`menu_name` text,
	`route_enabled` integer NOT NULL,
	`slug` text,
	`is_active` integer NOT NULL,
	`parent_id` integer,
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	FOREIGN KEY (`parent_id`) REFERENCES `menu`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX `menu_slug_unique` ON `menu` (`slug`);--> statement-breakpoint
CREATE TABLE `section` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`priority` integer NOT NULL,
	`template_id` integer,
	`type` text NOT NULL,
	FOREIGN KEY (`template_id`) REFERENCES `template`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `accordion` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`section_id` integer,
	`title` text NOT NULL,
	`description` text,
	`content` text,
	FOREIGN KEY (`section_id`) REFERENCES `section`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `card_template` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`section_id` integer,
	`layout` text NOT NULL,
	`title` text,
	`key` text NOT NULL,
	FOREIGN KEY (`section_id`) REFERENCES `section`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `card_content` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`card_id` integer NOT NULL,
	`priority` integer NOT NULL,
	`title` text NOT NULL,
	`content` text NOT NULL,
	FOREIGN KEY (`card_id`) REFERENCES `card_template`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `card_files` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`card_id` integer NOT NULL,
	`file_id` integer NOT NULL,
	FOREIGN KEY (`card_id`) REFERENCES `card_template`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`file_id`) REFERENCES `files`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `card_list_template` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`section_id` integer,
	`title` text NOT NULL,
	`description` text,
	`max_cards` integer NOT NULL,
	`min_cards` integer NOT NULL,
	`layout` text NOT NULL,
	FOREIGN KEY (`section_id`) REFERENCES `section`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `stats_template` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`section_id` integer,
	`title` text NOT NULL,
	`value` integer NOT NULL,
	`icon` text NOT NULL,
	FOREIGN KEY (`section_id`) REFERENCES `section`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `toggle_template` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`section_id` integer,
	`title` text NOT NULL,
	`is_active` integer NOT NULL,
	`toggle_type` text NOT NULL,
	`menu_id` integer,
	FOREIGN KEY (`section_id`) REFERENCES `section`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `template` (
	`menu_id` integer,
	`show_menu_links` integer NOT NULL,
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	FOREIGN KEY (`menu_id`) REFERENCES `menu`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE INDEX `template_menu_id_idx` ON `template` (`menu_id`);--> statement-breakpoint
CREATE TABLE `student` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`name` text NOT NULL,
	`department_id` integer NOT NULL,
	`type` text NOT NULL,
	`course_id` integer NOT NULL,
	`start_year` integer NOT NULL,
	`pass_out_year` integer,
	`mark` integer,
	`placed_at` text,
	`image_id` integer,
	FOREIGN KEY (`department_id`) REFERENCES `department`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`course_id`) REFERENCES `course`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`image_id`) REFERENCES `files`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `quick_link ` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	`title` text NOT NULL,
	`link` text,
	`icon` text,
	`type` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `quick_link _title_unique` ON `quick_link ` (`title`);