import type { Files } from './files';
import { relations } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { baseTable } from './base';
import { files } from './files';

export const album = sqliteTable(
  'album',
  {
    ...baseTable,
    title: text('title').notNull(),
    isActive: integer('is_active', { mode: 'boolean' }).notNull().default(true),
  },
  () => []
);

export const albumFiles = sqliteTable(
  'album_files',
  {
    ...baseTable,
    albumId: integer('album_id')
      .notNull()
      .references(() => album.id, { onDelete: 'cascade' }),
    fileId: integer('file_id')
      .notNull()
      .references(() => files.id, { onDelete: 'cascade' }),
  },
  () => []
);

export const albumFilesRelations = relations(albumFiles, ({ one }) => ({
  album: one(album, {
    fields: [albumFiles.albumId],
    references: [album.id],
  }),
  file: one(files, {
    fields: [albumFiles.fileId],
    references: [files.id],
  }),
}));

export type AlbumFiles = typeof albumFiles.$inferSelect;

export const albumRelations = relations(album, ({ many }) => ({
  files: many(albumFiles),
}));

export type Album = typeof album.$inferSelect;
export type AlbumWithRelations = typeof album.$inferSelect & {
  files: Files[];
};
