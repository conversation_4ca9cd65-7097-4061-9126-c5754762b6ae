import { relations } from 'drizzle-orm';
import { integer, text, sqliteTable } from 'drizzle-orm/sqlite-core';
import { files } from './files';
import { department } from './department';
import { linkButton } from './buttons/linkButton';
import { baseTable } from './base';
import { COURSE_TYPES } from './enums';
export const course = sqliteTable('course', {
  ...baseTable,
  name: text('name').notNull(),
  specialization: text('specialization'),
  durationInMonths: integer('duration_in_months').notNull(),
  semesterCount: integer('semester_count').notNull(),
  departmentId: integer('department_id')
    .references(() => department.id)
    .notNull(),
  imageId: integer('image_id').references(() => files.id),
  title: text('title').notNull(),
  seatsCount: integer('seats_count').notNull(),
  content: text('content').notNull(),
  type: text('type', {
    enum: COURSE_TYPES,
  }).notNull(),
  syllabusId: integer('syllabus_id').references(() => files.id, {
    onDelete: 'cascade',
  }),
  posId: integer('pos_id').references(() => files.id, {
    onDelete: 'cascade',
  }),
  linkButtonId: integer('link_button_id').references(() => linkButton.id, {
    onDelete: 'cascade',
  }),
});

export const courseRelations = relations(course, ({ one }) => ({
  department: one(department, {
    fields: [course.departmentId],
    references: [department.id],
  }),
  syllabus: one(files, {
    fields: [course.syllabusId],
    references: [files.id],
  }),
  pos: one(files, {
    fields: [course.posId],
    references: [files.id],
  }),
  image: one(files, {
    fields: [course.imageId],
    references: [files.id],
  }),
  linkButton: one(linkButton, {
    fields: [course.linkButtonId],
    references: [linkButton.id],
  }),
}));
