import type { Files } from './files';
import { relations } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { baseTable } from './base';
import { linkButton, type LinkButton } from './buttons/linkButton';
import { ANNOUNCEMENT_TYPES } from './enums';
import { files } from './files';

export const announcement = sqliteTable(
  'announcement',
  {
    ...baseTable,
    title: text('title').notNull(),
    link: text('link'),
    content: text('content').notNull(),
    eventDate: integer('event_date', { mode: 'number' }),
    scheduledAt: integer('scheduled_at', { mode: 'number' }).$default(() =>
      Date.now()
    ),
    expiresAt: integer('expires_at', { mode: 'number' }).$default(
      () => Date.now() + 1000 * 60 * 60 * 24 * 7
    ),
    isActive: integer('is_active', { mode: 'boolean' }).notNull(),
    priority: integer('priority').notNull().default(0),
    announcementType: text('announcement_type', {
      enum: ANNOUNCEMENT_TYPES,
    }).notNull(),
  },
  () => []
);

export const announcementFiles = sqliteTable(
  'announcement_files',
  {
    ...baseTable,
    announcementId: integer('announcement_id')
      .notNull()
      .references(() => announcement.id, { onDelete: 'cascade' }),
    fileId: integer('file_id')
      .notNull()
      .references(() => files.id, { onDelete: 'cascade' }),
  },
  () => []
);

export const announcementFilesRelations = relations(
  announcementFiles,
  ({ one }) => ({
    announcement: one(announcement, {
      fields: [announcementFiles.announcementId],
      references: [announcement.id],
    }),
    file: one(files, {
      fields: [announcementFiles.fileId],
      references: [files.id],
    }),
  })
);

export type AnnouncementFiles = typeof announcementFiles.$inferSelect;

export const announcementRelations = relations(
  announcement,
  ({ many, one }) => ({
    files: many(announcementFiles),
    button: one(linkButton, {
      fields: [announcement.id],
      references: [linkButton.announcementId],
    }),
  })
);

export type Announcement = typeof announcement.$inferSelect;
export type AnnouncementFileWithRelation = AnnouncementFiles & {
  file: Files;
};
export type AnnouncementWithRelations = typeof announcement.$inferSelect & {
  button: LinkButton;
  //Combine AnnouncementFiles and Files
  files: AnnouncementFileWithRelation[];
};
