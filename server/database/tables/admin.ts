import { relations } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { baseTable } from './base';

export const ADMIN_ROLES = ['admin', 'viewer'] as const;

export const admin = sqliteTable(
  'admin',
  {
    ...baseTable,
    username: text('username').notNull().unique(),
    email: text('email').notNull().unique(),
    password: text('password').notNull(),
    fullName: text('full_name').notNull(),
    role: text('role', {
      enum: ADMIN_ROLES,
    })
      .notNull()
      .default('admin'),
    lastLogin: integer('last_login', { mode: 'number' }),
    isActive: integer('is_active', { mode: 'boolean' }).notNull().default(true),
  },
  () => []
);

export const adminRelations = relations(admin, () => ({
  // Add relations if needed in the future
}));

export type Admin = typeof admin.$inferSelect;
export type NewAdmin = typeof admin.$inferInsert;
