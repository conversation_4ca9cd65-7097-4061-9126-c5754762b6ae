import { relations } from 'drizzle-orm';
import { sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { announcementFiles } from './announcement';
import { baseTable } from './base';
import { albumFiles } from './album';
import { cardFiles } from './sections';

// create a table to store the temp files
export const tempFiles = sqliteTable(
  'temp_files',
  {
    ...baseTable,
    pathname: text('pathname').notNull(),
    title: text('title'),
    type: text('type').notNull(),
  },
  () => []
);

export const files = sqliteTable(
  'files',
  {
    ...baseTable,
    pathname: text('pathname').notNull(),
    title: text('title'),
    type: text('type').notNull(),
  },
  () => []
);

export const popUp = sqliteTable(
  'pop_up',
  {
    ...baseTable,
    pathname: text('pathname').notNull(),
    title: text('title').notNull().default('Pop-Up Notification'),
    type: text('type').notNull(),
    prefix: text('prefix').notNull().default('pop-up'),
  },
  () => []
);

export const filesRelations = relations(files, ({ many, one }) => ({
  announcementFiles: many(announcementFiles),
  albumFiles: many(albumFiles),
  cardFile: one(cardFiles),
}));

export type Files = typeof files.$inferSelect;
