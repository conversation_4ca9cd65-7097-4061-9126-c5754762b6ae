import type { AnySQLiteColumn } from 'drizzle-orm/sqlite-core';
import type { Section } from './sections';
import { relations } from 'drizzle-orm';
import { index, integer, sqliteTable } from 'drizzle-orm/sqlite-core';
import { baseTable } from './base';
import { menu } from './menu';
import { section } from './sections';

export const template = sqliteTable(
  'template',
  {
    menuId: integer('menu_id').references((): AnySQLiteColumn => menu.id, {
      onDelete: 'cascade',
    }),
    showMenuLinks: integer('show_menu_links', { mode: 'boolean' }).notNull(),
    ...baseTable,
  },
  (t) => [index('template_menu_id_idx').on(t.menuId)]
);

export const templateRelations = relations(template, ({ one, many }) => ({
  menu: one(menu, {
    fields: [template.menuId],
    references: [menu.id],
  }),
  sections: many(section),
}));

export type Template = typeof template.$inferSelect;
export type TemplateWithRelations = Template & {
  sections: Section[];
};
