import type { AnySQLiteColumn } from 'drizzle-orm/sqlite-core';
import { relations } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { baseTable } from '../base';
import { section } from './index';

export const stats = sqliteTable(
  'stats_template',
  {
    ...baseTable,
    sectionId: integer('section_id').references(
      (): AnySQLiteColumn => section.id,
      { onDelete: 'cascade' }
    ),
    title: text('title').notNull(),
    value: integer('value').notNull(),
    icon: text('icon').notNull(),
  },
  () => []
);

export const statsRelations = relations(stats, ({ one }) => ({
  section: one(section, {
    fields: [stats.sectionId],
    references: [section.id],
  }),
}));

export type Stats = typeof stats.$inferSelect;
