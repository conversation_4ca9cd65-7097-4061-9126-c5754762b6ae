import type { AnySQLiteColumn } from 'drizzle-orm/sqlite-core';
import type { LinkButton } from '../buttons/linkButton';
import { relations } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { baseTable } from '../base';
import { linkButton } from '../buttons/linkButton';
import { CARD_LAYOUTS } from '../enums';
import { section } from './index';
import { files } from '../files';
import type { DownloadButton } from '../buttons/downloadButton';
import { downloadButton } from '../buttons/downloadButton';
import type { Files } from '../files';

export const card = sqliteTable(
  'card_template',
  {
    ...baseTable,
    sectionId: integer('section_id').references(
      (): AnySQLiteColumn => section.id,
      { onDelete: 'cascade' }
    ),
    layout: text('layout', {
      enum: CARD_LAYOUTS,
    }).notNull(),
    title: text('title'),
    key: text('key').notNull(),
  },
  () => []
);

export const cardContent = sqliteTable(
  'card_content',
  {
    ...baseTable,
    cardId: integer('card_id')
      .notNull()
      .references((): AnySQLiteColumn => card.id, { onDelete: 'cascade' }),
    priority: integer('priority').notNull(),
    title: text('title').notNull(),
    content: text('content').notNull(),
  },
  () => []
);

export const cardRelations = relations(card, ({ one, many }) => ({
  linkButton: one(linkButton),
  downloadButton: one(downloadButton),
  section: one(section, {
    fields: [card.sectionId],
    references: [section.id],
  }),
  cardFiles: many(cardFiles),
  contents: many(cardContent),
}));

export const cardContentRelations = relations(cardContent, ({ one }) => ({
  card: one(card, {
    fields: [cardContent.cardId],
    references: [card.id],
  }),
}));

export const cardFiles = sqliteTable(
  'card_files',
  {
    ...baseTable,
    cardId: integer('card_id')
      .notNull()
      .references((): AnySQLiteColumn => card.id, { onDelete: 'cascade' }),
    fileId: integer('file_id')
      .notNull()
      .references((): AnySQLiteColumn => files.id, { onDelete: 'cascade' }),
  },
  () => []
);

export const cardFilesRelations = relations(cardFiles, ({ one }) => ({
  card: one(card, {
    fields: [cardFiles.cardId],
    references: [card.id],
  }),
  file: one(files, {
    fields: [cardFiles.fileId],
    references: [files.id],
  }),
}));

export type CardFiles = typeof cardFiles.$inferSelect;

export type Card = typeof card.$inferSelect;
export type CardContent = typeof cardContent.$inferSelect;
export type CardWithRelations = Card & {
  contents: CardContent[];
  linkButton: LinkButton;
  downloadButton: DownloadButton;
  cardFiles: Files[];
};
