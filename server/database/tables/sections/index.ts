import type { AnySQLiteColumn } from 'drizzle-orm/sqlite-core';
import type { TemplateWithRelations } from '../template';
import type { Accordion } from './accordion';
import type { CardListWithRelations } from './cardList';
import { relations } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { baseTable } from '../base';
import { SECTION_TYPES } from '../enums';
import { template } from '../template';
import { accordion } from './accordion';
import { cardList } from './cardList';
import type { Stats } from './stats';
import type { Toggle } from './toggle';
import type { Card } from './card';
import { card } from './card';

export * from './accordion';
export * from './card';
export * from './cardList';
export * from './stats';
export * from './toggle';

export const section = sqliteTable(
  'section',
  {
    ...baseTable,
    priority: integer('priority').notNull(),
    templateId: integer('template_id').references(
      (): AnySQLiteColumn => template.id
    ),
    type: text('type', {
      enum: SECTION_TYPES,
    }).notNull(),
  },
  () => []
);

export const sectionRelations = relations(section, ({ one }) => ({
  template: one(template, {
    fields: [section.templateId],
    references: [template.id],
  }),
  cardListConfig: one(cardList, {
    fields: [section.id],
    references: [cardList.sectionId],
  }),
  cardConfig: one(card, {
    fields: [section.id],
    references: [card.sectionId],
  }),
  accordionConfig: one(accordion, {
    fields: [section.id],
    references: [accordion.sectionId],
  }),
}));

export type Section = typeof section.$inferSelect;

export type SectionWithRelations = typeof section.$inferSelect & {
  template: TemplateWithRelations;
  cardListConfig: CardListWithRelations;
  accordionConfig: Accordion;
  statsConfig: Stats;
  toggleConfig: Toggle;
  cardConfig: Card;
};
