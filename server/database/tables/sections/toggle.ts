import type { AnySQLiteColumn } from 'drizzle-orm/sqlite-core';
import { relations } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { baseTable } from '../base';
import { section } from './index';
import { TOGGLE_TYPES } from '../enums';
import { menu } from '../menu';

export const toggle = sqliteTable(
  'toggle_template',
  {
    ...baseTable,
    sectionId: integer('section_id').references(
      (): AnySQLiteColumn => section.id,
      { onDelete: 'cascade' }
    ),
    title: text('title').notNull(),
    isActive: integer('is_active', { mode: 'boolean' }).notNull(),
    type: text('toggle_type', {
      enum: TOGGLE_TYPES,
    }).notNull(),
    menuId: integer('menu_id').references(
      (): AnySQLiteColumn => menu.id,
      { onDelete: 'cascade' }
    ),
  },
  () => []
);

export const toggleRelations = relations(toggle, ({ one }) => ({
  section: one(section, {
    fields: [toggle.sectionId],
    references: [section.id],
  }),
  menu: one(menu, {
    fields: [toggle.menuId],
    references: [menu.id],
  }),
}));

export type Toggle = typeof toggle.$inferSelect;
