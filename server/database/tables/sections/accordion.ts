import type { AnySQLiteColumn } from 'drizzle-orm/sqlite-core';
import { relations } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { baseTable } from '../base';
import { section } from './index';
import { linkButton } from '../buttons/linkButton';
import { downloadButton } from '../buttons/downloadButton';
import { card } from './card';
import { cardList } from './cardList';

export const accordion = sqliteTable(
  'accordion',
  {
    ...baseTable,
    sectionId: integer('section_id').references(
      (): AnySQLiteColumn => section.id,
      { onDelete: 'cascade' }
    ),
    title: text('title').notNull(),
    description: text('description'),
    content: text('content'),
  },
  () => []
);

export const accordionRelations = relations(accordion, ({ one }) => ({
  section: one(section, {
    fields: [accordion.sectionId],
    references: [section.id],
  }),
  linkButtonConfig: one(linkButton),
  downloadButtonConfig: one(downloadButton),
  cardConfig: one(card),
  cardListConfig: one(cardList),
}));

export type Accordion = typeof accordion.$inferSelect;
