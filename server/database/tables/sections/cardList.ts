import type { AnySQLiteColumn } from 'drizzle-orm/sqlite-core';
import type { CardWithRelations } from './index';
import { relations } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { baseTable } from '../base';
import { CARD_LAYOUTS } from '../enums';
import { card, section } from './index';

// Card list template (for managing multiple cards)
export const cardList = sqliteTable(
  'card_list_template',
  {
    ...baseTable,
    sectionId: integer('section_id').references(
      (): AnySQLiteColumn => section.id,
      { onDelete: 'cascade' }
    ),
    title: text('title').notNull(),
    description: text('description'),
    maxCards: integer('max_cards').notNull(),
    minCards: integer('min_cards').notNull(),
    layout: text('layout', {
      enum: CARD_LAYOUTS,
    }).notNull(),
  },
  () => []
);

// Card list template relations
export const cardListRelations = relations(cardList, ({ one, many }) => ({
  section: one(section, {
    fields: [cardList.sectionId],
    references: [section.id],
  }),
  cards: many(card),
}));

export type CardListTemplate = typeof cardList.$inferSelect;
export type CardListWithRelations = typeof cardList.$inferSelect & {
  cards: CardWithRelations[];
};
