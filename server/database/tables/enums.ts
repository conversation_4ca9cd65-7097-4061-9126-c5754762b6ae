export const SECTION_TYPES = [
  'card',
  'cardList',
  'accordion',
  'stats',
  'toggle',
] as const;
export type SectionType = (typeof SECTION_TYPES)[number];

export const TOGGLE_TYPES = ['menu', 'event', 'gallery'] as const;
export type ToggleType = (typeof TOGGLE_TYPES)[number];

export const BUTTON_STYLES = ['primary', 'secondary', 'outline'] as const;
export type ButtonStyle = (typeof BUTTON_STYLES)[number];

export const CARD_LAYOUTS = ['profile', 'standard', 'vision'] as const;
export type CardLayout = (typeof CARD_LAYOUTS)[number];

export const QUICK_LINK_TYPES = ['eService', 'quickLink'] as const;
export type QuickLinkType = (typeof QUICK_LINK_TYPES)[number];

export const ONCLICK_ACTIONS = ['none', 'openModal', 'navigate'] as const;
export type OnClickAction = (typeof ONCLICK_ACTIONS)[number];

export const ANNOUNCEMENT_TYPES = ['notice', 'update', 'event'] as const;
export type AnnouncementType = (typeof ANNOUNCEMENT_TYPES)[number];

export const COURSE_TYPES = ['ug', 'pg', 'add_on'] as const;
export type CourseType = (typeof COURSE_TYPES)[number];
export const STUDENT_TYPES = ['placement', 'topper', 'regular'] as const;
export type StudentType = (typeof STUDENT_TYPES)[number];

export const MODAL_TYPES = ['member', 'standard'] as const;
export type ModalType = (typeof MODAL_TYPES)[number];

export const BUTTON_TYPES = ['link', 'download'] as const;
export type ButtonType = (typeof BUTTON_TYPES)[number];

export const ROUTE_TYPES = ['internal', 'external'] as const;
export type RouteType = (typeof ROUTE_TYPES)[number];

export const TABLE_DATA_TYPES = [
  'text',
  'number',
  'boolean',
  'date',
  'datetime',
  'email',
  'url',
  'image',
  'file',
  'pdf',
  'json',
  'array',
  'object',
  'richText',
  'phone',
  'currency',
  'percentage',
  'color',
  'rating',
  'select',
  'multiSelect',
] as const;
export type TableDataType = (typeof TABLE_DATA_TYPES)[number];
