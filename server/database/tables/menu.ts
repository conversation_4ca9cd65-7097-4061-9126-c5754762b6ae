import type { AnySQLiteColumn } from 'drizzle-orm/sqlite-core';

import type { TemplateWithRelations } from './template';
import { relations } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { baseTable } from './base';
import { template } from './template';

export const menu = sqliteTable(
  'menu',
  {
    title: text('title').notNull(),
    menuName: text('menu_name'),
    routeEnabled: integer('route_enabled', { mode: 'boolean' }).notNull(),
    slug: text('slug').unique(),
    isActive: integer('is_active', { mode: 'boolean' }).notNull(),
    parentId: integer('parent_id').references((): AnySQLiteColumn => menu.id, {
      onDelete: 'cascade',
    }),
    priority: integer('priority').default(0),
    ...baseTable,
  },
  () => []
);

// Define relations
export const menuRelations = relations(menu, ({ one, many }) => ({
  parentMenu: one(menu, {
    fields: [menu.parentId],
    references: [menu.id],
    relationName: 'parent_child',
  }),
  submenus: many(menu, {
    relationName: 'parent_child',
  }),
  template: one(template),
}));

export type Menu = typeof menu.$inferInsert;
export type MenuWithRelations = typeof menu.$inferSelect & {
  template: TemplateWithRelations;
  parentMenu: MenuWithRelations;
  submenus: MenuWithRelations[];
};
