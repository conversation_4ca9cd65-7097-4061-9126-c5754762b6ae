import { relations } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { announcement } from '../announcement';
import { card } from '../sections/card';
import { buttonBaseTable } from './buttonBase';

export const linkButton = sqliteTable(
  'link_button',
  {
    ...buttonBaseTable,
    externalLink: text('external_link'),
    internalLink: text('internal_link'),
    newTab: integer('new_tab', { mode: 'boolean' }).$default(() => true),
  },
  () => []
);

export const linkButtonRelations = relations(linkButton, ({ one }) => ({
  cardTemplate: one(card, {
    fields: [linkButton.cardTemplateId],
    references: [card.id],
  }),
  announcement: one(announcement, {
    fields: [linkButton.announcementId],
    references: [announcement.id],
  }),
}));

export type LinkButton = typeof linkButton.$inferSelect;
