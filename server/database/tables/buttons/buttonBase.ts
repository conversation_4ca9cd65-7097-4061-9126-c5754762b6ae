import { integer, text } from 'drizzle-orm/sqlite-core';
import { baseTable } from '../base';
import { announcement } from '../announcement';
import { BUTTON_STYLES } from '../enums';
import { card } from '../sections';

// Base table type with common fields
export const buttonBaseTable = {
  ...baseTable,
  cardTemplateId: integer('card_template_id').references(() => card.id, {
    onDelete: 'cascade',
  }),
  announcementId: integer('announcement_id').references(() => announcement.id, {
    onDelete: 'cascade',
  }),
  title: text('title').notNull(),
  style: text('style', {
    enum: BUTTON_STYLES,
  }).notNull(),
  icon: text('icon'),
  enabled: integer('enabled', { mode: 'boolean' }).$default(() => true),
};
