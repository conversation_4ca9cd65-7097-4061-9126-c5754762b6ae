import { relations } from 'drizzle-orm';
import { integer, sqliteTable } from 'drizzle-orm/sqlite-core';
import { announcement } from '../announcement';
import { baseTable } from '../base';
import { card } from '../sections/card';
import type { Files } from '../files';
import { files } from '../files';
import { buttonBaseTable } from './buttonBase';

export const downloadButton = sqliteTable(
  'download_button',
  {
    ...buttonBaseTable,
    newTab: integer('new_tab', { mode: 'boolean' }).$default(() => true),
  },
  () => []
);

export const downloadFiles = sqliteTable(
  'download_files',
  {
    ...baseTable,
    downloadButtonId: integer('download_button_id')
      .notNull()
      .references(() => downloadButton.id, { onDelete: 'cascade' }),
    fileId: integer('file_id')
      .notNull()
      .references(() => files.id, { onDelete: 'cascade' }),
  },
  () => []
);

export const downloadFilesRelations = relations(downloadFiles, ({ one }) => ({
  downloadButton: one(downloadButton, {
    fields: [downloadFiles.downloadButtonId],
    references: [downloadButton.id],
  }),
  file: one(files, {
    fields: [downloadFiles.fileId],
    references: [files.id],
  }),
}));

export type DownloadButtonFiles = typeof downloadFiles.$inferSelect;

export const downloadButtonRelations = relations(downloadButton, ({ one }) => ({
  cardTemplate: one(card, {
    fields: [downloadButton.cardTemplateId],
    references: [card.id],
  }),
  announcement: one(announcement, {
    fields: [downloadButton.announcementId],
    references: [announcement.id],
  }),
}));

export type DownloadButton = typeof downloadButton.$inferSelect;
export type DownloadButtonFileWithRelation = DownloadButtonFiles & {
  file: Files;
};
export type DownloadButtonWithRelations = typeof downloadButton.$inferSelect & {
  downloadButton: DownloadButton;
  //Combine DownloadButtonFiles and Files
  files: DownloadButtonFileWithRelation[];
};
