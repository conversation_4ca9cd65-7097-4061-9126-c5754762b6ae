import { text, sqliteTable, integer, unique } from 'drizzle-orm/sqlite-core';
import type { DynamicTableDataTypes } from './dynamic-types';
import { DYNAMIC_TABLE_TYPES } from './dynamic-types';
import { relations } from 'drizzle-orm';
import { baseTable } from './base';
// Define the tables table
export const dynamicTable = sqliteTable(
  'dynamicTable',
  {
    ...baseTable,
    name: text('name').notNull(),
    type: text('type', { enum: DYNAMIC_TABLE_TYPES }).notNull(),
    slug: text('slug').notNull(),
  },
  (table) => [unique('slug_idx').on(table.slug)]
);

// Define the tableData table with foreign key relationship
export const dynamicTableData = sqliteTable('dynamicTableData', {
  ...baseTable,
  tableId: integer('table_id')
    .notNull()
    .references(() => dynamicTable.id),
  data: text('data', { mode: 'json' }).$type<DynamicTableDataTypes>().notNull(),
});

export const dynamicTableRelations = relations(dynamicTable, ({ many }) => ({
  data: many(dynamicTableData),
}));

export const dynamicTableDataRelations = relations(
  dynamicTableData,
  ({ one }) => ({
    table: one(dynamicTable, {
      fields: [dynamicTableData.tableId],
      references: [dynamicTable.id],
    }),
  })
);

export type DynamicTable = typeof dynamicTable.$inferSelect;
export type DynamicTableData = typeof dynamicTableData.$inferSelect;
