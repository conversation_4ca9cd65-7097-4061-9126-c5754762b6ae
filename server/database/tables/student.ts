import { relations } from 'drizzle-orm';
import { integer, text, sqliteTable } from 'drizzle-orm/sqlite-core';
import { department } from './department';
import { course } from './course';
import { files } from './files';
import { baseTable } from './base';
import { STUDENT_TYPES } from './enums';

export const student = sqliteTable(
  'student',
  {
    ...baseTable,
    name: text('name').notNull(),
    departmentId: integer('department_id')
      .references(() => department.id)
      .notNull(),
    type: text('type', {
      enum: STUDENT_TYPES,
    }).notNull(),
    courseId: integer('course_id')
      .references(() => course.id)
      .notNull(),
    startYear: integer('start_year').notNull(),
    passOutYear: integer('pass_out_year'),
    mark: integer('mark'),
    placedAt: text('placed_at'),
    imageId: integer('image_id').references(() => files.id),
  },
  () => []
);

export const studentRelations = relations(student, ({ one }) => ({
  department: one(department, {
    fields: [student.departmentId],
    references: [department.id],
  }),
  course: one(course, {
    fields: [student.courseId],
    references: [course.id],
  }),
  image: one(files, {
    fields: [student.imageId],
    references: [files.id],
  }),
}));
