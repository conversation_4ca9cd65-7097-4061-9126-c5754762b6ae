import type { LinkButtonConfig } from '~/types/admin';
import type { FileData } from '~/types/home';

//NOTE Type enum for the tables
export const DYNAMIC_TABLE_TYPES = [
  'iqac_team_titles',
  'iqac_team_members',
  'iqac_overview',
  'iqac_downloads',
  'iqac_attachments',
  'club_and_committee',
  'admissions',
  'file_uploads',
] as const;
export type DynamicTableType = (typeof DYNAMIC_TABLE_TYPES)[number];

export type IQAC_TEAM_TITLES = {
  title: string;
  priority: number;
};

function isIQAC_TEAM_TITLES(data: any): data is IQAC_TEAM_TITLES {
  return (
    data && typeof data.title === 'string' && typeof data.priority === 'number'
  );
}

export type IQAC_TEAM_MEMBERS = {
  title: string;
  titleOrder: number;
  name: string;
  designation: string;
  priority: number;
};

function isIQAC_TEAM_MEMBERS(data: any): data is IQAC_TEAM_MEMBERS {
  return (
    data &&
    typeof data.title === 'string' &&
    typeof data.titleOrder === 'number' &&
    typeof data.name === 'string' &&
    typeof data.designation === 'string' &&
    typeof data.priority === 'number'
  );
}

export const IQAC_OVERVIEW_SLUGS = [
  'about',
  'objectives',
  'institution ',
] as const;
export type IQAC_OVERVIEW_SLUGS = (typeof IQAC_OVERVIEW_SLUGS)[number];
export type IQAC_OVERVIEW = {
  slug: IQAC_OVERVIEW_SLUGS;
  title: string;
  image: FileData | null;
  content: string;
  linkButton: LinkButtonConfig;
  files: FileData[];
};
function isIQAC_OVERVIEW(data: any): data is IQAC_OVERVIEW {
  return (
    data &&
    typeof data.slug === 'string' &&
    IQAC_OVERVIEW_SLUGS.includes(data.slug as IQAC_OVERVIEW_SLUGS) &&
    typeof data.title === 'string' &&
    typeof data.image === 'object' &&
    typeof data.content === 'string' &&
    typeof data.linkButton === 'object' &&
    Array.isArray(data.files) &&
    data.files.every((file: any) => typeof file === 'object')
  );
}

export const IQAC_DOWNLOAD_SLUGS = [
  'mou',
  'self_study_report',
  'aqar',
  'quality_audits',
  'policy_documents',
  'saac',
  'sss',
  'team_members_attachments',
] as const;
export type IQAC_DOWNLOAD_SLUGS = (typeof IQAC_DOWNLOAD_SLUGS)[number];

export type IQAC_DOWNLOAD = {
  slug: IQAC_DOWNLOAD_SLUGS;
  title: string;
  file: FileData;
};

function isIQAC_DOWNLOAD(data: any): data is IQAC_DOWNLOAD {
  return (
    data &&
    typeof data.slug === 'string' &&
    IQAC_DOWNLOAD_SLUGS.includes(data.slug as IQAC_DOWNLOAD_SLUGS) &&
    typeof data.title === 'string' &&
    typeof data.file === 'object'
  );
}

export type FILE_UPLOADS = {
  title: string;
  type: string;
  file: FileData;
};

export function isFILE_UPLOADS(data: any): data is FILE_UPLOADS {
  return (
    data &&
    typeof data.title === 'string' &&
    typeof data.type === 'string' &&
    typeof data.file === 'object'
  );
}

export const IQAC_ATTACHMENT_SLUGS = [
  'best_practice',
  'feedback',
  'reports',
  'minutes',
  'nirf',
] as const;
export type IQAC_ATTACHMENT_SLUGS = (typeof IQAC_ATTACHMENT_SLUGS)[number];

export type IQAC_ATTACHMENT = {
  slug: IQAC_ATTACHMENT_SLUGS;
  title: string;
  linkButton: LinkButtonConfig;
  files: FileData[];
};

function isIQAC_ATTACHMENT(data: any): data is IQAC_ATTACHMENT {
  return (
    data &&
    typeof data.slug === 'string' &&
    IQAC_ATTACHMENT_SLUGS.includes(data.slug as IQAC_ATTACHMENT_SLUGS)
  );
}

export const CLUB_COMMITTEE_SLUGS = [
  'sports_club',
  'cultural_committee',
  'science_club',
  'literary_committee',
] as const;
export type CLUB_COMMITTEE_SLUGS = (typeof CLUB_COMMITTEE_SLUGS)[number];

export type CLUB_COMMITTEE = {
  slug: string;
  title: string;
  overview: string;
  image: FileData | null;
  type: 'club' | 'committee';
  attachments: FileData[];
  linkButton: LinkButtonConfig;
  galleries: number[];
  events: number[];
};

function isCLUB_COMMITTEE(data: any): data is CLUB_COMMITTEE {
  return (
    data &&
    // typeof data.slug === 'string' &&
    // CLUB_COMMITTEE_SLUGS.includes(data.slug as CLUB_COMMITTEE_SLUGS) &&
    typeof data.title === 'string' &&
    typeof data.overview === 'string' &&
    typeof data.image === 'object' &&
    typeof data.type === 'string' &&
    Array.isArray(data.attachments) &&
    data.attachments.every((file: any) => typeof file === 'object') &&
    typeof data.linkButton === 'object' &&
    Array.isArray(data.galleries) &&
    data.galleries.every((gallery: any) => typeof gallery === 'number') &&
    Array.isArray(data.events) &&
    data.events.every((event: any) => typeof event === 'number')
  );
}

export const ADMISSIONS_SLUGS = ['admissions'] as const;
export type ADMISSIONS_SLUGS = (typeof ADMISSIONS_SLUGS)[number];
export type ADMISSIONS = {
  slug: string;
  title: string;
  image: FileData | null;
  content: {
    title: string;
    content: string;
    linkButton: LinkButtonConfig | null;
    file: FileData | null;
  }[];
};

function isADMISSIONS(data: any): data is ADMISSIONS {
  return (
    data &&
    typeof data.slug === 'string' &&
    typeof data.title === 'string' &&
    (data.image === null || typeof data.image === 'object') &&
    Array.isArray(data.content) &&
    data.content.every(
      (item: any) =>
        typeof item.title === 'string' &&
        typeof item.content === 'string' &&
        (item.linkButton === null || typeof item.linkButton === 'object') &&
        (item.file === null || typeof item.file === 'object')
    )
  );
}

// Validate data matches expected type
export function validateTableData(type: DynamicTableType, data: any): boolean {
  switch (type) {
    case 'iqac_team_titles':
      return isIQAC_TEAM_TITLES(data);
    case 'iqac_team_members':
      return isIQAC_TEAM_MEMBERS(data);
    case 'iqac_overview':
      return isIQAC_OVERVIEW(data);
    case 'iqac_downloads':
      return isIQAC_DOWNLOAD(data);
    case 'iqac_attachments':
      return isIQAC_ATTACHMENT(data);
    case 'club_and_committee':
      return isCLUB_COMMITTEE(data);
    case 'admissions':
      return isADMISSIONS(data);
    case 'file_uploads':
      return isFILE_UPLOADS(data);
    default:
      return true;
  }
}

export type DynamicTableDataTypes =
  | IQAC_TEAM_MEMBERS
  | IQAC_OVERVIEW
  | IQAC_DOWNLOAD
  | IQAC_ATTACHMENT
  | IQAC_TEAM_TITLES
  | CLUB_COMMITTEE
  | FILE_UPLOADS;
