import { relations, sql } from 'drizzle-orm';
import { integer, text, sqliteTable } from 'drizzle-orm/sqlite-core';
import type { Files } from './files';
import { files } from './files';
import { department } from './department';
import { baseTable } from './base';
import type { Education, Experience } from '~/types/home';

export const faculty = sqliteTable('faculty', {
  ...baseTable, // Extend from base table (id, createdAt, updatedAt)
  name: text('name').notNull(),
  departmentId: integer('department_id')
    .references(() => department.id)
    .notNull(),
  designation: text('designation').notNull(),
  startDate: text('start_date').notNull(),
  endDate: text('end_date'),
  imageId: integer('image_id').references(() => files.id),
  resumeId: integer('resume_id').references(() => files.id),
  // Store education as JSON array of Education objects
  education: text('education', { mode: 'json' })
    .$type<Education[]>()
    .notNull()
    .default(sql`'[]'`),
  // Store experience as JSON array of Experience objects
  experience: text('experience', { mode: 'json' })
    .$type<Experience[]>()
    .notNull()
    .default(sql`'[]'`),
  // Store area of interest as JSON array of strings
  areaOfInterest: text('area_of_interest', { mode: 'json' })
    .$type<string[]>()
    .notNull()
    .default(sql`'[]'`),
  priority: integer('priority').default(0),
});

export const facultyRelations = relations(faculty, ({ one }) => ({
  department: one(department, {
    fields: [faculty.departmentId],
    references: [department.id],
  }),
  resume: one(files, { fields: [faculty.resumeId], references: [files.id] }),
  image: one(files, { fields: [faculty.imageId], references: [files.id] }),
}));

export type Faculty = typeof faculty.$inferSelect;

export type FacultyWithRelations = Faculty & {
  resume: Files;
  image: Files;
};
