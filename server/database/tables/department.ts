import { relations } from 'drizzle-orm';
import { integer, text, sqliteTable } from 'drizzle-orm/sqlite-core';
import { files } from './files';
import { accordion } from './sections/accordion';
import { course } from './course';
import { faculty } from './faculty';
import { baseTable } from './base';
import { announcement } from './announcement';
import { album } from './album';

// Main department table
export const department = sqliteTable('department', {
  ...baseTable,
  name: text('name').notNull(),
  slug: text('slug').unique().notNull(),
  overview: text('overview'),
  achievementId: integer('achievement_id').references(() => accordion.id),
});

// Department files (for question papers, assignments, etc.)
export const departmentFiles = sqliteTable('department_files', {
  ...baseTable,
  title: text('title').notNull(),
  year: integer('year'),
  courseId: integer('course_id').references(() => course.id),
  type: text('type', {
    enum: ['question_paper', 'project', 'download'],
  }).notNull(),
  semester: integer('semester'),
  departmentId: integer('department_id')
    .references(() => department.id)
    .notNull(),
  fileId: integer('file_id')
    .references(() => files.id)
    .notNull(),
});

// Department gallery join table
export const departmentGallery = sqliteTable('department_gallery', {
  ...baseTable,
  departmentId: integer('department_id')
    .references(() => department.id)
    .notNull(),
  galleryId: integer('gallery_id').notNull(),
  displayOrder: integer('display_order').notNull().default(0),
});

// Department event join table
export const departmentEvent = sqliteTable('department_event', {
  ...baseTable,
  departmentId: integer('department_id')
    .references(() => department.id)
    .notNull(),
  eventId: integer('event_id')
    .references(() => announcement.id)
    .notNull(),
  displayOrder: integer('display_order').notNull().default(0),
});

// Department relations
export const departmentRelations = relations(department, ({ many, one }) => ({
  files: many(departmentFiles),
  courses: many(course),
  faculties: many(faculty),
  achievement: one(accordion, {
    fields: [department.achievementId],
    references: [accordion.id],
  }),
  galleries: many(departmentGallery),
  events: many(departmentEvent),
}));

// Department files relations
export const departmentFilesRelations = relations(
  departmentFiles,
  ({ one }) => ({
    department: one(department, {
      fields: [departmentFiles.departmentId],
      references: [department.id],
    }),
    file: one(files, {
      fields: [departmentFiles.fileId],
      references: [files.id],
    }),
    course: one(course, {
      fields: [departmentFiles.courseId],
      references: [course.id],
    }),
  })
);

// Department gallery relations
export const departmentGalleryRelations = relations(
  departmentGallery,
  ({ one, many }) => ({
    department: one(department, {
      fields: [departmentGallery.departmentId],
      references: [department.id],
    }),
    gallery: many(album),
  })
);

// Department event relations
export const departmentEventRelations = relations(
  departmentEvent,
  ({ one }) => ({
    department: one(department, {
      fields: [departmentEvent.departmentId],
      references: [department.id],
    }),
    events: one(announcement, {
      fields: [departmentEvent.eventId],
      references: [announcement.id],
    }),
  })
);
