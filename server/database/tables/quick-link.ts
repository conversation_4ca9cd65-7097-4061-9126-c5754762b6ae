import { sqliteTable, text } from 'drizzle-orm/sqlite-core';

import { baseTable } from './base';
import { QUICK_LINK_TYPES } from './enums';
export const quickLink = sqliteTable(
  'quick_link ',
  {
    ...baseTable,
    title: text('title').unique().notNull(),
    link: text('link'),
    icon: text('icon'),
    type: text('type', {
      enum: QUICK_LINK_TYPES,
    }).notNull(),
  },
  () => []
);

export type QuickLink = typeof quickLink.$inferSelect;
