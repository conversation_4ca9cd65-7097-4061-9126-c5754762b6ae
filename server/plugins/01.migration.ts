import consola from 'consola';
import { migrate } from 'drizzle-orm/d1/migrator';

export default defineNitroPlugin(async () => {
  if (!import.meta.dev) {
    return;
  }

  onHubReady(async () => {
    const db = useDrizzle();
    await migrate(db, { migrationsFolder: 'server/database/migrations' })
      .then(() => {
        consola.success('Database migrated');
      })
      .catch((err) => {
        // Optionally process specific error types
        if (err.message.includes('table already exists')) {
          consola.warn(
            'Table already exists - you may want to check your migration files'
          );
        }
      });
  });
});
