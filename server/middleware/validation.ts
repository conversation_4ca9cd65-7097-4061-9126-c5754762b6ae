import type { ZodError } from 'zod';

export default defineEventHandler((event) => {
  event.context.validation = {
    // Helper to handle zod validation errors consistently
    handleError: (error: unknown) => {
      // Check if error is a Zod validation error
      if (
        error &&
        typeof error === 'object' &&
        'name' in error &&
        error.name === 'ZodError'
      ) {
        const zodError = error as ZodError;
        throw createError({
          statusCode: 400,
          message: 'Validation failed',
          data: zodError.errors,
        });
      }
      // Re-throw other errors
      throw error;
    },
  };
});
