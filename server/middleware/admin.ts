export default defineEventHandler(async (event) => {
  if (event.path === '/api/_hub/**') {
    return;
  }
  if (event.path === '/api/admin/login') {
    // Skip middleware for login route
    return;
  }
  if (event.path.startsWith('/images') || event.path.startsWith('/pdfs')) {
    return;
  }

  // Check if the request is for an admin API route
  if (event.path.startsWith('/api/admin')) {
    try {
      // This will throw an error if the user is not logged in
      await requireUserSession(event);
    } catch {
      throw createError({
        statusCode: 401,
        message: 'Unauthorized',
      });
    }
  }
});
