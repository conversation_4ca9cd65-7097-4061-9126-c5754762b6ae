{
  "cSpell.words": [
    "aqar",
    "bunx",
    "ESERVICES",
    "iqac",
    "IQAC",
    "lefthook",
    "lockb",
    "nirf",
    "nuxt",
    "nuxthub",
    "nuxtjs",
    "oxlint",
    "pdfs",
    "pinia",
    "Raleway",
    "saac",
    "shadcn",
    "unenv",
    "vaul",
    "vueuse",
    "wght"
  ],
  "notebook.formatOnSave.enabled": false,
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
    "source.sortMembers": "explicit",
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // vscode.typescript-language-features
  "editor.formatOnType": false,
  "editor.formatOnPaste": true,
  "prettier.singleQuote": true,
  "prettier.jsxSingleQuote": true,
  "cSpell.enabled": true,
  "cSpell.logLevel": "Debug",
  "editor.tabSize": 2,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSaveMode": "file",
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "sequential-thinking",
        "command": "bunx",
        "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]
      }
    ]
  }
}
