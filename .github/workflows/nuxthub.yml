name: Deploy to NuxtHub
on: push

jobs:
  deploy:
    name: 'Deploy to NuxtHub'
    runs-on: blacksmith-8vcpu-ubuntu-2204-arm
    environment:
      name: ${{ github.ref == 'refs/heads/main' && 'production' || 'preview' }}
      url: ${{ steps.deploy.outputs.deployment-url }}
    permissions:
      contents: read
      id-token: write

    steps:
      - uses: actions/checkout@v4

      - name: Install Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Ensure NuxtHub module is installed
        run: bunx nuxthub@latest ensure

      - name: Build application
        run: bun run build
        # You can uncomment and add any environment variables needed for build
        # env:
        #   NUXT_PUBLIC_VAR: ${{ vars.NUXT_PUBLIC_VAR }}
        #   NUXT_SECRET_VAR: ${{ secrets.NUXT_SECRET_VAR }}

      - name: Deploy to NuxtHub
        uses: nuxt-hub/action@v1
        id: deploy
        with:
          project-key: donbosco-iz38
          # Uncomment if you have a custom build directory
          # directory: dist
