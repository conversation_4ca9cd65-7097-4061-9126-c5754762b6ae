declare module '#auth-utils' {
  interface User {
    id: number;
    username: string;
    email: string;
    fullName: string;
    role: 'admin' | 'viewer';
  }

  interface UserSession {
    loggedInAt: string;
  }

  // @ts-expect-error -- This will be populated with secure data later
  interface SecureSessionData {
    // Placeholder property to avoid empty interface warning
    _placeholder?: never;
    // Add secure fields here if needed
  }
}

export {};
