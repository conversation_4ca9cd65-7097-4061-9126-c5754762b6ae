---
description: Bun Exclusive Package Manager Rule
globs: 
alwaysApply: false
---
# Your rule content

## Bun Exclusive Package Manager Rule

- Only use Bun for all package management operations
- Replace npm/yarn/pnpm commands with Bun equivalents
- Enforce Bun for all project initializations

### Command Mappings

```
# Installation
npm install -> bun install
yarn add -> bun add
pnpm add -> bun add

# Running scripts
npm run -> bun run
yarn -> bun run
pnpm run -> bun run

# Development
npm start -> bun start
yarn dev -> bun dev
pnpm dev -> bun dev

# Building
npm build -> bun build
yarn build -> bun build
pnpm build -> bun build

# Project initialization
npm init -> bun init
yarn init -> bun init
pnpm init -> bun init
```

### Agent Instructions

When terminal commands are detected using npm, yarn, or pnpm, automatically suggest or replace with the Bun equivalent. Alert the user that only Bun should be used as the package manager.

### Performance Benefits

- Bun is significantly faster than other package managers
- Provides built-in bundling, testing, and runtime capabilities
- Reduces project configuration complexity
- Ensures consistent dependency management across projects